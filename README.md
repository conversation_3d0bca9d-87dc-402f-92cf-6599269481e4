# ui

## Project setup
```
yarn install
```

### Compiles and hot-reloads for development
```
yarn serve
```

### Compiles and minifies for production
```
yarn build
```

### Lints and fixes files
```
yarn lint
```

### Vue Router Installation and Setup

This project uses Vue Router for navigation. If you need to install or configure Vue Router:

#### Install Vue Router
```bash
# Install Vue Router 4 (for Vue 3)
yarn add vue-router@4

# Or using npm
npm install vue-router@4
```

#### Basic Router Configuration
The router is already configured in `src/router/index.js`. To add new routes:

1. Create your component in `src/views/`
2. Import and add the route in `src/router/index.js`:

```javascript
import { createRouter, createWebHistory } from 'vue-router'
import YourNewComponent from '../views/YourNewComponent.vue'

const routes = [
  // existing routes...
  {
    path: '/your-path',
    name: 'YourComponentName',
    component: YourNewComponent
  }
]
```

#### Using Router in Components
```javascript
// In your Vue component
import { useRouter } from 'vue-router'

export default {
  setup() {
    const router = useRouter()

    const navigateToPage = () => {
      router.push('/your-path')
    }

    return { navigateToPage }
  }
}
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

