# 脊柱侧弯筛查系统 - 前端项目

一个基于 Vue 3 + Vant UI 的现代化移动端医疗管理系统，专注于脊柱健康检测与管理。

## 📋 目录

- [技术栈](#技术栈)
- [环境要求](#环境要求)
- [快速开始](#快速开始)
- [项目结构](#项目结构)
- [Vue Router 配置](#vue-router-配置)
- [Vant UI 使用指南](#vant-ui-使用指南)
- [开发指南](#开发指南)
- [构建部署](#构建部署)
- [常见问题](#常见问题)

## 🛠 技术栈

### 核心框架
- **Vue 3.2.13** - 渐进式 JavaScript 框架，支持 Composition API
- **Vue Router 4.5.1** - Vue.js 官方路由管理器
- **Vant 4.9.21** - 轻量、可靠的移动端 Vue 组件库

### 开发工具
- **Vue CLI 5.0** - Vue.js 开发的标准工具
- **Webpack** - 模块打包器（通过 Vue CLI 集成）
- **Babel** - JavaScript 编译器
- **ESLint** - 代码质量检查工具

### 功能库
- **Axios 1.10.0** - HTTP 客户端库
- **qrcode.vue 3.6.0** - Vue 二维码生成组件
- **Core-js 3.8.3** - JavaScript 标准库的 polyfill

### 包管理器
- **Yarn** - 快速、可靠、安全的依赖管理工具

## 📋 环境要求

在开始之前，请确保您的开发环境满足以下要求：

### 必需软件
- **Node.js** >= 14.0.0 (推荐使用 LTS 版本)
- **Yarn** >= 1.22.0 (包管理器)

### 推荐工具
- **VS Code** - 代码编辑器
- **Vue DevTools** - Vue.js 开发者工具
- **Git** - 版本控制工具

### 检查环境
```bash
# 检查 Node.js 版本
node --version

# 检查 Yarn 版本
yarn --version

# 如果没有安装 Yarn，可以通过 npm 安装
npm install -g yarn
```

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <项目地址>
cd wr_ui
```

### 2. 安装依赖
```bash
# 使用 Yarn 安装依赖（推荐）
yarn install

# 或者使用 npm（不推荐，因为项目使用 yarn.lock）
npm install
```

### 3. 启动开发服务器
```bash
# 启动开发服务器，支持热重载
yarn serve

# 服务器启动后，在浏览器中访问：
# http://localhost:8080
```

### 4. 构建生产版本
```bash
# 构建生产环境代码
yarn build

# 构建完成后，dist/ 目录包含可部署的文件
```

### 5. 代码检查和修复
```bash
# 运行 ESLint 检查代码质量
yarn lint

# 自动修复可修复的代码问题
yarn lint --fix
```

## 📁 项目结构

```
wr_ui/
├── public/                 # 静态资源目录
│   ├── favicon.ico        # 网站图标
│   └── index.html         # HTML 模板
├── src/                   # 源代码目录
│   ├── assets/           # 静态资源（图片、样式等）
│   │   └── logo.png      # 项目 Logo
│   ├── components/       # 可复用组件
│   │   └── HelloWorld.vue
│   ├── router/           # 路由配置
│   │   └── index.js      # 路由定义和配置
│   ├── views/            # 页面组件
│   │   ├── Home.vue      # 主页
│   │   └── Login.vue     # 登录页
│   ├── App.vue           # 根组件
│   └── main.js           # 应用入口文件
├── babel.config.js        # Babel 配置
├── jsconfig.json         # JavaScript 项目配置
├── package.json          # 项目依赖和脚本
├── vue.config.js         # Vue CLI 配置
├── yarn.lock             # Yarn 锁定文件
└── README.md             # 项目说明文档
```

### 核心文件说明

- **src/main.js**: 应用程序的入口点，配置 Vue 实例、路由和 UI 库
- **src/App.vue**: 根组件，包含应用的基本布局
- **src/router/index.js**: 路由配置，定义页面路径和组件映射
- **src/views/**: 存放页面级组件
- **src/components/**: 存放可复用的组件

## 🛣 Vue Router 配置

本项目使用 Vue Router 4 进行路由管理，支持现代化的路由功能。

### 基本配置

```javascript
import { createRouter, createWebHistory } from 'vue-router'
import LoginPage from '../views/Login.vue'
import HomePage from '../views/Home.vue'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'LoginPage',
    component: LoginPage,
    meta: {
      title: '登录 - 脊柱侧弯筛查系统'
    }
  }
]
```

### 路由功能特性

#### 1. 路由守卫
项目实现了完整的路由守卫机制：

- **身份验证**: 检查用户登录状态
- **页面标题**: 自动设置页面标题
- **重定向逻辑**: 智能的页面跳转

#### 2. 路由元信息 (meta)
```javascript
{
  path: '/home',
  name: 'HomePage',
  component: HomePage,
  meta: {
    title: '用户管理 - 脊柱侧弯筛查系统',
    requiresAuth: true  // 需要登录验证
  }
}
```

#### 3. 编程式导航
在组件中使用路由：

```javascript
import { useRouter } from 'vue-router'

export default {
  setup() {
    const router = useRouter()

    // 跳转到指定页面
    const goToHome = () => {
      router.push('/home')
    }

    // 返回上一页
    const goBack = () => {
      router.back()
    }

    return { goToHome, goBack }
  }
}
```

### 添加新路由

1. **创建页面组件**（在 `src/views/` 目录下）
2. **在路由配置中添加路由**：

```javascript
// src/router/index.js
import NewPage from '../views/NewPage.vue'

const routes = [
  // ... 现有路由
  {
    path: '/new-page',
    name: 'NewPage',
    component: NewPage,
    meta: {
      title: '新页面标题',
      requiresAuth: true  // 如果需要登录验证
    }
  }
]
```

## 🎨 Vant UI 使用指南

本项目使用 Vant 4 作为 UI 组件库，这是一个轻量、可靠的移动端 Vue 组件库。

### 全局引入配置

```javascript
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Vant from 'vant'
import 'vant/lib/index.css'

const app = createApp(App)
app.use(router)
app.use(Vant)  // 全局注册 Vant 组件
app.mount('#app')
```

### 常用组件示例

#### 1. 表单组件
```vue
<template>
  <van-form @submit="onSubmit">
    <van-field
      v-model="username"
      name="username"
      label="用户名"
      placeholder="请输入用户名"
      :rules="[{ required: true, message: '请填写用户名' }]"
    />
    <van-field
      v-model="password"
      type="password"
      name="password"
      label="密码"
      placeholder="请输入密码"
      :rules="[{ required: true, message: '请填写密码' }]"
    />
    <div style="margin: 16px;">
      <van-button round block type="primary" native-type="submit">
        提交
      </van-button>
    </div>
  </van-form>
</template>
```

#### 2. 导航组件
```vue
<template>
  <!-- 顶部导航栏 -->
  <van-nav-bar
    title="页面标题"
    left-text="返回"
    right-text="按钮"
    left-arrow
    @click-left="onClickLeft"
    @click-right="onClickRight"
  />

  <!-- 底部标签栏 -->
  <van-tabbar v-model="active">
    <van-tabbar-item icon="home-o">首页</van-tabbar-item>
    <van-tabbar-item icon="search">搜索</van-tabbar-item>
    <van-tabbar-item icon="friends-o">朋友</van-tabbar-item>
    <van-tabbar-item icon="setting-o">设置</van-tabbar-item>
  </van-tabbar>
</template>
```

#### 3. 反馈组件
```vue
<template>
  <div>
    <!-- 消息提示 -->
    <van-button @click="showToast">显示提示</van-button>

    <!-- 对话框 -->
    <van-button @click="showDialog">显示对话框</van-button>

    <!-- 加载状态 -->
    <van-loading type="spinner" color="#1989fa" />
  </div>
</template>

<script>
import { showToast, showDialog } from 'vant'

export default {
  methods: {
    showToast() {
      showToast('提示信息')
    },
    showDialog() {
      showDialog({
        title: '标题',
        message: '弹窗内容'
      })
    }
  }
}
</script>
```

### 按需引入（可选优化）

如果需要减小包体积，可以使用按需引入：

1. **安装 babel 插件**：
```bash
yarn add babel-plugin-import -D
```

2. **配置 babel.config.js**：
```javascript
module.exports = {
  presets: [
    '@vue/cli-plugin-babel/preset'
  ],
  plugins: [
    ['import', {
      libraryName: 'vant',
      libraryDirectory: 'es',
      style: true
    }, 'vant']
  ]
}
```

3. **在组件中按需引入**：
```javascript
import { Button, Field } from 'vant'

export default {
  components: {
    [Button.name]: Button,
    [Field.name]: Field
  }
}
```

### Vant 主题定制

可以通过 CSS 变量自定义 Vant 主题：

```css
/* 在全局样式文件中 */
:root {
  --van-primary-color: #07c160;
  --van-success-color: #07c160;
  --van-danger-color: #ee0a24;
  --van-warning-color: #ff976a;
  --van-text-color: #323233;
  --van-background-color: #f7f8fa;
}
```
## 💻 开发指南

### Vue 3 Composition API 使用

本项目推荐使用 Vue 3 的 Composition API 进行开发：

```vue
<template>
  <div>
    <h1>{{ title }}</h1>
    <p>计数器: {{ count }}</p>
    <van-button @click="increment">增加</van-button>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'ExampleComponent',
  setup() {
    // 响应式数据
    const title = ref('示例页面')
    const count = ref(0)

    // 方法
    const increment = () => {
      count.value++
    }

    // 生命周期
    onMounted(() => {
      console.log('组件已挂载')
    })

    // 返回模板需要的数据和方法
    return {
      title,
      count,
      increment
    }
  }
}
</script>
```

### 代码规范

项目使用 ESLint 进行代码质量检查，请遵循以下规范：

#### 1. 组件命名
- 组件文件使用 PascalCase：`UserProfile.vue`
- 组件名称使用多个单词：`UserList.vue`（避免与 HTML 元素冲突）

#### 2. 变量命名
- 使用 camelCase：`userName`, `isLoggedIn`
- 常量使用 UPPER_SNAKE_CASE：`API_BASE_URL`

#### 3. 文件组织
```
src/
├── components/          # 可复用组件
│   ├── common/         # 通用组件
│   └── business/       # 业务组件
├── views/              # 页面组件
├── utils/              # 工具函数
├── api/                # API 接口
├── store/              # 状态管理（如使用 Vuex/Pinia）
└── styles/             # 样式文件
```

### API 请求处理

使用 Axios 进行 HTTP 请求：

```javascript
// api/user.js
import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:3000',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加认证 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    // 统一错误处理
    console.error('API 请求错误:', error)
    return Promise.reject(error)
  }
)

// 用户相关 API
export const userApi = {
  login: (credentials) => api.post('/auth/login', credentials),
  getUserInfo: () => api.get('/user/profile'),
  updateProfile: (data) => api.put('/user/profile', data)
}
```

### 环境变量配置

创建环境变量文件：

```bash
# .env.development (开发环境)
VUE_APP_API_BASE_URL=http://localhost:3000
VUE_APP_ENV=development

# .env.production (生产环境)
VUE_APP_API_BASE_URL=https://api.example.com
VUE_APP_ENV=production
```

在代码中使用：
```javascript
const apiUrl = process.env.VUE_APP_API_BASE_URL
```

## 🚀 构建部署

### 开发环境
```bash
# 启动开发服务器
yarn serve

# 开发服务器配置
# - 端口: 8080 (可在 vue.config.js 中修改)
# - 热重载: 已启用
# - 代理: 可配置 API 代理
```

### 生产构建
```bash
# 构建生产版本
yarn build

# 构建输出
# - 输出目录: dist/
# - 资源优化: 已启用
# - 代码分割: 已启用
```

### 部署选项

#### 1. 静态文件服务器
```bash
# 使用 serve 工具部署
npm install -g serve
serve -s dist -l 8080
```

#### 2. Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 处理 Vue Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 3. Docker 部署
```dockerfile
# Dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```
## ❓ 常见问题

### 安装和环境问题

#### Q: yarn install 失败怎么办？
**A:** 尝试以下解决方案：
```bash
# 清除 yarn 缓存
yarn cache clean

# 删除 node_modules 和 yarn.lock，重新安装
rm -rf node_modules yarn.lock
yarn install

# 如果网络问题，可以使用国内镜像
yarn config set registry https://registry.npmmirror.com
```

#### Q: Node.js 版本不兼容怎么办？
**A:** 确保使用 Node.js 14+ 版本：
```bash
# 检查当前版本
node --version

# 使用 nvm 管理 Node.js 版本
nvm install 16
nvm use 16
```

#### Q: 端口 8080 被占用怎么办？
**A:** 修改 `vue.config.js` 文件：
```javascript
module.exports = {
  devServer: {
    port: 3000  // 修改为其他端口
  }
}
```

### 开发问题

#### Q: ESLint 报错怎么处理？
**A:** 常见解决方案：
```bash
# 自动修复可修复的问题
yarn lint --fix

# 如果需要忽略某些规则，在文件顶部添加：
/* eslint-disable vue/multi-word-component-names */

# 或在 .eslintrc.js 中配置规则
```

#### Q: Vant 组件样式不生效？
**A:** 确保正确引入样式：
```javascript
// main.js 中确保引入了样式
import 'vant/lib/index.css'

// 如果使用按需引入，确保 babel 配置正确
```

#### Q: 路由跳转不生效？
**A:** 检查以下几点：
1. 路由配置是否正确
2. 组件是否正确导入
3. 路由守卫是否阻止了跳转
4. 使用 `router.push()` 而不是 `window.location`

#### Q: API 请求跨域问题？
**A:** 在 `vue.config.js` 中配置代理：
```javascript
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
}
```

### 构建和部署问题

#### Q: 构建后页面空白？
**A:** 可能的原因和解决方案：
1. **路由模式问题**：如果部署在子目录，需要配置 `publicPath`
```javascript
// vue.config.js
module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/your-app/' : '/'
}
```

2. **路由 history 模式**：需要服务器支持，或改为 hash 模式
```javascript
// router/index.js
const router = createRouter({
  history: createWebHashHistory(), // 使用 hash 模式
  routes
})
```

#### Q: 打包后文件过大？
**A:** 优化建议：
1. 使用 Vant 按需引入
2. 启用代码分割
3. 分析打包文件：
```bash
yarn add webpack-bundle-analyzer -D
yarn build --report
```

### Vue 3 相关问题

#### Q: 从 Vue 2 迁移需要注意什么？
**A:** 主要变化：
1. **Composition API**：推荐使用新的组合式 API
2. **生命周期**：`beforeDestroy` → `beforeUnmount`
3. **全局 API**：`Vue.use()` → `app.use()`
4. **事件总线**：不再支持，使用 provide/inject 或状态管理

#### Q: Composition API 和 Options API 可以混用吗？
**A:** 可以，但建议在新项目中统一使用 Composition API：
```vue
<script>
import { ref } from 'vue'

export default {
  // Options API
  data() {
    return {
      message: 'Hello'
    }
  },
  // Composition API
  setup() {
    const count = ref(0)
    return { count }
  }
}
</script>
```

## 📚 学习资源

### 官方文档
- [Vue 3 官方文档](https://cn.vuejs.org/)
- [Vue Router 4 文档](https://router.vuejs.org/zh/)
- [Vant 4 文档](https://vant-ui.github.io/vant/#/zh-CN)
- [Vue CLI 文档](https://cli.vuejs.org/zh/)

### 推荐教程
- [Vue 3 入门教程](https://cn.vuejs.org/tutorial/)
- [Composition API 指南](https://cn.vuejs.org/guide/composition-api-introduction.html)
- [移动端开发最佳实践](https://github.com/mcuking/mobile-web-best-practice)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如果您有任何问题或建议，请通过以下方式联系：

- 项目 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**感谢使用脊柱侧弯筛查系统！** 🎉