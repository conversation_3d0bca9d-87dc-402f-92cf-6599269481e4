[{"/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/main.js": "1", "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/App.vue": "2", "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/router/index.js": "3", "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/views/Login.vue": "4", "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/views/Home.vue": "5"}, {"size": 218, "mtime": 1754023776728, "results": "6", "hashOfConfig": "7"}, {"size": 535, "mtime": 1754023902275, "results": "8", "hashOfConfig": "7"}, {"size": 1182, "mtime": 1754024068014, "results": "9", "hashOfConfig": "7"}, {"size": 7489, "mtime": 1754024080754, "results": "10", "hashOfConfig": "7"}, {"size": 21971, "mtime": 1754024343539, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xtjjf3", {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/main.js", [], "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/App.vue", [], "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/router/index.js", [], "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/views/Login.vue", [], "/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/views/Home.vue", []]