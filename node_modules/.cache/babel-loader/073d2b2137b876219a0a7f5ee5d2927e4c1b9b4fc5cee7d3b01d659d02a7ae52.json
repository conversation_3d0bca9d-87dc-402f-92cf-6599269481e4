{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { watch, computed, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { pick, extend, truthProp, createNamespace } from \"../utils/index.mjs\";\nimport { CHECKBOX_GROUP_KEY } from \"../checkbox-group/CheckboxGroup.mjs\";\nimport { useParent, useCustomFieldValue } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport Checker, { checkerProps } from \"./Checker.mjs\";\nconst [name, bem] = createNamespace(\"checkbox\");\nconst checkboxProps = extend({}, checkerProps, {\n  shape: String,\n  bindGroup: truthProp,\n  indeterminate: {\n    type: Boolean,\n    default: null\n  }\n});\nvar stdin_default = defineComponent({\n  name,\n  props: checkboxProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      parent\n    } = useParent(CHECKBOX_GROUP_KEY);\n    const setParentValue = checked2 => {\n      const {\n        name: name2\n      } = props;\n      const {\n        max,\n        modelValue\n      } = parent.props;\n      const value = modelValue.slice();\n      if (checked2) {\n        const overlimit = max && value.length >= +max;\n        if (!overlimit && !value.includes(name2)) {\n          value.push(name2);\n          if (props.bindGroup) {\n            parent.updateValue(value);\n          }\n        }\n      } else {\n        const index = value.indexOf(name2);\n        if (index !== -1) {\n          value.splice(index, 1);\n          if (props.bindGroup) {\n            parent.updateValue(value);\n          }\n        }\n      }\n    };\n    const checked = computed(() => {\n      if (parent && props.bindGroup) {\n        return parent.props.modelValue.indexOf(props.name) !== -1;\n      }\n      return !!props.modelValue;\n    });\n    const toggle = (newValue = !checked.value) => {\n      if (parent && props.bindGroup) {\n        setParentValue(newValue);\n      } else {\n        emit(\"update:modelValue\", newValue);\n      }\n      if (props.indeterminate !== null) emit(\"change\", newValue);\n    };\n    watch(() => props.modelValue, value => {\n      if (props.indeterminate === null) emit(\"change\", value);\n    });\n    useExpose({\n      toggle,\n      props,\n      checked\n    });\n    useCustomFieldValue(() => props.modelValue);\n    return () => _createVNode(Checker, _mergeProps({\n      \"bem\": bem,\n      \"role\": \"checkbox\",\n      \"parent\": parent,\n      \"checked\": checked.value,\n      \"onToggle\": toggle\n    }, props), pick(slots, [\"default\", \"icon\"]));\n  }\n});\nexport { checkboxProps, stdin_default as default };", "map": {"version": 3, "names": ["watch", "computed", "defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "pick", "extend", "truthProp", "createNamespace", "CHECKBOX_GROUP_KEY", "useParent", "useCustomFieldValue", "useExpose", "Checker", "checkerProps", "name", "bem", "checkboxProps", "shape", "String", "bindGroup", "indeterminate", "type", "Boolean", "default", "stdin_default", "props", "emits", "setup", "emit", "slots", "parent", "setParentValue", "checked2", "name2", "max", "modelValue", "value", "slice", "overlimit", "length", "includes", "push", "updateValue", "index", "indexOf", "splice", "checked", "toggle", "newValue"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/checkbox/Checkbox.mjs"], "sourcesContent": ["import { watch, computed, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { pick, extend, truthProp, createNamespace } from \"../utils/index.mjs\";\nimport { CHECKBOX_GROUP_KEY } from \"../checkbox-group/CheckboxGroup.mjs\";\nimport { useParent, useCustomFieldValue } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport Checker, { checkerProps } from \"./Checker.mjs\";\nconst [name, bem] = createNamespace(\"checkbox\");\nconst checkboxProps = extend({}, checkerProps, {\n  shape: String,\n  bindGroup: truthProp,\n  indeterminate: {\n    type: Boolean,\n    default: null\n  }\n});\nvar stdin_default = defineComponent({\n  name,\n  props: checkboxProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      parent\n    } = useParent(CHECKBOX_GROUP_KEY);\n    const setParentValue = (checked2) => {\n      const {\n        name: name2\n      } = props;\n      const {\n        max,\n        modelValue\n      } = parent.props;\n      const value = modelValue.slice();\n      if (checked2) {\n        const overlimit = max && value.length >= +max;\n        if (!overlimit && !value.includes(name2)) {\n          value.push(name2);\n          if (props.bindGroup) {\n            parent.updateValue(value);\n          }\n        }\n      } else {\n        const index = value.indexOf(name2);\n        if (index !== -1) {\n          value.splice(index, 1);\n          if (props.bindGroup) {\n            parent.updateValue(value);\n          }\n        }\n      }\n    };\n    const checked = computed(() => {\n      if (parent && props.bindGroup) {\n        return parent.props.modelValue.indexOf(props.name) !== -1;\n      }\n      return !!props.modelValue;\n    });\n    const toggle = (newValue = !checked.value) => {\n      if (parent && props.bindGroup) {\n        setParentValue(newValue);\n      } else {\n        emit(\"update:modelValue\", newValue);\n      }\n      if (props.indeterminate !== null) emit(\"change\", newValue);\n    };\n    watch(() => props.modelValue, (value) => {\n      if (props.indeterminate === null) emit(\"change\", value);\n    });\n    useExpose({\n      toggle,\n      props,\n      checked\n    });\n    useCustomFieldValue(() => props.modelValue);\n    return () => _createVNode(Checker, _mergeProps({\n      \"bem\": bem,\n      \"role\": \"checkbox\",\n      \"parent\": parent,\n      \"checked\": checked.value,\n      \"onToggle\": toggle\n    }, props), pick(slots, [\"default\", \"icon\"]));\n  }\n});\nexport {\n  checkboxProps,\n  stdin_default as default\n};\n"], "mappings": ";AAAA,SAASA,KAAK,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC9G,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,QAAQ,oBAAoB;AAC7E,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,SAAS,EAAEC,mBAAmB,QAAQ,WAAW;AAC1D,SAASC,SAAS,QAAQ,+BAA+B;AACzD,OAAOC,OAAO,IAAIC,YAAY,QAAQ,eAAe;AACrD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGR,eAAe,CAAC,UAAU,CAAC;AAC/C,MAAMS,aAAa,GAAGX,MAAM,CAAC,CAAC,CAAC,EAAEQ,YAAY,EAAE;EAC7CI,KAAK,EAAEC,MAAM;EACbC,SAAS,EAAEb,SAAS;EACpBc,aAAa,EAAE;IACbC,IAAI,EAAEC,OAAO;IACbC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,IAAIC,aAAa,GAAGzB,eAAe,CAAC;EAClCe,IAAI;EACJW,KAAK,EAAET,aAAa;EACpBU,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAM;MACJC;IACF,CAAC,GAAGrB,SAAS,CAACD,kBAAkB,CAAC;IACjC,MAAMuB,cAAc,GAAIC,QAAQ,IAAK;MACnC,MAAM;QACJlB,IAAI,EAAEmB;MACR,CAAC,GAAGR,KAAK;MACT,MAAM;QACJS,GAAG;QACHC;MACF,CAAC,GAAGL,MAAM,CAACL,KAAK;MAChB,MAAMW,KAAK,GAAGD,UAAU,CAACE,KAAK,CAAC,CAAC;MAChC,IAAIL,QAAQ,EAAE;QACZ,MAAMM,SAAS,GAAGJ,GAAG,IAAIE,KAAK,CAACG,MAAM,IAAI,CAACL,GAAG;QAC7C,IAAI,CAACI,SAAS,IAAI,CAACF,KAAK,CAACI,QAAQ,CAACP,KAAK,CAAC,EAAE;UACxCG,KAAK,CAACK,IAAI,CAACR,KAAK,CAAC;UACjB,IAAIR,KAAK,CAACN,SAAS,EAAE;YACnBW,MAAM,CAACY,WAAW,CAACN,KAAK,CAAC;UAC3B;QACF;MACF,CAAC,MAAM;QACL,MAAMO,KAAK,GAAGP,KAAK,CAACQ,OAAO,CAACX,KAAK,CAAC;QAClC,IAAIU,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBP,KAAK,CAACS,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;UACtB,IAAIlB,KAAK,CAACN,SAAS,EAAE;YACnBW,MAAM,CAACY,WAAW,CAACN,KAAK,CAAC;UAC3B;QACF;MACF;IACF,CAAC;IACD,MAAMU,OAAO,GAAGhD,QAAQ,CAAC,MAAM;MAC7B,IAAIgC,MAAM,IAAIL,KAAK,CAACN,SAAS,EAAE;QAC7B,OAAOW,MAAM,CAACL,KAAK,CAACU,UAAU,CAACS,OAAO,CAACnB,KAAK,CAACX,IAAI,CAAC,KAAK,CAAC,CAAC;MAC3D;MACA,OAAO,CAAC,CAACW,KAAK,CAACU,UAAU;IAC3B,CAAC,CAAC;IACF,MAAMY,MAAM,GAAGA,CAACC,QAAQ,GAAG,CAACF,OAAO,CAACV,KAAK,KAAK;MAC5C,IAAIN,MAAM,IAAIL,KAAK,CAACN,SAAS,EAAE;QAC7BY,cAAc,CAACiB,QAAQ,CAAC;MAC1B,CAAC,MAAM;QACLpB,IAAI,CAAC,mBAAmB,EAAEoB,QAAQ,CAAC;MACrC;MACA,IAAIvB,KAAK,CAACL,aAAa,KAAK,IAAI,EAAEQ,IAAI,CAAC,QAAQ,EAAEoB,QAAQ,CAAC;IAC5D,CAAC;IACDnD,KAAK,CAAC,MAAM4B,KAAK,CAACU,UAAU,EAAGC,KAAK,IAAK;MACvC,IAAIX,KAAK,CAACL,aAAa,KAAK,IAAI,EAAEQ,IAAI,CAAC,QAAQ,EAAEQ,KAAK,CAAC;IACzD,CAAC,CAAC;IACFzB,SAAS,CAAC;MACRoC,MAAM;MACNtB,KAAK;MACLqB;IACF,CAAC,CAAC;IACFpC,mBAAmB,CAAC,MAAMe,KAAK,CAACU,UAAU,CAAC;IAC3C,OAAO,MAAMhC,YAAY,CAACS,OAAO,EAAEX,WAAW,CAAC;MAC7C,KAAK,EAAEc,GAAG;MACV,MAAM,EAAE,UAAU;MAClB,QAAQ,EAAEe,MAAM;MAChB,SAAS,EAAEgB,OAAO,CAACV,KAAK;MACxB,UAAU,EAAEW;IACd,CAAC,EAAEtB,KAAK,CAAC,EAAErB,IAAI,CAACyB,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;EAC9C;AACF,CAAC,CAAC;AACF,SACEb,aAAa,EACbQ,aAAa,IAAID,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}