{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Watermark from \"./Watermark.mjs\";\nconst Watermark = withInstall(_Watermark);\nvar stdin_default = Watermark;\nimport { watermarkProps } from \"./Watermark.mjs\";\nexport { Watermark, stdin_default as default, watermarkProps };", "map": {"version": 3, "names": ["withInstall", "_Watermark", "Watermark", "stdin_default", "watermarkProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/watermark/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Watermark from \"./Watermark.mjs\";\nconst Watermark = withInstall(_Watermark);\nvar stdin_default = Watermark;\nimport { watermarkProps } from \"./Watermark.mjs\";\nexport {\n  Watermark,\n  stdin_default as default,\n  watermarkProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SAASE,cAAc,QAAQ,iBAAiB;AAChD,SACEF,SAAS,EACTC,aAAa,IAAIE,OAAO,EACxBD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}