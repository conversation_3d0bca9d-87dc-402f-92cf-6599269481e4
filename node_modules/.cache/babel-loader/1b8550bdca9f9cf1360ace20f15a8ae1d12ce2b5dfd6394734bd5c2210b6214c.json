{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, computed, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, makeNumberProp, makeArrayProp, makeStringProp, addUnit } from \"../utils/index.mjs\";\nconst props = {\n  figureArr: makeArrayProp(),\n  delay: Number,\n  duration: makeNumberProp(2),\n  isStart: <PERSON><PERSON><PERSON>,\n  direction: makeStringProp(\"down\"),\n  height: makeNumberProp(40)\n};\nconst [name, bem] = createNamespace(\"rolling-text-item\");\nvar stdin_default = defineComponent({\n  name,\n  props,\n  setup(props2) {\n    const newFigureArr = computed(() => props2.direction === \"down\" ? props2.figureArr.slice().reverse() : props2.figureArr);\n    const translatePx = computed(() => {\n      const totalHeight = props2.height * (props2.figureArr.length - 1);\n      return `-${totalHeight}px`;\n    });\n    const itemStyle = computed(() => ({\n      lineHeight: addUnit(props2.height)\n    }));\n    const rootStyle = computed(() => ({\n      height: addUnit(props2.height),\n      \"--van-translate\": translatePx.value,\n      \"--van-duration\": props2.duration + \"s\",\n      \"--van-delay\": props2.delay + \"s\"\n    }));\n    return () => _createVNode(\"div\", {\n      \"class\": bem([props2.direction]),\n      \"style\": rootStyle.value\n    }, [_createVNode(\"div\", {\n      \"class\": bem(\"box\", {\n        animate: props2.isStart\n      })\n    }, [Array.isArray(newFigureArr.value) && newFigureArr.value.map(figure => _createVNode(\"div\", {\n      \"class\": bem(\"item\"),\n      \"style\": itemStyle.value\n    }, [figure]))])]);\n  }\n});\nexport { stdin_default as default, props };", "map": {"version": 3, "names": ["defineComponent", "computed", "createVNode", "_createVNode", "createNamespace", "makeNumberProp", "makeArrayProp", "makeStringProp", "addUnit", "props", "figureArr", "delay", "Number", "duration", "isStart", "Boolean", "direction", "height", "name", "bem", "stdin_default", "setup", "props2", "newFigureArr", "slice", "reverse", "translatePx", "totalHeight", "length", "itemStyle", "lineHeight", "rootStyle", "value", "animate", "Array", "isArray", "map", "figure", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/rolling-text/RollingTextItem.mjs"], "sourcesContent": ["import { defineComponent, computed, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, makeNumberProp, makeArrayProp, makeStringProp, addUnit } from \"../utils/index.mjs\";\nconst props = {\n  figureArr: makeArrayProp(),\n  delay: Number,\n  duration: makeNumberProp(2),\n  isStart: <PERSON><PERSON><PERSON>,\n  direction: makeStringProp(\"down\"),\n  height: makeNumberProp(40)\n};\nconst [name, bem] = createNamespace(\"rolling-text-item\");\nvar stdin_default = defineComponent({\n  name,\n  props,\n  setup(props2) {\n    const newFigureArr = computed(() => props2.direction === \"down\" ? props2.figureArr.slice().reverse() : props2.figureArr);\n    const translatePx = computed(() => {\n      const totalHeight = props2.height * (props2.figureArr.length - 1);\n      return `-${totalHeight}px`;\n    });\n    const itemStyle = computed(() => ({\n      lineHeight: addUnit(props2.height)\n    }));\n    const rootStyle = computed(() => ({\n      height: addUnit(props2.height),\n      \"--van-translate\": translatePx.value,\n      \"--van-duration\": props2.duration + \"s\",\n      \"--van-delay\": props2.delay + \"s\"\n    }));\n    return () => _createVNode(\"div\", {\n      \"class\": bem([props2.direction]),\n      \"style\": rootStyle.value\n    }, [_createVNode(\"div\", {\n      \"class\": bem(\"box\", {\n        animate: props2.isStart\n      })\n    }, [Array.isArray(newFigureArr.value) && newFigureArr.value.map((figure) => _createVNode(\"div\", {\n      \"class\": bem(\"item\"),\n      \"style\": itemStyle.value\n    }, [figure]))])]);\n  }\n});\nexport {\n  stdin_default as default,\n  props\n};\n"], "mappings": ";;AAAA,SAASA,eAAe,EAAEC,QAAQ,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,eAAe,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAEC,OAAO,QAAQ,oBAAoB;AAC5G,MAAMC,KAAK,GAAG;EACZC,SAAS,EAAEJ,aAAa,CAAC,CAAC;EAC1BK,KAAK,EAAEC,MAAM;EACbC,QAAQ,EAAER,cAAc,CAAC,CAAC,CAAC;EAC3BS,OAAO,EAAEC,OAAO;EAChBC,SAAS,EAAET,cAAc,CAAC,MAAM,CAAC;EACjCU,MAAM,EAAEZ,cAAc,CAAC,EAAE;AAC3B,CAAC;AACD,MAAM,CAACa,IAAI,EAAEC,GAAG,CAAC,GAAGf,eAAe,CAAC,mBAAmB,CAAC;AACxD,IAAIgB,aAAa,GAAGpB,eAAe,CAAC;EAClCkB,IAAI;EACJT,KAAK;EACLY,KAAKA,CAACC,MAAM,EAAE;IACZ,MAAMC,YAAY,GAAGtB,QAAQ,CAAC,MAAMqB,MAAM,CAACN,SAAS,KAAK,MAAM,GAAGM,MAAM,CAACZ,SAAS,CAACc,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGH,MAAM,CAACZ,SAAS,CAAC;IACxH,MAAMgB,WAAW,GAAGzB,QAAQ,CAAC,MAAM;MACjC,MAAM0B,WAAW,GAAGL,MAAM,CAACL,MAAM,IAAIK,MAAM,CAACZ,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC;MACjE,OAAO,IAAID,WAAW,IAAI;IAC5B,CAAC,CAAC;IACF,MAAME,SAAS,GAAG5B,QAAQ,CAAC,OAAO;MAChC6B,UAAU,EAAEtB,OAAO,CAACc,MAAM,CAACL,MAAM;IACnC,CAAC,CAAC,CAAC;IACH,MAAMc,SAAS,GAAG9B,QAAQ,CAAC,OAAO;MAChCgB,MAAM,EAAET,OAAO,CAACc,MAAM,CAACL,MAAM,CAAC;MAC9B,iBAAiB,EAAES,WAAW,CAACM,KAAK;MACpC,gBAAgB,EAAEV,MAAM,CAACT,QAAQ,GAAG,GAAG;MACvC,aAAa,EAAES,MAAM,CAACX,KAAK,GAAG;IAChC,CAAC,CAAC,CAAC;IACH,OAAO,MAAMR,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEgB,GAAG,CAAC,CAACG,MAAM,CAACN,SAAS,CAAC,CAAC;MAChC,OAAO,EAAEe,SAAS,CAACC;IACrB,CAAC,EAAE,CAAC7B,YAAY,CAAC,KAAK,EAAE;MACtB,OAAO,EAAEgB,GAAG,CAAC,KAAK,EAAE;QAClBc,OAAO,EAAEX,MAAM,CAACR;MAClB,CAAC;IACH,CAAC,EAAE,CAACoB,KAAK,CAACC,OAAO,CAACZ,YAAY,CAACS,KAAK,CAAC,IAAIT,YAAY,CAACS,KAAK,CAACI,GAAG,CAAEC,MAAM,IAAKlC,YAAY,CAAC,KAAK,EAAE;MAC9F,OAAO,EAAEgB,GAAG,CAAC,MAAM,CAAC;MACpB,OAAO,EAAEU,SAAS,CAACG;IACrB,CAAC,EAAE,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;AACF,CAAC,CAAC;AACF,SACEjB,aAAa,IAAIkB,OAAO,EACxB7B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}