{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { remove } from \"./util.mjs\";\nconst defaultOptions = {\n  selector: \"img\"\n};\nclass LazyContainer {\n  constructor({\n    el,\n    binding,\n    vnode,\n    lazy\n  }) {\n    this.el = null;\n    this.vnode = vnode;\n    this.binding = binding;\n    this.options = {};\n    this.lazy = lazy;\n    this.queue = [];\n    this.update({\n      el,\n      binding\n    });\n  }\n  update({\n    el,\n    binding\n  }) {\n    this.el = el;\n    this.options = Object.assign({}, defaultOptions, binding.value);\n    const imgs = this.getImgs();\n    imgs.forEach(el2 => {\n      this.lazy.add(el2, Object.assign({}, this.binding, {\n        value: {\n          src: \"dataset\" in el2 ? el2.dataset.src : el2.getAttribute(\"data-src\"),\n          error: (\"dataset\" in el2 ? el2.dataset.error : el2.getAttribute(\"data-error\")) || this.options.error,\n          loading: (\"dataset\" in el2 ? el2.dataset.loading : el2.getAttribute(\"data-loading\")) || this.options.loading\n        }\n      }), this.vnode);\n    });\n  }\n  getImgs() {\n    return Array.from(this.el.querySelectorAll(this.options.selector));\n  }\n  clear() {\n    const imgs = this.getImgs();\n    imgs.forEach(el => this.lazy.remove(el));\n    this.vnode = null;\n    this.binding = null;\n    this.lazy = null;\n  }\n}\nclass LazyContainerManager {\n  constructor({\n    lazy\n  }) {\n    this.lazy = lazy;\n    this.queue = [];\n  }\n  bind(el, binding, vnode) {\n    const container = new LazyContainer({\n      el,\n      binding,\n      vnode,\n      lazy: this.lazy\n    });\n    this.queue.push(container);\n  }\n  update(el, binding, vnode) {\n    const container = this.queue.find(item => item.el === el);\n    if (!container) return;\n    container.update({\n      el,\n      binding,\n      vnode\n    });\n  }\n  unbind(el) {\n    const container = this.queue.find(item => item.el === el);\n    if (!container) return;\n    container.clear();\n    remove(this.queue, container);\n  }\n}\nexport { LazyContainerManager as default };", "map": {"version": 3, "names": ["remove", "defaultOptions", "selector", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "el", "binding", "vnode", "lazy", "options", "queue", "update", "Object", "assign", "value", "imgs", "getImgs", "for<PERSON>ach", "el2", "add", "src", "dataset", "getAttribute", "error", "loading", "Array", "from", "querySelectorAll", "clear", "LazyContainerManager", "bind", "container", "push", "find", "item", "unbind", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/lazyload/vue-lazyload/lazy-container.mjs"], "sourcesContent": ["import { remove } from \"./util.mjs\";\nconst defaultOptions = {\n  selector: \"img\"\n};\nclass LazyContainer {\n  constructor({ el, binding, vnode, lazy }) {\n    this.el = null;\n    this.vnode = vnode;\n    this.binding = binding;\n    this.options = {};\n    this.lazy = lazy;\n    this.queue = [];\n    this.update({ el, binding });\n  }\n  update({ el, binding }) {\n    this.el = el;\n    this.options = Object.assign({}, defaultOptions, binding.value);\n    const imgs = this.getImgs();\n    imgs.forEach((el2) => {\n      this.lazy.add(\n        el2,\n        Object.assign({}, this.binding, {\n          value: {\n            src: \"dataset\" in el2 ? el2.dataset.src : el2.getAttribute(\"data-src\"),\n            error: (\"dataset\" in el2 ? el2.dataset.error : el2.getAttribute(\"data-error\")) || this.options.error,\n            loading: (\"dataset\" in el2 ? el2.dataset.loading : el2.getAttribute(\"data-loading\")) || this.options.loading\n          }\n        }),\n        this.vnode\n      );\n    });\n  }\n  getImgs() {\n    return Array.from(this.el.querySelectorAll(this.options.selector));\n  }\n  clear() {\n    const imgs = this.getImgs();\n    imgs.forEach((el) => this.lazy.remove(el));\n    this.vnode = null;\n    this.binding = null;\n    this.lazy = null;\n  }\n}\nclass LazyContainerManager {\n  constructor({ lazy }) {\n    this.lazy = lazy;\n    this.queue = [];\n  }\n  bind(el, binding, vnode) {\n    const container = new LazyContainer({\n      el,\n      binding,\n      vnode,\n      lazy: this.lazy\n    });\n    this.queue.push(container);\n  }\n  update(el, binding, vnode) {\n    const container = this.queue.find((item) => item.el === el);\n    if (!container) return;\n    container.update({ el, binding, vnode });\n  }\n  unbind(el) {\n    const container = this.queue.find((item) => item.el === el);\n    if (!container) return;\n    container.clear();\n    remove(this.queue, container);\n  }\n}\nexport {\n  LazyContainerManager as default\n};\n"], "mappings": ";;;;AAAA,SAASA,MAAM,QAAQ,YAAY;AACnC,MAAMC,cAAc,GAAG;EACrBC,QAAQ,EAAE;AACZ,CAAC;AACD,MAAMC,aAAa,CAAC;EAClBC,WAAWA,CAAC;IAAEC,EAAE;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAK,CAAC,EAAE;IACxC,IAAI,CAACH,EAAE,GAAG,IAAI;IACd,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,MAAM,CAAC;MAAEN,EAAE;MAAEC;IAAQ,CAAC,CAAC;EAC9B;EACAK,MAAMA,CAAC;IAAEN,EAAE;IAAEC;EAAQ,CAAC,EAAE;IACtB,IAAI,CAACD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACI,OAAO,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,cAAc,EAAEK,OAAO,CAACQ,KAAK,CAAC;IAC/D,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC3BD,IAAI,CAACE,OAAO,CAAEC,GAAG,IAAK;MACpB,IAAI,CAACV,IAAI,CAACW,GAAG,CACXD,GAAG,EACHN,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACP,OAAO,EAAE;QAC9BQ,KAAK,EAAE;UACLM,GAAG,EAAE,SAAS,IAAIF,GAAG,GAAGA,GAAG,CAACG,OAAO,CAACD,GAAG,GAAGF,GAAG,CAACI,YAAY,CAAC,UAAU,CAAC;UACtEC,KAAK,EAAE,CAAC,SAAS,IAAIL,GAAG,GAAGA,GAAG,CAACG,OAAO,CAACE,KAAK,GAAGL,GAAG,CAACI,YAAY,CAAC,YAAY,CAAC,KAAK,IAAI,CAACb,OAAO,CAACc,KAAK;UACpGC,OAAO,EAAE,CAAC,SAAS,IAAIN,GAAG,GAAGA,GAAG,CAACG,OAAO,CAACG,OAAO,GAAGN,GAAG,CAACI,YAAY,CAAC,cAAc,CAAC,KAAK,IAAI,CAACb,OAAO,CAACe;QACvG;MACF,CAAC,CAAC,EACF,IAAI,CAACjB,KACP,CAAC;IACH,CAAC,CAAC;EACJ;EACAS,OAAOA,CAAA,EAAG;IACR,OAAOS,KAAK,CAACC,IAAI,CAAC,IAAI,CAACrB,EAAE,CAACsB,gBAAgB,CAAC,IAAI,CAAClB,OAAO,CAACP,QAAQ,CAAC,CAAC;EACpE;EACA0B,KAAKA,CAAA,EAAG;IACN,MAAMb,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC3BD,IAAI,CAACE,OAAO,CAAEZ,EAAE,IAAK,IAAI,CAACG,IAAI,CAACR,MAAM,CAACK,EAAE,CAAC,CAAC;IAC1C,IAAI,CAACE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACE,IAAI,GAAG,IAAI;EAClB;AACF;AACA,MAAMqB,oBAAoB,CAAC;EACzBzB,WAAWA,CAAC;IAAEI;EAAK,CAAC,EAAE;IACpB,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,KAAK,GAAG,EAAE;EACjB;EACAoB,IAAIA,CAACzB,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACvB,MAAMwB,SAAS,GAAG,IAAI5B,aAAa,CAAC;MAClCE,EAAE;MACFC,OAAO;MACPC,KAAK;MACLC,IAAI,EAAE,IAAI,CAACA;IACb,CAAC,CAAC;IACF,IAAI,CAACE,KAAK,CAACsB,IAAI,CAACD,SAAS,CAAC;EAC5B;EACApB,MAAMA,CAACN,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACzB,MAAMwB,SAAS,GAAG,IAAI,CAACrB,KAAK,CAACuB,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IAC3D,IAAI,CAAC0B,SAAS,EAAE;IAChBA,SAAS,CAACpB,MAAM,CAAC;MAAEN,EAAE;MAAEC,OAAO;MAAEC;IAAM,CAAC,CAAC;EAC1C;EACA4B,MAAMA,CAAC9B,EAAE,EAAE;IACT,MAAM0B,SAAS,GAAG,IAAI,CAACrB,KAAK,CAACuB,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IAC3D,IAAI,CAAC0B,SAAS,EAAE;IAChBA,SAAS,CAACH,KAAK,CAAC,CAAC;IACjB5B,MAAM,CAAC,IAAI,CAACU,KAAK,EAAEqB,SAAS,CAAC;EAC/B;AACF;AACA,SACEF,oBAAoB,IAAIO,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}