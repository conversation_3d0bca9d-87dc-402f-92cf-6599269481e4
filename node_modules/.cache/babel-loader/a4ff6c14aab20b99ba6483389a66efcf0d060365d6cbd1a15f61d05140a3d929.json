{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { inBrowser } from \"@vant/use\";\nconst hasIntersectionObserver = inBrowser && \"IntersectionObserver\" in window && \"IntersectionObserverEntry\" in window && \"intersectionRatio\" in window.IntersectionObserverEntry.prototype;\nconst modeType = {\n  event: \"event\",\n  observer: \"observer\"\n};\nfunction remove(arr, item) {\n  if (!arr.length) return;\n  const index = arr.indexOf(item);\n  if (index > -1) return arr.splice(index, 1);\n}\nfunction getBestSelectionFromSrcset(el, scale) {\n  if (el.tagName !== \"IMG\" || !el.getAttribute(\"data-srcset\")) return;\n  let options = el.getAttribute(\"data-srcset\");\n  const container = el.parentNode;\n  const containerWidth = container.offsetWidth * scale;\n  let spaceIndex;\n  let tmpSrc;\n  let tmpWidth;\n  options = options.trim().split(\",\");\n  const result = options.map(item => {\n    item = item.trim();\n    spaceIndex = item.lastIndexOf(\" \");\n    if (spaceIndex === -1) {\n      tmpSrc = item;\n      tmpWidth = 999998;\n    } else {\n      tmpSrc = item.substr(0, spaceIndex);\n      tmpWidth = parseInt(item.substr(spaceIndex + 1, item.length - spaceIndex - 2), 10);\n    }\n    return [tmpWidth, tmpSrc];\n  });\n  result.sort((a, b) => {\n    if (a[0] < b[0]) {\n      return 1;\n    }\n    if (a[0] > b[0]) {\n      return -1;\n    }\n    if (a[0] === b[0]) {\n      if (b[1].indexOf(\".webp\", b[1].length - 5) !== -1) {\n        return 1;\n      }\n      if (a[1].indexOf(\".webp\", a[1].length - 5) !== -1) {\n        return -1;\n      }\n    }\n    return 0;\n  });\n  let bestSelectedSrc = \"\";\n  let tmpOption;\n  for (let i = 0; i < result.length; i++) {\n    tmpOption = result[i];\n    bestSelectedSrc = tmpOption[1];\n    const next = result[i + 1];\n    if (next && next[0] < containerWidth) {\n      bestSelectedSrc = tmpOption[1];\n      break;\n    } else if (!next) {\n      bestSelectedSrc = tmpOption[1];\n      break;\n    }\n  }\n  return bestSelectedSrc;\n}\nconst getDPR = (scale = 1) => inBrowser ? window.devicePixelRatio || scale : scale;\nfunction supportWebp() {\n  if (!inBrowser) return false;\n  let support = true;\n  try {\n    const elem = document.createElement(\"canvas\");\n    if (elem.getContext && elem.getContext(\"2d\")) {\n      support = elem.toDataURL(\"image/webp\").indexOf(\"data:image/webp\") === 0;\n    }\n  } catch (err) {\n    support = false;\n  }\n  return support;\n}\nfunction throttle(action, delay) {\n  let timeout = null;\n  let lastRun = 0;\n  return function (...args) {\n    if (timeout) {\n      return;\n    }\n    const elapsed = Date.now() - lastRun;\n    const runCallback = () => {\n      lastRun = Date.now();\n      timeout = false;\n      action.apply(this, args);\n    };\n    if (elapsed >= delay) {\n      runCallback();\n    } else {\n      timeout = setTimeout(runCallback, delay);\n    }\n  };\n}\nfunction on(el, type, func) {\n  el.addEventListener(type, func, {\n    capture: false,\n    passive: true\n  });\n}\nfunction off(el, type, func) {\n  el.removeEventListener(type, func, false);\n}\nconst loadImageAsync = (item, resolve, reject) => {\n  const image = new Image();\n  if (!item || !item.src) {\n    return reject(new Error(\"image src is required\"));\n  }\n  image.src = item.src;\n  if (item.cors) {\n    image.crossOrigin = item.cors;\n  }\n  image.onload = () => resolve({\n    naturalHeight: image.naturalHeight,\n    naturalWidth: image.naturalWidth,\n    src: image.src\n  });\n  image.onerror = e => reject(e);\n};\nclass ImageCache {\n  constructor({\n    max\n  }) {\n    this.options = {\n      max: max || 100\n    };\n    this.caches = [];\n  }\n  has(key) {\n    return this.caches.indexOf(key) > -1;\n  }\n  add(key) {\n    if (this.has(key)) return;\n    this.caches.push(key);\n    if (this.caches.length > this.options.max) {\n      this.free();\n    }\n  }\n  free() {\n    this.caches.shift();\n  }\n}\nexport { ImageCache, getBestSelectionFromSrcset, getDPR, hasIntersectionObserver, loadImageAsync, modeType, off, on, remove, supportWebp, throttle };", "map": {"version": 3, "names": ["inBrowser", "hasIntersectionObserver", "window", "IntersectionObserverEntry", "prototype", "modeType", "event", "observer", "remove", "arr", "item", "length", "index", "indexOf", "splice", "getBestSelectionFromSrcset", "el", "scale", "tagName", "getAttribute", "options", "container", "parentNode", "containerWidth", "offsetWidth", "spaceIndex", "tmpSrc", "tmpWidth", "trim", "split", "result", "map", "lastIndexOf", "substr", "parseInt", "sort", "a", "b", "bestSelectedSrc", "tmpOption", "i", "next", "getDPR", "devicePixelRatio", "supportWebp", "support", "elem", "document", "createElement", "getContext", "toDataURL", "err", "throttle", "action", "delay", "timeout", "lastRun", "args", "elapsed", "Date", "now", "<PERSON><PERSON><PERSON><PERSON>", "apply", "setTimeout", "on", "type", "func", "addEventListener", "capture", "passive", "off", "removeEventListener", "loadImageAsync", "resolve", "reject", "image", "Image", "src", "Error", "cors", "crossOrigin", "onload", "naturalHeight", "naturalWidth", "onerror", "e", "ImageCache", "constructor", "max", "caches", "has", "key", "add", "push", "free", "shift"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/lazyload/vue-lazyload/util.mjs"], "sourcesContent": ["import { inBrowser } from \"@vant/use\";\nconst hasIntersectionObserver = inBrowser && \"IntersectionObserver\" in window && \"IntersectionObserverEntry\" in window && \"intersectionRatio\" in window.IntersectionObserverEntry.prototype;\nconst modeType = {\n  event: \"event\",\n  observer: \"observer\"\n};\nfunction remove(arr, item) {\n  if (!arr.length) return;\n  const index = arr.indexOf(item);\n  if (index > -1) return arr.splice(index, 1);\n}\nfunction getBestSelectionFromSrcset(el, scale) {\n  if (el.tagName !== \"IMG\" || !el.getAttribute(\"data-srcset\")) return;\n  let options = el.getAttribute(\"data-srcset\");\n  const container = el.parentNode;\n  const containerWidth = container.offsetWidth * scale;\n  let spaceIndex;\n  let tmpSrc;\n  let tmpWidth;\n  options = options.trim().split(\",\");\n  const result = options.map((item) => {\n    item = item.trim();\n    spaceIndex = item.lastIndexOf(\" \");\n    if (spaceIndex === -1) {\n      tmpSrc = item;\n      tmpWidth = 999998;\n    } else {\n      tmpSrc = item.substr(0, spaceIndex);\n      tmpWidth = parseInt(\n        item.substr(spaceIndex + 1, item.length - spaceIndex - 2),\n        10\n      );\n    }\n    return [tmpWidth, tmpSrc];\n  });\n  result.sort((a, b) => {\n    if (a[0] < b[0]) {\n      return 1;\n    }\n    if (a[0] > b[0]) {\n      return -1;\n    }\n    if (a[0] === b[0]) {\n      if (b[1].indexOf(\".webp\", b[1].length - 5) !== -1) {\n        return 1;\n      }\n      if (a[1].indexOf(\".webp\", a[1].length - 5) !== -1) {\n        return -1;\n      }\n    }\n    return 0;\n  });\n  let bestSelectedSrc = \"\";\n  let tmpOption;\n  for (let i = 0; i < result.length; i++) {\n    tmpOption = result[i];\n    bestSelectedSrc = tmpOption[1];\n    const next = result[i + 1];\n    if (next && next[0] < containerWidth) {\n      bestSelectedSrc = tmpOption[1];\n      break;\n    } else if (!next) {\n      bestSelectedSrc = tmpOption[1];\n      break;\n    }\n  }\n  return bestSelectedSrc;\n}\nconst getDPR = (scale = 1) => inBrowser ? window.devicePixelRatio || scale : scale;\nfunction supportWebp() {\n  if (!inBrowser) return false;\n  let support = true;\n  try {\n    const elem = document.createElement(\"canvas\");\n    if (elem.getContext && elem.getContext(\"2d\")) {\n      support = elem.toDataURL(\"image/webp\").indexOf(\"data:image/webp\") === 0;\n    }\n  } catch (err) {\n    support = false;\n  }\n  return support;\n}\nfunction throttle(action, delay) {\n  let timeout = null;\n  let lastRun = 0;\n  return function(...args) {\n    if (timeout) {\n      return;\n    }\n    const elapsed = Date.now() - lastRun;\n    const runCallback = () => {\n      lastRun = Date.now();\n      timeout = false;\n      action.apply(this, args);\n    };\n    if (elapsed >= delay) {\n      runCallback();\n    } else {\n      timeout = setTimeout(runCallback, delay);\n    }\n  };\n}\nfunction on(el, type, func) {\n  el.addEventListener(type, func, {\n    capture: false,\n    passive: true\n  });\n}\nfunction off(el, type, func) {\n  el.removeEventListener(type, func, false);\n}\nconst loadImageAsync = (item, resolve, reject) => {\n  const image = new Image();\n  if (!item || !item.src) {\n    return reject(new Error(\"image src is required\"));\n  }\n  image.src = item.src;\n  if (item.cors) {\n    image.crossOrigin = item.cors;\n  }\n  image.onload = () => resolve({\n    naturalHeight: image.naturalHeight,\n    naturalWidth: image.naturalWidth,\n    src: image.src\n  });\n  image.onerror = (e) => reject(e);\n};\nclass ImageCache {\n  constructor({ max }) {\n    this.options = {\n      max: max || 100\n    };\n    this.caches = [];\n  }\n  has(key) {\n    return this.caches.indexOf(key) > -1;\n  }\n  add(key) {\n    if (this.has(key)) return;\n    this.caches.push(key);\n    if (this.caches.length > this.options.max) {\n      this.free();\n    }\n  }\n  free() {\n    this.caches.shift();\n  }\n}\nexport {\n  ImageCache,\n  getBestSelectionFromSrcset,\n  getDPR,\n  hasIntersectionObserver,\n  loadImageAsync,\n  modeType,\n  off,\n  on,\n  remove,\n  supportWebp,\n  throttle\n};\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,WAAW;AACrC,MAAMC,uBAAuB,GAAGD,SAAS,IAAI,sBAAsB,IAAIE,MAAM,IAAI,2BAA2B,IAAIA,MAAM,IAAI,mBAAmB,IAAIA,MAAM,CAACC,yBAAyB,CAACC,SAAS;AAC3L,MAAMC,QAAQ,GAAG;EACfC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE;AACZ,CAAC;AACD,SAASC,MAAMA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,IAAI,CAACD,GAAG,CAACE,MAAM,EAAE;EACjB,MAAMC,KAAK,GAAGH,GAAG,CAACI,OAAO,CAACH,IAAI,CAAC;EAC/B,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAE,OAAOH,GAAG,CAACK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;AAC7C;AACA,SAASG,0BAA0BA,CAACC,EAAE,EAAEC,KAAK,EAAE;EAC7C,IAAID,EAAE,CAACE,OAAO,KAAK,KAAK,IAAI,CAACF,EAAE,CAACG,YAAY,CAAC,aAAa,CAAC,EAAE;EAC7D,IAAIC,OAAO,GAAGJ,EAAE,CAACG,YAAY,CAAC,aAAa,CAAC;EAC5C,MAAME,SAAS,GAAGL,EAAE,CAACM,UAAU;EAC/B,MAAMC,cAAc,GAAGF,SAAS,CAACG,WAAW,GAAGP,KAAK;EACpD,IAAIQ,UAAU;EACd,IAAIC,MAAM;EACV,IAAIC,QAAQ;EACZP,OAAO,GAAGA,OAAO,CAACQ,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EACnC,MAAMC,MAAM,GAAGV,OAAO,CAACW,GAAG,CAAErB,IAAI,IAAK;IACnCA,IAAI,GAAGA,IAAI,CAACkB,IAAI,CAAC,CAAC;IAClBH,UAAU,GAAGf,IAAI,CAACsB,WAAW,CAAC,GAAG,CAAC;IAClC,IAAIP,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBC,MAAM,GAAGhB,IAAI;MACbiB,QAAQ,GAAG,MAAM;IACnB,CAAC,MAAM;MACLD,MAAM,GAAGhB,IAAI,CAACuB,MAAM,CAAC,CAAC,EAAER,UAAU,CAAC;MACnCE,QAAQ,GAAGO,QAAQ,CACjBxB,IAAI,CAACuB,MAAM,CAACR,UAAU,GAAG,CAAC,EAAEf,IAAI,CAACC,MAAM,GAAGc,UAAU,GAAG,CAAC,CAAC,EACzD,EACF,CAAC;IACH;IACA,OAAO,CAACE,QAAQ,EAAED,MAAM,CAAC;EAC3B,CAAC,CAAC;EACFI,MAAM,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACpB,IAAID,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,OAAO,CAAC;IACV;IACA,IAAID,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,OAAO,CAAC,CAAC;IACX;IACA,IAAID,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACxB,OAAO,CAAC,OAAO,EAAEwB,CAAC,CAAC,CAAC,CAAC,CAAC1B,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACjD,OAAO,CAAC;MACV;MACA,IAAIyB,CAAC,CAAC,CAAC,CAAC,CAACvB,OAAO,CAAC,OAAO,EAAEuB,CAAC,CAAC,CAAC,CAAC,CAACzB,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACjD,OAAO,CAAC,CAAC;MACX;IACF;IACA,OAAO,CAAC;EACV,CAAC,CAAC;EACF,IAAI2B,eAAe,GAAG,EAAE;EACxB,IAAIC,SAAS;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,CAACnB,MAAM,EAAE6B,CAAC,EAAE,EAAE;IACtCD,SAAS,GAAGT,MAAM,CAACU,CAAC,CAAC;IACrBF,eAAe,GAAGC,SAAS,CAAC,CAAC,CAAC;IAC9B,MAAME,IAAI,GAAGX,MAAM,CAACU,CAAC,GAAG,CAAC,CAAC;IAC1B,IAAIC,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,GAAGlB,cAAc,EAAE;MACpCe,eAAe,GAAGC,SAAS,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC,MAAM,IAAI,CAACE,IAAI,EAAE;MAChBH,eAAe,GAAGC,SAAS,CAAC,CAAC,CAAC;MAC9B;IACF;EACF;EACA,OAAOD,eAAe;AACxB;AACA,MAAMI,MAAM,GAAGA,CAACzB,KAAK,GAAG,CAAC,KAAKjB,SAAS,GAAGE,MAAM,CAACyC,gBAAgB,IAAI1B,KAAK,GAAGA,KAAK;AAClF,SAAS2B,WAAWA,CAAA,EAAG;EACrB,IAAI,CAAC5C,SAAS,EAAE,OAAO,KAAK;EAC5B,IAAI6C,OAAO,GAAG,IAAI;EAClB,IAAI;IACF,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC7C,IAAIF,IAAI,CAACG,UAAU,IAAIH,IAAI,CAACG,UAAU,CAAC,IAAI,CAAC,EAAE;MAC5CJ,OAAO,GAAGC,IAAI,CAACI,SAAS,CAAC,YAAY,CAAC,CAACrC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC;IACzE;EACF,CAAC,CAAC,OAAOsC,GAAG,EAAE;IACZN,OAAO,GAAG,KAAK;EACjB;EACA,OAAOA,OAAO;AAChB;AACA,SAASO,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/B,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,OAAO,GAAG,CAAC;EACf,OAAO,UAAS,GAAGC,IAAI,EAAE;IACvB,IAAIF,OAAO,EAAE;MACX;IACF;IACA,MAAMG,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGJ,OAAO;IACpC,MAAMK,WAAW,GAAGA,CAAA,KAAM;MACxBL,OAAO,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC;MACpBL,OAAO,GAAG,KAAK;MACfF,MAAM,CAACS,KAAK,CAAC,IAAI,EAAEL,IAAI,CAAC;IAC1B,CAAC;IACD,IAAIC,OAAO,IAAIJ,KAAK,EAAE;MACpBO,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLN,OAAO,GAAGQ,UAAU,CAACF,WAAW,EAAEP,KAAK,CAAC;IAC1C;EACF,CAAC;AACH;AACA,SAASU,EAAEA,CAAChD,EAAE,EAAEiD,IAAI,EAAEC,IAAI,EAAE;EAC1BlD,EAAE,CAACmD,gBAAgB,CAACF,IAAI,EAAEC,IAAI,EAAE;IAC9BE,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AACA,SAASC,GAAGA,CAACtD,EAAE,EAAEiD,IAAI,EAAEC,IAAI,EAAE;EAC3BlD,EAAE,CAACuD,mBAAmB,CAACN,IAAI,EAAEC,IAAI,EAAE,KAAK,CAAC;AAC3C;AACA,MAAMM,cAAc,GAAGA,CAAC9D,IAAI,EAAE+D,OAAO,EAAEC,MAAM,KAAK;EAChD,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;EACzB,IAAI,CAAClE,IAAI,IAAI,CAACA,IAAI,CAACmE,GAAG,EAAE;IACtB,OAAOH,MAAM,CAAC,IAAII,KAAK,CAAC,uBAAuB,CAAC,CAAC;EACnD;EACAH,KAAK,CAACE,GAAG,GAAGnE,IAAI,CAACmE,GAAG;EACpB,IAAInE,IAAI,CAACqE,IAAI,EAAE;IACbJ,KAAK,CAACK,WAAW,GAAGtE,IAAI,CAACqE,IAAI;EAC/B;EACAJ,KAAK,CAACM,MAAM,GAAG,MAAMR,OAAO,CAAC;IAC3BS,aAAa,EAAEP,KAAK,CAACO,aAAa;IAClCC,YAAY,EAAER,KAAK,CAACQ,YAAY;IAChCN,GAAG,EAAEF,KAAK,CAACE;EACb,CAAC,CAAC;EACFF,KAAK,CAACS,OAAO,GAAIC,CAAC,IAAKX,MAAM,CAACW,CAAC,CAAC;AAClC,CAAC;AACD,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAC;IAAEC;EAAI,CAAC,EAAE;IACnB,IAAI,CAACpE,OAAO,GAAG;MACboE,GAAG,EAAEA,GAAG,IAAI;IACd,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,EAAE;EAClB;EACAC,GAAGA,CAACC,GAAG,EAAE;IACP,OAAO,IAAI,CAACF,MAAM,CAAC5E,OAAO,CAAC8E,GAAG,CAAC,GAAG,CAAC,CAAC;EACtC;EACAC,GAAGA,CAACD,GAAG,EAAE;IACP,IAAI,IAAI,CAACD,GAAG,CAACC,GAAG,CAAC,EAAE;IACnB,IAAI,CAACF,MAAM,CAACI,IAAI,CAACF,GAAG,CAAC;IACrB,IAAI,IAAI,CAACF,MAAM,CAAC9E,MAAM,GAAG,IAAI,CAACS,OAAO,CAACoE,GAAG,EAAE;MACzC,IAAI,CAACM,IAAI,CAAC,CAAC;IACb;EACF;EACAA,IAAIA,CAAA,EAAG;IACL,IAAI,CAACL,MAAM,CAACM,KAAK,CAAC,CAAC;EACrB;AACF;AACA,SACET,UAAU,EACVvE,0BAA0B,EAC1B2B,MAAM,EACNzC,uBAAuB,EACvBuE,cAAc,EACdnE,QAAQ,EACRiE,GAAG,EACHN,EAAE,EACFxD,MAAM,EACNoC,WAAW,EACXQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}