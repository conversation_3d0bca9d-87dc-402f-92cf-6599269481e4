{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CountDown from \"./CountDown.mjs\";\nconst CountDown = withInstall(_CountDown);\nvar stdin_default = CountDown;\nimport { countDownProps } from \"./CountDown.mjs\";\nexport { CountDown, countDownProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CountDown", "CountDown", "stdin_default", "countDownProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/count-down/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CountDown from \"./CountDown.mjs\";\nconst CountDown = withInstall(_CountDown);\nvar stdin_default = CountDown;\nimport { countDownProps } from \"./CountDown.mjs\";\nexport {\n  CountDown,\n  countDownProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SAASE,cAAc,QAAQ,iBAAiB;AAChD,SACEF,SAAS,EACTE,cAAc,EACdD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}