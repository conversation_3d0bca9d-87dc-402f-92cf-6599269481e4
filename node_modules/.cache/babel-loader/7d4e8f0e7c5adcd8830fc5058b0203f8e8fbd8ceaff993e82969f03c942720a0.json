{"ast": null, "code": "import { ref, watch, computed, nextTick, onUpdated, onMounted, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isHidden, truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useRect, useScrollParent, useEventListener } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { useAllTabStatus } from \"../composables/use-tab-status.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem, t] = createNamespace(\"list\");\nconst listProps = {\n  error: <PERSON><PERSON><PERSON>,\n  offset: makeNumericProp(300),\n  loading: <PERSON><PERSON><PERSON>,\n  disabled: <PERSON><PERSON>an,\n  finished: <PERSON><PERSON><PERSON>,\n  scroller: Object,\n  errorText: String,\n  direction: makeStringProp(\"down\"),\n  loadingText: String,\n  finishedText: String,\n  immediateCheck: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: listProps,\n  emits: [\"load\", \"update:error\", \"update:loading\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const loading = ref(props.loading);\n    const root = ref();\n    const placeholder = ref();\n    const tabStatus = useAllTabStatus();\n    const scrollParent = useScrollParent(root);\n    const scroller = computed(() => props.scroller || scrollParent.value);\n    const check = () => {\n      nextTick(() => {\n        if (loading.value || props.finished || props.disabled || props.error ||\n        // skip check when inside an inactive tab\n        (tabStatus == null ? void 0 : tabStatus.value) === false) {\n          return;\n        }\n        const {\n          direction\n        } = props;\n        const offset = +props.offset;\n        const scrollParentRect = useRect(scroller);\n        if (!scrollParentRect.height || isHidden(root)) {\n          return;\n        }\n        let isReachEdge = false;\n        const placeholderRect = useRect(placeholder);\n        if (direction === \"up\") {\n          isReachEdge = scrollParentRect.top - placeholderRect.top <= offset;\n        } else {\n          isReachEdge = placeholderRect.bottom - scrollParentRect.bottom <= offset;\n        }\n        if (isReachEdge) {\n          loading.value = true;\n          emit(\"update:loading\", true);\n          emit(\"load\");\n        }\n      });\n    };\n    const renderFinishedText = () => {\n      if (props.finished) {\n        const text = slots.finished ? slots.finished() : props.finishedText;\n        if (text) {\n          return _createVNode(\"div\", {\n            \"class\": bem(\"finished-text\")\n          }, [text]);\n        }\n      }\n    };\n    const clickErrorText = () => {\n      emit(\"update:error\", false);\n      check();\n    };\n    const renderErrorText = () => {\n      if (props.error) {\n        const text = slots.error ? slots.error() : props.errorText;\n        if (text) {\n          return _createVNode(\"div\", {\n            \"role\": \"button\",\n            \"class\": bem(\"error-text\"),\n            \"tabindex\": 0,\n            \"onClick\": clickErrorText\n          }, [text]);\n        }\n      }\n    };\n    const renderLoading = () => {\n      if (loading.value && !props.finished && !props.disabled) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"loading\")\n        }, [slots.loading ? slots.loading() : _createVNode(Loading, {\n          \"class\": bem(\"loading-icon\")\n        }, {\n          default: () => [props.loadingText || t(\"loading\")]\n        })]);\n      }\n    };\n    watch(() => [props.loading, props.finished, props.error], check);\n    if (tabStatus) {\n      watch(tabStatus, tabActive => {\n        if (tabActive) {\n          check();\n        }\n      });\n    }\n    onUpdated(() => {\n      loading.value = props.loading;\n    });\n    onMounted(() => {\n      if (props.immediateCheck) {\n        check();\n      }\n    });\n    useExpose({\n      check\n    });\n    useEventListener(\"scroll\", check, {\n      target: scroller,\n      passive: true\n    });\n    return () => {\n      var _a;\n      const Content = (_a = slots.default) == null ? void 0 : _a.call(slots);\n      const Placeholder = _createVNode(\"div\", {\n        \"ref\": placeholder,\n        \"class\": bem(\"placeholder\")\n      }, null);\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"role\": \"feed\",\n        \"class\": bem(),\n        \"aria-busy\": loading.value\n      }, [props.direction === \"down\" ? Content : Placeholder, renderLoading(), renderFinishedText(), renderErrorText(), props.direction === \"up\" ? Content : Placeholder]);\n    };\n  }\n});\nexport { stdin_default as default, listProps };", "map": {"version": 3, "names": ["ref", "watch", "computed", "nextTick", "onUpdated", "onMounted", "defineComponent", "createVNode", "_createVNode", "isHidden", "truthProp", "makeStringProp", "makeNumericProp", "createNamespace", "useRect", "useScrollParent", "useEventListener", "useExpose", "useAllTabStatus", "Loading", "name", "bem", "t", "listProps", "error", "Boolean", "offset", "loading", "disabled", "finished", "scroller", "Object", "errorText", "String", "direction", "loadingText", "finishedText", "immediate<PERSON>heck", "stdin_default", "props", "emits", "setup", "emit", "slots", "root", "placeholder", "tabStatus", "scrollParent", "value", "check", "scrollParentRect", "height", "isReachEdge", "placeholder<PERSON><PERSON><PERSON>", "top", "bottom", "renderFinishedText", "text", "clickErrorText", "renderErrorText", "renderLoading", "default", "tabActive", "target", "passive", "_a", "Content", "call", "Placeholder"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/list/List.mjs"], "sourcesContent": ["import { ref, watch, computed, nextTick, onUpdated, onMounted, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isHidden, truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useRect, useScrollParent, useEventListener } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { useAllTabStatus } from \"../composables/use-tab-status.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem, t] = createNamespace(\"list\");\nconst listProps = {\n  error: <PERSON><PERSON><PERSON>,\n  offset: makeNumericProp(300),\n  loading: <PERSON><PERSON><PERSON>,\n  disabled: <PERSON><PERSON>an,\n  finished: <PERSON><PERSON><PERSON>,\n  scroller: Object,\n  errorText: String,\n  direction: makeStringProp(\"down\"),\n  loadingText: String,\n  finishedText: String,\n  immediateCheck: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: listProps,\n  emits: [\"load\", \"update:error\", \"update:loading\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const loading = ref(props.loading);\n    const root = ref();\n    const placeholder = ref();\n    const tabStatus = useAllTabStatus();\n    const scrollParent = useScrollParent(root);\n    const scroller = computed(() => props.scroller || scrollParent.value);\n    const check = () => {\n      nextTick(() => {\n        if (loading.value || props.finished || props.disabled || props.error || // skip check when inside an inactive tab\n        (tabStatus == null ? void 0 : tabStatus.value) === false) {\n          return;\n        }\n        const {\n          direction\n        } = props;\n        const offset = +props.offset;\n        const scrollParentRect = useRect(scroller);\n        if (!scrollParentRect.height || isHidden(root)) {\n          return;\n        }\n        let isReachEdge = false;\n        const placeholderRect = useRect(placeholder);\n        if (direction === \"up\") {\n          isReachEdge = scrollParentRect.top - placeholderRect.top <= offset;\n        } else {\n          isReachEdge = placeholderRect.bottom - scrollParentRect.bottom <= offset;\n        }\n        if (isReachEdge) {\n          loading.value = true;\n          emit(\"update:loading\", true);\n          emit(\"load\");\n        }\n      });\n    };\n    const renderFinishedText = () => {\n      if (props.finished) {\n        const text = slots.finished ? slots.finished() : props.finishedText;\n        if (text) {\n          return _createVNode(\"div\", {\n            \"class\": bem(\"finished-text\")\n          }, [text]);\n        }\n      }\n    };\n    const clickErrorText = () => {\n      emit(\"update:error\", false);\n      check();\n    };\n    const renderErrorText = () => {\n      if (props.error) {\n        const text = slots.error ? slots.error() : props.errorText;\n        if (text) {\n          return _createVNode(\"div\", {\n            \"role\": \"button\",\n            \"class\": bem(\"error-text\"),\n            \"tabindex\": 0,\n            \"onClick\": clickErrorText\n          }, [text]);\n        }\n      }\n    };\n    const renderLoading = () => {\n      if (loading.value && !props.finished && !props.disabled) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"loading\")\n        }, [slots.loading ? slots.loading() : _createVNode(Loading, {\n          \"class\": bem(\"loading-icon\")\n        }, {\n          default: () => [props.loadingText || t(\"loading\")]\n        })]);\n      }\n    };\n    watch(() => [props.loading, props.finished, props.error], check);\n    if (tabStatus) {\n      watch(tabStatus, (tabActive) => {\n        if (tabActive) {\n          check();\n        }\n      });\n    }\n    onUpdated(() => {\n      loading.value = props.loading;\n    });\n    onMounted(() => {\n      if (props.immediateCheck) {\n        check();\n      }\n    });\n    useExpose({\n      check\n    });\n    useEventListener(\"scroll\", check, {\n      target: scroller,\n      passive: true\n    });\n    return () => {\n      var _a;\n      const Content = (_a = slots.default) == null ? void 0 : _a.call(slots);\n      const Placeholder = _createVNode(\"div\", {\n        \"ref\": placeholder,\n        \"class\": bem(\"placeholder\")\n      }, null);\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"role\": \"feed\",\n        \"class\": bem(),\n        \"aria-busy\": loading.value\n      }, [props.direction === \"down\" ? Content : Placeholder, renderLoading(), renderFinishedText(), renderErrorText(), props.direction === \"up\" ? Content : Placeholder]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  listProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACxH,SAASC,QAAQ,EAAEC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAC1G,SAASC,OAAO,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,WAAW;AACtE,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGT,eAAe,CAAC,MAAM,CAAC;AAC9C,MAAMU,SAAS,GAAG;EAChBC,KAAK,EAAEC,OAAO;EACdC,MAAM,EAAEd,eAAe,CAAC,GAAG,CAAC;EAC5Be,OAAO,EAAEF,OAAO;EAChBG,QAAQ,EAAEH,OAAO;EACjBI,QAAQ,EAAEJ,OAAO;EACjBK,QAAQ,EAAEC,MAAM;EAChBC,SAAS,EAAEC,MAAM;EACjBC,SAAS,EAAEvB,cAAc,CAAC,MAAM,CAAC;EACjCwB,WAAW,EAAEF,MAAM;EACnBG,YAAY,EAAEH,MAAM;EACpBI,cAAc,EAAE3B;AAClB,CAAC;AACD,IAAI4B,aAAa,GAAGhC,eAAe,CAAC;EAClCc,IAAI;EACJmB,KAAK,EAAEhB,SAAS;EAChBiB,KAAK,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC;EACjDC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMhB,OAAO,GAAG3B,GAAG,CAACuC,KAAK,CAACZ,OAAO,CAAC;IAClC,MAAMiB,IAAI,GAAG5C,GAAG,CAAC,CAAC;IAClB,MAAM6C,WAAW,GAAG7C,GAAG,CAAC,CAAC;IACzB,MAAM8C,SAAS,GAAG5B,eAAe,CAAC,CAAC;IACnC,MAAM6B,YAAY,GAAGhC,eAAe,CAAC6B,IAAI,CAAC;IAC1C,MAAMd,QAAQ,GAAG5B,QAAQ,CAAC,MAAMqC,KAAK,CAACT,QAAQ,IAAIiB,YAAY,CAACC,KAAK,CAAC;IACrE,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClB9C,QAAQ,CAAC,MAAM;QACb,IAAIwB,OAAO,CAACqB,KAAK,IAAIT,KAAK,CAACV,QAAQ,IAAIU,KAAK,CAACX,QAAQ,IAAIW,KAAK,CAACf,KAAK;QAAI;QACxE,CAACsB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,KAAK,MAAM,KAAK,EAAE;UACxD;QACF;QACA,MAAM;UACJd;QACF,CAAC,GAAGK,KAAK;QACT,MAAMb,MAAM,GAAG,CAACa,KAAK,CAACb,MAAM;QAC5B,MAAMwB,gBAAgB,GAAGpC,OAAO,CAACgB,QAAQ,CAAC;QAC1C,IAAI,CAACoB,gBAAgB,CAACC,MAAM,IAAI1C,QAAQ,CAACmC,IAAI,CAAC,EAAE;UAC9C;QACF;QACA,IAAIQ,WAAW,GAAG,KAAK;QACvB,MAAMC,eAAe,GAAGvC,OAAO,CAAC+B,WAAW,CAAC;QAC5C,IAAIX,SAAS,KAAK,IAAI,EAAE;UACtBkB,WAAW,GAAGF,gBAAgB,CAACI,GAAG,GAAGD,eAAe,CAACC,GAAG,IAAI5B,MAAM;QACpE,CAAC,MAAM;UACL0B,WAAW,GAAGC,eAAe,CAACE,MAAM,GAAGL,gBAAgB,CAACK,MAAM,IAAI7B,MAAM;QAC1E;QACA,IAAI0B,WAAW,EAAE;UACfzB,OAAO,CAACqB,KAAK,GAAG,IAAI;UACpBN,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC;UAC5BA,IAAI,CAAC,MAAM,CAAC;QACd;MACF,CAAC,CAAC;IACJ,CAAC;IACD,MAAMc,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAIjB,KAAK,CAACV,QAAQ,EAAE;QAClB,MAAM4B,IAAI,GAAGd,KAAK,CAACd,QAAQ,GAAGc,KAAK,CAACd,QAAQ,CAAC,CAAC,GAAGU,KAAK,CAACH,YAAY;QACnE,IAAIqB,IAAI,EAAE;UACR,OAAOjD,YAAY,CAAC,KAAK,EAAE;YACzB,OAAO,EAAEa,GAAG,CAAC,eAAe;UAC9B,CAAC,EAAE,CAACoC,IAAI,CAAC,CAAC;QACZ;MACF;IACF,CAAC;IACD,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC3BhB,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC;MAC3BO,KAAK,CAAC,CAAC;IACT,CAAC;IACD,MAAMU,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIpB,KAAK,CAACf,KAAK,EAAE;QACf,MAAMiC,IAAI,GAAGd,KAAK,CAACnB,KAAK,GAAGmB,KAAK,CAACnB,KAAK,CAAC,CAAC,GAAGe,KAAK,CAACP,SAAS;QAC1D,IAAIyB,IAAI,EAAE;UACR,OAAOjD,YAAY,CAAC,KAAK,EAAE;YACzB,MAAM,EAAE,QAAQ;YAChB,OAAO,EAAEa,GAAG,CAAC,YAAY,CAAC;YAC1B,UAAU,EAAE,CAAC;YACb,SAAS,EAAEqC;UACb,CAAC,EAAE,CAACD,IAAI,CAAC,CAAC;QACZ;MACF;IACF,CAAC;IACD,MAAMG,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIjC,OAAO,CAACqB,KAAK,IAAI,CAACT,KAAK,CAACV,QAAQ,IAAI,CAACU,KAAK,CAACX,QAAQ,EAAE;QACvD,OAAOpB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEa,GAAG,CAAC,SAAS;QACxB,CAAC,EAAE,CAACsB,KAAK,CAAChB,OAAO,GAAGgB,KAAK,CAAChB,OAAO,CAAC,CAAC,GAAGnB,YAAY,CAACW,OAAO,EAAE;UAC1D,OAAO,EAAEE,GAAG,CAAC,cAAc;QAC7B,CAAC,EAAE;UACDwC,OAAO,EAAEA,CAAA,KAAM,CAACtB,KAAK,CAACJ,WAAW,IAAIb,CAAC,CAAC,SAAS,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC;IACDrB,KAAK,CAAC,MAAM,CAACsC,KAAK,CAACZ,OAAO,EAAEY,KAAK,CAACV,QAAQ,EAAEU,KAAK,CAACf,KAAK,CAAC,EAAEyB,KAAK,CAAC;IAChE,IAAIH,SAAS,EAAE;MACb7C,KAAK,CAAC6C,SAAS,EAAGgB,SAAS,IAAK;QAC9B,IAAIA,SAAS,EAAE;UACbb,KAAK,CAAC,CAAC;QACT;MACF,CAAC,CAAC;IACJ;IACA7C,SAAS,CAAC,MAAM;MACduB,OAAO,CAACqB,KAAK,GAAGT,KAAK,CAACZ,OAAO;IAC/B,CAAC,CAAC;IACFtB,SAAS,CAAC,MAAM;MACd,IAAIkC,KAAK,CAACF,cAAc,EAAE;QACxBY,KAAK,CAAC,CAAC;MACT;IACF,CAAC,CAAC;IACFhC,SAAS,CAAC;MACRgC;IACF,CAAC,CAAC;IACFjC,gBAAgB,CAAC,QAAQ,EAAEiC,KAAK,EAAE;MAChCc,MAAM,EAAEjC,QAAQ;MAChBkC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIC,EAAE;MACN,MAAMC,OAAO,GAAG,CAACD,EAAE,GAAGtB,KAAK,CAACkB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,EAAE,CAACE,IAAI,CAACxB,KAAK,CAAC;MACtE,MAAMyB,WAAW,GAAG5D,YAAY,CAAC,KAAK,EAAE;QACtC,KAAK,EAAEqC,WAAW;QAClB,OAAO,EAAExB,GAAG,CAAC,aAAa;MAC5B,CAAC,EAAE,IAAI,CAAC;MACR,OAAOb,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEoC,IAAI;QACX,MAAM,EAAE,MAAM;QACd,OAAO,EAAEvB,GAAG,CAAC,CAAC;QACd,WAAW,EAAEM,OAAO,CAACqB;MACvB,CAAC,EAAE,CAACT,KAAK,CAACL,SAAS,KAAK,MAAM,GAAGgC,OAAO,GAAGE,WAAW,EAAER,aAAa,CAAC,CAAC,EAAEJ,kBAAkB,CAAC,CAAC,EAAEG,eAAe,CAAC,CAAC,EAAEpB,KAAK,CAACL,SAAS,KAAK,IAAI,GAAGgC,OAAO,GAAGE,WAAW,CAAC,CAAC;IACtK,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE9B,aAAa,IAAIuB,OAAO,EACxBtC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}