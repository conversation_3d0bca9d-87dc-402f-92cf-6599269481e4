{"ast": null, "code": "import { inject, computed, defineComponent, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { addUnit, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nimport { CONFIG_PROVIDER_KEY } from \"../config-provider/ConfigProvider.mjs\";\nconst [name, bem] = createNamespace(\"icon\");\nconst isImage = name2 => name2 == null ? void 0 : name2.includes(\"/\");\nconst iconProps = {\n  dot: Boolean,\n  tag: makeStringProp(\"i\"),\n  name: String,\n  size: numericProp,\n  badge: numericProp,\n  color: String,\n  badgeProps: Object,\n  classPrefix: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: iconProps,\n  setup(props, {\n    slots\n  }) {\n    const config = inject(CONFIG_PROVIDER_KEY, null);\n    const classPrefix = computed(() => props.classPrefix || (config == null ? void 0 : config.iconPrefix) || bem());\n    return () => {\n      const {\n        tag,\n        dot,\n        name: name2,\n        size,\n        badge,\n        color\n      } = props;\n      const isImageIcon = isImage(name2);\n      return _createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"tag\": tag,\n        \"class\": [classPrefix.value, isImageIcon ? \"\" : `${classPrefix.value}-${name2}`],\n        \"style\": {\n          color,\n          fontSize: addUnit(size)\n        },\n        \"content\": badge\n      }, props.badgeProps), {\n        default: () => {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots), isImageIcon && _createVNode(\"img\", {\n            \"class\": bem(\"image\"),\n            \"src\": name2\n          }, null)];\n        }\n      });\n    };\n  }\n});\nexport { stdin_default as default, iconProps };", "map": {"version": 3, "names": ["inject", "computed", "defineComponent", "createVNode", "_createVNode", "mergeProps", "_mergeProps", "addUnit", "numericProp", "makeStringProp", "createNamespace", "Badge", "CONFIG_PROVIDER_KEY", "name", "bem", "isImage", "name2", "includes", "iconProps", "dot", "Boolean", "tag", "String", "size", "badge", "color", "badgeProps", "Object", "classPrefix", "stdin_default", "props", "setup", "slots", "config", "iconPrefix", "isImageIcon", "value", "fontSize", "default", "_a", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/icon/Icon.mjs"], "sourcesContent": ["import { inject, computed, defineComponent, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { addUnit, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nimport { CONFIG_PROVIDER_KEY } from \"../config-provider/ConfigProvider.mjs\";\nconst [name, bem] = createNamespace(\"icon\");\nconst isImage = (name2) => name2 == null ? void 0 : name2.includes(\"/\");\nconst iconProps = {\n  dot: Boolean,\n  tag: makeStringProp(\"i\"),\n  name: String,\n  size: numericProp,\n  badge: numericProp,\n  color: String,\n  badgeProps: Object,\n  classPrefix: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: iconProps,\n  setup(props, {\n    slots\n  }) {\n    const config = inject(CONFIG_PROVIDER_KEY, null);\n    const classPrefix = computed(() => props.classPrefix || (config == null ? void 0 : config.iconPrefix) || bem());\n    return () => {\n      const {\n        tag,\n        dot,\n        name: name2,\n        size,\n        badge,\n        color\n      } = props;\n      const isImageIcon = isImage(name2);\n      return _createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"tag\": tag,\n        \"class\": [classPrefix.value, isImageIcon ? \"\" : `${classPrefix.value}-${name2}`],\n        \"style\": {\n          color,\n          fontSize: addUnit(size)\n        },\n        \"content\": badge\n      }, props.badgeProps), {\n        default: () => {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots), isImageIcon && _createVNode(\"img\", {\n            \"class\": bem(\"image\"),\n            \"src\": name2\n          }, null)];\n        }\n      });\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  iconProps\n};\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC/G,SAASC,OAAO,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC1F,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,MAAM,CAAC;AAC3C,MAAMK,OAAO,GAAIC,KAAK,IAAKA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC;AACvE,MAAMC,SAAS,GAAG;EAChBC,GAAG,EAAEC,OAAO;EACZC,GAAG,EAAEZ,cAAc,CAAC,GAAG,CAAC;EACxBI,IAAI,EAAES,MAAM;EACZC,IAAI,EAAEf,WAAW;EACjBgB,KAAK,EAAEhB,WAAW;EAClBiB,KAAK,EAAEH,MAAM;EACbI,UAAU,EAAEC,MAAM;EAClBC,WAAW,EAAEN;AACf,CAAC;AACD,IAAIO,aAAa,GAAG3B,eAAe,CAAC;EAClCW,IAAI;EACJiB,KAAK,EAAEZ,SAAS;EAChBa,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,MAAM,GAAGjC,MAAM,CAACY,mBAAmB,EAAE,IAAI,CAAC;IAChD,MAAMgB,WAAW,GAAG3B,QAAQ,CAAC,MAAM6B,KAAK,CAACF,WAAW,KAAKK,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,UAAU,CAAC,IAAIpB,GAAG,CAAC,CAAC,CAAC;IAC/G,OAAO,MAAM;MACX,MAAM;QACJO,GAAG;QACHF,GAAG;QACHN,IAAI,EAAEG,KAAK;QACXO,IAAI;QACJC,KAAK;QACLC;MACF,CAAC,GAAGK,KAAK;MACT,MAAMK,WAAW,GAAGpB,OAAO,CAACC,KAAK,CAAC;MAClC,OAAOZ,YAAY,CAACO,KAAK,EAAEL,WAAW,CAAC;QACrC,KAAK,EAAEa,GAAG;QACV,KAAK,EAAEE,GAAG;QACV,OAAO,EAAE,CAACO,WAAW,CAACQ,KAAK,EAAED,WAAW,GAAG,EAAE,GAAG,GAAGP,WAAW,CAACQ,KAAK,IAAIpB,KAAK,EAAE,CAAC;QAChF,OAAO,EAAE;UACPS,KAAK;UACLY,QAAQ,EAAE9B,OAAO,CAACgB,IAAI;QACxB,CAAC;QACD,SAAS,EAAEC;MACb,CAAC,EAAEM,KAAK,CAACJ,UAAU,CAAC,EAAE;QACpBY,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIC,EAAE;UACN,OAAO,CAAC,CAACA,EAAE,GAAGP,KAAK,CAACM,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,EAAE,CAACC,IAAI,CAACR,KAAK,CAAC,EAAEG,WAAW,IAAI/B,YAAY,CAAC,KAAK,EAAE;YACjG,OAAO,EAAEU,GAAG,CAAC,OAAO,CAAC;YACrB,KAAK,EAAEE;UACT,CAAC,EAAE,IAAI,CAAC,CAAC;QACX;MACF,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEa,aAAa,IAAIS,OAAO,EACxBpB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}