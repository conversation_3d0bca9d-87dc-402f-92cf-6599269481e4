{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _DropdownItem from \"./DropdownItem.mjs\";\nconst DropdownItem = withInstall(_DropdownItem);\nvar stdin_default = DropdownItem;\nimport { dropdownItemProps } from \"./DropdownItem.mjs\";\nexport { DropdownItem, stdin_default as default, dropdownItemProps };", "map": {"version": 3, "names": ["withInstall", "_DropdownItem", "DropdownItem", "stdin_default", "dropdownItemProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/dropdown-item/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _DropdownItem from \"./DropdownItem.mjs\";\nconst DropdownItem = withInstall(_DropdownItem);\nvar stdin_default = DropdownItem;\nimport { dropdownItemProps } from \"./DropdownItem.mjs\";\nexport {\n  DropdownItem,\n  stdin_default as default,\n  dropdownItemProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,MAAMC,YAAY,GAAGF,WAAW,CAACC,aAAa,CAAC;AAC/C,IAAIE,aAAa,GAAGD,YAAY;AAChC,SAASE,iBAAiB,QAAQ,oBAAoB;AACtD,SACEF,YAAY,EACZC,aAAa,IAAIE,OAAO,EACxBD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}