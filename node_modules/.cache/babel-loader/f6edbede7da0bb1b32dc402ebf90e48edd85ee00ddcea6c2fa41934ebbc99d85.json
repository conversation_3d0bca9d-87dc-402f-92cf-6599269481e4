{"ast": null, "code": "import { ref, watch, reactive, defineComponent, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nimport { isDef, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { raf, useRect, doubleRaf, useEventListener, onMountedOrActivated } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"notice-bar\");\nconst noticeBarProps = {\n  text: String,\n  mode: String,\n  color: String,\n  delay: makeNumericProp(1),\n  speed: makeNumericProp(60),\n  leftIcon: String,\n  wrapable: Boolean,\n  background: String,\n  scrollable: {\n    type: Boolean,\n    default: null\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: noticeBarProps,\n  emits: [\"close\", \"replay\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let wrapWidth = 0;\n    let contentWidth = 0;\n    let startTimer;\n    const wrapRef = ref();\n    const contentRef = ref();\n    const state = reactive({\n      show: true,\n      offset: 0,\n      duration: 0\n    });\n    const renderLeftIcon = () => {\n      if (slots[\"left-icon\"]) {\n        return slots[\"left-icon\"]();\n      }\n      if (props.leftIcon) {\n        return _createVNode(Icon, {\n          \"class\": bem(\"left-icon\"),\n          \"name\": props.leftIcon\n        }, null);\n      }\n    };\n    const getRightIconName = () => {\n      if (props.mode === \"closeable\") {\n        return \"cross\";\n      }\n      if (props.mode === \"link\") {\n        return \"arrow\";\n      }\n    };\n    const onClickRightIcon = event => {\n      if (props.mode === \"closeable\") {\n        state.show = false;\n        emit(\"close\", event);\n      }\n    };\n    const renderRightIcon = () => {\n      if (slots[\"right-icon\"]) {\n        return slots[\"right-icon\"]();\n      }\n      const name2 = getRightIconName();\n      if (name2) {\n        return _createVNode(Icon, {\n          \"name\": name2,\n          \"class\": bem(\"right-icon\"),\n          \"onClick\": onClickRightIcon\n        }, null);\n      }\n    };\n    const onTransitionEnd = () => {\n      state.offset = wrapWidth;\n      state.duration = 0;\n      raf(() => {\n        doubleRaf(() => {\n          state.offset = -contentWidth;\n          state.duration = (contentWidth + wrapWidth) / +props.speed;\n          emit(\"replay\");\n        });\n      });\n    };\n    const renderMarquee = () => {\n      const ellipsis = props.scrollable === false && !props.wrapable;\n      const style = {\n        transform: state.offset ? `translateX(${state.offset}px)` : \"\",\n        transitionDuration: `${state.duration}s`\n      };\n      return _createVNode(\"div\", {\n        \"ref\": wrapRef,\n        \"role\": \"marquee\",\n        \"class\": bem(\"wrap\")\n      }, [_createVNode(\"div\", {\n        \"ref\": contentRef,\n        \"style\": style,\n        \"class\": [bem(\"content\"), {\n          \"van-ellipsis\": ellipsis\n        }],\n        \"onTransitionend\": onTransitionEnd\n      }, [slots.default ? slots.default() : props.text])]);\n    };\n    const reset = () => {\n      const {\n        delay,\n        speed,\n        scrollable\n      } = props;\n      const ms = isDef(delay) ? +delay * 1e3 : 0;\n      wrapWidth = 0;\n      contentWidth = 0;\n      state.offset = 0;\n      state.duration = 0;\n      clearTimeout(startTimer);\n      startTimer = setTimeout(() => {\n        if (!wrapRef.value || !contentRef.value || scrollable === false) {\n          return;\n        }\n        const wrapRefWidth = useRect(wrapRef).width;\n        const contentRefWidth = useRect(contentRef).width;\n        if (scrollable || contentRefWidth > wrapRefWidth) {\n          doubleRaf(() => {\n            wrapWidth = wrapRefWidth;\n            contentWidth = contentRefWidth;\n            state.offset = -contentWidth;\n            state.duration = contentWidth / +speed;\n          });\n        }\n      }, ms);\n    };\n    onPopupReopen(reset);\n    onMountedOrActivated(reset);\n    useEventListener(\"pageshow\", reset);\n    useExpose({\n      reset\n    });\n    watch(() => [props.text, props.scrollable], reset);\n    return () => {\n      const {\n        color,\n        wrapable,\n        background\n      } = props;\n      return _withDirectives(_createVNode(\"div\", {\n        \"role\": \"alert\",\n        \"class\": bem({\n          wrapable\n        }),\n        \"style\": {\n          color,\n          background\n        }\n      }, [renderLeftIcon(), renderMarquee(), renderRightIcon()]), [[_vShow, state.show]]);\n    };\n  }\n});\nexport { stdin_default as default, noticeBarProps };", "map": {"version": 3, "names": ["ref", "watch", "reactive", "defineComponent", "createVNode", "_createVNode", "vShow", "_vShow", "withDirectives", "_withDirectives", "isDef", "createNamespace", "makeNumericProp", "raf", "useRect", "doubleRaf", "useEventListener", "onMountedOrActivated", "useExpose", "onPopupReopen", "Icon", "name", "bem", "noticeBarProps", "text", "String", "mode", "color", "delay", "speed", "leftIcon", "wrapable", "Boolean", "background", "scrollable", "type", "default", "stdin_default", "props", "emits", "setup", "emit", "slots", "wrapWidth", "contentWidth", "startTimer", "wrapRef", "contentRef", "state", "show", "offset", "duration", "renderLeftIcon", "getRightIconName", "onClickRightIcon", "event", "renderRightIcon", "name2", "onTransitionEnd", "renderMarquee", "ellipsis", "style", "transform", "transitionDuration", "reset", "ms", "clearTimeout", "setTimeout", "value", "wrapRefWidth", "width", "contentRefWidth"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/notice-bar/NoticeBar.mjs"], "sourcesContent": ["import { ref, watch, reactive, defineComponent, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nimport { isDef, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { raf, useRect, doubleRaf, useEventListener, onMountedOrActivated } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"notice-bar\");\nconst noticeBarProps = {\n  text: String,\n  mode: String,\n  color: String,\n  delay: makeNumericProp(1),\n  speed: makeNumericProp(60),\n  leftIcon: String,\n  wrapable: Boolean,\n  background: String,\n  scrollable: {\n    type: Boolean,\n    default: null\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: noticeBarProps,\n  emits: [\"close\", \"replay\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let wrapWidth = 0;\n    let contentWidth = 0;\n    let startTimer;\n    const wrapRef = ref();\n    const contentRef = ref();\n    const state = reactive({\n      show: true,\n      offset: 0,\n      duration: 0\n    });\n    const renderLeftIcon = () => {\n      if (slots[\"left-icon\"]) {\n        return slots[\"left-icon\"]();\n      }\n      if (props.leftIcon) {\n        return _createVNode(Icon, {\n          \"class\": bem(\"left-icon\"),\n          \"name\": props.leftIcon\n        }, null);\n      }\n    };\n    const getRightIconName = () => {\n      if (props.mode === \"closeable\") {\n        return \"cross\";\n      }\n      if (props.mode === \"link\") {\n        return \"arrow\";\n      }\n    };\n    const onClickRightIcon = (event) => {\n      if (props.mode === \"closeable\") {\n        state.show = false;\n        emit(\"close\", event);\n      }\n    };\n    const renderRightIcon = () => {\n      if (slots[\"right-icon\"]) {\n        return slots[\"right-icon\"]();\n      }\n      const name2 = getRightIconName();\n      if (name2) {\n        return _createVNode(Icon, {\n          \"name\": name2,\n          \"class\": bem(\"right-icon\"),\n          \"onClick\": onClickRightIcon\n        }, null);\n      }\n    };\n    const onTransitionEnd = () => {\n      state.offset = wrapWidth;\n      state.duration = 0;\n      raf(() => {\n        doubleRaf(() => {\n          state.offset = -contentWidth;\n          state.duration = (contentWidth + wrapWidth) / +props.speed;\n          emit(\"replay\");\n        });\n      });\n    };\n    const renderMarquee = () => {\n      const ellipsis = props.scrollable === false && !props.wrapable;\n      const style = {\n        transform: state.offset ? `translateX(${state.offset}px)` : \"\",\n        transitionDuration: `${state.duration}s`\n      };\n      return _createVNode(\"div\", {\n        \"ref\": wrapRef,\n        \"role\": \"marquee\",\n        \"class\": bem(\"wrap\")\n      }, [_createVNode(\"div\", {\n        \"ref\": contentRef,\n        \"style\": style,\n        \"class\": [bem(\"content\"), {\n          \"van-ellipsis\": ellipsis\n        }],\n        \"onTransitionend\": onTransitionEnd\n      }, [slots.default ? slots.default() : props.text])]);\n    };\n    const reset = () => {\n      const {\n        delay,\n        speed,\n        scrollable\n      } = props;\n      const ms = isDef(delay) ? +delay * 1e3 : 0;\n      wrapWidth = 0;\n      contentWidth = 0;\n      state.offset = 0;\n      state.duration = 0;\n      clearTimeout(startTimer);\n      startTimer = setTimeout(() => {\n        if (!wrapRef.value || !contentRef.value || scrollable === false) {\n          return;\n        }\n        const wrapRefWidth = useRect(wrapRef).width;\n        const contentRefWidth = useRect(contentRef).width;\n        if (scrollable || contentRefWidth > wrapRefWidth) {\n          doubleRaf(() => {\n            wrapWidth = wrapRefWidth;\n            contentWidth = contentRefWidth;\n            state.offset = -contentWidth;\n            state.duration = contentWidth / +speed;\n          });\n        }\n      }, ms);\n    };\n    onPopupReopen(reset);\n    onMountedOrActivated(reset);\n    useEventListener(\"pageshow\", reset);\n    useExpose({\n      reset\n    });\n    watch(() => [props.text, props.scrollable], reset);\n    return () => {\n      const {\n        color,\n        wrapable,\n        background\n      } = props;\n      return _withDirectives(_createVNode(\"div\", {\n        \"role\": \"alert\",\n        \"class\": bem({\n          wrapable\n        }),\n        \"style\": {\n          color,\n          background\n        }\n      }, [renderLeftIcon(), renderMarquee(), renderRightIcon()]), [[_vShow, state.show]]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  noticeBarProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,EAAEC,KAAK,IAAIC,MAAM,EAAEC,cAAc,IAAIC,eAAe,QAAQ,KAAK;AAC5I,SAASC,KAAK,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAC5E,SAASC,GAAG,EAAEC,OAAO,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,WAAW;AAC3F,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGX,eAAe,CAAC,YAAY,CAAC;AACjD,MAAMY,cAAc,GAAG;EACrBC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAED,MAAM;EACZE,KAAK,EAAEF,MAAM;EACbG,KAAK,EAAEhB,eAAe,CAAC,CAAC,CAAC;EACzBiB,KAAK,EAAEjB,eAAe,CAAC,EAAE,CAAC;EAC1BkB,QAAQ,EAAEL,MAAM;EAChBM,QAAQ,EAAEC,OAAO;EACjBC,UAAU,EAAER,MAAM;EAClBS,UAAU,EAAE;IACVC,IAAI,EAAEH,OAAO;IACbI,OAAO,EAAE;EACX;AACF,CAAC;AACD,IAAIC,aAAa,GAAGlC,eAAe,CAAC;EAClCkB,IAAI;EACJiB,KAAK,EAAEf,cAAc;EACrBgB,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;EAC1BC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,UAAU;IACd,MAAMC,OAAO,GAAG9C,GAAG,CAAC,CAAC;IACrB,MAAM+C,UAAU,GAAG/C,GAAG,CAAC,CAAC;IACxB,MAAMgD,KAAK,GAAG9C,QAAQ,CAAC;MACrB+C,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIV,KAAK,CAAC,WAAW,CAAC,EAAE;QACtB,OAAOA,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;MAC7B;MACA,IAAIJ,KAAK,CAACR,QAAQ,EAAE;QAClB,OAAOzB,YAAY,CAACe,IAAI,EAAE;UACxB,OAAO,EAAEE,GAAG,CAAC,WAAW,CAAC;UACzB,MAAM,EAAEgB,KAAK,CAACR;QAChB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAIf,KAAK,CAACZ,IAAI,KAAK,WAAW,EAAE;QAC9B,OAAO,OAAO;MAChB;MACA,IAAIY,KAAK,CAACZ,IAAI,KAAK,MAAM,EAAE;QACzB,OAAO,OAAO;MAChB;IACF,CAAC;IACD,MAAM4B,gBAAgB,GAAIC,KAAK,IAAK;MAClC,IAAIjB,KAAK,CAACZ,IAAI,KAAK,WAAW,EAAE;QAC9BsB,KAAK,CAACC,IAAI,GAAG,KAAK;QAClBR,IAAI,CAAC,OAAO,EAAEc,KAAK,CAAC;MACtB;IACF,CAAC;IACD,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAId,KAAK,CAAC,YAAY,CAAC,EAAE;QACvB,OAAOA,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;MAC9B;MACA,MAAMe,KAAK,GAAGJ,gBAAgB,CAAC,CAAC;MAChC,IAAII,KAAK,EAAE;QACT,OAAOpD,YAAY,CAACe,IAAI,EAAE;UACxB,MAAM,EAAEqC,KAAK;UACb,OAAO,EAAEnC,GAAG,CAAC,YAAY,CAAC;UAC1B,SAAS,EAAEgC;QACb,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,MAAMI,eAAe,GAAGA,CAAA,KAAM;MAC5BV,KAAK,CAACE,MAAM,GAAGP,SAAS;MACxBK,KAAK,CAACG,QAAQ,GAAG,CAAC;MAClBtC,GAAG,CAAC,MAAM;QACRE,SAAS,CAAC,MAAM;UACdiC,KAAK,CAACE,MAAM,GAAG,CAACN,YAAY;UAC5BI,KAAK,CAACG,QAAQ,GAAG,CAACP,YAAY,GAAGD,SAAS,IAAI,CAACL,KAAK,CAACT,KAAK;UAC1DY,IAAI,CAAC,QAAQ,CAAC;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,MAAMkB,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,QAAQ,GAAGtB,KAAK,CAACJ,UAAU,KAAK,KAAK,IAAI,CAACI,KAAK,CAACP,QAAQ;MAC9D,MAAM8B,KAAK,GAAG;QACZC,SAAS,EAAEd,KAAK,CAACE,MAAM,GAAG,cAAcF,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;QAC9Da,kBAAkB,EAAE,GAAGf,KAAK,CAACG,QAAQ;MACvC,CAAC;MACD,OAAO9C,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEyC,OAAO;QACd,MAAM,EAAE,SAAS;QACjB,OAAO,EAAExB,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACjB,YAAY,CAAC,KAAK,EAAE;QACtB,KAAK,EAAE0C,UAAU;QACjB,OAAO,EAAEc,KAAK;QACd,OAAO,EAAE,CAACvC,GAAG,CAAC,SAAS,CAAC,EAAE;UACxB,cAAc,EAAEsC;QAClB,CAAC,CAAC;QACF,iBAAiB,EAAEF;MACrB,CAAC,EAAE,CAAChB,KAAK,CAACN,OAAO,GAAGM,KAAK,CAACN,OAAO,CAAC,CAAC,GAAGE,KAAK,CAACd,IAAI,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC;IACD,MAAMwC,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAM;QACJpC,KAAK;QACLC,KAAK;QACLK;MACF,CAAC,GAAGI,KAAK;MACT,MAAM2B,EAAE,GAAGvD,KAAK,CAACkB,KAAK,CAAC,GAAG,CAACA,KAAK,GAAG,GAAG,GAAG,CAAC;MAC1Ce,SAAS,GAAG,CAAC;MACbC,YAAY,GAAG,CAAC;MAChBI,KAAK,CAACE,MAAM,GAAG,CAAC;MAChBF,KAAK,CAACG,QAAQ,GAAG,CAAC;MAClBe,YAAY,CAACrB,UAAU,CAAC;MACxBA,UAAU,GAAGsB,UAAU,CAAC,MAAM;QAC5B,IAAI,CAACrB,OAAO,CAACsB,KAAK,IAAI,CAACrB,UAAU,CAACqB,KAAK,IAAIlC,UAAU,KAAK,KAAK,EAAE;UAC/D;QACF;QACA,MAAMmC,YAAY,GAAGvD,OAAO,CAACgC,OAAO,CAAC,CAACwB,KAAK;QAC3C,MAAMC,eAAe,GAAGzD,OAAO,CAACiC,UAAU,CAAC,CAACuB,KAAK;QACjD,IAAIpC,UAAU,IAAIqC,eAAe,GAAGF,YAAY,EAAE;UAChDtD,SAAS,CAAC,MAAM;YACd4B,SAAS,GAAG0B,YAAY;YACxBzB,YAAY,GAAG2B,eAAe;YAC9BvB,KAAK,CAACE,MAAM,GAAG,CAACN,YAAY;YAC5BI,KAAK,CAACG,QAAQ,GAAGP,YAAY,GAAG,CAACf,KAAK;UACxC,CAAC,CAAC;QACJ;MACF,CAAC,EAAEoC,EAAE,CAAC;IACR,CAAC;IACD9C,aAAa,CAAC6C,KAAK,CAAC;IACpB/C,oBAAoB,CAAC+C,KAAK,CAAC;IAC3BhD,gBAAgB,CAAC,UAAU,EAAEgD,KAAK,CAAC;IACnC9C,SAAS,CAAC;MACR8C;IACF,CAAC,CAAC;IACF/D,KAAK,CAAC,MAAM,CAACqC,KAAK,CAACd,IAAI,EAAEc,KAAK,CAACJ,UAAU,CAAC,EAAE8B,KAAK,CAAC;IAClD,OAAO,MAAM;MACX,MAAM;QACJrC,KAAK;QACLI,QAAQ;QACRE;MACF,CAAC,GAAGK,KAAK;MACT,OAAO7B,eAAe,CAACJ,YAAY,CAAC,KAAK,EAAE;QACzC,MAAM,EAAE,OAAO;QACf,OAAO,EAAEiB,GAAG,CAAC;UACXS;QACF,CAAC,CAAC;QACF,OAAO,EAAE;UACPJ,KAAK;UACLM;QACF;MACF,CAAC,EAAE,CAACmB,cAAc,CAAC,CAAC,EAAEO,aAAa,CAAC,CAAC,EAAEH,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAACjD,MAAM,EAAEyC,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;IACrF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEZ,aAAa,IAAID,OAAO,EACxBb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}