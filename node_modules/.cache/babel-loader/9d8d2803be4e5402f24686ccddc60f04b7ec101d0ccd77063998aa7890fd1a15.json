{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _NavBar from \"./NavBar.mjs\";\nconst NavBar = withInstall(_NavBar);\nvar stdin_default = NavBar;\nimport { navBarProps } from \"./NavBar.mjs\";\nexport { NavBar, stdin_default as default, navBarProps };", "map": {"version": 3, "names": ["withInstall", "_NavBar", "NavBar", "stdin_default", "navBarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/nav-bar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _NavBar from \"./NavBar.mjs\";\nconst NavBar = withInstall(_NavBar);\nvar stdin_default = NavBar;\nimport { navBarProps } from \"./NavBar.mjs\";\nexport {\n  NavBar,\n  stdin_default as default,\n  navBarProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEF,MAAM,EACNC,aAAa,IAAIE,OAAO,EACxBD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}