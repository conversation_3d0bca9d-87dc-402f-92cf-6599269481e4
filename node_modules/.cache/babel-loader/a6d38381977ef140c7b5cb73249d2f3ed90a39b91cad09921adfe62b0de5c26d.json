{"ast": null, "code": "import { ref, reactive } from \"vue\";\nimport { deepAssign } from \"../utils/deep-assign.mjs\";\nimport defaultMessages from \"./lang/zh-CN.mjs\";\nconst lang = ref(\"zh-CN\");\nconst messages = reactive({\n  \"zh-CN\": defaultMessages\n});\nconst Locale = {\n  messages() {\n    return messages[lang.value];\n  },\n  use(newLang, newMessages) {\n    lang.value = newLang;\n    this.add({\n      [newLang]: newMessages\n    });\n  },\n  add(newMessages = {}) {\n    deepAssign(messages, newMessages);\n  }\n};\nconst useCurrentLang = () => lang;\nvar stdin_default = Locale;\nexport { Locale, stdin_default as default, useCurrentLang };", "map": {"version": 3, "names": ["ref", "reactive", "deepAssign", "defaultMessages", "lang", "messages", "Locale", "value", "use", "newLang", "newMessages", "add", "useCurrentLang", "stdin_default", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/locale/index.mjs"], "sourcesContent": ["import { ref, reactive } from \"vue\";\nimport { deepAssign } from \"../utils/deep-assign.mjs\";\nimport defaultMessages from \"./lang/zh-CN.mjs\";\nconst lang = ref(\"zh-CN\");\nconst messages = reactive({\n  \"zh-CN\": defaultMessages\n});\nconst Locale = {\n  messages() {\n    return messages[lang.value];\n  },\n  use(newLang, newMessages) {\n    lang.value = newLang;\n    this.add({ [newLang]: newMessages });\n  },\n  add(newMessages = {}) {\n    deepAssign(messages, newMessages);\n  }\n};\nconst useCurrentLang = () => lang;\nvar stdin_default = Locale;\nexport {\n  Locale,\n  stdin_default as default,\n  useCurrentLang\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AACnC,SAASC,UAAU,QAAQ,0BAA0B;AACrD,OAAOC,eAAe,MAAM,kBAAkB;AAC9C,MAAMC,IAAI,GAAGJ,GAAG,CAAC,OAAO,CAAC;AACzB,MAAMK,QAAQ,GAAGJ,QAAQ,CAAC;EACxB,OAAO,EAAEE;AACX,CAAC,CAAC;AACF,MAAMG,MAAM,GAAG;EACbD,QAAQA,CAAA,EAAG;IACT,OAAOA,QAAQ,CAACD,IAAI,CAACG,KAAK,CAAC;EAC7B,CAAC;EACDC,GAAGA,CAACC,OAAO,EAAEC,WAAW,EAAE;IACxBN,IAAI,CAACG,KAAK,GAAGE,OAAO;IACpB,IAAI,CAACE,GAAG,CAAC;MAAE,CAACF,OAAO,GAAGC;IAAY,CAAC,CAAC;EACtC,CAAC;EACDC,GAAGA,CAACD,WAAW,GAAG,CAAC,CAAC,EAAE;IACpBR,UAAU,CAACG,QAAQ,EAAEK,WAAW,CAAC;EACnC;AACF,CAAC;AACD,MAAME,cAAc,GAAGA,CAAA,KAAMR,IAAI;AACjC,IAAIS,aAAa,GAAGP,MAAM;AAC1B,SACEA,MAAM,EACNO,aAAa,IAAIC,OAAO,EACxBF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}