{"ast": null, "code": "import _SkeletonImage from \"./SkeletonImage.mjs\";\nimport { withInstall } from \"../utils/index.mjs\";\nconst SkeletonImage = withInstall(_SkeletonImage);\nvar stdin_default = SkeletonImage;\nimport { skeletonImageProps } from \"./SkeletonImage.mjs\";\nexport { SkeletonImage, stdin_default as default, skeletonImageProps };", "map": {"version": 3, "names": ["_SkeletonImage", "withInstall", "SkeletonImage", "stdin_default", "skeletonImageProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/skeleton-image/index.mjs"], "sourcesContent": ["import _SkeletonImage from \"./SkeletonImage.mjs\";\nimport { withInstall } from \"../utils/index.mjs\";\nconst SkeletonImage = withInstall(_SkeletonImage);\nvar stdin_default = SkeletonImage;\nimport { skeletonImageProps } from \"./SkeletonImage.mjs\";\nexport {\n  SkeletonImage,\n  stdin_default as default,\n  skeletonImageProps\n};\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,MAAMC,aAAa,GAAGD,WAAW,CAACD,cAAc,CAAC;AACjD,IAAIG,aAAa,GAAGD,aAAa;AACjC,SAASE,kBAAkB,QAAQ,qBAAqB;AACxD,SACEF,aAAa,EACbC,aAAa,IAAIE,OAAO,EACxBD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}