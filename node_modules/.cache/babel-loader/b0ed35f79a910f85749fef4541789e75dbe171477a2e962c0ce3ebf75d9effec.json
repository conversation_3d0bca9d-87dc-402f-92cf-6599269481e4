{"ast": null, "code": "import { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, isObject, inBrowser } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanNotify from \"./Notify.mjs\";\nlet timer;\nlet instance;\nconst parseOptions = message => isObject(message) ? message : {\n  message\n};\nfunction initInstance() {\n  ({\n    instance\n  } = mountComponent({\n    setup() {\n      const {\n        state,\n        toggle\n      } = usePopupState();\n      return () => _createVNode(VanNotify, _mergeProps(state, {\n        \"onUpdate:show\": toggle\n      }), null);\n    }\n  }));\n}\nconst getDefaultOptions = () => ({\n  type: \"danger\",\n  color: void 0,\n  message: \"\",\n  onClose: void 0,\n  onClick: void 0,\n  onOpened: void 0,\n  duration: 3e3,\n  position: void 0,\n  className: \"\",\n  lockScroll: false,\n  background: void 0\n});\nlet currentOptions = getDefaultOptions();\nconst closeNotify = () => {\n  if (instance) {\n    instance.toggle(false);\n  }\n};\nfunction showNotify(options) {\n  if (!inBrowser) {\n    return;\n  }\n  if (!instance) {\n    initInstance();\n  }\n  options = extend({}, currentOptions, parseOptions(options));\n  instance.open(options);\n  clearTimeout(timer);\n  if (options.duration > 0) {\n    timer = setTimeout(closeNotify, options.duration);\n  }\n  return instance;\n}\nconst setNotifyDefaultOptions = options => extend(currentOptions, options);\nconst resetNotifyDefaultOptions = () => {\n  currentOptions = getDefaultOptions();\n};\nexport { closeNotify, resetNotifyDefaultOptions, setNotifyDefaultOptions, showNotify };", "map": {"version": 3, "names": ["mergeProps", "_mergeProps", "createVNode", "_createVNode", "extend", "isObject", "inBrowser", "mountComponent", "usePopupState", "VanNotify", "timer", "instance", "parseOptions", "message", "initInstance", "setup", "state", "toggle", "getDefaultOptions", "type", "color", "onClose", "onClick", "onOpened", "duration", "position", "className", "lockScroll", "background", "currentOptions", "closeNotify", "showNotify", "options", "open", "clearTimeout", "setTimeout", "setNotifyDefaultOptions", "resetNotifyDefaultOptions"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/notify/function-call.mjs"], "sourcesContent": ["import { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, isObject, inBrowser } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanNotify from \"./Notify.mjs\";\nlet timer;\nlet instance;\nconst parseOptions = (message) => isObject(message) ? message : {\n  message\n};\nfunction initInstance() {\n  ({\n    instance\n  } = mountComponent({\n    setup() {\n      const {\n        state,\n        toggle\n      } = usePopupState();\n      return () => _createVNode(VanNotify, _mergeProps(state, {\n        \"onUpdate:show\": toggle\n      }), null);\n    }\n  }));\n}\nconst getDefaultOptions = () => ({\n  type: \"danger\",\n  color: void 0,\n  message: \"\",\n  onClose: void 0,\n  onClick: void 0,\n  onOpened: void 0,\n  duration: 3e3,\n  position: void 0,\n  className: \"\",\n  lockScroll: false,\n  background: void 0\n});\nlet currentOptions = getDefaultOptions();\nconst closeNotify = () => {\n  if (instance) {\n    instance.toggle(false);\n  }\n};\nfunction showNotify(options) {\n  if (!inBrowser) {\n    return;\n  }\n  if (!instance) {\n    initInstance();\n  }\n  options = extend({}, currentOptions, parseOptions(options));\n  instance.open(options);\n  clearTimeout(timer);\n  if (options.duration > 0) {\n    timer = setTimeout(closeNotify, options.duration);\n  }\n  return instance;\n}\nconst setNotifyDefaultOptions = (options) => extend(currentOptions, options);\nconst resetNotifyDefaultOptions = () => {\n  currentOptions = getDefaultOptions();\n};\nexport {\n  closeNotify,\n  resetNotifyDefaultOptions,\n  setNotifyDefaultOptions,\n  showNotify\n};\n"], "mappings": "AAAA,SAASA,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AAChE,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,OAAOC,SAAS,MAAM,cAAc;AACpC,IAAIC,KAAK;AACT,IAAIC,QAAQ;AACZ,MAAMC,YAAY,GAAIC,OAAO,IAAKR,QAAQ,CAACQ,OAAO,CAAC,GAAGA,OAAO,GAAG;EAC9DA;AACF,CAAC;AACD,SAASC,YAAYA,CAAA,EAAG;EACtB,CAAC;IACCH;EACF,CAAC,GAAGJ,cAAc,CAAC;IACjBQ,KAAKA,CAAA,EAAG;MACN,MAAM;QACJC,KAAK;QACLC;MACF,CAAC,GAAGT,aAAa,CAAC,CAAC;MACnB,OAAO,MAAML,YAAY,CAACM,SAAS,EAAER,WAAW,CAACe,KAAK,EAAE;QACtD,eAAe,EAAEC;MACnB,CAAC,CAAC,EAAE,IAAI,CAAC;IACX;EACF,CAAC,CAAC;AACJ;AACA,MAAMC,iBAAiB,GAAGA,CAAA,MAAO;EAC/BC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,KAAK,CAAC;EACbP,OAAO,EAAE,EAAE;EACXQ,OAAO,EAAE,KAAK,CAAC;EACfC,OAAO,EAAE,KAAK,CAAC;EACfC,QAAQ,EAAE,KAAK,CAAC;EAChBC,QAAQ,EAAE,GAAG;EACbC,QAAQ,EAAE,KAAK,CAAC;EAChBC,SAAS,EAAE,EAAE;EACbC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;AACnB,CAAC,CAAC;AACF,IAAIC,cAAc,GAAGX,iBAAiB,CAAC,CAAC;AACxC,MAAMY,WAAW,GAAGA,CAAA,KAAM;EACxB,IAAInB,QAAQ,EAAE;IACZA,QAAQ,CAACM,MAAM,CAAC,KAAK,CAAC;EACxB;AACF,CAAC;AACD,SAASc,UAAUA,CAACC,OAAO,EAAE;EAC3B,IAAI,CAAC1B,SAAS,EAAE;IACd;EACF;EACA,IAAI,CAACK,QAAQ,EAAE;IACbG,YAAY,CAAC,CAAC;EAChB;EACAkB,OAAO,GAAG5B,MAAM,CAAC,CAAC,CAAC,EAAEyB,cAAc,EAAEjB,YAAY,CAACoB,OAAO,CAAC,CAAC;EAC3DrB,QAAQ,CAACsB,IAAI,CAACD,OAAO,CAAC;EACtBE,YAAY,CAACxB,KAAK,CAAC;EACnB,IAAIsB,OAAO,CAACR,QAAQ,GAAG,CAAC,EAAE;IACxBd,KAAK,GAAGyB,UAAU,CAACL,WAAW,EAAEE,OAAO,CAACR,QAAQ,CAAC;EACnD;EACA,OAAOb,QAAQ;AACjB;AACA,MAAMyB,uBAAuB,GAAIJ,OAAO,IAAK5B,MAAM,CAACyB,cAAc,EAAEG,OAAO,CAAC;AAC5E,MAAMK,yBAAyB,GAAGA,CAAA,KAAM;EACtCR,cAAc,GAAGX,iBAAiB,CAAC,CAAC;AACtC,CAAC;AACD,SACEY,WAAW,EACXO,yBAAyB,EACzBD,uBAAuB,EACvBL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}