{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CouponCell from \"./CouponCell.mjs\";\nconst CouponCell = withInstall(_CouponCell);\nvar stdin_default = CouponCell;\nimport { couponCellProps } from \"./CouponCell.mjs\";\nexport { CouponCell, couponCellProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CouponCell", "CouponCell", "stdin_default", "couponCellProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/coupon-cell/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CouponCell from \"./CouponCell.mjs\";\nconst CouponCell = withInstall(_CouponCell);\nvar stdin_default = CouponCell;\nimport { couponCellProps } from \"./CouponCell.mjs\";\nexport {\n  CouponCell,\n  couponCellProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVE,eAAe,EACfD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}