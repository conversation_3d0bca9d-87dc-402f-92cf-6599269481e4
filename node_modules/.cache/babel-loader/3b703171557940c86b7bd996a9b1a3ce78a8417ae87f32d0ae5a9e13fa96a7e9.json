{"ast": null, "code": "export default {\n  __name: 'App',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n\n    // App.vue 现在只作为路由容器，不需要其他逻辑\n\n    const __returned__ = {};\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view />\n  </div>\n</template>\n\n<script setup>\n// App.vue 现在只作为路由容器，不需要其他逻辑\n</script>\n\n<style>\n/* 全局样式 */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n}\n\n#app {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  min-height: 100vh;\n}\n</style>"], "mappings": ";;;;;;;IAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}