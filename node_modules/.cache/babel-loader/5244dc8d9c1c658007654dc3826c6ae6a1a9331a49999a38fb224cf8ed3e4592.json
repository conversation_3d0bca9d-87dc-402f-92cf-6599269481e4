{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { nextTick } from \"vue\";\nimport { inBrowser, getScrollParent } from \"@vant/use\";\nimport { remove, on, off, throttle, supportWebp, getDPR, getBestSelectionFromSrcset, hasIntersectionObserver, modeType, ImageCache } from \"./util.mjs\";\nimport { isObject } from \"../../utils/index.mjs\";\nimport ReactiveListener from \"./listener.mjs\";\nconst DEFAULT_URL = \"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7\";\nconst DEFAULT_EVENTS = [\"scroll\", \"wheel\", \"mousewheel\", \"resize\", \"animationend\", \"transitionend\", \"touchmove\"];\nconst DEFAULT_OBSERVER_OPTIONS = {\n  rootMargin: \"0px\",\n  threshold: 0\n};\nfunction stdin_default() {\n  return class Lazy {\n    constructor({\n      preLoad,\n      error,\n      throttleWait,\n      preLoadTop,\n      dispatchEvent,\n      loading,\n      attempt,\n      silent = true,\n      scale,\n      listenEvents,\n      filter,\n      adapter,\n      observer,\n      observerOptions\n    }) {\n      this.mode = modeType.event;\n      this.listeners = [];\n      this.targetIndex = 0;\n      this.targets = [];\n      this.options = {\n        silent,\n        dispatchEvent: !!dispatchEvent,\n        throttleWait: throttleWait || 200,\n        preLoad: preLoad || 1.3,\n        preLoadTop: preLoadTop || 0,\n        error: error || DEFAULT_URL,\n        loading: loading || DEFAULT_URL,\n        attempt: attempt || 3,\n        scale: scale || getDPR(scale),\n        ListenEvents: listenEvents || DEFAULT_EVENTS,\n        supportWebp: supportWebp(),\n        filter: filter || {},\n        adapter: adapter || {},\n        observer: !!observer,\n        observerOptions: observerOptions || DEFAULT_OBSERVER_OPTIONS\n      };\n      this.initEvent();\n      this.imageCache = new ImageCache({\n        max: 200\n      });\n      this.lazyLoadHandler = throttle(this.lazyLoadHandler.bind(this), this.options.throttleWait);\n      this.setMode(this.options.observer ? modeType.observer : modeType.event);\n    }\n    /**\n     * update config\n     * @param  {Object} config params\n     * @return\n     */\n    config(options = {}) {\n      Object.assign(this.options, options);\n    }\n    /**\n     * output listener's load performance\n     * @return {Array}\n     */\n    performance() {\n      return this.listeners.map(item => item.performance());\n    }\n    /*\n     * add lazy component to queue\n     * @param  {Vue} vm lazy component instance\n     * @return\n     */\n    addLazyBox(vm) {\n      this.listeners.push(vm);\n      if (inBrowser) {\n        this.addListenerTarget(window);\n        this.observer && this.observer.observe(vm.el);\n        if (vm.$el && vm.$el.parentNode) {\n          this.addListenerTarget(vm.$el.parentNode);\n        }\n      }\n    }\n    /*\n     * add image listener to queue\n     * @param  {DOM} el\n     * @param  {object} binding vue directive binding\n     * @param  {vnode} vnode vue directive vnode\n     * @return\n     */\n    add(el, binding, vnode) {\n      if (this.listeners.some(item => item.el === el)) {\n        this.update(el, binding);\n        return nextTick(this.lazyLoadHandler);\n      }\n      const value = this.valueFormatter(binding.value);\n      let {\n        src\n      } = value;\n      nextTick(() => {\n        src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n        this.observer && this.observer.observe(el);\n        const container = Object.keys(binding.modifiers)[0];\n        let $parent;\n        if (container) {\n          $parent = vnode.context.$refs[container];\n          $parent = $parent ? $parent.$el || $parent : document.getElementById(container);\n        }\n        if (!$parent) {\n          $parent = getScrollParent(el);\n        }\n        const newListener = new ReactiveListener({\n          bindType: binding.arg,\n          $parent,\n          el,\n          src,\n          loading: value.loading,\n          error: value.error,\n          cors: value.cors,\n          elRenderer: this.elRenderer.bind(this),\n          options: this.options,\n          imageCache: this.imageCache\n        });\n        this.listeners.push(newListener);\n        if (inBrowser) {\n          this.addListenerTarget(window);\n          this.addListenerTarget($parent);\n        }\n        this.lazyLoadHandler();\n        nextTick(() => this.lazyLoadHandler());\n      });\n    }\n    /**\n     * update image src\n     * @param  {DOM} el\n     * @param  {object} vue directive binding\n     * @return\n     */\n    update(el, binding, vnode) {\n      const value = this.valueFormatter(binding.value);\n      let {\n        src\n      } = value;\n      src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n      const exist = this.listeners.find(item => item.el === el);\n      if (!exist) {\n        this.add(el, binding, vnode);\n      } else {\n        exist.update({\n          src,\n          error: value.error,\n          loading: value.loading\n        });\n      }\n      if (this.observer) {\n        this.observer.unobserve(el);\n        this.observer.observe(el);\n      }\n      this.lazyLoadHandler();\n      nextTick(() => this.lazyLoadHandler());\n    }\n    /**\n     * remove listener form list\n     * @param  {DOM} el\n     * @return\n     */\n    remove(el) {\n      if (!el) return;\n      this.observer && this.observer.unobserve(el);\n      const existItem = this.listeners.find(item => item.el === el);\n      if (existItem) {\n        this.removeListenerTarget(existItem.$parent);\n        this.removeListenerTarget(window);\n        remove(this.listeners, existItem);\n        existItem.$destroy();\n      }\n    }\n    /*\n     * remove lazy components form list\n     * @param  {Vue} vm Vue instance\n     * @return\n     */\n    removeComponent(vm) {\n      if (!vm) return;\n      remove(this.listeners, vm);\n      this.observer && this.observer.unobserve(vm.el);\n      if (vm.$parent && vm.$el.parentNode) {\n        this.removeListenerTarget(vm.$el.parentNode);\n      }\n      this.removeListenerTarget(window);\n    }\n    setMode(mode) {\n      if (!hasIntersectionObserver && mode === modeType.observer) {\n        mode = modeType.event;\n      }\n      this.mode = mode;\n      if (mode === modeType.event) {\n        if (this.observer) {\n          this.listeners.forEach(listener => {\n            this.observer.unobserve(listener.el);\n          });\n          this.observer = null;\n        }\n        this.targets.forEach(target => {\n          this.initListen(target.el, true);\n        });\n      } else {\n        this.targets.forEach(target => {\n          this.initListen(target.el, false);\n        });\n        this.initIntersectionObserver();\n      }\n    }\n    /*\n     *** Private functions ***\n     */\n    /*\n     * add listener target\n     * @param  {DOM} el listener target\n     * @return\n     */\n    addListenerTarget(el) {\n      if (!el) return;\n      let target = this.targets.find(target2 => target2.el === el);\n      if (!target) {\n        target = {\n          el,\n          id: ++this.targetIndex,\n          childrenCount: 1,\n          listened: true\n        };\n        this.mode === modeType.event && this.initListen(target.el, true);\n        this.targets.push(target);\n      } else {\n        target.childrenCount++;\n      }\n      return this.targetIndex;\n    }\n    /*\n     * remove listener target or reduce target childrenCount\n     * @param  {DOM} el or window\n     * @return\n     */\n    removeListenerTarget(el) {\n      this.targets.forEach((target, index) => {\n        if (target.el === el) {\n          target.childrenCount--;\n          if (!target.childrenCount) {\n            this.initListen(target.el, false);\n            this.targets.splice(index, 1);\n            target = null;\n          }\n        }\n      });\n    }\n    /*\n     * add or remove eventlistener\n     * @param  {DOM} el DOM or Window\n     * @param  {boolean} start flag\n     * @return\n     */\n    initListen(el, start) {\n      this.options.ListenEvents.forEach(evt => (start ? on : off)(el, evt, this.lazyLoadHandler));\n    }\n    initEvent() {\n      this.Event = {\n        listeners: {\n          loading: [],\n          loaded: [],\n          error: []\n        }\n      };\n      this.$on = (event, func) => {\n        if (!this.Event.listeners[event]) this.Event.listeners[event] = [];\n        this.Event.listeners[event].push(func);\n      };\n      this.$once = (event, func) => {\n        const on2 = (...args) => {\n          this.$off(event, on2);\n          func.apply(this, args);\n        };\n        this.$on(event, on2);\n      };\n      this.$off = (event, func) => {\n        if (!func) {\n          if (!this.Event.listeners[event]) return;\n          this.Event.listeners[event].length = 0;\n          return;\n        }\n        remove(this.Event.listeners[event], func);\n      };\n      this.$emit = (event, context, inCache) => {\n        if (!this.Event.listeners[event]) return;\n        this.Event.listeners[event].forEach(func => func(context, inCache));\n      };\n    }\n    /**\n     * find nodes which in viewport and trigger load\n     * @return\n     */\n    lazyLoadHandler() {\n      const freeList = [];\n      this.listeners.forEach(listener => {\n        if (!listener.el || !listener.el.parentNode) {\n          freeList.push(listener);\n        }\n        const catIn = listener.checkInView();\n        if (!catIn) return;\n        listener.load();\n      });\n      freeList.forEach(item => {\n        remove(this.listeners, item);\n        item.$destroy();\n      });\n    }\n    /**\n     * init IntersectionObserver\n     * set mode to observer\n     * @return\n     */\n    initIntersectionObserver() {\n      if (!hasIntersectionObserver) {\n        return;\n      }\n      this.observer = new IntersectionObserver(this.observerHandler.bind(this), this.options.observerOptions);\n      if (this.listeners.length) {\n        this.listeners.forEach(listener => {\n          this.observer.observe(listener.el);\n        });\n      }\n    }\n    /**\n     * init IntersectionObserver\n     * @return\n     */\n    observerHandler(entries) {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          this.listeners.forEach(listener => {\n            if (listener.el === entry.target) {\n              if (listener.state.loaded) return this.observer.unobserve(listener.el);\n              listener.load();\n            }\n          });\n        }\n      });\n    }\n    /**\n     * set element attribute with image'url and state\n     * @param  {object} lazyload listener object\n     * @param  {string} state will be rendered\n     * @param  {bool} inCache  is rendered from cache\n     * @return\n     */\n    elRenderer(listener, state, cache) {\n      if (!listener.el) return;\n      const {\n        el,\n        bindType\n      } = listener;\n      let src;\n      switch (state) {\n        case \"loading\":\n          src = listener.loading;\n          break;\n        case \"error\":\n          src = listener.error;\n          break;\n        default:\n          ({\n            src\n          } = listener);\n          break;\n      }\n      if (bindType) {\n        el.style[bindType] = 'url(\"' + src + '\")';\n      } else if (el.getAttribute(\"src\") !== src) {\n        el.setAttribute(\"src\", src);\n      }\n      el.setAttribute(\"lazy\", state);\n      this.$emit(state, listener, cache);\n      this.options.adapter[state] && this.options.adapter[state](listener, this.options);\n      if (this.options.dispatchEvent) {\n        const event = new CustomEvent(state, {\n          detail: listener\n        });\n        el.dispatchEvent(event);\n      }\n    }\n    /**\n     * generate loading loaded error image url\n     * @param {string} image's src\n     * @return {object} image's loading, loaded, error url\n     */\n    valueFormatter(value) {\n      let src = value;\n      let {\n        loading,\n        error\n      } = this.options;\n      if (isObject(value)) {\n        if (process.env.NODE_ENV !== \"production\" && !value.src && !this.options.silent) {\n          console.error(\"[@vant/lazyload] miss src with \" + value);\n        }\n        ({\n          src\n        } = value);\n        loading = value.loading || this.options.loading;\n        error = value.error || this.options.error;\n      }\n      return {\n        src,\n        loading,\n        error\n      };\n    }\n  };\n}\nexport { stdin_default as default };", "map": {"version": 3, "names": ["nextTick", "inBrowser", "getScrollParent", "remove", "on", "off", "throttle", "supportWebp", "getDPR", "getBestSelectionFromSrcset", "hasIntersectionObserver", "modeType", "ImageCache", "isObject", "ReactiveListener", "DEFAULT_URL", "DEFAULT_EVENTS", "DEFAULT_OBSERVER_OPTIONS", "rootMargin", "threshold", "stdin_default", "Lazy", "constructor", "preLoad", "error", "throttleWait", "preLoadTop", "dispatchEvent", "loading", "attempt", "silent", "scale", "listenEvents", "filter", "adapter", "observer", "observerOptions", "mode", "event", "listeners", "targetIndex", "targets", "options", "ListenEvents", "initEvent", "imageCache", "max", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bind", "setMode", "config", "Object", "assign", "performance", "map", "item", "addLazyBox", "vm", "push", "addListenerTarget", "window", "observe", "el", "$el", "parentNode", "add", "binding", "vnode", "some", "update", "value", "valueFormatter", "src", "container", "keys", "modifiers", "$parent", "context", "$refs", "document", "getElementById", "newListener", "bindType", "arg", "cors", "<PERSON><PERSON><PERSON><PERSON>", "exist", "find", "unobserve", "existItem", "removeListenerTarget", "$destroy", "removeComponent", "for<PERSON>ach", "listener", "target", "initListen", "initIntersectionObserver", "target2", "id", "childrenCount", "listened", "index", "splice", "start", "evt", "Event", "loaded", "$on", "func", "$once", "on2", "args", "$off", "apply", "length", "$emit", "inCache", "freeList", "catIn", "checkInView", "load", "IntersectionObserver", "observer<PERSON><PERSON><PERSON>", "entries", "entry", "isIntersecting", "state", "cache", "style", "getAttribute", "setAttribute", "CustomEvent", "detail", "process", "env", "NODE_ENV", "console", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/lazyload/vue-lazyload/lazy.mjs"], "sourcesContent": ["import { nextTick } from \"vue\";\nimport { inBrowser, getScrollParent } from \"@vant/use\";\nimport {\n  remove,\n  on,\n  off,\n  throttle,\n  supportWebp,\n  getDPR,\n  getBestSelectionFromSrcset,\n  hasIntersectionObserver,\n  modeType,\n  ImageCache\n} from \"./util.mjs\";\nimport { isObject } from \"../../utils/index.mjs\";\nimport ReactiveListener from \"./listener.mjs\";\nconst DEFAULT_URL = \"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7\";\nconst DEFAULT_EVENTS = [\n  \"scroll\",\n  \"wheel\",\n  \"mousewheel\",\n  \"resize\",\n  \"animationend\",\n  \"transitionend\",\n  \"touchmove\"\n];\nconst DEFAULT_OBSERVER_OPTIONS = {\n  rootMargin: \"0px\",\n  threshold: 0\n};\nfunction stdin_default() {\n  return class Lazy {\n    constructor({\n      preLoad,\n      error,\n      throttleWait,\n      preLoadTop,\n      dispatchEvent,\n      loading,\n      attempt,\n      silent = true,\n      scale,\n      listenEvents,\n      filter,\n      adapter,\n      observer,\n      observerOptions\n    }) {\n      this.mode = modeType.event;\n      this.listeners = [];\n      this.targetIndex = 0;\n      this.targets = [];\n      this.options = {\n        silent,\n        dispatchEvent: !!dispatchEvent,\n        throttleWait: throttleWait || 200,\n        preLoad: preLoad || 1.3,\n        preLoadTop: preLoadTop || 0,\n        error: error || DEFAULT_URL,\n        loading: loading || DEFAULT_URL,\n        attempt: attempt || 3,\n        scale: scale || getDPR(scale),\n        ListenEvents: listenEvents || DEFAULT_EVENTS,\n        supportWebp: supportWebp(),\n        filter: filter || {},\n        adapter: adapter || {},\n        observer: !!observer,\n        observerOptions: observerOptions || DEFAULT_OBSERVER_OPTIONS\n      };\n      this.initEvent();\n      this.imageCache = new ImageCache({ max: 200 });\n      this.lazyLoadHandler = throttle(\n        this.lazyLoadHandler.bind(this),\n        this.options.throttleWait\n      );\n      this.setMode(this.options.observer ? modeType.observer : modeType.event);\n    }\n    /**\n     * update config\n     * @param  {Object} config params\n     * @return\n     */\n    config(options = {}) {\n      Object.assign(this.options, options);\n    }\n    /**\n     * output listener's load performance\n     * @return {Array}\n     */\n    performance() {\n      return this.listeners.map((item) => item.performance());\n    }\n    /*\n     * add lazy component to queue\n     * @param  {Vue} vm lazy component instance\n     * @return\n     */\n    addLazyBox(vm) {\n      this.listeners.push(vm);\n      if (inBrowser) {\n        this.addListenerTarget(window);\n        this.observer && this.observer.observe(vm.el);\n        if (vm.$el && vm.$el.parentNode) {\n          this.addListenerTarget(vm.$el.parentNode);\n        }\n      }\n    }\n    /*\n     * add image listener to queue\n     * @param  {DOM} el\n     * @param  {object} binding vue directive binding\n     * @param  {vnode} vnode vue directive vnode\n     * @return\n     */\n    add(el, binding, vnode) {\n      if (this.listeners.some((item) => item.el === el)) {\n        this.update(el, binding);\n        return nextTick(this.lazyLoadHandler);\n      }\n      const value = this.valueFormatter(binding.value);\n      let { src } = value;\n      nextTick(() => {\n        src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n        this.observer && this.observer.observe(el);\n        const container = Object.keys(binding.modifiers)[0];\n        let $parent;\n        if (container) {\n          $parent = vnode.context.$refs[container];\n          $parent = $parent ? $parent.$el || $parent : document.getElementById(container);\n        }\n        if (!$parent) {\n          $parent = getScrollParent(el);\n        }\n        const newListener = new ReactiveListener({\n          bindType: binding.arg,\n          $parent,\n          el,\n          src,\n          loading: value.loading,\n          error: value.error,\n          cors: value.cors,\n          elRenderer: this.elRenderer.bind(this),\n          options: this.options,\n          imageCache: this.imageCache\n        });\n        this.listeners.push(newListener);\n        if (inBrowser) {\n          this.addListenerTarget(window);\n          this.addListenerTarget($parent);\n        }\n        this.lazyLoadHandler();\n        nextTick(() => this.lazyLoadHandler());\n      });\n    }\n    /**\n     * update image src\n     * @param  {DOM} el\n     * @param  {object} vue directive binding\n     * @return\n     */\n    update(el, binding, vnode) {\n      const value = this.valueFormatter(binding.value);\n      let { src } = value;\n      src = getBestSelectionFromSrcset(el, this.options.scale) || src;\n      const exist = this.listeners.find((item) => item.el === el);\n      if (!exist) {\n        this.add(el, binding, vnode);\n      } else {\n        exist.update({\n          src,\n          error: value.error,\n          loading: value.loading\n        });\n      }\n      if (this.observer) {\n        this.observer.unobserve(el);\n        this.observer.observe(el);\n      }\n      this.lazyLoadHandler();\n      nextTick(() => this.lazyLoadHandler());\n    }\n    /**\n     * remove listener form list\n     * @param  {DOM} el\n     * @return\n     */\n    remove(el) {\n      if (!el) return;\n      this.observer && this.observer.unobserve(el);\n      const existItem = this.listeners.find((item) => item.el === el);\n      if (existItem) {\n        this.removeListenerTarget(existItem.$parent);\n        this.removeListenerTarget(window);\n        remove(this.listeners, existItem);\n        existItem.$destroy();\n      }\n    }\n    /*\n     * remove lazy components form list\n     * @param  {Vue} vm Vue instance\n     * @return\n     */\n    removeComponent(vm) {\n      if (!vm) return;\n      remove(this.listeners, vm);\n      this.observer && this.observer.unobserve(vm.el);\n      if (vm.$parent && vm.$el.parentNode) {\n        this.removeListenerTarget(vm.$el.parentNode);\n      }\n      this.removeListenerTarget(window);\n    }\n    setMode(mode) {\n      if (!hasIntersectionObserver && mode === modeType.observer) {\n        mode = modeType.event;\n      }\n      this.mode = mode;\n      if (mode === modeType.event) {\n        if (this.observer) {\n          this.listeners.forEach((listener) => {\n            this.observer.unobserve(listener.el);\n          });\n          this.observer = null;\n        }\n        this.targets.forEach((target) => {\n          this.initListen(target.el, true);\n        });\n      } else {\n        this.targets.forEach((target) => {\n          this.initListen(target.el, false);\n        });\n        this.initIntersectionObserver();\n      }\n    }\n    /*\n     *** Private functions ***\n     */\n    /*\n     * add listener target\n     * @param  {DOM} el listener target\n     * @return\n     */\n    addListenerTarget(el) {\n      if (!el) return;\n      let target = this.targets.find((target2) => target2.el === el);\n      if (!target) {\n        target = {\n          el,\n          id: ++this.targetIndex,\n          childrenCount: 1,\n          listened: true\n        };\n        this.mode === modeType.event && this.initListen(target.el, true);\n        this.targets.push(target);\n      } else {\n        target.childrenCount++;\n      }\n      return this.targetIndex;\n    }\n    /*\n     * remove listener target or reduce target childrenCount\n     * @param  {DOM} el or window\n     * @return\n     */\n    removeListenerTarget(el) {\n      this.targets.forEach((target, index) => {\n        if (target.el === el) {\n          target.childrenCount--;\n          if (!target.childrenCount) {\n            this.initListen(target.el, false);\n            this.targets.splice(index, 1);\n            target = null;\n          }\n        }\n      });\n    }\n    /*\n     * add or remove eventlistener\n     * @param  {DOM} el DOM or Window\n     * @param  {boolean} start flag\n     * @return\n     */\n    initListen(el, start) {\n      this.options.ListenEvents.forEach(\n        (evt) => (start ? on : off)(el, evt, this.lazyLoadHandler)\n      );\n    }\n    initEvent() {\n      this.Event = {\n        listeners: {\n          loading: [],\n          loaded: [],\n          error: []\n        }\n      };\n      this.$on = (event, func) => {\n        if (!this.Event.listeners[event]) this.Event.listeners[event] = [];\n        this.Event.listeners[event].push(func);\n      };\n      this.$once = (event, func) => {\n        const on2 = (...args) => {\n          this.$off(event, on2);\n          func.apply(this, args);\n        };\n        this.$on(event, on2);\n      };\n      this.$off = (event, func) => {\n        if (!func) {\n          if (!this.Event.listeners[event]) return;\n          this.Event.listeners[event].length = 0;\n          return;\n        }\n        remove(this.Event.listeners[event], func);\n      };\n      this.$emit = (event, context, inCache) => {\n        if (!this.Event.listeners[event]) return;\n        this.Event.listeners[event].forEach((func) => func(context, inCache));\n      };\n    }\n    /**\n     * find nodes which in viewport and trigger load\n     * @return\n     */\n    lazyLoadHandler() {\n      const freeList = [];\n      this.listeners.forEach((listener) => {\n        if (!listener.el || !listener.el.parentNode) {\n          freeList.push(listener);\n        }\n        const catIn = listener.checkInView();\n        if (!catIn) return;\n        listener.load();\n      });\n      freeList.forEach((item) => {\n        remove(this.listeners, item);\n        item.$destroy();\n      });\n    }\n    /**\n     * init IntersectionObserver\n     * set mode to observer\n     * @return\n     */\n    initIntersectionObserver() {\n      if (!hasIntersectionObserver) {\n        return;\n      }\n      this.observer = new IntersectionObserver(\n        this.observerHandler.bind(this),\n        this.options.observerOptions\n      );\n      if (this.listeners.length) {\n        this.listeners.forEach((listener) => {\n          this.observer.observe(listener.el);\n        });\n      }\n    }\n    /**\n     * init IntersectionObserver\n     * @return\n     */\n    observerHandler(entries) {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting) {\n          this.listeners.forEach((listener) => {\n            if (listener.el === entry.target) {\n              if (listener.state.loaded)\n                return this.observer.unobserve(listener.el);\n              listener.load();\n            }\n          });\n        }\n      });\n    }\n    /**\n     * set element attribute with image'url and state\n     * @param  {object} lazyload listener object\n     * @param  {string} state will be rendered\n     * @param  {bool} inCache  is rendered from cache\n     * @return\n     */\n    elRenderer(listener, state, cache) {\n      if (!listener.el) return;\n      const { el, bindType } = listener;\n      let src;\n      switch (state) {\n        case \"loading\":\n          src = listener.loading;\n          break;\n        case \"error\":\n          src = listener.error;\n          break;\n        default:\n          ({ src } = listener);\n          break;\n      }\n      if (bindType) {\n        el.style[bindType] = 'url(\"' + src + '\")';\n      } else if (el.getAttribute(\"src\") !== src) {\n        el.setAttribute(\"src\", src);\n      }\n      el.setAttribute(\"lazy\", state);\n      this.$emit(state, listener, cache);\n      this.options.adapter[state] && this.options.adapter[state](listener, this.options);\n      if (this.options.dispatchEvent) {\n        const event = new CustomEvent(state, {\n          detail: listener\n        });\n        el.dispatchEvent(event);\n      }\n    }\n    /**\n     * generate loading loaded error image url\n     * @param {string} image's src\n     * @return {object} image's loading, loaded, error url\n     */\n    valueFormatter(value) {\n      let src = value;\n      let { loading, error } = this.options;\n      if (isObject(value)) {\n        if (process.env.NODE_ENV !== \"production\" && !value.src && !this.options.silent) {\n          console.error(\"[@vant/lazyload] miss src with \" + value);\n        }\n        ({ src } = value);\n        loading = value.loading || this.options.loading;\n        error = value.error || this.options.error;\n      }\n      return {\n        src,\n        loading,\n        error\n      };\n    }\n  };\n}\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;AAAA,SAASA,QAAQ,QAAQ,KAAK;AAC9B,SAASC,SAAS,EAAEC,eAAe,QAAQ,WAAW;AACtD,SACEC,MAAM,EACNC,EAAE,EACFC,GAAG,EACHC,QAAQ,EACRC,WAAW,EACXC,MAAM,EACNC,0BAA0B,EAC1BC,uBAAuB,EACvBC,QAAQ,EACRC,UAAU,QACL,YAAY;AACnB,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAOC,gBAAgB,MAAM,gBAAgB;AAC7C,MAAMC,WAAW,GAAG,gFAAgF;AACpG,MAAMC,cAAc,GAAG,CACrB,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,eAAe,EACf,WAAW,CACZ;AACD,MAAMC,wBAAwB,GAAG;EAC/BC,UAAU,EAAE,KAAK;EACjBC,SAAS,EAAE;AACb,CAAC;AACD,SAASC,aAAaA,CAAA,EAAG;EACvB,OAAO,MAAMC,IAAI,CAAC;IAChBC,WAAWA,CAAC;MACVC,OAAO;MACPC,KAAK;MACLC,YAAY;MACZC,UAAU;MACVC,aAAa;MACbC,OAAO;MACPC,OAAO;MACPC,MAAM,GAAG,IAAI;MACbC,KAAK;MACLC,YAAY;MACZC,MAAM;MACNC,OAAO;MACPC,QAAQ;MACRC;IACF,CAAC,EAAE;MACD,IAAI,CAACC,IAAI,GAAG1B,QAAQ,CAAC2B,KAAK;MAC1B,IAAI,CAACC,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,OAAO,GAAG,EAAE;MACjB,IAAI,CAACC,OAAO,GAAG;QACbZ,MAAM;QACNH,aAAa,EAAE,CAAC,CAACA,aAAa;QAC9BF,YAAY,EAAEA,YAAY,IAAI,GAAG;QACjCF,OAAO,EAAEA,OAAO,IAAI,GAAG;QACvBG,UAAU,EAAEA,UAAU,IAAI,CAAC;QAC3BF,KAAK,EAAEA,KAAK,IAAIT,WAAW;QAC3Ba,OAAO,EAAEA,OAAO,IAAIb,WAAW;QAC/Bc,OAAO,EAAEA,OAAO,IAAI,CAAC;QACrBE,KAAK,EAAEA,KAAK,IAAIvB,MAAM,CAACuB,KAAK,CAAC;QAC7BY,YAAY,EAAEX,YAAY,IAAIhB,cAAc;QAC5CT,WAAW,EAAEA,WAAW,CAAC,CAAC;QAC1B0B,MAAM,EAAEA,MAAM,IAAI,CAAC,CAAC;QACpBC,OAAO,EAAEA,OAAO,IAAI,CAAC,CAAC;QACtBC,QAAQ,EAAE,CAAC,CAACA,QAAQ;QACpBC,eAAe,EAAEA,eAAe,IAAInB;MACtC,CAAC;MACD,IAAI,CAAC2B,SAAS,CAAC,CAAC;MAChB,IAAI,CAACC,UAAU,GAAG,IAAIjC,UAAU,CAAC;QAAEkC,GAAG,EAAE;MAAI,CAAC,CAAC;MAC9C,IAAI,CAACC,eAAe,GAAGzC,QAAQ,CAC7B,IAAI,CAACyC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,EAC/B,IAAI,CAACN,OAAO,CAACjB,YACf,CAAC;MACD,IAAI,CAACwB,OAAO,CAAC,IAAI,CAACP,OAAO,CAACP,QAAQ,GAAGxB,QAAQ,CAACwB,QAAQ,GAAGxB,QAAQ,CAAC2B,KAAK,CAAC;IAC1E;IACA;AACJ;AACA;AACA;AACA;IACIY,MAAMA,CAACR,OAAO,GAAG,CAAC,CAAC,EAAE;MACnBS,MAAM,CAACC,MAAM,CAAC,IAAI,CAACV,OAAO,EAAEA,OAAO,CAAC;IACtC;IACA;AACJ;AACA;AACA;IACIW,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACd,SAAS,CAACe,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACF,WAAW,CAAC,CAAC,CAAC;IACzD;IACA;AACJ;AACA;AACA;AACA;IACIG,UAAUA,CAACC,EAAE,EAAE;MACb,IAAI,CAAClB,SAAS,CAACmB,IAAI,CAACD,EAAE,CAAC;MACvB,IAAIxD,SAAS,EAAE;QACb,IAAI,CAAC0D,iBAAiB,CAACC,MAAM,CAAC;QAC9B,IAAI,CAACzB,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC0B,OAAO,CAACJ,EAAE,CAACK,EAAE,CAAC;QAC7C,IAAIL,EAAE,CAACM,GAAG,IAAIN,EAAE,CAACM,GAAG,CAACC,UAAU,EAAE;UAC/B,IAAI,CAACL,iBAAiB,CAACF,EAAE,CAACM,GAAG,CAACC,UAAU,CAAC;QAC3C;MACF;IACF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACIC,GAAGA,CAACH,EAAE,EAAEI,OAAO,EAAEC,KAAK,EAAE;MACtB,IAAI,IAAI,CAAC5B,SAAS,CAAC6B,IAAI,CAAEb,IAAI,IAAKA,IAAI,CAACO,EAAE,KAAKA,EAAE,CAAC,EAAE;QACjD,IAAI,CAACO,MAAM,CAACP,EAAE,EAAEI,OAAO,CAAC;QACxB,OAAOlE,QAAQ,CAAC,IAAI,CAAC+C,eAAe,CAAC;MACvC;MACA,MAAMuB,KAAK,GAAG,IAAI,CAACC,cAAc,CAACL,OAAO,CAACI,KAAK,CAAC;MAChD,IAAI;QAAEE;MAAI,CAAC,GAAGF,KAAK;MACnBtE,QAAQ,CAAC,MAAM;QACbwE,GAAG,GAAG/D,0BAA0B,CAACqD,EAAE,EAAE,IAAI,CAACpB,OAAO,CAACX,KAAK,CAAC,IAAIyC,GAAG;QAC/D,IAAI,CAACrC,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC0B,OAAO,CAACC,EAAE,CAAC;QAC1C,MAAMW,SAAS,GAAGtB,MAAM,CAACuB,IAAI,CAACR,OAAO,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC;QACnD,IAAIC,OAAO;QACX,IAAIH,SAAS,EAAE;UACbG,OAAO,GAAGT,KAAK,CAACU,OAAO,CAACC,KAAK,CAACL,SAAS,CAAC;UACxCG,OAAO,GAAGA,OAAO,GAAGA,OAAO,CAACb,GAAG,IAAIa,OAAO,GAAGG,QAAQ,CAACC,cAAc,CAACP,SAAS,CAAC;QACjF;QACA,IAAI,CAACG,OAAO,EAAE;UACZA,OAAO,GAAG1E,eAAe,CAAC4D,EAAE,CAAC;QAC/B;QACA,MAAMmB,WAAW,GAAG,IAAInE,gBAAgB,CAAC;UACvCoE,QAAQ,EAAEhB,OAAO,CAACiB,GAAG;UACrBP,OAAO;UACPd,EAAE;UACFU,GAAG;UACH5C,OAAO,EAAE0C,KAAK,CAAC1C,OAAO;UACtBJ,KAAK,EAAE8C,KAAK,CAAC9C,KAAK;UAClB4D,IAAI,EAAEd,KAAK,CAACc,IAAI;UAChBC,UAAU,EAAE,IAAI,CAACA,UAAU,CAACrC,IAAI,CAAC,IAAI,CAAC;UACtCN,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBG,UAAU,EAAE,IAAI,CAACA;QACnB,CAAC,CAAC;QACF,IAAI,CAACN,SAAS,CAACmB,IAAI,CAACuB,WAAW,CAAC;QAChC,IAAIhF,SAAS,EAAE;UACb,IAAI,CAAC0D,iBAAiB,CAACC,MAAM,CAAC;UAC9B,IAAI,CAACD,iBAAiB,CAACiB,OAAO,CAAC;QACjC;QACA,IAAI,CAAC7B,eAAe,CAAC,CAAC;QACtB/C,QAAQ,CAAC,MAAM,IAAI,CAAC+C,eAAe,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACIsB,MAAMA,CAACP,EAAE,EAAEI,OAAO,EAAEC,KAAK,EAAE;MACzB,MAAMG,KAAK,GAAG,IAAI,CAACC,cAAc,CAACL,OAAO,CAACI,KAAK,CAAC;MAChD,IAAI;QAAEE;MAAI,CAAC,GAAGF,KAAK;MACnBE,GAAG,GAAG/D,0BAA0B,CAACqD,EAAE,EAAE,IAAI,CAACpB,OAAO,CAACX,KAAK,CAAC,IAAIyC,GAAG;MAC/D,MAAMc,KAAK,GAAG,IAAI,CAAC/C,SAAS,CAACgD,IAAI,CAAEhC,IAAI,IAAKA,IAAI,CAACO,EAAE,KAAKA,EAAE,CAAC;MAC3D,IAAI,CAACwB,KAAK,EAAE;QACV,IAAI,CAACrB,GAAG,CAACH,EAAE,EAAEI,OAAO,EAAEC,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLmB,KAAK,CAACjB,MAAM,CAAC;UACXG,GAAG;UACHhD,KAAK,EAAE8C,KAAK,CAAC9C,KAAK;UAClBI,OAAO,EAAE0C,KAAK,CAAC1C;QACjB,CAAC,CAAC;MACJ;MACA,IAAI,IAAI,CAACO,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACqD,SAAS,CAAC1B,EAAE,CAAC;QAC3B,IAAI,CAAC3B,QAAQ,CAAC0B,OAAO,CAACC,EAAE,CAAC;MAC3B;MACA,IAAI,CAACf,eAAe,CAAC,CAAC;MACtB/C,QAAQ,CAAC,MAAM,IAAI,CAAC+C,eAAe,CAAC,CAAC,CAAC;IACxC;IACA;AACJ;AACA;AACA;AACA;IACI5C,MAAMA,CAAC2D,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,EAAE;MACT,IAAI,CAAC3B,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACqD,SAAS,CAAC1B,EAAE,CAAC;MAC5C,MAAM2B,SAAS,GAAG,IAAI,CAAClD,SAAS,CAACgD,IAAI,CAAEhC,IAAI,IAAKA,IAAI,CAACO,EAAE,KAAKA,EAAE,CAAC;MAC/D,IAAI2B,SAAS,EAAE;QACb,IAAI,CAACC,oBAAoB,CAACD,SAAS,CAACb,OAAO,CAAC;QAC5C,IAAI,CAACc,oBAAoB,CAAC9B,MAAM,CAAC;QACjCzD,MAAM,CAAC,IAAI,CAACoC,SAAS,EAAEkD,SAAS,CAAC;QACjCA,SAAS,CAACE,QAAQ,CAAC,CAAC;MACtB;IACF;IACA;AACJ;AACA;AACA;AACA;IACIC,eAAeA,CAACnC,EAAE,EAAE;MAClB,IAAI,CAACA,EAAE,EAAE;MACTtD,MAAM,CAAC,IAAI,CAACoC,SAAS,EAAEkB,EAAE,CAAC;MAC1B,IAAI,CAACtB,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACqD,SAAS,CAAC/B,EAAE,CAACK,EAAE,CAAC;MAC/C,IAAIL,EAAE,CAACmB,OAAO,IAAInB,EAAE,CAACM,GAAG,CAACC,UAAU,EAAE;QACnC,IAAI,CAAC0B,oBAAoB,CAACjC,EAAE,CAACM,GAAG,CAACC,UAAU,CAAC;MAC9C;MACA,IAAI,CAAC0B,oBAAoB,CAAC9B,MAAM,CAAC;IACnC;IACAX,OAAOA,CAACZ,IAAI,EAAE;MACZ,IAAI,CAAC3B,uBAAuB,IAAI2B,IAAI,KAAK1B,QAAQ,CAACwB,QAAQ,EAAE;QAC1DE,IAAI,GAAG1B,QAAQ,CAAC2B,KAAK;MACvB;MACA,IAAI,CAACD,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,KAAK1B,QAAQ,CAAC2B,KAAK,EAAE;QAC3B,IAAI,IAAI,CAACH,QAAQ,EAAE;UACjB,IAAI,CAACI,SAAS,CAACsD,OAAO,CAAEC,QAAQ,IAAK;YACnC,IAAI,CAAC3D,QAAQ,CAACqD,SAAS,CAACM,QAAQ,CAAChC,EAAE,CAAC;UACtC,CAAC,CAAC;UACF,IAAI,CAAC3B,QAAQ,GAAG,IAAI;QACtB;QACA,IAAI,CAACM,OAAO,CAACoD,OAAO,CAAEE,MAAM,IAAK;UAC/B,IAAI,CAACC,UAAU,CAACD,MAAM,CAACjC,EAAE,EAAE,IAAI,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACrB,OAAO,CAACoD,OAAO,CAAEE,MAAM,IAAK;UAC/B,IAAI,CAACC,UAAU,CAACD,MAAM,CAACjC,EAAE,EAAE,KAAK,CAAC;QACnC,CAAC,CAAC;QACF,IAAI,CAACmC,wBAAwB,CAAC,CAAC;MACjC;IACF;IACA;AACJ;AACA;IACI;AACJ;AACA;AACA;AACA;IACItC,iBAAiBA,CAACG,EAAE,EAAE;MACpB,IAAI,CAACA,EAAE,EAAE;MACT,IAAIiC,MAAM,GAAG,IAAI,CAACtD,OAAO,CAAC8C,IAAI,CAAEW,OAAO,IAAKA,OAAO,CAACpC,EAAE,KAAKA,EAAE,CAAC;MAC9D,IAAI,CAACiC,MAAM,EAAE;QACXA,MAAM,GAAG;UACPjC,EAAE;UACFqC,EAAE,EAAE,EAAE,IAAI,CAAC3D,WAAW;UACtB4D,aAAa,EAAE,CAAC;UAChBC,QAAQ,EAAE;QACZ,CAAC;QACD,IAAI,CAAChE,IAAI,KAAK1B,QAAQ,CAAC2B,KAAK,IAAI,IAAI,CAAC0D,UAAU,CAACD,MAAM,CAACjC,EAAE,EAAE,IAAI,CAAC;QAChE,IAAI,CAACrB,OAAO,CAACiB,IAAI,CAACqC,MAAM,CAAC;MAC3B,CAAC,MAAM;QACLA,MAAM,CAACK,aAAa,EAAE;MACxB;MACA,OAAO,IAAI,CAAC5D,WAAW;IACzB;IACA;AACJ;AACA;AACA;AACA;IACIkD,oBAAoBA,CAAC5B,EAAE,EAAE;MACvB,IAAI,CAACrB,OAAO,CAACoD,OAAO,CAAC,CAACE,MAAM,EAAEO,KAAK,KAAK;QACtC,IAAIP,MAAM,CAACjC,EAAE,KAAKA,EAAE,EAAE;UACpBiC,MAAM,CAACK,aAAa,EAAE;UACtB,IAAI,CAACL,MAAM,CAACK,aAAa,EAAE;YACzB,IAAI,CAACJ,UAAU,CAACD,MAAM,CAACjC,EAAE,EAAE,KAAK,CAAC;YACjC,IAAI,CAACrB,OAAO,CAAC8D,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;YAC7BP,MAAM,GAAG,IAAI;UACf;QACF;MACF,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACIC,UAAUA,CAAClC,EAAE,EAAE0C,KAAK,EAAE;MACpB,IAAI,CAAC9D,OAAO,CAACC,YAAY,CAACkD,OAAO,CAC9BY,GAAG,IAAK,CAACD,KAAK,GAAGpG,EAAE,GAAGC,GAAG,EAAEyD,EAAE,EAAE2C,GAAG,EAAE,IAAI,CAAC1D,eAAe,CAC3D,CAAC;IACH;IACAH,SAASA,CAAA,EAAG;MACV,IAAI,CAAC8D,KAAK,GAAG;QACXnE,SAAS,EAAE;UACTX,OAAO,EAAE,EAAE;UACX+E,MAAM,EAAE,EAAE;UACVnF,KAAK,EAAE;QACT;MACF,CAAC;MACD,IAAI,CAACoF,GAAG,GAAG,CAACtE,KAAK,EAAEuE,IAAI,KAAK;QAC1B,IAAI,CAAC,IAAI,CAACH,KAAK,CAACnE,SAAS,CAACD,KAAK,CAAC,EAAE,IAAI,CAACoE,KAAK,CAACnE,SAAS,CAACD,KAAK,CAAC,GAAG,EAAE;QAClE,IAAI,CAACoE,KAAK,CAACnE,SAAS,CAACD,KAAK,CAAC,CAACoB,IAAI,CAACmD,IAAI,CAAC;MACxC,CAAC;MACD,IAAI,CAACC,KAAK,GAAG,CAACxE,KAAK,EAAEuE,IAAI,KAAK;QAC5B,MAAME,GAAG,GAAGA,CAAC,GAAGC,IAAI,KAAK;UACvB,IAAI,CAACC,IAAI,CAAC3E,KAAK,EAAEyE,GAAG,CAAC;UACrBF,IAAI,CAACK,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;QACxB,CAAC;QACD,IAAI,CAACJ,GAAG,CAACtE,KAAK,EAAEyE,GAAG,CAAC;MACtB,CAAC;MACD,IAAI,CAACE,IAAI,GAAG,CAAC3E,KAAK,EAAEuE,IAAI,KAAK;QAC3B,IAAI,CAACA,IAAI,EAAE;UACT,IAAI,CAAC,IAAI,CAACH,KAAK,CAACnE,SAAS,CAACD,KAAK,CAAC,EAAE;UAClC,IAAI,CAACoE,KAAK,CAACnE,SAAS,CAACD,KAAK,CAAC,CAAC6E,MAAM,GAAG,CAAC;UACtC;QACF;QACAhH,MAAM,CAAC,IAAI,CAACuG,KAAK,CAACnE,SAAS,CAACD,KAAK,CAAC,EAAEuE,IAAI,CAAC;MAC3C,CAAC;MACD,IAAI,CAACO,KAAK,GAAG,CAAC9E,KAAK,EAAEuC,OAAO,EAAEwC,OAAO,KAAK;QACxC,IAAI,CAAC,IAAI,CAACX,KAAK,CAACnE,SAAS,CAACD,KAAK,CAAC,EAAE;QAClC,IAAI,CAACoE,KAAK,CAACnE,SAAS,CAACD,KAAK,CAAC,CAACuD,OAAO,CAAEgB,IAAI,IAAKA,IAAI,CAAChC,OAAO,EAAEwC,OAAO,CAAC,CAAC;MACvE,CAAC;IACH;IACA;AACJ;AACA;AACA;IACItE,eAAeA,CAAA,EAAG;MAChB,MAAMuE,QAAQ,GAAG,EAAE;MACnB,IAAI,CAAC/E,SAAS,CAACsD,OAAO,CAAEC,QAAQ,IAAK;QACnC,IAAI,CAACA,QAAQ,CAAChC,EAAE,IAAI,CAACgC,QAAQ,CAAChC,EAAE,CAACE,UAAU,EAAE;UAC3CsD,QAAQ,CAAC5D,IAAI,CAACoC,QAAQ,CAAC;QACzB;QACA,MAAMyB,KAAK,GAAGzB,QAAQ,CAAC0B,WAAW,CAAC,CAAC;QACpC,IAAI,CAACD,KAAK,EAAE;QACZzB,QAAQ,CAAC2B,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;MACFH,QAAQ,CAACzB,OAAO,CAAEtC,IAAI,IAAK;QACzBpD,MAAM,CAAC,IAAI,CAACoC,SAAS,EAAEgB,IAAI,CAAC;QAC5BA,IAAI,CAACoC,QAAQ,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIM,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACvF,uBAAuB,EAAE;QAC5B;MACF;MACA,IAAI,CAACyB,QAAQ,GAAG,IAAIuF,oBAAoB,CACtC,IAAI,CAACC,eAAe,CAAC3E,IAAI,CAAC,IAAI,CAAC,EAC/B,IAAI,CAACN,OAAO,CAACN,eACf,CAAC;MACD,IAAI,IAAI,CAACG,SAAS,CAAC4E,MAAM,EAAE;QACzB,IAAI,CAAC5E,SAAS,CAACsD,OAAO,CAAEC,QAAQ,IAAK;UACnC,IAAI,CAAC3D,QAAQ,CAAC0B,OAAO,CAACiC,QAAQ,CAAChC,EAAE,CAAC;QACpC,CAAC,CAAC;MACJ;IACF;IACA;AACJ;AACA;AACA;IACI6D,eAAeA,CAACC,OAAO,EAAE;MACvBA,OAAO,CAAC/B,OAAO,CAAEgC,KAAK,IAAK;QACzB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxB,IAAI,CAACvF,SAAS,CAACsD,OAAO,CAAEC,QAAQ,IAAK;YACnC,IAAIA,QAAQ,CAAChC,EAAE,KAAK+D,KAAK,CAAC9B,MAAM,EAAE;cAChC,IAAID,QAAQ,CAACiC,KAAK,CAACpB,MAAM,EACvB,OAAO,IAAI,CAACxE,QAAQ,CAACqD,SAAS,CAACM,QAAQ,CAAChC,EAAE,CAAC;cAC7CgC,QAAQ,CAAC2B,IAAI,CAAC,CAAC;YACjB;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACIpC,UAAUA,CAACS,QAAQ,EAAEiC,KAAK,EAAEC,KAAK,EAAE;MACjC,IAAI,CAAClC,QAAQ,CAAChC,EAAE,EAAE;MAClB,MAAM;QAAEA,EAAE;QAAEoB;MAAS,CAAC,GAAGY,QAAQ;MACjC,IAAItB,GAAG;MACP,QAAQuD,KAAK;QACX,KAAK,SAAS;UACZvD,GAAG,GAAGsB,QAAQ,CAAClE,OAAO;UACtB;QACF,KAAK,OAAO;UACV4C,GAAG,GAAGsB,QAAQ,CAACtE,KAAK;UACpB;QACF;UACE,CAAC;YAAEgD;UAAI,CAAC,GAAGsB,QAAQ;UACnB;MACJ;MACA,IAAIZ,QAAQ,EAAE;QACZpB,EAAE,CAACmE,KAAK,CAAC/C,QAAQ,CAAC,GAAG,OAAO,GAAGV,GAAG,GAAG,IAAI;MAC3C,CAAC,MAAM,IAAIV,EAAE,CAACoE,YAAY,CAAC,KAAK,CAAC,KAAK1D,GAAG,EAAE;QACzCV,EAAE,CAACqE,YAAY,CAAC,KAAK,EAAE3D,GAAG,CAAC;MAC7B;MACAV,EAAE,CAACqE,YAAY,CAAC,MAAM,EAAEJ,KAAK,CAAC;MAC9B,IAAI,CAACX,KAAK,CAACW,KAAK,EAAEjC,QAAQ,EAAEkC,KAAK,CAAC;MAClC,IAAI,CAACtF,OAAO,CAACR,OAAO,CAAC6F,KAAK,CAAC,IAAI,IAAI,CAACrF,OAAO,CAACR,OAAO,CAAC6F,KAAK,CAAC,CAACjC,QAAQ,EAAE,IAAI,CAACpD,OAAO,CAAC;MAClF,IAAI,IAAI,CAACA,OAAO,CAACf,aAAa,EAAE;QAC9B,MAAMW,KAAK,GAAG,IAAI8F,WAAW,CAACL,KAAK,EAAE;UACnCM,MAAM,EAAEvC;QACV,CAAC,CAAC;QACFhC,EAAE,CAACnC,aAAa,CAACW,KAAK,CAAC;MACzB;IACF;IACA;AACJ;AACA;AACA;AACA;IACIiC,cAAcA,CAACD,KAAK,EAAE;MACpB,IAAIE,GAAG,GAAGF,KAAK;MACf,IAAI;QAAE1C,OAAO;QAAEJ;MAAM,CAAC,GAAG,IAAI,CAACkB,OAAO;MACrC,IAAI7B,QAAQ,CAACyD,KAAK,CAAC,EAAE;QACnB,IAAIgE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAClE,KAAK,CAACE,GAAG,IAAI,CAAC,IAAI,CAAC9B,OAAO,CAACZ,MAAM,EAAE;UAC/E2G,OAAO,CAACjH,KAAK,CAAC,iCAAiC,GAAG8C,KAAK,CAAC;QAC1D;QACA,CAAC;UAAEE;QAAI,CAAC,GAAGF,KAAK;QAChB1C,OAAO,GAAG0C,KAAK,CAAC1C,OAAO,IAAI,IAAI,CAACc,OAAO,CAACd,OAAO;QAC/CJ,KAAK,GAAG8C,KAAK,CAAC9C,KAAK,IAAI,IAAI,CAACkB,OAAO,CAAClB,KAAK;MAC3C;MACA,OAAO;QACLgD,GAAG;QACH5C,OAAO;QACPJ;MACF,CAAC;IACH;EACF,CAAC;AACH;AACA,SACEJ,aAAa,IAAIsH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}