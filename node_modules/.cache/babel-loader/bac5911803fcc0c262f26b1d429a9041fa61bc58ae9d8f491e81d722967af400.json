{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Divider from \"./Divider.mjs\";\nconst Divider = withInstall(_Divider);\nvar stdin_default = Divider;\nimport { dividerProps } from \"./Divider.mjs\";\nexport { Divider, stdin_default as default, dividerProps };", "map": {"version": 3, "names": ["withInstall", "_Divider", "Divider", "stdin_default", "dividerProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/divider/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Divider from \"./Divider.mjs\";\nconst Divider = withInstall(_Divider);\nvar stdin_default = Divider;\nimport { dividerProps } from \"./Divider.mjs\";\nexport {\n  Divider,\n  stdin_default as default,\n  dividerProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,MAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SAASE,YAAY,QAAQ,eAAe;AAC5C,SACEF,OAAO,EACPC,aAAa,IAAIE,OAAO,EACxBD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}