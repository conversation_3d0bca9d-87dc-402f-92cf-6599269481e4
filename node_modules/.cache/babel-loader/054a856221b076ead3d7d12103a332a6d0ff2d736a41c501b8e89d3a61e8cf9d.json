{"ast": null, "code": "import { inject, provide, computed } from \"vue\";\nconst TAB_STATUS_KEY = Symbol();\nconst ALL_TAB_STATUS_KEY = Symbol();\nconst useTabStatus = () => inject(TAB_STATUS_KEY, null);\nconst useAllTabStatus = () => inject(ALL_TAB_STATUS_KEY, null);\nconst useProvideTabStatus = status => {\n  const allTabStatus = useAllTabStatus();\n  provide(TAB_STATUS_KEY, status);\n  provide(ALL_TAB_STATUS_KEY, computed(() => {\n    return (allTabStatus == null || allTabStatus.value) && status.value;\n  }));\n};\nexport { ALL_TAB_STATUS_KEY, TAB_STATUS_KEY, useAllTabStatus, useProvideTabStatus, useTabStatus };", "map": {"version": 3, "names": ["inject", "provide", "computed", "TAB_STATUS_KEY", "Symbol", "ALL_TAB_STATUS_KEY", "useTabStatus", "useAllTabStatus", "useProvideTabStatus", "status", "allTabStatus", "value"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-tab-status.mjs"], "sourcesContent": ["import { inject, provide, computed } from \"vue\";\nconst TAB_STATUS_KEY = Symbol();\nconst ALL_TAB_STATUS_KEY = Symbol();\nconst useTabStatus = () => inject(TAB_STATUS_KEY, null);\nconst useAllTabStatus = () => inject(ALL_TAB_STATUS_KEY, null);\nconst useProvideTabStatus = (status) => {\n  const allTabStatus = useAllTabStatus();\n  provide(TAB_STATUS_KEY, status);\n  provide(\n    ALL_TAB_STATUS_KEY,\n    computed(() => {\n      return (allTabStatus == null || allTabStatus.value) && status.value;\n    })\n  );\n};\nexport {\n  ALL_TAB_STATUS_KEY,\n  TAB_STATUS_KEY,\n  useAllTabStatus,\n  useProvideTabStatus,\n  useTabStatus\n};\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,KAAK;AAC/C,MAAMC,cAAc,GAAGC,MAAM,CAAC,CAAC;AAC/B,MAAMC,kBAAkB,GAAGD,MAAM,CAAC,CAAC;AACnC,MAAME,YAAY,GAAGA,CAAA,KAAMN,MAAM,CAACG,cAAc,EAAE,IAAI,CAAC;AACvD,MAAMI,eAAe,GAAGA,CAAA,KAAMP,MAAM,CAACK,kBAAkB,EAAE,IAAI,CAAC;AAC9D,MAAMG,mBAAmB,GAAIC,MAAM,IAAK;EACtC,MAAMC,YAAY,GAAGH,eAAe,CAAC,CAAC;EACtCN,OAAO,CAACE,cAAc,EAAEM,MAAM,CAAC;EAC/BR,OAAO,CACLI,kBAAkB,EAClBH,QAAQ,CAAC,MAAM;IACb,OAAO,CAACQ,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACC,KAAK,KAAKF,MAAM,CAACE,KAAK;EACrE,CAAC,CACH,CAAC;AACH,CAAC;AACD,SACEN,kBAAkB,EAClBF,cAAc,EACdI,eAAe,EACfC,mBAAmB,EACnBF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}