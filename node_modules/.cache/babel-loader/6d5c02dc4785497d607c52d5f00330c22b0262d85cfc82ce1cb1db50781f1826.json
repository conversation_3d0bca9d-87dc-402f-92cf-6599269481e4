{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _TreeSelect from \"./TreeSelect.mjs\";\nconst TreeSelect = withInstall(_TreeSelect);\nvar stdin_default = TreeSelect;\nimport { treeSelectProps } from \"./TreeSelect.mjs\";\nexport { TreeSelect, stdin_default as default, treeSelectProps };", "map": {"version": 3, "names": ["withInstall", "_TreeSelect", "TreeSelect", "stdin_default", "treeSelectProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tree-select/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _TreeSelect from \"./TreeSelect.mjs\";\nconst TreeSelect = withInstall(_TreeSelect);\nvar stdin_default = TreeSelect;\nimport { treeSelectProps } from \"./TreeSelect.mjs\";\nexport {\n  TreeSelect,\n  stdin_default as default,\n  treeSelectProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVC,aAAa,IAAIE,OAAO,EACxBD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}