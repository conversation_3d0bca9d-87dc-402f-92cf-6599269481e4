{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Field from \"./Field.mjs\";\nconst Field = withInstall(_Field);\nvar stdin_default = Field;\nimport { fieldProps } from \"./Field.mjs\";\nexport { Field, stdin_default as default, fieldProps };", "map": {"version": 3, "names": ["withInstall", "_Field", "Field", "stdin_default", "fieldProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/field/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Field from \"./Field.mjs\";\nconst Field = withInstall(_Field);\nvar stdin_default = Field;\nimport { fieldProps } from \"./Field.mjs\";\nexport {\n  Field,\n  stdin_default as default,\n  fieldProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SAASE,UAAU,QAAQ,aAAa;AACxC,SACEF,KAAK,EACLC,aAAa,IAAIE,OAAO,EACxBD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}