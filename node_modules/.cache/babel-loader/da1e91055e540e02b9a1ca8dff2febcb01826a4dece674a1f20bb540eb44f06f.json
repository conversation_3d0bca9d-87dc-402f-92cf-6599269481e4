{"ast": null, "code": "import { Transition, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace, HAPTICS_FEEDBACK } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"tag\");\nconst tagProps = {\n  size: String,\n  mark: Boolean,\n  show: truthProp,\n  type: makeStringProp(\"default\"),\n  color: String,\n  plain: Boolean,\n  round: Boolean,\n  textColor: String,\n  closeable: Boolean\n};\nvar stdin_default = defineComponent({\n  name,\n  props: tagProps,\n  emits: [\"close\"],\n  setup(props, {\n    slots,\n    emit\n  }) {\n    const onClose = event => {\n      event.stopPropagation();\n      emit(\"close\", event);\n    };\n    const getStyle = () => {\n      if (props.plain) {\n        return {\n          color: props.textColor || props.color,\n          borderColor: props.color\n        };\n      }\n      return {\n        color: props.textColor,\n        background: props.color\n      };\n    };\n    const renderTag = () => {\n      var _a;\n      const {\n        type,\n        mark,\n        plain,\n        round,\n        size,\n        closeable\n      } = props;\n      const classes = {\n        mark,\n        plain,\n        round\n      };\n      if (size) {\n        classes[size] = size;\n      }\n      const CloseIcon = closeable && _createVNode(Icon, {\n        \"name\": \"cross\",\n        \"class\": [bem(\"close\"), HAPTICS_FEEDBACK],\n        \"onClick\": onClose\n      }, null);\n      return _createVNode(\"span\", {\n        \"style\": getStyle(),\n        \"class\": bem([classes, type])\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots), CloseIcon]);\n    };\n    return () => _createVNode(Transition, {\n      \"name\": props.closeable ? \"van-fade\" : void 0\n    }, {\n      default: () => [props.show ? renderTag() : null]\n    });\n  }\n});\nexport { stdin_default as default, tagProps };", "map": {"version": 3, "names": ["Transition", "defineComponent", "createVNode", "_createVNode", "truthProp", "makeStringProp", "createNamespace", "HAPTICS_FEEDBACK", "Icon", "name", "bem", "tagProps", "size", "String", "mark", "Boolean", "show", "type", "color", "plain", "round", "textColor", "closeable", "stdin_default", "props", "emits", "setup", "slots", "emit", "onClose", "event", "stopPropagation", "getStyle", "borderColor", "background", "renderTag", "_a", "classes", "CloseIcon", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tag/Tag.mjs"], "sourcesContent": ["import { Transition, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace, HAPTICS_FEEDBACK } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"tag\");\nconst tagProps = {\n  size: String,\n  mark: Boolean,\n  show: truthProp,\n  type: makeStringProp(\"default\"),\n  color: String,\n  plain: Boolean,\n  round: Boolean,\n  textColor: String,\n  closeable: Boolean\n};\nvar stdin_default = defineComponent({\n  name,\n  props: tagProps,\n  emits: [\"close\"],\n  setup(props, {\n    slots,\n    emit\n  }) {\n    const onClose = (event) => {\n      event.stopPropagation();\n      emit(\"close\", event);\n    };\n    const getStyle = () => {\n      if (props.plain) {\n        return {\n          color: props.textColor || props.color,\n          borderColor: props.color\n        };\n      }\n      return {\n        color: props.textColor,\n        background: props.color\n      };\n    };\n    const renderTag = () => {\n      var _a;\n      const {\n        type,\n        mark,\n        plain,\n        round,\n        size,\n        closeable\n      } = props;\n      const classes = {\n        mark,\n        plain,\n        round\n      };\n      if (size) {\n        classes[size] = size;\n      }\n      const CloseIcon = closeable && _createVNode(Icon, {\n        \"name\": \"cross\",\n        \"class\": [bem(\"close\"), HAPTICS_FEEDBACK],\n        \"onClick\": onClose\n      }, null);\n      return _createVNode(\"span\", {\n        \"style\": getStyle(),\n        \"class\": bem([classes, type])\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots), CloseIcon]);\n    };\n    return () => _createVNode(Transition, {\n      \"name\": props.closeable ? \"van-fade\" : void 0\n    }, {\n      default: () => [props.show ? renderTag() : null]\n    });\n  }\n});\nexport {\n  stdin_default as default,\n  tagProps\n};\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC9E,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AACjG,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,KAAK,CAAC;AAC1C,MAAMK,QAAQ,GAAG;EACfC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAEC,OAAO;EACbC,IAAI,EAAEZ,SAAS;EACfa,IAAI,EAAEZ,cAAc,CAAC,SAAS,CAAC;EAC/Ba,KAAK,EAAEL,MAAM;EACbM,KAAK,EAAEJ,OAAO;EACdK,KAAK,EAAEL,OAAO;EACdM,SAAS,EAAER,MAAM;EACjBS,SAAS,EAAEP;AACb,CAAC;AACD,IAAIQ,aAAa,GAAGtB,eAAe,CAAC;EAClCQ,IAAI;EACJe,KAAK,EAAEb,QAAQ;EACfc,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAKA,CAACF,KAAK,EAAE;IACXG,KAAK;IACLC;EACF,CAAC,EAAE;IACD,MAAMC,OAAO,GAAIC,KAAK,IAAK;MACzBA,KAAK,CAACC,eAAe,CAAC,CAAC;MACvBH,IAAI,CAAC,OAAO,EAAEE,KAAK,CAAC;IACtB,CAAC;IACD,MAAME,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAIR,KAAK,CAACL,KAAK,EAAE;QACf,OAAO;UACLD,KAAK,EAAEM,KAAK,CAACH,SAAS,IAAIG,KAAK,CAACN,KAAK;UACrCe,WAAW,EAAET,KAAK,CAACN;QACrB,CAAC;MACH;MACA,OAAO;QACLA,KAAK,EAAEM,KAAK,CAACH,SAAS;QACtBa,UAAU,EAAEV,KAAK,CAACN;MACpB,CAAC;IACH,CAAC;IACD,MAAMiB,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAIC,EAAE;MACN,MAAM;QACJnB,IAAI;QACJH,IAAI;QACJK,KAAK;QACLC,KAAK;QACLR,IAAI;QACJU;MACF,CAAC,GAAGE,KAAK;MACT,MAAMa,OAAO,GAAG;QACdvB,IAAI;QACJK,KAAK;QACLC;MACF,CAAC;MACD,IAAIR,IAAI,EAAE;QACRyB,OAAO,CAACzB,IAAI,CAAC,GAAGA,IAAI;MACtB;MACA,MAAM0B,SAAS,GAAGhB,SAAS,IAAInB,YAAY,CAACK,IAAI,EAAE;QAChD,MAAM,EAAE,OAAO;QACf,OAAO,EAAE,CAACE,GAAG,CAAC,OAAO,CAAC,EAAEH,gBAAgB,CAAC;QACzC,SAAS,EAAEsB;MACb,CAAC,EAAE,IAAI,CAAC;MACR,OAAO1B,YAAY,CAAC,MAAM,EAAE;QAC1B,OAAO,EAAE6B,QAAQ,CAAC,CAAC;QACnB,OAAO,EAAEtB,GAAG,CAAC,CAAC2B,OAAO,EAAEpB,IAAI,CAAC;MAC9B,CAAC,EAAE,CAAC,CAACmB,EAAE,GAAGT,KAAK,CAACY,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACI,IAAI,CAACb,KAAK,CAAC,EAAEW,SAAS,CAAC,CAAC;IACzE,CAAC;IACD,OAAO,MAAMnC,YAAY,CAACH,UAAU,EAAE;MACpC,MAAM,EAAEwB,KAAK,CAACF,SAAS,GAAG,UAAU,GAAG,KAAK;IAC9C,CAAC,EAAE;MACDiB,OAAO,EAAEA,CAAA,KAAM,CAACf,KAAK,CAACR,IAAI,GAAGmB,SAAS,CAAC,CAAC,GAAG,IAAI;IACjD,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AACF,SACEZ,aAAa,IAAIgB,OAAO,EACxB5B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}