{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { FORM_KEY, truthProp, numericProp, preventDefault, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"form\");\nconst formProps = {\n  colon: <PERSON><PERSON><PERSON>,\n  disabled: <PERSON><PERSON><PERSON>,\n  readonly: <PERSON><PERSON><PERSON>,\n  required: [<PERSON><PERSON><PERSON>, String],\n  showError: <PERSON><PERSON><PERSON>,\n  labelWidth: numericProp,\n  labelAlign: String,\n  inputAlign: String,\n  scrollToError: Boolean,\n  scrollToErrorPosition: String,\n  validateFirst: Boolean,\n  submitOnEnter: truthProp,\n  showErrorMessage: truthProp,\n  errorMessageAlign: String,\n  validateTrigger: {\n    type: [String, Array],\n    default: \"onBlur\"\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: formProps,\n  emits: [\"submit\", \"failed\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      children,\n      linkChildren\n    } = useChildren(FORM_KEY);\n    const getFieldsByNames = names => {\n      if (names) {\n        return children.filter(field => names.includes(field.name));\n      }\n      return children;\n    };\n    const validateSeq = names => new Promise((resolve, reject) => {\n      const errors = [];\n      const fields = getFieldsByNames(names);\n      fields.reduce((promise, field) => promise.then(() => {\n        if (!errors.length) {\n          return field.validate().then(error => {\n            if (error) {\n              errors.push(error);\n            }\n          });\n        }\n      }), Promise.resolve()).then(() => {\n        if (errors.length) {\n          reject(errors);\n        } else {\n          resolve();\n        }\n      });\n    });\n    const validateAll = names => new Promise((resolve, reject) => {\n      const fields = getFieldsByNames(names);\n      Promise.all(fields.map(item => item.validate())).then(errors => {\n        errors = errors.filter(Boolean);\n        if (errors.length) {\n          reject(errors);\n        } else {\n          resolve();\n        }\n      });\n    });\n    const validateField = name2 => {\n      const matched = children.find(item => item.name === name2);\n      if (matched) {\n        return new Promise((resolve, reject) => {\n          matched.validate().then(error => {\n            if (error) {\n              reject(error);\n            } else {\n              resolve();\n            }\n          });\n        });\n      }\n      return Promise.reject();\n    };\n    const validate = name2 => {\n      if (typeof name2 === \"string\") {\n        return validateField(name2);\n      }\n      return props.validateFirst ? validateSeq(name2) : validateAll(name2);\n    };\n    const resetValidation = name2 => {\n      if (typeof name2 === \"string\") {\n        name2 = [name2];\n      }\n      const fields = getFieldsByNames(name2);\n      fields.forEach(item => {\n        item.resetValidation();\n      });\n    };\n    const getValidationStatus = () => children.reduce((form, field) => {\n      form[field.name] = field.getValidationStatus();\n      return form;\n    }, {});\n    const scrollToField = (name2, options) => {\n      children.some(item => {\n        if (item.name === name2) {\n          item.$el.scrollIntoView(options);\n          return true;\n        }\n        return false;\n      });\n    };\n    const getValues = () => children.reduce((form, field) => {\n      if (field.name !== void 0) {\n        form[field.name] = field.formValue.value;\n      }\n      return form;\n    }, {});\n    const submit = () => {\n      const values = getValues();\n      validate().then(() => emit(\"submit\", values)).catch(errors => {\n        emit(\"failed\", {\n          values,\n          errors\n        });\n        const {\n          scrollToError,\n          scrollToErrorPosition\n        } = props;\n        if (scrollToError && errors[0].name) {\n          scrollToField(errors[0].name, scrollToErrorPosition ? {\n            block: scrollToErrorPosition\n          } : void 0);\n        }\n      });\n    };\n    const onSubmit = event => {\n      preventDefault(event);\n      submit();\n    };\n    linkChildren({\n      props\n    });\n    useExpose({\n      submit,\n      validate,\n      getValues,\n      scrollToField,\n      resetValidation,\n      getValidationStatus\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"form\", {\n        \"class\": bem(),\n        \"onSubmit\": onSubmit\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { stdin_default as default, formProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "FORM_KEY", "truthProp", "numericProp", "preventDefault", "createNamespace", "useChildren", "useExpose", "name", "bem", "formProps", "colon", "Boolean", "disabled", "readonly", "required", "String", "showError", "labelWidth", "labelAlign", "inputAlign", "scrollToError", "scrollToErrorPosition", "validate<PERSON><PERSON><PERSON>", "submitOnEnter", "showErrorMessage", "errorMessageAlign", "validate<PERSON><PERSON>ger", "type", "Array", "default", "stdin_default", "props", "emits", "setup", "emit", "slots", "children", "linkChildren", "getFieldsByNames", "names", "filter", "field", "includes", "validateSeq", "Promise", "resolve", "reject", "errors", "fields", "reduce", "promise", "then", "length", "validate", "error", "push", "validateAll", "all", "map", "item", "validateField", "name2", "matched", "find", "resetValidation", "for<PERSON>ach", "getValidationStatus", "form", "scrollToField", "options", "some", "$el", "scrollIntoView", "getV<PERSON>ues", "formValue", "value", "submit", "values", "catch", "block", "onSubmit", "event", "_a", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/form/Form.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { FORM_KEY, truthProp, numericProp, preventDefault, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"form\");\nconst formProps = {\n  colon: <PERSON><PERSON><PERSON>,\n  disabled: <PERSON><PERSON><PERSON>,\n  readonly: <PERSON><PERSON><PERSON>,\n  required: [<PERSON><PERSON>an, String],\n  showError: <PERSON>olean,\n  labelWidth: numericProp,\n  labelAlign: String,\n  inputAlign: String,\n  scrollToError: <PERSON>olean,\n  scrollToErrorPosition: String,\n  validateFirst: <PERSON>olean,\n  submitOnEnter: truthProp,\n  showErrorMessage: truthProp,\n  errorMessageAlign: String,\n  validateTrigger: {\n    type: [String, Array],\n    default: \"onBlur\"\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: formProps,\n  emits: [\"submit\", \"failed\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      children,\n      linkChildren\n    } = useChildren(FORM_KEY);\n    const getFieldsByNames = (names) => {\n      if (names) {\n        return children.filter((field) => names.includes(field.name));\n      }\n      return children;\n    };\n    const validateSeq = (names) => new Promise((resolve, reject) => {\n      const errors = [];\n      const fields = getFieldsByNames(names);\n      fields.reduce((promise, field) => promise.then(() => {\n        if (!errors.length) {\n          return field.validate().then((error) => {\n            if (error) {\n              errors.push(error);\n            }\n          });\n        }\n      }), Promise.resolve()).then(() => {\n        if (errors.length) {\n          reject(errors);\n        } else {\n          resolve();\n        }\n      });\n    });\n    const validateAll = (names) => new Promise((resolve, reject) => {\n      const fields = getFieldsByNames(names);\n      Promise.all(fields.map((item) => item.validate())).then((errors) => {\n        errors = errors.filter(Boolean);\n        if (errors.length) {\n          reject(errors);\n        } else {\n          resolve();\n        }\n      });\n    });\n    const validateField = (name2) => {\n      const matched = children.find((item) => item.name === name2);\n      if (matched) {\n        return new Promise((resolve, reject) => {\n          matched.validate().then((error) => {\n            if (error) {\n              reject(error);\n            } else {\n              resolve();\n            }\n          });\n        });\n      }\n      return Promise.reject();\n    };\n    const validate = (name2) => {\n      if (typeof name2 === \"string\") {\n        return validateField(name2);\n      }\n      return props.validateFirst ? validateSeq(name2) : validateAll(name2);\n    };\n    const resetValidation = (name2) => {\n      if (typeof name2 === \"string\") {\n        name2 = [name2];\n      }\n      const fields = getFieldsByNames(name2);\n      fields.forEach((item) => {\n        item.resetValidation();\n      });\n    };\n    const getValidationStatus = () => children.reduce((form, field) => {\n      form[field.name] = field.getValidationStatus();\n      return form;\n    }, {});\n    const scrollToField = (name2, options) => {\n      children.some((item) => {\n        if (item.name === name2) {\n          item.$el.scrollIntoView(options);\n          return true;\n        }\n        return false;\n      });\n    };\n    const getValues = () => children.reduce((form, field) => {\n      if (field.name !== void 0) {\n        form[field.name] = field.formValue.value;\n      }\n      return form;\n    }, {});\n    const submit = () => {\n      const values = getValues();\n      validate().then(() => emit(\"submit\", values)).catch((errors) => {\n        emit(\"failed\", {\n          values,\n          errors\n        });\n        const {\n          scrollToError,\n          scrollToErrorPosition\n        } = props;\n        if (scrollToError && errors[0].name) {\n          scrollToField(errors[0].name, scrollToErrorPosition ? {\n            block: scrollToErrorPosition\n          } : void 0);\n        }\n      });\n    };\n    const onSubmit = (event) => {\n      preventDefault(event);\n      submit();\n    };\n    linkChildren({\n      props\n    });\n    useExpose({\n      submit,\n      validate,\n      getValues,\n      scrollToField,\n      resetValidation,\n      getValidationStatus\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"form\", {\n        \"class\": bem(),\n        \"onSubmit\": onSubmit\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  formProps\n};\n"], "mappings": ";;;;;;;;AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACtG,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,MAAM,CAAC;AAC3C,MAAMK,SAAS,GAAG;EAChBC,KAAK,EAAEC,OAAO;EACdC,QAAQ,EAAED,OAAO;EACjBE,QAAQ,EAAEF,OAAO;EACjBG,QAAQ,EAAE,CAACH,OAAO,EAAEI,MAAM,CAAC;EAC3BC,SAAS,EAAEL,OAAO;EAClBM,UAAU,EAAEf,WAAW;EACvBgB,UAAU,EAAEH,MAAM;EAClBI,UAAU,EAAEJ,MAAM;EAClBK,aAAa,EAAET,OAAO;EACtBU,qBAAqB,EAAEN,MAAM;EAC7BO,aAAa,EAAEX,OAAO;EACtBY,aAAa,EAAEtB,SAAS;EACxBuB,gBAAgB,EAAEvB,SAAS;EAC3BwB,iBAAiB,EAAEV,MAAM;EACzBW,eAAe,EAAE;IACfC,IAAI,EAAE,CAACZ,MAAM,EAAEa,KAAK,CAAC;IACrBC,OAAO,EAAE;EACX;AACF,CAAC;AACD,IAAIC,aAAa,GAAGjC,eAAe,CAAC;EAClCU,IAAI;EACJwB,KAAK,EAAEtB,SAAS;EAChBuB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC3BC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAM;MACJC,QAAQ;MACRC;IACF,CAAC,GAAGhC,WAAW,CAACL,QAAQ,CAAC;IACzB,MAAMsC,gBAAgB,GAAIC,KAAK,IAAK;MAClC,IAAIA,KAAK,EAAE;QACT,OAAOH,QAAQ,CAACI,MAAM,CAAEC,KAAK,IAAKF,KAAK,CAACG,QAAQ,CAACD,KAAK,CAAClC,IAAI,CAAC,CAAC;MAC/D;MACA,OAAO6B,QAAQ;IACjB,CAAC;IACD,MAAMO,WAAW,GAAIJ,KAAK,IAAK,IAAIK,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC9D,MAAMC,MAAM,GAAG,EAAE;MACjB,MAAMC,MAAM,GAAGV,gBAAgB,CAACC,KAAK,CAAC;MACtCS,MAAM,CAACC,MAAM,CAAC,CAACC,OAAO,EAAET,KAAK,KAAKS,OAAO,CAACC,IAAI,CAAC,MAAM;QACnD,IAAI,CAACJ,MAAM,CAACK,MAAM,EAAE;UAClB,OAAOX,KAAK,CAACY,QAAQ,CAAC,CAAC,CAACF,IAAI,CAAEG,KAAK,IAAK;YACtC,IAAIA,KAAK,EAAE;cACTP,MAAM,CAACQ,IAAI,CAACD,KAAK,CAAC;YACpB;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,EAAEV,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,MAAM;QAChC,IAAIJ,MAAM,CAACK,MAAM,EAAE;UACjBN,MAAM,CAACC,MAAM,CAAC;QAChB,CAAC,MAAM;UACLF,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,MAAMW,WAAW,GAAIjB,KAAK,IAAK,IAAIK,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAC9D,MAAME,MAAM,GAAGV,gBAAgB,CAACC,KAAK,CAAC;MACtCK,OAAO,CAACa,GAAG,CAACT,MAAM,CAACU,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACN,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAAEJ,MAAM,IAAK;QAClEA,MAAM,GAAGA,MAAM,CAACP,MAAM,CAAC7B,OAAO,CAAC;QAC/B,IAAIoC,MAAM,CAACK,MAAM,EAAE;UACjBN,MAAM,CAACC,MAAM,CAAC;QAChB,CAAC,MAAM;UACLF,OAAO,CAAC,CAAC;QACX;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,MAAMe,aAAa,GAAIC,KAAK,IAAK;MAC/B,MAAMC,OAAO,GAAG1B,QAAQ,CAAC2B,IAAI,CAAEJ,IAAI,IAAKA,IAAI,CAACpD,IAAI,KAAKsD,KAAK,CAAC;MAC5D,IAAIC,OAAO,EAAE;QACX,OAAO,IAAIlB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACtCgB,OAAO,CAACT,QAAQ,CAAC,CAAC,CAACF,IAAI,CAAEG,KAAK,IAAK;YACjC,IAAIA,KAAK,EAAE;cACTR,MAAM,CAACQ,KAAK,CAAC;YACf,CAAC,MAAM;cACLT,OAAO,CAAC,CAAC;YACX;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACA,OAAOD,OAAO,CAACE,MAAM,CAAC,CAAC;IACzB,CAAC;IACD,MAAMO,QAAQ,GAAIQ,KAAK,IAAK;MAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOD,aAAa,CAACC,KAAK,CAAC;MAC7B;MACA,OAAO9B,KAAK,CAACT,aAAa,GAAGqB,WAAW,CAACkB,KAAK,CAAC,GAAGL,WAAW,CAACK,KAAK,CAAC;IACtE,CAAC;IACD,MAAMG,eAAe,GAAIH,KAAK,IAAK;MACjC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BA,KAAK,GAAG,CAACA,KAAK,CAAC;MACjB;MACA,MAAMb,MAAM,GAAGV,gBAAgB,CAACuB,KAAK,CAAC;MACtCb,MAAM,CAACiB,OAAO,CAAEN,IAAI,IAAK;QACvBA,IAAI,CAACK,eAAe,CAAC,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC;IACD,MAAME,mBAAmB,GAAGA,CAAA,KAAM9B,QAAQ,CAACa,MAAM,CAAC,CAACkB,IAAI,EAAE1B,KAAK,KAAK;MACjE0B,IAAI,CAAC1B,KAAK,CAAClC,IAAI,CAAC,GAAGkC,KAAK,CAACyB,mBAAmB,CAAC,CAAC;MAC9C,OAAOC,IAAI;IACb,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,MAAMC,aAAa,GAAGA,CAACP,KAAK,EAAEQ,OAAO,KAAK;MACxCjC,QAAQ,CAACkC,IAAI,CAAEX,IAAI,IAAK;QACtB,IAAIA,IAAI,CAACpD,IAAI,KAAKsD,KAAK,EAAE;UACvBF,IAAI,CAACY,GAAG,CAACC,cAAc,CAACH,OAAO,CAAC;UAChC,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC,CAAC;IACJ,CAAC;IACD,MAAMI,SAAS,GAAGA,CAAA,KAAMrC,QAAQ,CAACa,MAAM,CAAC,CAACkB,IAAI,EAAE1B,KAAK,KAAK;MACvD,IAAIA,KAAK,CAAClC,IAAI,KAAK,KAAK,CAAC,EAAE;QACzB4D,IAAI,CAAC1B,KAAK,CAAClC,IAAI,CAAC,GAAGkC,KAAK,CAACiC,SAAS,CAACC,KAAK;MAC1C;MACA,OAAOR,IAAI;IACb,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,MAAMS,MAAM,GAAGA,CAAA,KAAM;MACnB,MAAMC,MAAM,GAAGJ,SAAS,CAAC,CAAC;MAC1BpB,QAAQ,CAAC,CAAC,CAACF,IAAI,CAAC,MAAMjB,IAAI,CAAC,QAAQ,EAAE2C,MAAM,CAAC,CAAC,CAACC,KAAK,CAAE/B,MAAM,IAAK;QAC9Db,IAAI,CAAC,QAAQ,EAAE;UACb2C,MAAM;UACN9B;QACF,CAAC,CAAC;QACF,MAAM;UACJ3B,aAAa;UACbC;QACF,CAAC,GAAGU,KAAK;QACT,IAAIX,aAAa,IAAI2B,MAAM,CAAC,CAAC,CAAC,CAACxC,IAAI,EAAE;UACnC6D,aAAa,CAACrB,MAAM,CAAC,CAAC,CAAC,CAACxC,IAAI,EAAEc,qBAAqB,GAAG;YACpD0D,KAAK,EAAE1D;UACT,CAAC,GAAG,KAAK,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ,CAAC;IACD,MAAM2D,QAAQ,GAAIC,KAAK,IAAK;MAC1B9E,cAAc,CAAC8E,KAAK,CAAC;MACrBL,MAAM,CAAC,CAAC;IACV,CAAC;IACDvC,YAAY,CAAC;MACXN;IACF,CAAC,CAAC;IACFzB,SAAS,CAAC;MACRsE,MAAM;MACNvB,QAAQ;MACRoB,SAAS;MACTL,aAAa;MACbJ,eAAe;MACfE;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIgB,EAAE;MACN,OAAOnF,YAAY,CAAC,MAAM,EAAE;QAC1B,OAAO,EAAES,GAAG,CAAC,CAAC;QACd,UAAU,EAAEwE;MACd,CAAC,EAAE,CAAC,CAACE,EAAE,GAAG/C,KAAK,CAACN,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqD,EAAE,CAACC,IAAI,CAAChD,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEL,aAAa,IAAID,OAAO,EACxBpB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}