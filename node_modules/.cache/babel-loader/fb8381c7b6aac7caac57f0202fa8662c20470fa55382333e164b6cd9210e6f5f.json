{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { get, isFunction } from \"./basic.mjs\";\nimport { camelize } from \"./format.mjs\";\nimport locale from \"../locale/index.mjs\";\nfunction createTranslate(name) {\n  const prefix = camelize(name) + \".\";\n  return (path, ...args) => {\n    const messages = locale.messages();\n    const message = get(messages, prefix + path) || get(messages, path);\n    return isFunction(message) ? message(...args) : message;\n  };\n}\nfunction genBem(name, mods) {\n  if (!mods) {\n    return \"\";\n  }\n  if (typeof mods === \"string\") {\n    return ` ${name}--${mods}`;\n  }\n  if (Array.isArray(mods)) {\n    return mods.reduce((ret, item) => ret + genBem(name, item), \"\");\n  }\n  return Object.keys(mods).reduce((ret, key) => ret + (mods[key] ? genBem(name, key) : \"\"), \"\");\n}\nfunction createBEM(name) {\n  return (el, mods) => {\n    if (el && typeof el !== \"string\") {\n      mods = el;\n      el = \"\";\n    }\n    el = el ? `${name}__${el}` : name;\n    return `${el}${genBem(el, mods)}`;\n  };\n}\nfunction createNamespace(name) {\n  const prefixedName = `van-${name}`;\n  return [prefixedName, createBEM(prefixedName), createTranslate(prefixedName)];\n}\nexport { createBEM, createNamespace, createTranslate };", "map": {"version": 3, "names": ["get", "isFunction", "camelize", "locale", "createTranslate", "name", "prefix", "path", "args", "messages", "message", "genBem", "mods", "Array", "isArray", "reduce", "ret", "item", "Object", "keys", "key", "createBEM", "el", "createNamespace", "prefixedName"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/utils/create.mjs"], "sourcesContent": ["import { get, isFunction } from \"./basic.mjs\";\nimport { camelize } from \"./format.mjs\";\nimport locale from \"../locale/index.mjs\";\nfunction createTranslate(name) {\n  const prefix = camelize(name) + \".\";\n  return (path, ...args) => {\n    const messages = locale.messages();\n    const message = get(messages, prefix + path) || get(messages, path);\n    return isFunction(message) ? message(...args) : message;\n  };\n}\nfunction genBem(name, mods) {\n  if (!mods) {\n    return \"\";\n  }\n  if (typeof mods === \"string\") {\n    return ` ${name}--${mods}`;\n  }\n  if (Array.isArray(mods)) {\n    return mods.reduce(\n      (ret, item) => ret + genBem(name, item),\n      \"\"\n    );\n  }\n  return Object.keys(mods).reduce(\n    (ret, key) => ret + (mods[key] ? genBem(name, key) : \"\"),\n    \"\"\n  );\n}\nfunction createBEM(name) {\n  return (el, mods) => {\n    if (el && typeof el !== \"string\") {\n      mods = el;\n      el = \"\";\n    }\n    el = el ? `${name}__${el}` : name;\n    return `${el}${genBem(el, mods)}`;\n  };\n}\nfunction createNamespace(name) {\n  const prefixedName = `van-${name}`;\n  return [\n    prefixedName,\n    createBEM(prefixedName),\n    createTranslate(prefixedName)\n  ];\n}\nexport {\n  createBEM,\n  createNamespace,\n  createTranslate\n};\n"], "mappings": ";;AAAA,SAASA,GAAG,EAAEC,UAAU,QAAQ,aAAa;AAC7C,SAASC,QAAQ,QAAQ,cAAc;AACvC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,MAAMC,MAAM,GAAGJ,QAAQ,CAACG,IAAI,CAAC,GAAG,GAAG;EACnC,OAAO,CAACE,IAAI,EAAE,GAAGC,IAAI,KAAK;IACxB,MAAMC,QAAQ,GAAGN,MAAM,CAACM,QAAQ,CAAC,CAAC;IAClC,MAAMC,OAAO,GAAGV,GAAG,CAACS,QAAQ,EAAEH,MAAM,GAAGC,IAAI,CAAC,IAAIP,GAAG,CAACS,QAAQ,EAAEF,IAAI,CAAC;IACnE,OAAON,UAAU,CAACS,OAAO,CAAC,GAAGA,OAAO,CAAC,GAAGF,IAAI,CAAC,GAAGE,OAAO;EACzD,CAAC;AACH;AACA,SAASC,MAAMA,CAACN,IAAI,EAAEO,IAAI,EAAE;EAC1B,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO,IAAIP,IAAI,KAAKO,IAAI,EAAE;EAC5B;EACA,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAACG,MAAM,CAChB,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGL,MAAM,CAACN,IAAI,EAAEY,IAAI,CAAC,EACvC,EACF,CAAC;EACH;EACA,OAAOC,MAAM,CAACC,IAAI,CAACP,IAAI,CAAC,CAACG,MAAM,CAC7B,CAACC,GAAG,EAAEI,GAAG,KAAKJ,GAAG,IAAIJ,IAAI,CAACQ,GAAG,CAAC,GAAGT,MAAM,CAACN,IAAI,EAAEe,GAAG,CAAC,GAAG,EAAE,CAAC,EACxD,EACF,CAAC;AACH;AACA,SAASC,SAASA,CAAChB,IAAI,EAAE;EACvB,OAAO,CAACiB,EAAE,EAAEV,IAAI,KAAK;IACnB,IAAIU,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MAChCV,IAAI,GAAGU,EAAE;MACTA,EAAE,GAAG,EAAE;IACT;IACAA,EAAE,GAAGA,EAAE,GAAG,GAAGjB,IAAI,KAAKiB,EAAE,EAAE,GAAGjB,IAAI;IACjC,OAAO,GAAGiB,EAAE,GAAGX,MAAM,CAACW,EAAE,EAAEV,IAAI,CAAC,EAAE;EACnC,CAAC;AACH;AACA,SAASW,eAAeA,CAAClB,IAAI,EAAE;EAC7B,MAAMmB,YAAY,GAAG,OAAOnB,IAAI,EAAE;EAClC,OAAO,CACLmB,YAAY,EACZH,SAAS,CAACG,YAAY,CAAC,EACvBpB,eAAe,CAACoB,YAAY,CAAC,CAC9B;AACH;AACA,SACEH,SAAS,EACTE,eAAe,EACfnB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}