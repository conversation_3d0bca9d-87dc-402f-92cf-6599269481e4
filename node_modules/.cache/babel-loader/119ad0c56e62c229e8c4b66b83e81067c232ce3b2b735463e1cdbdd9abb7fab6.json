{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _PullRefresh from \"./PullRefresh.mjs\";\nconst PullRefresh = withInstall(_PullRefresh);\nvar stdin_default = PullRefresh;\nimport { pullRefreshProps } from \"./PullRefresh.mjs\";\nexport { PullRefresh, stdin_default as default, pullRefreshProps };", "map": {"version": 3, "names": ["withInstall", "_<PERSON>ull<PERSON><PERSON>resh", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stdin_default", "pullRefreshProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/pull-refresh/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _PullRefresh from \"./PullRefresh.mjs\";\nconst PullRefresh = withInstall(_PullRefresh);\nvar stdin_default = PullRefresh;\nimport { pullRefreshProps } from \"./PullRefresh.mjs\";\nexport {\n  PullRefresh,\n  stdin_default as default,\n  pullRefreshProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXC,aAAa,IAAIE,OAAO,EACxBD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}