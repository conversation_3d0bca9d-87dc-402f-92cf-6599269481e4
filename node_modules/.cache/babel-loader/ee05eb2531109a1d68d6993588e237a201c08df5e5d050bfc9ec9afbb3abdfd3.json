{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Calendar from \"./Calendar.mjs\";\nconst Calendar = withInstall(_Calendar);\nvar stdin_default = Calendar;\nimport { calendarProps } from \"./Calendar.mjs\";\nexport { Calendar, calendarProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Calendar", "Calendar", "stdin_default", "calendarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/calendar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Calendar from \"./Calendar.mjs\";\nconst Calendar = withInstall(_Calendar);\nvar stdin_default = Calendar;\nimport { calendarProps } from \"./Calendar.mjs\";\nexport {\n  Calendar,\n  calendarProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SAASE,aAAa,QAAQ,gBAAgB;AAC9C,SACEF,QAAQ,EACRE,aAAa,EACbD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}