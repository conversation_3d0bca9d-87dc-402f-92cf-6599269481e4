{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ShareSheet from \"./ShareSheet.mjs\";\nconst ShareSheet = withInstall(_ShareSheet);\nvar stdin_default = ShareSheet;\nimport { shareSheetProps } from \"./ShareSheet.mjs\";\nexport { ShareSheet, stdin_default as default, shareSheetProps };", "map": {"version": 3, "names": ["withInstall", "_ShareSheet", "ShareSheet", "stdin_default", "shareSheetProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/share-sheet/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ShareSheet from \"./ShareSheet.mjs\";\nconst ShareSheet = withInstall(_ShareSheet);\nvar stdin_default = ShareSheet;\nimport { shareSheetProps } from \"./ShareSheet.mjs\";\nexport {\n  ShareSheet,\n  stdin_default as default,\n  shareSheetProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVC,aAAa,IAAIE,OAAO,EACxBD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}