{"ast": null, "code": "import { useRect } from \"@vant/use\";\nimport { loadImageAsync } from \"./util.mjs\";\nimport { noop } from \"../../utils/index.mjs\";\nimport { h } from \"vue\";\nvar stdin_default = lazyManager => ({\n  props: {\n    src: [String, Object],\n    tag: {\n      type: String,\n      default: \"img\"\n    }\n  },\n  render() {\n    var _a, _b;\n    return h(this.tag, {\n      src: this.renderSrc\n    }, (_b = (_a = this.$slots).default) == null ? void 0 : _b.call(_a));\n  },\n  data() {\n    return {\n      el: null,\n      options: {\n        src: \"\",\n        error: \"\",\n        loading: \"\",\n        attempt: lazyManager.options.attempt\n      },\n      state: {\n        loaded: false,\n        error: false,\n        attempt: 0\n      },\n      renderSrc: \"\"\n    };\n  },\n  watch: {\n    src() {\n      this.init();\n      lazyManager.addLazyBox(this);\n      lazyManager.lazyLoadHandler();\n    }\n  },\n  created() {\n    this.init();\n  },\n  mounted() {\n    this.el = this.$el;\n    lazyManager.addLazyBox(this);\n    lazyManager.lazyLoadHandler();\n  },\n  beforeUnmount() {\n    lazyManager.removeComponent(this);\n  },\n  methods: {\n    init() {\n      const {\n        src,\n        loading,\n        error\n      } = lazyManager.valueFormatter(this.src);\n      this.state.loaded = false;\n      this.options.src = src;\n      this.options.error = error;\n      this.options.loading = loading;\n      this.renderSrc = this.options.loading;\n    },\n    checkInView() {\n      const rect = useRect(this.$el);\n      return rect.top < window.innerHeight * lazyManager.options.preLoad && rect.bottom > 0 && rect.left < window.innerWidth * lazyManager.options.preLoad && rect.right > 0;\n    },\n    load(onFinish = noop) {\n      if (this.state.attempt > this.options.attempt - 1 && this.state.error) {\n        if (process.env.NODE_ENV !== \"production\" && !lazyManager.options.silent) {\n          console.log(`[@vant/lazyload] ${this.options.src} tried too more than ${this.options.attempt} times`);\n        }\n        onFinish();\n        return;\n      }\n      const {\n        src\n      } = this.options;\n      loadImageAsync({\n        src\n      }, ({\n        src: src2\n      }) => {\n        this.renderSrc = src2;\n        this.state.loaded = true;\n      }, () => {\n        this.state.attempt++;\n        this.renderSrc = this.options.error;\n        this.state.error = true;\n      });\n    }\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["useRect", "loadImageAsync", "noop", "h", "stdin_default", "<PERSON><PERSON><PERSON><PERSON>", "props", "src", "String", "Object", "tag", "type", "default", "render", "_a", "_b", "renderSrc", "$slots", "call", "data", "el", "options", "error", "loading", "attempt", "state", "loaded", "watch", "init", "addLazyBox", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created", "mounted", "$el", "beforeUnmount", "removeComponent", "methods", "valueFormatter", "checkInView", "rect", "top", "window", "innerHeight", "preLoad", "bottom", "left", "innerWidth", "right", "load", "onFinish", "process", "env", "NODE_ENV", "silent", "console", "log", "src2"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/lazyload/vue-lazyload/lazy-image.mjs"], "sourcesContent": ["import { useRect } from \"@vant/use\";\nimport { loadImageAsync } from \"./util.mjs\";\nimport { noop } from \"../../utils/index.mjs\";\nimport { h } from \"vue\";\nvar stdin_default = (lazyManager) => ({\n  props: {\n    src: [String, Object],\n    tag: {\n      type: String,\n      default: \"img\"\n    }\n  },\n  render() {\n    var _a, _b;\n    return h(\n      this.tag,\n      {\n        src: this.renderSrc\n      },\n      (_b = (_a = this.$slots).default) == null ? void 0 : _b.call(_a)\n    );\n  },\n  data() {\n    return {\n      el: null,\n      options: {\n        src: \"\",\n        error: \"\",\n        loading: \"\",\n        attempt: lazyManager.options.attempt\n      },\n      state: {\n        loaded: false,\n        error: false,\n        attempt: 0\n      },\n      renderSrc: \"\"\n    };\n  },\n  watch: {\n    src() {\n      this.init();\n      lazyManager.addLazyBox(this);\n      lazyManager.lazyLoadHandler();\n    }\n  },\n  created() {\n    this.init();\n  },\n  mounted() {\n    this.el = this.$el;\n    lazyManager.addLazyBox(this);\n    lazyManager.lazyLoadHandler();\n  },\n  beforeUnmount() {\n    lazyManager.removeComponent(this);\n  },\n  methods: {\n    init() {\n      const { src, loading, error } = lazyManager.valueFormatter(this.src);\n      this.state.loaded = false;\n      this.options.src = src;\n      this.options.error = error;\n      this.options.loading = loading;\n      this.renderSrc = this.options.loading;\n    },\n    checkInView() {\n      const rect = useRect(this.$el);\n      return rect.top < window.innerHeight * lazyManager.options.preLoad && rect.bottom > 0 && rect.left < window.innerWidth * lazyManager.options.preLoad && rect.right > 0;\n    },\n    load(onFinish = noop) {\n      if (this.state.attempt > this.options.attempt - 1 && this.state.error) {\n        if (process.env.NODE_ENV !== \"production\" && !lazyManager.options.silent) {\n          console.log(\n            `[@vant/lazyload] ${this.options.src} tried too more than ${this.options.attempt} times`\n          );\n        }\n        onFinish();\n        return;\n      }\n      const { src } = this.options;\n      loadImageAsync(\n        { src },\n        ({ src: src2 }) => {\n          this.renderSrc = src2;\n          this.state.loaded = true;\n        },\n        () => {\n          this.state.attempt++;\n          this.renderSrc = this.options.error;\n          this.state.error = true;\n        }\n      );\n    }\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,CAAC,QAAQ,KAAK;AACvB,IAAIC,aAAa,GAAIC,WAAW,KAAM;EACpCC,KAAK,EAAE;IACLC,GAAG,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;IACrBC,GAAG,EAAE;MACHC,IAAI,EAAEH,MAAM;MACZI,OAAO,EAAE;IACX;EACF,CAAC;EACDC,MAAMA,CAAA,EAAG;IACP,IAAIC,EAAE,EAAEC,EAAE;IACV,OAAOZ,CAAC,CACN,IAAI,CAACO,GAAG,EACR;MACEH,GAAG,EAAE,IAAI,CAACS;IACZ,CAAC,EACD,CAACD,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACG,MAAM,EAAEL,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,EAAE,CAACG,IAAI,CAACJ,EAAE,CACjE,CAAC;EACH,CAAC;EACDK,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,IAAI;MACRC,OAAO,EAAE;QACPd,GAAG,EAAE,EAAE;QACPe,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAEnB,WAAW,CAACgB,OAAO,CAACG;MAC/B,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,KAAK;QACbJ,KAAK,EAAE,KAAK;QACZE,OAAO,EAAE;MACX,CAAC;MACDR,SAAS,EAAE;IACb,CAAC;EACH,CAAC;EACDW,KAAK,EAAE;IACLpB,GAAGA,CAAA,EAAG;MACJ,IAAI,CAACqB,IAAI,CAAC,CAAC;MACXvB,WAAW,CAACwB,UAAU,CAAC,IAAI,CAAC;MAC5BxB,WAAW,CAACyB,eAAe,CAAC,CAAC;IAC/B;EACF,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACH,IAAI,CAAC,CAAC;EACb,CAAC;EACDI,OAAOA,CAAA,EAAG;IACR,IAAI,CAACZ,EAAE,GAAG,IAAI,CAACa,GAAG;IAClB5B,WAAW,CAACwB,UAAU,CAAC,IAAI,CAAC;IAC5BxB,WAAW,CAACyB,eAAe,CAAC,CAAC;EAC/B,CAAC;EACDI,aAAaA,CAAA,EAAG;IACd7B,WAAW,CAAC8B,eAAe,CAAC,IAAI,CAAC;EACnC,CAAC;EACDC,OAAO,EAAE;IACPR,IAAIA,CAAA,EAAG;MACL,MAAM;QAAErB,GAAG;QAAEgB,OAAO;QAAED;MAAM,CAAC,GAAGjB,WAAW,CAACgC,cAAc,CAAC,IAAI,CAAC9B,GAAG,CAAC;MACpE,IAAI,CAACkB,KAAK,CAACC,MAAM,GAAG,KAAK;MACzB,IAAI,CAACL,OAAO,CAACd,GAAG,GAAGA,GAAG;MACtB,IAAI,CAACc,OAAO,CAACC,KAAK,GAAGA,KAAK;MAC1B,IAAI,CAACD,OAAO,CAACE,OAAO,GAAGA,OAAO;MAC9B,IAAI,CAACP,SAAS,GAAG,IAAI,CAACK,OAAO,CAACE,OAAO;IACvC,CAAC;IACDe,WAAWA,CAAA,EAAG;MACZ,MAAMC,IAAI,GAAGvC,OAAO,CAAC,IAAI,CAACiC,GAAG,CAAC;MAC9B,OAAOM,IAAI,CAACC,GAAG,GAAGC,MAAM,CAACC,WAAW,GAAGrC,WAAW,CAACgB,OAAO,CAACsB,OAAO,IAAIJ,IAAI,CAACK,MAAM,GAAG,CAAC,IAAIL,IAAI,CAACM,IAAI,GAAGJ,MAAM,CAACK,UAAU,GAAGzC,WAAW,CAACgB,OAAO,CAACsB,OAAO,IAAIJ,IAAI,CAACQ,KAAK,GAAG,CAAC;IACxK,CAAC;IACDC,IAAIA,CAACC,QAAQ,GAAG/C,IAAI,EAAE;MACpB,IAAI,IAAI,CAACuB,KAAK,CAACD,OAAO,GAAG,IAAI,CAACH,OAAO,CAACG,OAAO,GAAG,CAAC,IAAI,IAAI,CAACC,KAAK,CAACH,KAAK,EAAE;QACrE,IAAI4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC/C,WAAW,CAACgB,OAAO,CAACgC,MAAM,EAAE;UACxEC,OAAO,CAACC,GAAG,CACT,oBAAoB,IAAI,CAAClC,OAAO,CAACd,GAAG,wBAAwB,IAAI,CAACc,OAAO,CAACG,OAAO,QAClF,CAAC;QACH;QACAyB,QAAQ,CAAC,CAAC;QACV;MACF;MACA,MAAM;QAAE1C;MAAI,CAAC,GAAG,IAAI,CAACc,OAAO;MAC5BpB,cAAc,CACZ;QAAEM;MAAI,CAAC,EACP,CAAC;QAAEA,GAAG,EAAEiD;MAAK,CAAC,KAAK;QACjB,IAAI,CAACxC,SAAS,GAAGwC,IAAI;QACrB,IAAI,CAAC/B,KAAK,CAACC,MAAM,GAAG,IAAI;MAC1B,CAAC,EACD,MAAM;QACJ,IAAI,CAACD,KAAK,CAACD,OAAO,EAAE;QACpB,IAAI,CAACR,SAAS,GAAG,IAAI,CAACK,OAAO,CAACC,KAAK;QACnC,IAAI,CAACG,KAAK,CAACH,KAAK,GAAG,IAAI;MACzB,CACF,CAAC;IACH;EACF;AACF,CAAC,CAAC;AACF,SACElB,aAAa,IAAIQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}