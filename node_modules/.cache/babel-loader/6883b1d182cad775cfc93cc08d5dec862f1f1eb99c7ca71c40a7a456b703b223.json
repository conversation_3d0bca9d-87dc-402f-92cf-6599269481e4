{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, makeArrayProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Sidebar } from \"../sidebar/index.mjs\";\nimport { SidebarItem } from \"../sidebar-item/index.mjs\";\nconst [name, bem] = createNamespace(\"tree-select\");\nconst treeSelectProps = {\n  max: makeNumericProp(Infinity),\n  items: makeArrayProp(),\n  height: makeNumericProp(300),\n  selectedIcon: makeStringProp(\"success\"),\n  mainActiveIndex: makeNumericProp(0),\n  activeId: {\n    type: [Number, String, Array],\n    default: 0\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: treeSelectProps,\n  emits: [\"clickNav\", \"clickItem\", \"update:activeId\", \"update:mainActiveIndex\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const isActiveItem = id => Array.isArray(props.activeId) ? props.activeId.includes(id) : props.activeId === id;\n    const renderSubItem = item => {\n      const onClick = () => {\n        if (item.disabled) {\n          return;\n        }\n        let activeId;\n        if (Array.isArray(props.activeId)) {\n          activeId = props.activeId.slice();\n          const index = activeId.indexOf(item.id);\n          if (index !== -1) {\n            activeId.splice(index, 1);\n          } else if (activeId.length < +props.max) {\n            activeId.push(item.id);\n          }\n        } else {\n          activeId = item.id;\n        }\n        emit(\"update:activeId\", activeId);\n        emit(\"clickItem\", item);\n      };\n      return _createVNode(\"div\", {\n        \"key\": item.id,\n        \"class\": [\"van-ellipsis\", bem(\"item\", {\n          active: isActiveItem(item.id),\n          disabled: item.disabled\n        })],\n        \"onClick\": onClick\n      }, [item.text, isActiveItem(item.id) && _createVNode(Icon, {\n        \"name\": props.selectedIcon,\n        \"class\": bem(\"selected\")\n      }, null)]);\n    };\n    const onSidebarChange = index => {\n      emit(\"update:mainActiveIndex\", index);\n    };\n    const onClickSidebarItem = index => emit(\"clickNav\", index);\n    const renderSidebar = () => {\n      const Items = props.items.map(item => _createVNode(SidebarItem, {\n        \"dot\": item.dot,\n        \"badge\": item.badge,\n        \"class\": [bem(\"nav-item\"), item.className],\n        \"disabled\": item.disabled,\n        \"onClick\": onClickSidebarItem\n      }, {\n        title: () => slots[\"nav-text\"] ? slots[\"nav-text\"](item) : item.text\n      }));\n      return _createVNode(Sidebar, {\n        \"class\": bem(\"nav\"),\n        \"modelValue\": props.mainActiveIndex,\n        \"onChange\": onSidebarChange\n      }, {\n        default: () => [Items]\n      });\n    };\n    const renderContent = () => {\n      if (slots.content) {\n        return slots.content();\n      }\n      const selected = props.items[+props.mainActiveIndex] || {};\n      if (selected.children) {\n        return selected.children.map(renderSubItem);\n      }\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(),\n      \"style\": {\n        height: addUnit(props.height)\n      }\n    }, [renderSidebar(), _createVNode(\"div\", {\n      \"class\": bem(\"content\")\n    }, [renderContent()])]);\n  }\n});\nexport { stdin_default as default, treeSelectProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "addUnit", "makeArrayProp", "makeStringProp", "makeNumericProp", "createNamespace", "Icon", "Sidebar", "SidebarItem", "name", "bem", "treeSelectProps", "max", "Infinity", "items", "height", "selectedIcon", "mainActiveIndex", "activeId", "type", "Number", "String", "Array", "default", "stdin_default", "props", "emits", "setup", "emit", "slots", "isActiveItem", "id", "isArray", "includes", "renderSubItem", "item", "onClick", "disabled", "slice", "index", "indexOf", "splice", "length", "push", "active", "text", "onSidebarChange", "onClickSidebarItem", "renderSidebar", "Items", "map", "dot", "badge", "className", "title", "renderContent", "content", "selected", "children"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tree-select/TreeSelect.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, makeArrayProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Sidebar } from \"../sidebar/index.mjs\";\nimport { SidebarItem } from \"../sidebar-item/index.mjs\";\nconst [name, bem] = createNamespace(\"tree-select\");\nconst treeSelectProps = {\n  max: makeNumericProp(Infinity),\n  items: makeArrayProp(),\n  height: makeNumericProp(300),\n  selectedIcon: makeStringProp(\"success\"),\n  mainActiveIndex: makeNumericProp(0),\n  activeId: {\n    type: [Number, String, Array],\n    default: 0\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: treeSelectProps,\n  emits: [\"clickNav\", \"clickItem\", \"update:activeId\", \"update:mainActiveIndex\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const isActiveItem = (id) => Array.isArray(props.activeId) ? props.activeId.includes(id) : props.activeId === id;\n    const renderSubItem = (item) => {\n      const onClick = () => {\n        if (item.disabled) {\n          return;\n        }\n        let activeId;\n        if (Array.isArray(props.activeId)) {\n          activeId = props.activeId.slice();\n          const index = activeId.indexOf(item.id);\n          if (index !== -1) {\n            activeId.splice(index, 1);\n          } else if (activeId.length < +props.max) {\n            activeId.push(item.id);\n          }\n        } else {\n          activeId = item.id;\n        }\n        emit(\"update:activeId\", activeId);\n        emit(\"clickItem\", item);\n      };\n      return _createVNode(\"div\", {\n        \"key\": item.id,\n        \"class\": [\"van-ellipsis\", bem(\"item\", {\n          active: isActiveItem(item.id),\n          disabled: item.disabled\n        })],\n        \"onClick\": onClick\n      }, [item.text, isActiveItem(item.id) && _createVNode(Icon, {\n        \"name\": props.selectedIcon,\n        \"class\": bem(\"selected\")\n      }, null)]);\n    };\n    const onSidebarChange = (index) => {\n      emit(\"update:mainActiveIndex\", index);\n    };\n    const onClickSidebarItem = (index) => emit(\"clickNav\", index);\n    const renderSidebar = () => {\n      const Items = props.items.map((item) => _createVNode(SidebarItem, {\n        \"dot\": item.dot,\n        \"badge\": item.badge,\n        \"class\": [bem(\"nav-item\"), item.className],\n        \"disabled\": item.disabled,\n        \"onClick\": onClickSidebarItem\n      }, {\n        title: () => slots[\"nav-text\"] ? slots[\"nav-text\"](item) : item.text\n      }));\n      return _createVNode(Sidebar, {\n        \"class\": bem(\"nav\"),\n        \"modelValue\": props.mainActiveIndex,\n        \"onChange\": onSidebarChange\n      }, {\n        default: () => [Items]\n      });\n    };\n    const renderContent = () => {\n      if (slots.content) {\n        return slots.content();\n      }\n      const selected = props.items[+props.mainActiveIndex] || {};\n      if (selected.children) {\n        return selected.children.map(renderSubItem);\n      }\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(),\n      \"style\": {\n        height: addUnit(props.height)\n      }\n    }, [renderSidebar(), _createVNode(\"div\", {\n      \"class\": bem(\"content\")\n    }, [renderContent()])]);\n  }\n});\nexport {\n  stdin_default as default,\n  treeSelectProps\n};\n"], "mappings": ";;;AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,OAAO,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAC7G,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,WAAW,QAAQ,2BAA2B;AACvD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,aAAa,CAAC;AAClD,MAAMM,eAAe,GAAG;EACtBC,GAAG,EAAER,eAAe,CAACS,QAAQ,CAAC;EAC9BC,KAAK,EAAEZ,aAAa,CAAC,CAAC;EACtBa,MAAM,EAAEX,eAAe,CAAC,GAAG,CAAC;EAC5BY,YAAY,EAAEb,cAAc,CAAC,SAAS,CAAC;EACvCc,eAAe,EAAEb,eAAe,CAAC,CAAC,CAAC;EACnCc,QAAQ,EAAE;IACRC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC;IAC7BC,OAAO,EAAE;EACX;AACF,CAAC;AACD,IAAIC,aAAa,GAAG1B,eAAe,CAAC;EAClCW,IAAI;EACJgB,KAAK,EAAEd,eAAe;EACtBe,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,iBAAiB,EAAE,wBAAwB,CAAC;EAC7EC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,YAAY,GAAIC,EAAE,IAAKT,KAAK,CAACU,OAAO,CAACP,KAAK,CAACP,QAAQ,CAAC,GAAGO,KAAK,CAACP,QAAQ,CAACe,QAAQ,CAACF,EAAE,CAAC,GAAGN,KAAK,CAACP,QAAQ,KAAKa,EAAE;IAChH,MAAMG,aAAa,GAAIC,IAAI,IAAK;MAC9B,MAAMC,OAAO,GAAGA,CAAA,KAAM;QACpB,IAAID,IAAI,CAACE,QAAQ,EAAE;UACjB;QACF;QACA,IAAInB,QAAQ;QACZ,IAAII,KAAK,CAACU,OAAO,CAACP,KAAK,CAACP,QAAQ,CAAC,EAAE;UACjCA,QAAQ,GAAGO,KAAK,CAACP,QAAQ,CAACoB,KAAK,CAAC,CAAC;UACjC,MAAMC,KAAK,GAAGrB,QAAQ,CAACsB,OAAO,CAACL,IAAI,CAACJ,EAAE,CAAC;UACvC,IAAIQ,KAAK,KAAK,CAAC,CAAC,EAAE;YAChBrB,QAAQ,CAACuB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;UAC3B,CAAC,MAAM,IAAIrB,QAAQ,CAACwB,MAAM,GAAG,CAACjB,KAAK,CAACb,GAAG,EAAE;YACvCM,QAAQ,CAACyB,IAAI,CAACR,IAAI,CAACJ,EAAE,CAAC;UACxB;QACF,CAAC,MAAM;UACLb,QAAQ,GAAGiB,IAAI,CAACJ,EAAE;QACpB;QACAH,IAAI,CAAC,iBAAiB,EAAEV,QAAQ,CAAC;QACjCU,IAAI,CAAC,WAAW,EAAEO,IAAI,CAAC;MACzB,CAAC;MACD,OAAOnC,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEmC,IAAI,CAACJ,EAAE;QACd,OAAO,EAAE,CAAC,cAAc,EAAErB,GAAG,CAAC,MAAM,EAAE;UACpCkC,MAAM,EAAEd,YAAY,CAACK,IAAI,CAACJ,EAAE,CAAC;UAC7BM,QAAQ,EAAEF,IAAI,CAACE;QACjB,CAAC,CAAC,CAAC;QACH,SAAS,EAAED;MACb,CAAC,EAAE,CAACD,IAAI,CAACU,IAAI,EAAEf,YAAY,CAACK,IAAI,CAACJ,EAAE,CAAC,IAAI/B,YAAY,CAACM,IAAI,EAAE;QACzD,MAAM,EAAEmB,KAAK,CAACT,YAAY;QAC1B,OAAO,EAAEN,GAAG,CAAC,UAAU;MACzB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,MAAMoC,eAAe,GAAIP,KAAK,IAAK;MACjCX,IAAI,CAAC,wBAAwB,EAAEW,KAAK,CAAC;IACvC,CAAC;IACD,MAAMQ,kBAAkB,GAAIR,KAAK,IAAKX,IAAI,CAAC,UAAU,EAAEW,KAAK,CAAC;IAC7D,MAAMS,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,KAAK,GAAGxB,KAAK,CAACX,KAAK,CAACoC,GAAG,CAAEf,IAAI,IAAKnC,YAAY,CAACQ,WAAW,EAAE;QAChE,KAAK,EAAE2B,IAAI,CAACgB,GAAG;QACf,OAAO,EAAEhB,IAAI,CAACiB,KAAK;QACnB,OAAO,EAAE,CAAC1C,GAAG,CAAC,UAAU,CAAC,EAAEyB,IAAI,CAACkB,SAAS,CAAC;QAC1C,UAAU,EAAElB,IAAI,CAACE,QAAQ;QACzB,SAAS,EAAEU;MACb,CAAC,EAAE;QACDO,KAAK,EAAEA,CAAA,KAAMzB,KAAK,CAAC,UAAU,CAAC,GAAGA,KAAK,CAAC,UAAU,CAAC,CAACM,IAAI,CAAC,GAAGA,IAAI,CAACU;MAClE,CAAC,CAAC,CAAC;MACH,OAAO7C,YAAY,CAACO,OAAO,EAAE;QAC3B,OAAO,EAAEG,GAAG,CAAC,KAAK,CAAC;QACnB,YAAY,EAAEe,KAAK,CAACR,eAAe;QACnC,UAAU,EAAE6B;MACd,CAAC,EAAE;QACDvB,OAAO,EAAEA,CAAA,KAAM,CAAC0B,KAAK;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,MAAMM,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI1B,KAAK,CAAC2B,OAAO,EAAE;QACjB,OAAO3B,KAAK,CAAC2B,OAAO,CAAC,CAAC;MACxB;MACA,MAAMC,QAAQ,GAAGhC,KAAK,CAACX,KAAK,CAAC,CAACW,KAAK,CAACR,eAAe,CAAC,IAAI,CAAC,CAAC;MAC1D,IAAIwC,QAAQ,CAACC,QAAQ,EAAE;QACrB,OAAOD,QAAQ,CAACC,QAAQ,CAACR,GAAG,CAAChB,aAAa,CAAC;MAC7C;IACF,CAAC;IACD,OAAO,MAAMlC,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEU,GAAG,CAAC,CAAC;MACd,OAAO,EAAE;QACPK,MAAM,EAAEd,OAAO,CAACwB,KAAK,CAACV,MAAM;MAC9B;IACF,CAAC,EAAE,CAACiC,aAAa,CAAC,CAAC,EAAEhD,YAAY,CAAC,KAAK,EAAE;MACvC,OAAO,EAAEU,GAAG,CAAC,SAAS;IACxB,CAAC,EAAE,CAAC6C,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;AACF,CAAC,CAAC;AACF,SACE/B,aAAa,IAAID,OAAO,EACxBZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}