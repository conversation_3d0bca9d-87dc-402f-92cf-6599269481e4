{"ast": null, "code": "import { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, inBrowser } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanImagePreview from \"./ImagePreview.mjs\";\nlet instance;\nconst defaultConfig = {\n  loop: true,\n  images: [],\n  maxZoom: 3,\n  minZoom: 1 / 3,\n  onScale: void 0,\n  onClose: void 0,\n  onChange: void 0,\n  vertical: false,\n  teleport: \"body\",\n  className: \"\",\n  showIndex: true,\n  closeable: false,\n  closeIcon: \"clear\",\n  transition: void 0,\n  beforeClose: void 0,\n  doubleScale: true,\n  overlayStyle: void 0,\n  overlayClass: void 0,\n  startPosition: 0,\n  swipeDuration: 300,\n  showIndicators: false,\n  closeOnPopstate: true,\n  closeOnClickOverlay: true,\n  closeIconPosition: \"top-right\"\n};\nfunction initInstance() {\n  ({\n    instance\n  } = mountComponent({\n    setup() {\n      const {\n        state,\n        toggle\n      } = usePopupState();\n      const onClosed = () => {\n        state.images = [];\n      };\n      return () => _createVNode(VanImagePreview, _mergeProps(state, {\n        \"onClosed\": onClosed,\n        \"onUpdate:show\": toggle\n      }), null);\n    }\n  }));\n}\nconst showImagePreview = (options, startPosition = 0) => {\n  if (!inBrowser) {\n    return;\n  }\n  if (!instance) {\n    initInstance();\n  }\n  options = Array.isArray(options) ? {\n    images: options,\n    startPosition\n  } : options;\n  instance.open(extend({}, defaultConfig, options));\n  return instance;\n};\nexport { showImagePreview };", "map": {"version": 3, "names": ["mergeProps", "_mergeProps", "createVNode", "_createVNode", "extend", "inBrowser", "mountComponent", "usePopupState", "VanImagePreview", "instance", "defaultConfig", "loop", "images", "max<PERSON><PERSON>", "minZoom", "onScale", "onClose", "onChange", "vertical", "teleport", "className", "showIndex", "closeable", "closeIcon", "transition", "beforeClose", "doubleScale", "overlayStyle", "overlayClass", "startPosition", "swipeDuration", "showIndicators", "closeOnPopstate", "closeOnClickOverlay", "closeIconPosition", "initInstance", "setup", "state", "toggle", "onClosed", "showImagePreview", "options", "Array", "isArray", "open"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/image-preview/function-call.mjs"], "sourcesContent": ["import { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, inBrowser } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanImagePreview from \"./ImagePreview.mjs\";\nlet instance;\nconst defaultConfig = {\n  loop: true,\n  images: [],\n  maxZoom: 3,\n  minZoom: 1 / 3,\n  onScale: void 0,\n  onClose: void 0,\n  onChange: void 0,\n  vertical: false,\n  teleport: \"body\",\n  className: \"\",\n  showIndex: true,\n  closeable: false,\n  closeIcon: \"clear\",\n  transition: void 0,\n  beforeClose: void 0,\n  doubleScale: true,\n  overlayStyle: void 0,\n  overlayClass: void 0,\n  startPosition: 0,\n  swipeDuration: 300,\n  showIndicators: false,\n  closeOnPopstate: true,\n  closeOnClickOverlay: true,\n  closeIconPosition: \"top-right\"\n};\nfunction initInstance() {\n  ({\n    instance\n  } = mountComponent({\n    setup() {\n      const {\n        state,\n        toggle\n      } = usePopupState();\n      const onClosed = () => {\n        state.images = [];\n      };\n      return () => _createVNode(VanImagePreview, _mergeProps(state, {\n        \"onClosed\": onClosed,\n        \"onUpdate:show\": toggle\n      }), null);\n    }\n  }));\n}\nconst showImagePreview = (options, startPosition = 0) => {\n  if (!inBrowser) {\n    return;\n  }\n  if (!instance) {\n    initInstance();\n  }\n  options = Array.isArray(options) ? {\n    images: options,\n    startPosition\n  } : options;\n  instance.open(extend({}, defaultConfig, options));\n  return instance;\n};\nexport {\n  showImagePreview\n};\n"], "mappings": "AAAA,SAASA,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,MAAM,EAAEC,SAAS,QAAQ,oBAAoB;AACtD,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,OAAOC,eAAe,MAAM,oBAAoB;AAChD,IAAIC,QAAQ;AACZ,MAAMC,aAAa,GAAG;EACpBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC,GAAG,CAAC;EACdC,OAAO,EAAE,KAAK,CAAC;EACfC,OAAO,EAAE,KAAK,CAAC;EACfC,QAAQ,EAAE,KAAK,CAAC;EAChBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE,OAAO;EAClBC,UAAU,EAAE,KAAK,CAAC;EAClBC,WAAW,EAAE,KAAK,CAAC;EACnBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,KAAK,CAAC;EACpBC,YAAY,EAAE,KAAK,CAAC;EACpBC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,GAAG;EAClBC,cAAc,EAAE,KAAK;EACrBC,eAAe,EAAE,IAAI;EACrBC,mBAAmB,EAAE,IAAI;EACzBC,iBAAiB,EAAE;AACrB,CAAC;AACD,SAASC,YAAYA,CAAA,EAAG;EACtB,CAAC;IACC1B;EACF,CAAC,GAAGH,cAAc,CAAC;IACjB8B,KAAKA,CAAA,EAAG;MACN,MAAM;QACJC,KAAK;QACLC;MACF,CAAC,GAAG/B,aAAa,CAAC,CAAC;MACnB,MAAMgC,QAAQ,GAAGA,CAAA,KAAM;QACrBF,KAAK,CAACzB,MAAM,GAAG,EAAE;MACnB,CAAC;MACD,OAAO,MAAMT,YAAY,CAACK,eAAe,EAAEP,WAAW,CAACoC,KAAK,EAAE;QAC5D,UAAU,EAAEE,QAAQ;QACpB,eAAe,EAAED;MACnB,CAAC,CAAC,EAAE,IAAI,CAAC;IACX;EACF,CAAC,CAAC;AACJ;AACA,MAAME,gBAAgB,GAAGA,CAACC,OAAO,EAAEZ,aAAa,GAAG,CAAC,KAAK;EACvD,IAAI,CAACxB,SAAS,EAAE;IACd;EACF;EACA,IAAI,CAACI,QAAQ,EAAE;IACb0B,YAAY,CAAC,CAAC;EAChB;EACAM,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,GAAG;IACjC7B,MAAM,EAAE6B,OAAO;IACfZ;EACF,CAAC,GAAGY,OAAO;EACXhC,QAAQ,CAACmC,IAAI,CAACxC,MAAM,CAAC,CAAC,CAAC,EAAEM,aAAa,EAAE+B,OAAO,CAAC,CAAC;EACjD,OAAOhC,QAAQ;AACjB,CAAC;AACD,SACE+B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}