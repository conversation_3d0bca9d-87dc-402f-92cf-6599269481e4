{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, truthProp, numericProp, BORDER_LEFT, makeStringProp, BORDER_SURROUND, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"password-input\");\nconst passwordInputProps = {\n  info: String,\n  mask: truthProp,\n  value: makeStringProp(\"\"),\n  gutter: numericProp,\n  length: makeNumericProp(6),\n  focused: Boolean,\n  errorInfo: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: passwordInputProps,\n  emits: [\"focus\"],\n  setup(props, {\n    emit\n  }) {\n    const onTouchStart = event => {\n      event.stopPropagation();\n      emit(\"focus\", event);\n    };\n    const renderPoints = () => {\n      const Points = [];\n      const {\n        mask,\n        value,\n        gutter,\n        focused\n      } = props;\n      const length = +props.length;\n      for (let i = 0; i < length; i++) {\n        const char = value[i];\n        const showBorder = i !== 0 && !gutter;\n        const showCursor = focused && i === value.length;\n        let style;\n        if (i !== 0 && gutter) {\n          style = {\n            marginLeft: addUnit(gutter)\n          };\n        }\n        Points.push(_createVNode(\"li\", {\n          \"class\": [{\n            [BORDER_LEFT]: showBorder\n          }, bem(\"item\", {\n            focus: showCursor\n          })],\n          \"style\": style\n        }, [mask ? _createVNode(\"i\", {\n          \"style\": {\n            visibility: char ? \"visible\" : \"hidden\"\n          }\n        }, null) : char, showCursor && _createVNode(\"div\", {\n          \"class\": bem(\"cursor\")\n        }, null)]));\n      }\n      return Points;\n    };\n    return () => {\n      const info = props.errorInfo || props.info;\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(\"ul\", {\n        \"class\": [bem(\"security\"), {\n          [BORDER_SURROUND]: !props.gutter\n        }],\n        \"onTouchstartPassive\": onTouchStart\n      }, [renderPoints()]), info && _createVNode(\"div\", {\n        \"class\": bem(props.errorInfo ? \"error-info\" : \"info\")\n      }, [info])]);\n    };\n  }\n});\nexport { stdin_default as default, passwordInputProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "addUnit", "truthProp", "numericProp", "BORDER_LEFT", "makeStringProp", "BORDER_SURROUND", "createNamespace", "makeNumericProp", "name", "bem", "passwordInputProps", "info", "String", "mask", "value", "gutter", "length", "focused", "Boolean", "errorInfo", "stdin_default", "props", "emits", "setup", "emit", "onTouchStart", "event", "stopPropagation", "renderPoints", "Points", "i", "char", "showBorder", "showCursor", "style", "marginLeft", "push", "focus", "visibility", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/password-input/PasswordInput.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, truthProp, numericProp, BORDER_LEFT, makeStringProp, BORDER_SURROUND, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"password-input\");\nconst passwordInputProps = {\n  info: String,\n  mask: truthProp,\n  value: makeStringProp(\"\"),\n  gutter: numericProp,\n  length: makeNumericProp(6),\n  focused: Boolean,\n  errorInfo: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: passwordInputProps,\n  emits: [\"focus\"],\n  setup(props, {\n    emit\n  }) {\n    const onTouchStart = (event) => {\n      event.stopPropagation();\n      emit(\"focus\", event);\n    };\n    const renderPoints = () => {\n      const Points = [];\n      const {\n        mask,\n        value,\n        gutter,\n        focused\n      } = props;\n      const length = +props.length;\n      for (let i = 0; i < length; i++) {\n        const char = value[i];\n        const showBorder = i !== 0 && !gutter;\n        const showCursor = focused && i === value.length;\n        let style;\n        if (i !== 0 && gutter) {\n          style = {\n            marginLeft: addUnit(gutter)\n          };\n        }\n        Points.push(_createVNode(\"li\", {\n          \"class\": [{\n            [BORDER_LEFT]: showBorder\n          }, bem(\"item\", {\n            focus: showCursor\n          })],\n          \"style\": style\n        }, [mask ? _createVNode(\"i\", {\n          \"style\": {\n            visibility: char ? \"visible\" : \"hidden\"\n          }\n        }, null) : char, showCursor && _createVNode(\"div\", {\n          \"class\": bem(\"cursor\")\n        }, null)]));\n      }\n      return Points;\n    };\n    return () => {\n      const info = props.errorInfo || props.info;\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(\"ul\", {\n        \"class\": [bem(\"security\"), {\n          [BORDER_SURROUND]: !props.gutter\n        }],\n        \"onTouchstartPassive\": onTouchStart\n      }, [renderPoints()]), info && _createVNode(\"div\", {\n        \"class\": bem(props.errorInfo ? \"error-info\" : \"info\")\n      }, [info])]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  passwordInputProps\n};\n"], "mappings": ";AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACpJ,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGH,eAAe,CAAC,gBAAgB,CAAC;AACrD,MAAMI,kBAAkB,GAAG;EACzBC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAEZ,SAAS;EACfa,KAAK,EAAEV,cAAc,CAAC,EAAE,CAAC;EACzBW,MAAM,EAAEb,WAAW;EACnBc,MAAM,EAAET,eAAe,CAAC,CAAC,CAAC;EAC1BU,OAAO,EAAEC,OAAO;EAChBC,SAAS,EAAEP;AACb,CAAC;AACD,IAAIQ,aAAa,GAAGvB,eAAe,CAAC;EAClCW,IAAI;EACJa,KAAK,EAAEX,kBAAkB;EACzBY,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAKA,CAACF,KAAK,EAAE;IACXG;EACF,CAAC,EAAE;IACD,MAAMC,YAAY,GAAIC,KAAK,IAAK;MAC9BA,KAAK,CAACC,eAAe,CAAC,CAAC;MACvBH,IAAI,CAAC,OAAO,EAAEE,KAAK,CAAC;IACtB,CAAC;IACD,MAAME,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,MAAM,GAAG,EAAE;MACjB,MAAM;QACJhB,IAAI;QACJC,KAAK;QACLC,MAAM;QACNE;MACF,CAAC,GAAGI,KAAK;MACT,MAAML,MAAM,GAAG,CAACK,KAAK,CAACL,MAAM;MAC5B,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,MAAM,EAAEc,CAAC,EAAE,EAAE;QAC/B,MAAMC,IAAI,GAAGjB,KAAK,CAACgB,CAAC,CAAC;QACrB,MAAME,UAAU,GAAGF,CAAC,KAAK,CAAC,IAAI,CAACf,MAAM;QACrC,MAAMkB,UAAU,GAAGhB,OAAO,IAAIa,CAAC,KAAKhB,KAAK,CAACE,MAAM;QAChD,IAAIkB,KAAK;QACT,IAAIJ,CAAC,KAAK,CAAC,IAAIf,MAAM,EAAE;UACrBmB,KAAK,GAAG;YACNC,UAAU,EAAEnC,OAAO,CAACe,MAAM;UAC5B,CAAC;QACH;QACAc,MAAM,CAACO,IAAI,CAACrC,YAAY,CAAC,IAAI,EAAE;UAC7B,OAAO,EAAE,CAAC;YACR,CAACI,WAAW,GAAG6B;UACjB,CAAC,EAAEvB,GAAG,CAAC,MAAM,EAAE;YACb4B,KAAK,EAAEJ;UACT,CAAC,CAAC,CAAC;UACH,OAAO,EAAEC;QACX,CAAC,EAAE,CAACrB,IAAI,GAAGd,YAAY,CAAC,GAAG,EAAE;UAC3B,OAAO,EAAE;YACPuC,UAAU,EAAEP,IAAI,GAAG,SAAS,GAAG;UACjC;QACF,CAAC,EAAE,IAAI,CAAC,GAAGA,IAAI,EAAEE,UAAU,IAAIlC,YAAY,CAAC,KAAK,EAAE;UACjD,OAAO,EAAEU,GAAG,CAAC,QAAQ;QACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;MACb;MACA,OAAOoB,MAAM;IACf,CAAC;IACD,OAAO,MAAM;MACX,MAAMlB,IAAI,GAAGU,KAAK,CAACF,SAAS,IAAIE,KAAK,CAACV,IAAI;MAC1C,OAAOZ,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEU,GAAG,CAAC;MACf,CAAC,EAAE,CAACV,YAAY,CAAC,IAAI,EAAE;QACrB,OAAO,EAAE,CAACU,GAAG,CAAC,UAAU,CAAC,EAAE;UACzB,CAACJ,eAAe,GAAG,CAACgB,KAAK,CAACN;QAC5B,CAAC,CAAC;QACF,qBAAqB,EAAEU;MACzB,CAAC,EAAE,CAACG,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEjB,IAAI,IAAIZ,YAAY,CAAC,KAAK,EAAE;QAChD,OAAO,EAAEU,GAAG,CAACY,KAAK,CAACF,SAAS,GAAG,YAAY,GAAG,MAAM;MACtD,CAAC,EAAE,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACES,aAAa,IAAImB,OAAO,EACxB7B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}