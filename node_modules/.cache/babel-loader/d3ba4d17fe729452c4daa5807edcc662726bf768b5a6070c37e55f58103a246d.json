{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _SkeletonAvatar from \"./SkeletonAvatar.mjs\";\nconst SkeletonAvatar = withInstall(_SkeletonAvatar);\nvar stdin_default = SkeletonAvatar;\nimport { skeletonAvatarProps } from \"./SkeletonAvatar.mjs\";\nexport { SkeletonAvatar, stdin_default as default, skeletonAvatarProps };", "map": {"version": 3, "names": ["withInstall", "_SkeletonAvatar", "SkeletonAvatar", "stdin_default", "skeletonAvatarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/skeleton-avatar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _SkeletonAvatar from \"./SkeletonAvatar.mjs\";\nconst SkeletonAvatar = withInstall(_SkeletonAvatar);\nvar stdin_default = SkeletonAvatar;\nimport { skeletonAvatarProps } from \"./SkeletonAvatar.mjs\";\nexport {\n  SkeletonAvatar,\n  stdin_default as default,\n  skeletonAvatarProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,MAAMC,cAAc,GAAGF,WAAW,CAACC,eAAe,CAAC;AACnD,IAAIE,aAAa,GAAGD,cAAc;AAClC,SAASE,mBAAmB,QAAQ,sBAAsB;AAC1D,SACEF,cAAc,EACdC,aAAa,IAAIE,OAAO,EACxBD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}