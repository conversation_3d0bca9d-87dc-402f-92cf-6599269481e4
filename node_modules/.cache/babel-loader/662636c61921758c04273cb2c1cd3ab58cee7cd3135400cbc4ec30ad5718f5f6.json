{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { useRect } from \"@vant/use\";\nimport { loadImageAsync } from \"./util.mjs\";\nimport { noop } from \"../../utils/index.mjs\";\nclass ReactiveListener {\n  constructor({\n    el,\n    src,\n    error,\n    loading,\n    bindType,\n    $parent,\n    options,\n    cors,\n    elRenderer,\n    imageCache\n  }) {\n    this.el = el;\n    this.src = src;\n    this.error = error;\n    this.loading = loading;\n    this.bindType = bindType;\n    this.attempt = 0;\n    this.cors = cors;\n    this.naturalHeight = 0;\n    this.naturalWidth = 0;\n    this.options = options;\n    this.$parent = $parent;\n    this.elRenderer = elRenderer;\n    this.imageCache = imageCache;\n    this.performanceData = {\n      loadStart: 0,\n      loadEnd: 0\n    };\n    this.filter();\n    this.initState();\n    this.render(\"loading\", false);\n  }\n  /*\n   * init listener state\n   * @return\n   */\n  initState() {\n    if (\"dataset\" in this.el) {\n      this.el.dataset.src = this.src;\n    } else {\n      this.el.setAttribute(\"data-src\", this.src);\n    }\n    this.state = {\n      loading: false,\n      error: false,\n      loaded: false,\n      rendered: false\n    };\n  }\n  /*\n   * record performance\n   * @return\n   */\n  record(event) {\n    this.performanceData[event] = Date.now();\n  }\n  /*\n   * update image listener data\n   * @param  {String} image uri\n   * @param  {String} loading image uri\n   * @param  {String} error image uri\n   * @return\n   */\n  update({\n    src,\n    loading,\n    error\n  }) {\n    const oldSrc = this.src;\n    this.src = src;\n    this.loading = loading;\n    this.error = error;\n    this.filter();\n    if (oldSrc !== this.src) {\n      this.attempt = 0;\n      this.initState();\n    }\n  }\n  /*\n   *  check el is in view\n   * @return {Boolean} el is in view\n   */\n  checkInView() {\n    const rect = useRect(this.el);\n    return rect.top < window.innerHeight * this.options.preLoad && rect.bottom > this.options.preLoadTop && rect.left < window.innerWidth * this.options.preLoad && rect.right > 0;\n  }\n  /*\n   * listener filter\n   */\n  filter() {\n    Object.keys(this.options.filter).forEach(key => {\n      this.options.filter[key](this, this.options);\n    });\n  }\n  /*\n   * render loading first\n   * @params cb:Function\n   * @return\n   */\n  renderLoading(cb) {\n    this.state.loading = true;\n    loadImageAsync({\n      src: this.loading,\n      cors: this.cors\n    }, () => {\n      this.render(\"loading\", false);\n      this.state.loading = false;\n      cb();\n    }, () => {\n      cb();\n      this.state.loading = false;\n      if (process.env.NODE_ENV !== \"production\" && !this.options.silent) console.warn(`[@vant/lazyload] load failed with loading image(${this.loading})`);\n    });\n  }\n  /*\n   * try load image and  render it\n   * @return\n   */\n  load(onFinish = noop) {\n    if (this.attempt > this.options.attempt - 1 && this.state.error) {\n      if (process.env.NODE_ENV !== \"production\" && !this.options.silent) {\n        console.log(`[@vant/lazyload] ${this.src} tried too more than ${this.options.attempt} times`);\n      }\n      onFinish();\n      return;\n    }\n    if (this.state.rendered && this.state.loaded) return;\n    if (this.imageCache.has(this.src)) {\n      this.state.loaded = true;\n      this.render(\"loaded\", true);\n      this.state.rendered = true;\n      return onFinish();\n    }\n    this.renderLoading(() => {\n      var _a, _b;\n      this.attempt++;\n      (_b = (_a = this.options.adapter).beforeLoad) == null ? void 0 : _b.call(_a, this, this.options);\n      this.record(\"loadStart\");\n      loadImageAsync({\n        src: this.src,\n        cors: this.cors\n      }, data => {\n        this.naturalHeight = data.naturalHeight;\n        this.naturalWidth = data.naturalWidth;\n        this.state.loaded = true;\n        this.state.error = false;\n        this.record(\"loadEnd\");\n        this.render(\"loaded\", false);\n        this.state.rendered = true;\n        this.imageCache.add(this.src);\n        onFinish();\n      }, err => {\n        !this.options.silent && console.error(err);\n        this.state.error = true;\n        this.state.loaded = false;\n        this.render(\"error\", false);\n      });\n    });\n  }\n  /*\n   * render image\n   * @param  {String} state to render // ['loading', 'src', 'error']\n   * @param  {String} is form cache\n   * @return\n   */\n  render(state, cache) {\n    this.elRenderer(this, state, cache);\n  }\n  /*\n   * output performance data\n   * @return {Object} performance data\n   */\n  performance() {\n    let state = \"loading\";\n    let time = 0;\n    if (this.state.loaded) {\n      state = \"loaded\";\n      time = (this.performanceData.loadEnd - this.performanceData.loadStart) / 1e3;\n    }\n    if (this.state.error) state = \"error\";\n    return {\n      src: this.src,\n      state,\n      time\n    };\n  }\n  /*\n   * $destroy\n   * @return\n   */\n  $destroy() {\n    this.el = null;\n    this.src = null;\n    this.error = null;\n    this.loading = null;\n    this.bindType = null;\n    this.attempt = 0;\n  }\n}\nexport { ReactiveListener as default };", "map": {"version": 3, "names": ["useRect", "loadImageAsync", "noop", "ReactiveListener", "constructor", "el", "src", "error", "loading", "bindType", "$parent", "options", "cors", "<PERSON><PERSON><PERSON><PERSON>", "imageCache", "attempt", "naturalHeight", "naturalWidth", "performanceData", "loadStart", "loadEnd", "filter", "initState", "render", "dataset", "setAttribute", "state", "loaded", "rendered", "record", "event", "Date", "now", "update", "oldSrc", "checkInView", "rect", "top", "window", "innerHeight", "preLoad", "bottom", "preLoadTop", "left", "innerWidth", "right", "Object", "keys", "for<PERSON>ach", "key", "renderLoading", "cb", "process", "env", "NODE_ENV", "silent", "console", "warn", "load", "onFinish", "log", "has", "_a", "_b", "adapter", "beforeLoad", "call", "data", "add", "err", "cache", "performance", "time", "$destroy", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/lazyload/vue-lazyload/listener.mjs"], "sourcesContent": ["import { useRect } from \"@vant/use\";\nimport { loadImageAsync } from \"./util.mjs\";\nimport { noop } from \"../../utils/index.mjs\";\nclass ReactiveListener {\n  constructor({\n    el,\n    src,\n    error,\n    loading,\n    bindType,\n    $parent,\n    options,\n    cors,\n    elRenderer,\n    imageCache\n  }) {\n    this.el = el;\n    this.src = src;\n    this.error = error;\n    this.loading = loading;\n    this.bindType = bindType;\n    this.attempt = 0;\n    this.cors = cors;\n    this.naturalHeight = 0;\n    this.naturalWidth = 0;\n    this.options = options;\n    this.$parent = $parent;\n    this.elRenderer = elRenderer;\n    this.imageCache = imageCache;\n    this.performanceData = {\n      loadStart: 0,\n      loadEnd: 0\n    };\n    this.filter();\n    this.initState();\n    this.render(\"loading\", false);\n  }\n  /*\n   * init listener state\n   * @return\n   */\n  initState() {\n    if (\"dataset\" in this.el) {\n      this.el.dataset.src = this.src;\n    } else {\n      this.el.setAttribute(\"data-src\", this.src);\n    }\n    this.state = {\n      loading: false,\n      error: false,\n      loaded: false,\n      rendered: false\n    };\n  }\n  /*\n   * record performance\n   * @return\n   */\n  record(event) {\n    this.performanceData[event] = Date.now();\n  }\n  /*\n   * update image listener data\n   * @param  {String} image uri\n   * @param  {String} loading image uri\n   * @param  {String} error image uri\n   * @return\n   */\n  update({ src, loading, error }) {\n    const oldSrc = this.src;\n    this.src = src;\n    this.loading = loading;\n    this.error = error;\n    this.filter();\n    if (oldSrc !== this.src) {\n      this.attempt = 0;\n      this.initState();\n    }\n  }\n  /*\n   *  check el is in view\n   * @return {Boolean} el is in view\n   */\n  checkInView() {\n    const rect = useRect(this.el);\n    return rect.top < window.innerHeight * this.options.preLoad && rect.bottom > this.options.preLoadTop && rect.left < window.innerWidth * this.options.preLoad && rect.right > 0;\n  }\n  /*\n   * listener filter\n   */\n  filter() {\n    Object.keys(this.options.filter).forEach((key) => {\n      this.options.filter[key](this, this.options);\n    });\n  }\n  /*\n   * render loading first\n   * @params cb:Function\n   * @return\n   */\n  renderLoading(cb) {\n    this.state.loading = true;\n    loadImageAsync(\n      {\n        src: this.loading,\n        cors: this.cors\n      },\n      () => {\n        this.render(\"loading\", false);\n        this.state.loading = false;\n        cb();\n      },\n      () => {\n        cb();\n        this.state.loading = false;\n        if (process.env.NODE_ENV !== \"production\" && !this.options.silent)\n          console.warn(\n            `[@vant/lazyload] load failed with loading image(${this.loading})`\n          );\n      }\n    );\n  }\n  /*\n   * try load image and  render it\n   * @return\n   */\n  load(onFinish = noop) {\n    if (this.attempt > this.options.attempt - 1 && this.state.error) {\n      if (process.env.NODE_ENV !== \"production\" && !this.options.silent) {\n        console.log(\n          `[@vant/lazyload] ${this.src} tried too more than ${this.options.attempt} times`\n        );\n      }\n      onFinish();\n      return;\n    }\n    if (this.state.rendered && this.state.loaded) return;\n    if (this.imageCache.has(this.src)) {\n      this.state.loaded = true;\n      this.render(\"loaded\", true);\n      this.state.rendered = true;\n      return onFinish();\n    }\n    this.renderLoading(() => {\n      var _a, _b;\n      this.attempt++;\n      (_b = (_a = this.options.adapter).beforeLoad) == null ? void 0 : _b.call(_a, this, this.options);\n      this.record(\"loadStart\");\n      loadImageAsync(\n        {\n          src: this.src,\n          cors: this.cors\n        },\n        (data) => {\n          this.naturalHeight = data.naturalHeight;\n          this.naturalWidth = data.naturalWidth;\n          this.state.loaded = true;\n          this.state.error = false;\n          this.record(\"loadEnd\");\n          this.render(\"loaded\", false);\n          this.state.rendered = true;\n          this.imageCache.add(this.src);\n          onFinish();\n        },\n        (err) => {\n          !this.options.silent && console.error(err);\n          this.state.error = true;\n          this.state.loaded = false;\n          this.render(\"error\", false);\n        }\n      );\n    });\n  }\n  /*\n   * render image\n   * @param  {String} state to render // ['loading', 'src', 'error']\n   * @param  {String} is form cache\n   * @return\n   */\n  render(state, cache) {\n    this.elRenderer(this, state, cache);\n  }\n  /*\n   * output performance data\n   * @return {Object} performance data\n   */\n  performance() {\n    let state = \"loading\";\n    let time = 0;\n    if (this.state.loaded) {\n      state = \"loaded\";\n      time = (this.performanceData.loadEnd - this.performanceData.loadStart) / 1e3;\n    }\n    if (this.state.error) state = \"error\";\n    return {\n      src: this.src,\n      state,\n      time\n    };\n  }\n  /*\n   * $destroy\n   * @return\n   */\n  $destroy() {\n    this.el = null;\n    this.src = null;\n    this.error = null;\n    this.loading = null;\n    this.bindType = null;\n    this.attempt = 0;\n  }\n}\nexport {\n  ReactiveListener as default\n};\n"], "mappings": ";;;AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAC;IACVC,EAAE;IACFC,GAAG;IACHC,KAAK;IACLC,OAAO;IACPC,QAAQ;IACRC,OAAO;IACPC,OAAO;IACPC,IAAI;IACJC,UAAU;IACVC;EACF,CAAC,EAAE;IACD,IAAI,CAACT,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACM,OAAO,GAAG,CAAC;IAChB,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACI,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACN,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACI,eAAe,GAAG;MACrBC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE;IACX,CAAC;IACD,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAI,CAACC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC;EAC/B;EACA;AACF;AACA;AACA;EACED,SAASA,CAAA,EAAG;IACV,IAAI,SAAS,IAAI,IAAI,CAACjB,EAAE,EAAE;MACxB,IAAI,CAACA,EAAE,CAACmB,OAAO,CAAClB,GAAG,GAAG,IAAI,CAACA,GAAG;IAChC,CAAC,MAAM;MACL,IAAI,CAACD,EAAE,CAACoB,YAAY,CAAC,UAAU,EAAE,IAAI,CAACnB,GAAG,CAAC;IAC5C;IACA,IAAI,CAACoB,KAAK,GAAG;MACXlB,OAAO,EAAE,KAAK;MACdD,KAAK,EAAE,KAAK;MACZoB,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE;IACZ,CAAC;EACH;EACA;AACF;AACA;AACA;EACEC,MAAMA,CAACC,KAAK,EAAE;IACZ,IAAI,CAACZ,eAAe,CAACY,KAAK,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC1C;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAAC;IAAE3B,GAAG;IAAEE,OAAO;IAAED;EAAM,CAAC,EAAE;IAC9B,MAAM2B,MAAM,GAAG,IAAI,CAAC5B,GAAG;IACvB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACc,MAAM,CAAC,CAAC;IACb,IAAIa,MAAM,KAAK,IAAI,CAAC5B,GAAG,EAAE;MACvB,IAAI,CAACS,OAAO,GAAG,CAAC;MAChB,IAAI,CAACO,SAAS,CAAC,CAAC;IAClB;EACF;EACA;AACF;AACA;AACA;EACEa,WAAWA,CAAA,EAAG;IACZ,MAAMC,IAAI,GAAGpC,OAAO,CAAC,IAAI,CAACK,EAAE,CAAC;IAC7B,OAAO+B,IAAI,CAACC,GAAG,GAAGC,MAAM,CAACC,WAAW,GAAG,IAAI,CAAC5B,OAAO,CAAC6B,OAAO,IAAIJ,IAAI,CAACK,MAAM,GAAG,IAAI,CAAC9B,OAAO,CAAC+B,UAAU,IAAIN,IAAI,CAACO,IAAI,GAAGL,MAAM,CAACM,UAAU,GAAG,IAAI,CAACjC,OAAO,CAAC6B,OAAO,IAAIJ,IAAI,CAACS,KAAK,GAAG,CAAC;EAChL;EACA;AACF;AACA;EACExB,MAAMA,CAAA,EAAG;IACPyB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpC,OAAO,CAACU,MAAM,CAAC,CAAC2B,OAAO,CAAEC,GAAG,IAAK;MAChD,IAAI,CAACtC,OAAO,CAACU,MAAM,CAAC4B,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAACtC,OAAO,CAAC;IAC9C,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;EACEuC,aAAaA,CAACC,EAAE,EAAE;IAChB,IAAI,CAACzB,KAAK,CAAClB,OAAO,GAAG,IAAI;IACzBP,cAAc,CACZ;MACEK,GAAG,EAAE,IAAI,CAACE,OAAO;MACjBI,IAAI,EAAE,IAAI,CAACA;IACb,CAAC,EACD,MAAM;MACJ,IAAI,CAACW,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC;MAC7B,IAAI,CAACG,KAAK,CAAClB,OAAO,GAAG,KAAK;MAC1B2C,EAAE,CAAC,CAAC;IACN,CAAC,EACD,MAAM;MACJA,EAAE,CAAC,CAAC;MACJ,IAAI,CAACzB,KAAK,CAAClB,OAAO,GAAG,KAAK;MAC1B,IAAI4C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC3C,OAAO,CAAC4C,MAAM,EAC/DC,OAAO,CAACC,IAAI,CACV,mDAAmD,IAAI,CAACjD,OAAO,GACjE,CAAC;IACL,CACF,CAAC;EACH;EACA;AACF;AACA;AACA;EACEkD,IAAIA,CAACC,QAAQ,GAAGzD,IAAI,EAAE;IACpB,IAAI,IAAI,CAACa,OAAO,GAAG,IAAI,CAACJ,OAAO,CAACI,OAAO,GAAG,CAAC,IAAI,IAAI,CAACW,KAAK,CAACnB,KAAK,EAAE;MAC/D,IAAI6C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC3C,OAAO,CAAC4C,MAAM,EAAE;QACjEC,OAAO,CAACI,GAAG,CACT,oBAAoB,IAAI,CAACtD,GAAG,wBAAwB,IAAI,CAACK,OAAO,CAACI,OAAO,QAC1E,CAAC;MACH;MACA4C,QAAQ,CAAC,CAAC;MACV;IACF;IACA,IAAI,IAAI,CAACjC,KAAK,CAACE,QAAQ,IAAI,IAAI,CAACF,KAAK,CAACC,MAAM,EAAE;IAC9C,IAAI,IAAI,CAACb,UAAU,CAAC+C,GAAG,CAAC,IAAI,CAACvD,GAAG,CAAC,EAAE;MACjC,IAAI,CAACoB,KAAK,CAACC,MAAM,GAAG,IAAI;MACxB,IAAI,CAACJ,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;MAC3B,IAAI,CAACG,KAAK,CAACE,QAAQ,GAAG,IAAI;MAC1B,OAAO+B,QAAQ,CAAC,CAAC;IACnB;IACA,IAAI,CAACT,aAAa,CAAC,MAAM;MACvB,IAAIY,EAAE,EAAEC,EAAE;MACV,IAAI,CAAChD,OAAO,EAAE;MACd,CAACgD,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACnD,OAAO,CAACqD,OAAO,EAAEC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,IAAI,CAACJ,EAAE,EAAE,IAAI,EAAE,IAAI,CAACnD,OAAO,CAAC;MAChG,IAAI,CAACkB,MAAM,CAAC,WAAW,CAAC;MACxB5B,cAAc,CACZ;QACEK,GAAG,EAAE,IAAI,CAACA,GAAG;QACbM,IAAI,EAAE,IAAI,CAACA;MACb,CAAC,EACAuD,IAAI,IAAK;QACR,IAAI,CAACnD,aAAa,GAAGmD,IAAI,CAACnD,aAAa;QACvC,IAAI,CAACC,YAAY,GAAGkD,IAAI,CAAClD,YAAY;QACrC,IAAI,CAACS,KAAK,CAACC,MAAM,GAAG,IAAI;QACxB,IAAI,CAACD,KAAK,CAACnB,KAAK,GAAG,KAAK;QACxB,IAAI,CAACsB,MAAM,CAAC,SAAS,CAAC;QACtB,IAAI,CAACN,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;QAC5B,IAAI,CAACG,KAAK,CAACE,QAAQ,GAAG,IAAI;QAC1B,IAAI,CAACd,UAAU,CAACsD,GAAG,CAAC,IAAI,CAAC9D,GAAG,CAAC;QAC7BqD,QAAQ,CAAC,CAAC;MACZ,CAAC,EACAU,GAAG,IAAK;QACP,CAAC,IAAI,CAAC1D,OAAO,CAAC4C,MAAM,IAAIC,OAAO,CAACjD,KAAK,CAAC8D,GAAG,CAAC;QAC1C,IAAI,CAAC3C,KAAK,CAACnB,KAAK,GAAG,IAAI;QACvB,IAAI,CAACmB,KAAK,CAACC,MAAM,GAAG,KAAK;QACzB,IAAI,CAACJ,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC;MAC7B,CACF,CAAC;IACH,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;EACEA,MAAMA,CAACG,KAAK,EAAE4C,KAAK,EAAE;IACnB,IAAI,CAACzD,UAAU,CAAC,IAAI,EAAEa,KAAK,EAAE4C,KAAK,CAAC;EACrC;EACA;AACF;AACA;AACA;EACEC,WAAWA,CAAA,EAAG;IACZ,IAAI7C,KAAK,GAAG,SAAS;IACrB,IAAI8C,IAAI,GAAG,CAAC;IACZ,IAAI,IAAI,CAAC9C,KAAK,CAACC,MAAM,EAAE;MACrBD,KAAK,GAAG,QAAQ;MAChB8C,IAAI,GAAG,CAAC,IAAI,CAACtD,eAAe,CAACE,OAAO,GAAG,IAAI,CAACF,eAAe,CAACC,SAAS,IAAI,GAAG;IAC9E;IACA,IAAI,IAAI,CAACO,KAAK,CAACnB,KAAK,EAAEmB,KAAK,GAAG,OAAO;IACrC,OAAO;MACLpB,GAAG,EAAE,IAAI,CAACA,GAAG;MACboB,KAAK;MACL8C;IACF,CAAC;EACH;EACA;AACF;AACA;AACA;EACEC,QAAQA,CAAA,EAAG;IACT,IAAI,CAACpE,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACM,OAAO,GAAG,CAAC;EAClB;AACF;AACA,SACEZ,gBAAgB,IAAIuE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}