{"ast": null, "code": "import { ref, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nconst [name, bem, t] = createNamespace(\"submit-bar\");\nconst submitBarProps = {\n  tip: String,\n  label: String,\n  price: Number,\n  tipIcon: String,\n  loading: Boolean,\n  currency: makeStringProp(\"\\xA5\"),\n  disabled: Boolean,\n  textAlign: String,\n  buttonText: String,\n  buttonType: makeStringProp(\"danger\"),\n  buttonColor: String,\n  suffixLabel: String,\n  placeholder: Boolean,\n  decimalLength: makeNumericProp(2),\n  safeAreaInsetBottom: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: submitBarProps,\n  emits: [\"submit\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const renderPlaceholder = usePlaceholder(root, bem);\n    const renderText = () => {\n      const {\n        price,\n        label,\n        currency,\n        textAlign,\n        suffixLabel,\n        decimalLength\n      } = props;\n      if (typeof price === \"number\") {\n        const pricePair = (price / 100).toFixed(+decimalLength).split(\".\");\n        const decimal = decimalLength ? `.${pricePair[1]}` : \"\";\n        return _createVNode(\"div\", {\n          \"class\": bem(\"text\"),\n          \"style\": {\n            textAlign\n          }\n        }, [_createVNode(\"span\", null, [label || t(\"label\")]), _createVNode(\"span\", {\n          \"class\": bem(\"price\")\n        }, [currency, _createVNode(\"span\", {\n          \"class\": bem(\"price-integer\")\n        }, [pricePair[0]]), decimal]), suffixLabel && _createVNode(\"span\", {\n          \"class\": bem(\"suffix-label\")\n        }, [suffixLabel])]);\n      }\n    };\n    const renderTip = () => {\n      var _a;\n      const {\n        tip,\n        tipIcon\n      } = props;\n      if (slots.tip || tip) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"tip\")\n        }, [tipIcon && _createVNode(Icon, {\n          \"class\": bem(\"tip-icon\"),\n          \"name\": tipIcon\n        }, null), tip && _createVNode(\"span\", {\n          \"class\": bem(\"tip-text\")\n        }, [tip]), (_a = slots.tip) == null ? void 0 : _a.call(slots)]);\n      }\n    };\n    const onClickButton = () => emit(\"submit\");\n    const renderButton = () => {\n      if (slots.button) {\n        return slots.button();\n      }\n      return _createVNode(Button, {\n        \"round\": true,\n        \"type\": props.buttonType,\n        \"text\": props.buttonText,\n        \"class\": bem(\"button\", props.buttonType),\n        \"color\": props.buttonColor,\n        \"loading\": props.loading,\n        \"disabled\": props.disabled,\n        \"onClick\": onClickButton\n      }, null);\n    };\n    const renderSubmitBar = () => {\n      var _a, _b;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": [bem(), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }]\n      }, [(_a = slots.top) == null ? void 0 : _a.call(slots), renderTip(), _createVNode(\"div\", {\n        \"class\": bem(\"bar\")\n      }, [(_b = slots.default) == null ? void 0 : _b.call(slots), renderText(), renderButton()])]);\n    };\n    return () => {\n      if (props.placeholder) {\n        return renderPlaceholder(renderSubmitBar);\n      }\n      return renderSubmitBar();\n    };\n  }\n});\nexport { stdin_default as default, submitBarProps };", "map": {"version": 3, "names": ["ref", "defineComponent", "createVNode", "_createVNode", "truthProp", "makeStringProp", "makeNumericProp", "createNamespace", "Icon", "<PERSON><PERSON>", "usePlaceholder", "name", "bem", "t", "submitBarProps", "tip", "String", "label", "price", "Number", "tipIcon", "loading", "Boolean", "currency", "disabled", "textAlign", "buttonText", "buttonType", "buttonColor", "suffix<PERSON>abel", "placeholder", "decimalLength", "safeAreaInsetBottom", "stdin_default", "props", "emits", "setup", "emit", "slots", "root", "renderPlaceholder", "renderText", "pricePair", "toFixed", "split", "decimal", "renderTip", "_a", "call", "onClickButton", "renderButton", "button", "renderSubmitBar", "_b", "top", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/submit-bar/SubmitBar.mjs"], "sourcesContent": ["import { ref, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nconst [name, bem, t] = createNamespace(\"submit-bar\");\nconst submitBarProps = {\n  tip: String,\n  label: String,\n  price: Number,\n  tipIcon: String,\n  loading: Boolean,\n  currency: makeStringProp(\"\\xA5\"),\n  disabled: Boolean,\n  textAlign: String,\n  buttonText: String,\n  buttonType: makeStringProp(\"danger\"),\n  buttonColor: String,\n  suffixLabel: String,\n  placeholder: Boolean,\n  decimalLength: makeNumericProp(2),\n  safeAreaInsetBottom: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: submitBarProps,\n  emits: [\"submit\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const renderPlaceholder = usePlaceholder(root, bem);\n    const renderText = () => {\n      const {\n        price,\n        label,\n        currency,\n        textAlign,\n        suffixLabel,\n        decimalLength\n      } = props;\n      if (typeof price === \"number\") {\n        const pricePair = (price / 100).toFixed(+decimalLength).split(\".\");\n        const decimal = decimalLength ? `.${pricePair[1]}` : \"\";\n        return _createVNode(\"div\", {\n          \"class\": bem(\"text\"),\n          \"style\": {\n            textAlign\n          }\n        }, [_createVNode(\"span\", null, [label || t(\"label\")]), _createVNode(\"span\", {\n          \"class\": bem(\"price\")\n        }, [currency, _createVNode(\"span\", {\n          \"class\": bem(\"price-integer\")\n        }, [pricePair[0]]), decimal]), suffixLabel && _createVNode(\"span\", {\n          \"class\": bem(\"suffix-label\")\n        }, [suffixLabel])]);\n      }\n    };\n    const renderTip = () => {\n      var _a;\n      const {\n        tip,\n        tipIcon\n      } = props;\n      if (slots.tip || tip) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"tip\")\n        }, [tipIcon && _createVNode(Icon, {\n          \"class\": bem(\"tip-icon\"),\n          \"name\": tipIcon\n        }, null), tip && _createVNode(\"span\", {\n          \"class\": bem(\"tip-text\")\n        }, [tip]), (_a = slots.tip) == null ? void 0 : _a.call(slots)]);\n      }\n    };\n    const onClickButton = () => emit(\"submit\");\n    const renderButton = () => {\n      if (slots.button) {\n        return slots.button();\n      }\n      return _createVNode(Button, {\n        \"round\": true,\n        \"type\": props.buttonType,\n        \"text\": props.buttonText,\n        \"class\": bem(\"button\", props.buttonType),\n        \"color\": props.buttonColor,\n        \"loading\": props.loading,\n        \"disabled\": props.disabled,\n        \"onClick\": onClickButton\n      }, null);\n    };\n    const renderSubmitBar = () => {\n      var _a, _b;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": [bem(), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }]\n      }, [(_a = slots.top) == null ? void 0 : _a.call(slots), renderTip(), _createVNode(\"div\", {\n        \"class\": bem(\"bar\")\n      }, [(_b = slots.default) == null ? void 0 : _b.call(slots), renderText(), renderButton()])]);\n    };\n    return () => {\n      if (props.placeholder) {\n        return renderPlaceholder(renderSubmitBar);\n      }\n      return renderSubmitBar();\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  submitBarProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACvE,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAChG,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGN,eAAe,CAAC,YAAY,CAAC;AACpD,MAAMO,cAAc,GAAG;EACrBC,GAAG,EAAEC,MAAM;EACXC,KAAK,EAAED,MAAM;EACbE,KAAK,EAAEC,MAAM;EACbC,OAAO,EAAEJ,MAAM;EACfK,OAAO,EAAEC,OAAO;EAChBC,QAAQ,EAAElB,cAAc,CAAC,MAAM,CAAC;EAChCmB,QAAQ,EAAEF,OAAO;EACjBG,SAAS,EAAET,MAAM;EACjBU,UAAU,EAAEV,MAAM;EAClBW,UAAU,EAAEtB,cAAc,CAAC,QAAQ,CAAC;EACpCuB,WAAW,EAAEZ,MAAM;EACnBa,WAAW,EAAEb,MAAM;EACnBc,WAAW,EAAER,OAAO;EACpBS,aAAa,EAAEzB,eAAe,CAAC,CAAC,CAAC;EACjC0B,mBAAmB,EAAE5B;AACvB,CAAC;AACD,IAAI6B,aAAa,GAAGhC,eAAe,CAAC;EAClCU,IAAI;EACJuB,KAAK,EAAEpB,cAAc;EACrBqB,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAGvC,GAAG,CAAC,CAAC;IAClB,MAAMwC,iBAAiB,GAAG9B,cAAc,CAAC6B,IAAI,EAAE3B,GAAG,CAAC;IACnD,MAAM6B,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAM;QACJvB,KAAK;QACLD,KAAK;QACLM,QAAQ;QACRE,SAAS;QACTI,WAAW;QACXE;MACF,CAAC,GAAGG,KAAK;MACT,IAAI,OAAOhB,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMwB,SAAS,GAAG,CAACxB,KAAK,GAAG,GAAG,EAAEyB,OAAO,CAAC,CAACZ,aAAa,CAAC,CAACa,KAAK,CAAC,GAAG,CAAC;QAClE,MAAMC,OAAO,GAAGd,aAAa,GAAG,IAAIW,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;QACvD,OAAOvC,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAES,GAAG,CAAC,MAAM,CAAC;UACpB,OAAO,EAAE;YACPa;UACF;QACF,CAAC,EAAE,CAACtB,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACc,KAAK,IAAIJ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAEV,YAAY,CAAC,MAAM,EAAE;UAC1E,OAAO,EAAES,GAAG,CAAC,OAAO;QACtB,CAAC,EAAE,CAACW,QAAQ,EAAEpB,YAAY,CAAC,MAAM,EAAE;UACjC,OAAO,EAAES,GAAG,CAAC,eAAe;QAC9B,CAAC,EAAE,CAAC8B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEG,OAAO,CAAC,CAAC,EAAEhB,WAAW,IAAI1B,YAAY,CAAC,MAAM,EAAE;UACjE,OAAO,EAAES,GAAG,CAAC,cAAc;QAC7B,CAAC,EAAE,CAACiB,WAAW,CAAC,CAAC,CAAC,CAAC;MACrB;IACF,CAAC;IACD,MAAMiB,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAIC,EAAE;MACN,MAAM;QACJhC,GAAG;QACHK;MACF,CAAC,GAAGc,KAAK;MACT,IAAII,KAAK,CAACvB,GAAG,IAAIA,GAAG,EAAE;QACpB,OAAOZ,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAES,GAAG,CAAC,KAAK;QACpB,CAAC,EAAE,CAACQ,OAAO,IAAIjB,YAAY,CAACK,IAAI,EAAE;UAChC,OAAO,EAAEI,GAAG,CAAC,UAAU,CAAC;UACxB,MAAM,EAAEQ;QACV,CAAC,EAAE,IAAI,CAAC,EAAEL,GAAG,IAAIZ,YAAY,CAAC,MAAM,EAAE;UACpC,OAAO,EAAES,GAAG,CAAC,UAAU;QACzB,CAAC,EAAE,CAACG,GAAG,CAAC,CAAC,EAAE,CAACgC,EAAE,GAAGT,KAAK,CAACvB,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgC,EAAE,CAACC,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC;MACjE;IACF,CAAC;IACD,MAAMW,aAAa,GAAGA,CAAA,KAAMZ,IAAI,CAAC,QAAQ,CAAC;IAC1C,MAAMa,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIZ,KAAK,CAACa,MAAM,EAAE;QAChB,OAAOb,KAAK,CAACa,MAAM,CAAC,CAAC;MACvB;MACA,OAAOhD,YAAY,CAACM,MAAM,EAAE;QAC1B,OAAO,EAAE,IAAI;QACb,MAAM,EAAEyB,KAAK,CAACP,UAAU;QACxB,MAAM,EAAEO,KAAK,CAACR,UAAU;QACxB,OAAO,EAAEd,GAAG,CAAC,QAAQ,EAAEsB,KAAK,CAACP,UAAU,CAAC;QACxC,OAAO,EAAEO,KAAK,CAACN,WAAW;QAC1B,SAAS,EAAEM,KAAK,CAACb,OAAO;QACxB,UAAU,EAAEa,KAAK,CAACV,QAAQ;QAC1B,SAAS,EAAEyB;MACb,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,MAAMG,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIL,EAAE,EAAEM,EAAE;MACV,OAAOlD,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEoC,IAAI;QACX,OAAO,EAAE,CAAC3B,GAAG,CAAC,CAAC,EAAE;UACf,sBAAsB,EAAEsB,KAAK,CAACF;QAChC,CAAC;MACH,CAAC,EAAE,CAAC,CAACe,EAAE,GAAGT,KAAK,CAACgB,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACC,IAAI,CAACV,KAAK,CAAC,EAAEQ,SAAS,CAAC,CAAC,EAAE3C,YAAY,CAAC,KAAK,EAAE;QACvF,OAAO,EAAES,GAAG,CAAC,KAAK;MACpB,CAAC,EAAE,CAAC,CAACyC,EAAE,GAAGf,KAAK,CAACiB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACL,IAAI,CAACV,KAAK,CAAC,EAAEG,UAAU,CAAC,CAAC,EAAES,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC;IACD,OAAO,MAAM;MACX,IAAIhB,KAAK,CAACJ,WAAW,EAAE;QACrB,OAAOU,iBAAiB,CAACY,eAAe,CAAC;MAC3C;MACA,OAAOA,eAAe,CAAC,CAAC;IAC1B,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEnB,aAAa,IAAIsB,OAAO,EACxBzC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}