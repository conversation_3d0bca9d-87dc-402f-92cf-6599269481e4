{"ast": null, "code": "import { ref, watch, computed, nextTick, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nimport { cellSharedProps } from \"../cell/Cell.mjs\";\nimport { pick, extend, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { COLLAPSE_KEY } from \"../collapse/Collapse.mjs\";\nimport { raf, doubleRaf, useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { useLazyRender } from \"../composables/use-lazy-render.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem] = createNamespace(\"collapse-item\");\nconst CELL_SLOTS = [\"icon\", \"title\", \"value\", \"label\", \"right-icon\"];\nconst collapseItemProps = extend({}, cellSharedProps, {\n  name: numericProp,\n  isLink: truthProp,\n  disabled: <PERSON><PERSON><PERSON>,\n  readonly: <PERSON><PERSON><PERSON>,\n  lazyRender: truthProp\n});\nvar stdin_default = defineComponent({\n  name,\n  props: collapseItemProps,\n  setup(props, {\n    slots\n  }) {\n    const wrapperRef = ref();\n    const contentRef = ref();\n    const {\n      parent,\n      index\n    } = useParent(COLLAPSE_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <CollapseItem> must be a child component of <Collapse>.\");\n      }\n      return;\n    }\n    const name2 = computed(() => {\n      var _a;\n      return (_a = props.name) != null ? _a : index.value;\n    });\n    const expanded = computed(() => parent.isExpanded(name2.value));\n    const show = ref(expanded.value);\n    const lazyRender = useLazyRender(() => show.value || !props.lazyRender);\n    const onTransitionEnd = () => {\n      if (!expanded.value) {\n        show.value = false;\n      } else if (wrapperRef.value) {\n        wrapperRef.value.style.height = \"\";\n      }\n    };\n    watch(expanded, (value, oldValue) => {\n      if (oldValue === null) {\n        return;\n      }\n      if (value) {\n        show.value = true;\n      }\n      const tick = value ? nextTick : raf;\n      tick(() => {\n        if (!contentRef.value || !wrapperRef.value) {\n          return;\n        }\n        const {\n          offsetHeight\n        } = contentRef.value;\n        if (offsetHeight) {\n          const contentHeight = `${offsetHeight}px`;\n          wrapperRef.value.style.height = value ? \"0\" : contentHeight;\n          doubleRaf(() => {\n            if (wrapperRef.value) {\n              wrapperRef.value.style.height = value ? contentHeight : \"0\";\n            }\n          });\n        } else {\n          onTransitionEnd();\n        }\n      });\n    });\n    const toggle = (newValue = !expanded.value) => {\n      parent.toggle(name2.value, newValue);\n    };\n    const onClickTitle = () => {\n      if (!props.disabled && !props.readonly) {\n        toggle();\n      }\n    };\n    const renderTitle = () => {\n      const {\n        border,\n        disabled,\n        readonly\n      } = props;\n      const attrs = pick(props, Object.keys(cellSharedProps));\n      if (readonly) {\n        attrs.isLink = false;\n      }\n      if (disabled || readonly) {\n        attrs.clickable = false;\n      }\n      return _createVNode(Cell, _mergeProps({\n        \"role\": \"button\",\n        \"class\": bem(\"title\", {\n          disabled,\n          expanded: expanded.value,\n          borderless: !border\n        }),\n        \"aria-expanded\": String(expanded.value),\n        \"onClick\": onClickTitle\n      }, attrs), pick(slots, CELL_SLOTS));\n    };\n    const renderContent = lazyRender(() => {\n      var _a;\n      return _withDirectives(_createVNode(\"div\", {\n        \"ref\": wrapperRef,\n        \"class\": bem(\"wrapper\"),\n        \"onTransitionend\": onTransitionEnd\n      }, [_createVNode(\"div\", {\n        \"ref\": contentRef,\n        \"class\": bem(\"content\")\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]), [[_vShow, show.value]]);\n    });\n    useExpose({\n      toggle,\n      expanded,\n      itemName: name2\n    });\n    return () => _createVNode(\"div\", {\n      \"class\": [bem({\n        border: index.value && props.border\n      })]\n    }, [renderTitle(), renderContent()]);\n  }\n});\nexport { collapseItemProps, stdin_default as default };", "map": {"version": 3, "names": ["ref", "watch", "computed", "nextTick", "defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "vShow", "_vShow", "withDirectives", "_withDirectives", "cellSharedProps", "pick", "extend", "truthProp", "numericProp", "createNamespace", "COLLAPSE_KEY", "raf", "doubleRaf", "useParent", "useExpose", "useLazyRender", "Cell", "name", "bem", "CELL_SLOTS", "collapseItemProps", "isLink", "disabled", "Boolean", "readonly", "lazy<PERSON>ender", "stdin_default", "props", "setup", "slots", "wrapperRef", "contentRef", "parent", "index", "process", "env", "NODE_ENV", "console", "error", "name2", "_a", "value", "expanded", "isExpanded", "show", "onTransitionEnd", "style", "height", "oldValue", "tick", "offsetHeight", "contentHeight", "toggle", "newValue", "onClickTitle", "renderTitle", "border", "attrs", "Object", "keys", "clickable", "borderless", "String", "renderContent", "default", "call", "itemName"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/collapse-item/CollapseItem.mjs"], "sourcesContent": ["import { ref, watch, computed, nextTick, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nimport { cellSharedProps } from \"../cell/Cell.mjs\";\nimport { pick, extend, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { COLLAPSE_KEY } from \"../collapse/Collapse.mjs\";\nimport { raf, doubleRaf, useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { useLazyRender } from \"../composables/use-lazy-render.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem] = createNamespace(\"collapse-item\");\nconst CELL_SLOTS = [\"icon\", \"title\", \"value\", \"label\", \"right-icon\"];\nconst collapseItemProps = extend({}, cellSharedProps, {\n  name: numericProp,\n  isLink: truthProp,\n  disabled: <PERSON><PERSON><PERSON>,\n  readonly: <PERSON><PERSON><PERSON>,\n  lazyRender: truthProp\n});\nvar stdin_default = defineComponent({\n  name,\n  props: collapseItemProps,\n  setup(props, {\n    slots\n  }) {\n    const wrapperRef = ref();\n    const contentRef = ref();\n    const {\n      parent,\n      index\n    } = useParent(COLLAPSE_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <CollapseItem> must be a child component of <Collapse>.\");\n      }\n      return;\n    }\n    const name2 = computed(() => {\n      var _a;\n      return (_a = props.name) != null ? _a : index.value;\n    });\n    const expanded = computed(() => parent.isExpanded(name2.value));\n    const show = ref(expanded.value);\n    const lazyRender = useLazyRender(() => show.value || !props.lazyRender);\n    const onTransitionEnd = () => {\n      if (!expanded.value) {\n        show.value = false;\n      } else if (wrapperRef.value) {\n        wrapperRef.value.style.height = \"\";\n      }\n    };\n    watch(expanded, (value, oldValue) => {\n      if (oldValue === null) {\n        return;\n      }\n      if (value) {\n        show.value = true;\n      }\n      const tick = value ? nextTick : raf;\n      tick(() => {\n        if (!contentRef.value || !wrapperRef.value) {\n          return;\n        }\n        const {\n          offsetHeight\n        } = contentRef.value;\n        if (offsetHeight) {\n          const contentHeight = `${offsetHeight}px`;\n          wrapperRef.value.style.height = value ? \"0\" : contentHeight;\n          doubleRaf(() => {\n            if (wrapperRef.value) {\n              wrapperRef.value.style.height = value ? contentHeight : \"0\";\n            }\n          });\n        } else {\n          onTransitionEnd();\n        }\n      });\n    });\n    const toggle = (newValue = !expanded.value) => {\n      parent.toggle(name2.value, newValue);\n    };\n    const onClickTitle = () => {\n      if (!props.disabled && !props.readonly) {\n        toggle();\n      }\n    };\n    const renderTitle = () => {\n      const {\n        border,\n        disabled,\n        readonly\n      } = props;\n      const attrs = pick(props, Object.keys(cellSharedProps));\n      if (readonly) {\n        attrs.isLink = false;\n      }\n      if (disabled || readonly) {\n        attrs.clickable = false;\n      }\n      return _createVNode(Cell, _mergeProps({\n        \"role\": \"button\",\n        \"class\": bem(\"title\", {\n          disabled,\n          expanded: expanded.value,\n          borderless: !border\n        }),\n        \"aria-expanded\": String(expanded.value),\n        \"onClick\": onClickTitle\n      }, attrs), pick(slots, CELL_SLOTS));\n    };\n    const renderContent = lazyRender(() => {\n      var _a;\n      return _withDirectives(_createVNode(\"div\", {\n        \"ref\": wrapperRef,\n        \"class\": bem(\"wrapper\"),\n        \"onTransitionend\": onTransitionEnd\n      }, [_createVNode(\"div\", {\n        \"ref\": contentRef,\n        \"class\": bem(\"content\")\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]), [[_vShow, show.value]]);\n    });\n    useExpose({\n      toggle,\n      expanded,\n      itemName: name2\n    });\n    return () => _createVNode(\"div\", {\n      \"class\": [bem({\n        border: index.value && props.border\n      })]\n    }, [renderTitle(), renderContent()]);\n  }\n});\nexport {\n  collapseItemProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,EAAEC,KAAK,IAAIC,MAAM,EAAEC,cAAc,IAAIC,eAAe,QAAQ,KAAK;AACjL,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AAC1F,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,WAAW;AACrD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGT,eAAe,CAAC,eAAe,CAAC;AACpD,MAAMU,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC;AACpE,MAAMC,iBAAiB,GAAGd,MAAM,CAAC,CAAC,CAAC,EAAEF,eAAe,EAAE;EACpDa,IAAI,EAAET,WAAW;EACjBa,MAAM,EAAEd,SAAS;EACjBe,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAED,OAAO;EACjBE,UAAU,EAAElB;AACd,CAAC,CAAC;AACF,IAAImB,aAAa,GAAG/B,eAAe,CAAC;EAClCsB,IAAI;EACJU,KAAK,EAAEP,iBAAiB;EACxBQ,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,UAAU,GAAGvC,GAAG,CAAC,CAAC;IACxB,MAAMwC,UAAU,GAAGxC,GAAG,CAAC,CAAC;IACxB,MAAM;MACJyC,MAAM;MACNC;IACF,CAAC,GAAGpB,SAAS,CAACH,YAAY,CAAC;IAC3B,IAAI,CAACsB,MAAM,EAAE;MACX,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,gEAAgE,CAAC;MACjF;MACA;IACF;IACA,MAAMC,KAAK,GAAG9C,QAAQ,CAAC,MAAM;MAC3B,IAAI+C,EAAE;MACN,OAAO,CAACA,EAAE,GAAGb,KAAK,CAACV,IAAI,KAAK,IAAI,GAAGuB,EAAE,GAAGP,KAAK,CAACQ,KAAK;IACrD,CAAC,CAAC;IACF,MAAMC,QAAQ,GAAGjD,QAAQ,CAAC,MAAMuC,MAAM,CAACW,UAAU,CAACJ,KAAK,CAACE,KAAK,CAAC,CAAC;IAC/D,MAAMG,IAAI,GAAGrD,GAAG,CAACmD,QAAQ,CAACD,KAAK,CAAC;IAChC,MAAMhB,UAAU,GAAGV,aAAa,CAAC,MAAM6B,IAAI,CAACH,KAAK,IAAI,CAACd,KAAK,CAACF,UAAU,CAAC;IACvE,MAAMoB,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI,CAACH,QAAQ,CAACD,KAAK,EAAE;QACnBG,IAAI,CAACH,KAAK,GAAG,KAAK;MACpB,CAAC,MAAM,IAAIX,UAAU,CAACW,KAAK,EAAE;QAC3BX,UAAU,CAACW,KAAK,CAACK,KAAK,CAACC,MAAM,GAAG,EAAE;MACpC;IACF,CAAC;IACDvD,KAAK,CAACkD,QAAQ,EAAE,CAACD,KAAK,EAAEO,QAAQ,KAAK;MACnC,IAAIA,QAAQ,KAAK,IAAI,EAAE;QACrB;MACF;MACA,IAAIP,KAAK,EAAE;QACTG,IAAI,CAACH,KAAK,GAAG,IAAI;MACnB;MACA,MAAMQ,IAAI,GAAGR,KAAK,GAAG/C,QAAQ,GAAGiB,GAAG;MACnCsC,IAAI,CAAC,MAAM;QACT,IAAI,CAAClB,UAAU,CAACU,KAAK,IAAI,CAACX,UAAU,CAACW,KAAK,EAAE;UAC1C;QACF;QACA,MAAM;UACJS;QACF,CAAC,GAAGnB,UAAU,CAACU,KAAK;QACpB,IAAIS,YAAY,EAAE;UAChB,MAAMC,aAAa,GAAG,GAAGD,YAAY,IAAI;UACzCpB,UAAU,CAACW,KAAK,CAACK,KAAK,CAACC,MAAM,GAAGN,KAAK,GAAG,GAAG,GAAGU,aAAa;UAC3DvC,SAAS,CAAC,MAAM;YACd,IAAIkB,UAAU,CAACW,KAAK,EAAE;cACpBX,UAAU,CAACW,KAAK,CAACK,KAAK,CAACC,MAAM,GAAGN,KAAK,GAAGU,aAAa,GAAG,GAAG;YAC7D;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLN,eAAe,CAAC,CAAC;QACnB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,MAAMO,MAAM,GAAGA,CAACC,QAAQ,GAAG,CAACX,QAAQ,CAACD,KAAK,KAAK;MAC7CT,MAAM,CAACoB,MAAM,CAACb,KAAK,CAACE,KAAK,EAAEY,QAAQ,CAAC;IACtC,CAAC;IACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAAC3B,KAAK,CAACL,QAAQ,IAAI,CAACK,KAAK,CAACH,QAAQ,EAAE;QACtC4B,MAAM,CAAC,CAAC;MACV;IACF,CAAC;IACD,MAAMG,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAM;QACJC,MAAM;QACNlC,QAAQ;QACRE;MACF,CAAC,GAAGG,KAAK;MACT,MAAM8B,KAAK,GAAGpD,IAAI,CAACsB,KAAK,EAAE+B,MAAM,CAACC,IAAI,CAACvD,eAAe,CAAC,CAAC;MACvD,IAAIoB,QAAQ,EAAE;QACZiC,KAAK,CAACpC,MAAM,GAAG,KAAK;MACtB;MACA,IAAIC,QAAQ,IAAIE,QAAQ,EAAE;QACxBiC,KAAK,CAACG,SAAS,GAAG,KAAK;MACzB;MACA,OAAO7D,YAAY,CAACiB,IAAI,EAAEnB,WAAW,CAAC;QACpC,MAAM,EAAE,QAAQ;QAChB,OAAO,EAAEqB,GAAG,CAAC,OAAO,EAAE;UACpBI,QAAQ;UACRoB,QAAQ,EAAEA,QAAQ,CAACD,KAAK;UACxBoB,UAAU,EAAE,CAACL;QACf,CAAC,CAAC;QACF,eAAe,EAAEM,MAAM,CAACpB,QAAQ,CAACD,KAAK,CAAC;QACvC,SAAS,EAAEa;MACb,CAAC,EAAEG,KAAK,CAAC,EAAEpD,IAAI,CAACwB,KAAK,EAAEV,UAAU,CAAC,CAAC;IACrC,CAAC;IACD,MAAM4C,aAAa,GAAGtC,UAAU,CAAC,MAAM;MACrC,IAAIe,EAAE;MACN,OAAOrC,eAAe,CAACJ,YAAY,CAAC,KAAK,EAAE;QACzC,KAAK,EAAE+B,UAAU;QACjB,OAAO,EAAEZ,GAAG,CAAC,SAAS,CAAC;QACvB,iBAAiB,EAAE2B;MACrB,CAAC,EAAE,CAAC9C,YAAY,CAAC,KAAK,EAAE;QACtB,KAAK,EAAEgC,UAAU;QACjB,OAAO,EAAEb,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,CAAC,CAACsB,EAAE,GAAGX,KAAK,CAACmC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGxB,EAAE,CAACyB,IAAI,CAACpC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC5B,MAAM,EAAE2C,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC;IACF3B,SAAS,CAAC;MACRsC,MAAM;MACNV,QAAQ;MACRwB,QAAQ,EAAE3B;IACZ,CAAC,CAAC;IACF,OAAO,MAAMxC,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAE,CAACmB,GAAG,CAAC;QACZsC,MAAM,EAAEvB,KAAK,CAACQ,KAAK,IAAId,KAAK,CAAC6B;MAC/B,CAAC,CAAC;IACJ,CAAC,EAAE,CAACD,WAAW,CAAC,CAAC,EAAEQ,aAAa,CAAC,CAAC,CAAC,CAAC;EACtC;AACF,CAAC,CAAC;AACF,SACE3C,iBAAiB,EACjBM,aAAa,IAAIsC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}