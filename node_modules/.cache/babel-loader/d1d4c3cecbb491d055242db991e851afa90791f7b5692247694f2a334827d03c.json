{"ast": null, "code": "import { getCurrentInstance } from \"vue\";\nconst useScopeId = () => {\n  var _a;\n  const {\n    scopeId\n  } = ((_a = getCurrentInstance()) == null ? void 0 : _a.vnode) || {};\n  return scopeId ? {\n    [scopeId]: \"\"\n  } : null;\n};\nexport { useScopeId };", "map": {"version": 3, "names": ["getCurrentInstance", "useScopeId", "_a", "scopeId", "vnode"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-scope-id.mjs"], "sourcesContent": ["import { getCurrentInstance } from \"vue\";\nconst useScopeId = () => {\n  var _a;\n  const { scopeId } = ((_a = getCurrentInstance()) == null ? void 0 : _a.vnode) || {};\n  return scopeId ? { [scopeId]: \"\" } : null;\n};\nexport {\n  useScopeId\n};\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,KAAK;AACxC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,IAAIC,EAAE;EACN,MAAM;IAAEC;EAAQ,CAAC,GAAG,CAAC,CAACD,EAAE,GAAGF,kBAAkB,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACE,KAAK,KAAK,CAAC,CAAC;EACnF,OAAOD,OAAO,GAAG;IAAE,CAACA,OAAO,GAAG;EAAG,CAAC,GAAG,IAAI;AAC3C,CAAC;AACD,SACEF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}