{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { watch, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, makeArrayProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren, useCustomFieldValue } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"checkbox-group\");\nconst checkboxGroupProps = {\n  max: numericProp,\n  shape: makeStringProp(\"round\"),\n  disabled: <PERSON><PERSON><PERSON>,\n  iconSize: numericProp,\n  direction: String,\n  modelValue: makeArrayProp(),\n  checkedColor: String\n};\nconst CHECKBOX_GROUP_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: checkboxGroupProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      children,\n      linkChildren\n    } = useChildren(CHECKBOX_GROUP_KEY);\n    const updateValue = value => emit(\"update:modelValue\", value);\n    const toggleAll = (options = {}) => {\n      if (typeof options === \"boolean\") {\n        options = {\n          checked: options\n        };\n      }\n      const {\n        checked,\n        skipDisabled\n      } = options;\n      const checkedChildren = children.filter(item => {\n        if (!item.props.bindGroup) {\n          return false;\n        }\n        if (item.props.disabled && skipDisabled) {\n          return item.checked.value;\n        }\n        return checked != null ? checked : !item.checked.value;\n      });\n      const names = checkedChildren.map(item => item.name);\n      updateValue(names);\n    };\n    watch(() => props.modelValue, value => emit(\"change\", value));\n    useExpose({\n      toggleAll\n    });\n    useCustomFieldValue(() => props.modelValue);\n    linkChildren({\n      props,\n      updateValue\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem([props.direction])\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { CHECKBOX_GROUP_KEY, checkboxGroupProps, stdin_default as default };", "map": {"version": 3, "names": ["watch", "defineComponent", "createVNode", "_createVNode", "numericProp", "makeArrayProp", "makeStringProp", "createNamespace", "useChildren", "useCustomFieldValue", "useExpose", "name", "bem", "checkboxGroupProps", "max", "shape", "disabled", "Boolean", "iconSize", "direction", "String", "modelValue", "checkedColor", "CHECKBOX_GROUP_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "emit", "slots", "children", "linkChildren", "updateValue", "value", "toggleAll", "options", "checked", "skipDisabled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "item", "bindGroup", "names", "map", "_a", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/checkbox-group/CheckboxGroup.mjs"], "sourcesContent": ["import { watch, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, makeArrayProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren, useCustomFieldValue } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"checkbox-group\");\nconst checkboxGroupProps = {\n  max: numericProp,\n  shape: makeStringProp(\"round\"),\n  disabled: <PERSON><PERSON><PERSON>,\n  iconSize: numericProp,\n  direction: String,\n  modelValue: makeArrayProp(),\n  checkedColor: String\n};\nconst CHECKBOX_GROUP_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: checkboxGroupProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      children,\n      linkChildren\n    } = useChildren(CHECKBOX_GROUP_KEY);\n    const updateValue = (value) => emit(\"update:modelValue\", value);\n    const toggleAll = (options = {}) => {\n      if (typeof options === \"boolean\") {\n        options = {\n          checked: options\n        };\n      }\n      const {\n        checked,\n        skipDisabled\n      } = options;\n      const checkedChildren = children.filter((item) => {\n        if (!item.props.bindGroup) {\n          return false;\n        }\n        if (item.props.disabled && skipDisabled) {\n          return item.checked.value;\n        }\n        return checked != null ? checked : !item.checked.value;\n      });\n      const names = checkedChildren.map((item) => item.name);\n      updateValue(names);\n    };\n    watch(() => props.modelValue, (value) => emit(\"change\", value));\n    useExpose({\n      toggleAll\n    });\n    useCustomFieldValue(() => props.modelValue);\n    linkChildren({\n      props,\n      updateValue\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem([props.direction])\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  CHECKBOX_GROUP_KEY,\n  checkboxGroupProps,\n  stdin_default as default\n};\n"], "mappings": ";;;AAAA,SAASA,KAAK,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACzE,SAASC,WAAW,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAChG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,WAAW;AAC5D,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,gBAAgB,CAAC;AACrD,MAAMM,kBAAkB,GAAG;EACzBC,GAAG,EAAEV,WAAW;EAChBW,KAAK,EAAET,cAAc,CAAC,OAAO,CAAC;EAC9BU,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAEd,WAAW;EACrBe,SAAS,EAAEC,MAAM;EACjBC,UAAU,EAAEhB,aAAa,CAAC,CAAC;EAC3BiB,YAAY,EAAEF;AAChB,CAAC;AACD,MAAMG,kBAAkB,GAAGC,MAAM,CAACb,IAAI,CAAC;AACvC,IAAIc,aAAa,GAAGxB,eAAe,CAAC;EAClCU,IAAI;EACJe,KAAK,EAAEb,kBAAkB;EACzBc,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAM;MACJC,QAAQ;MACRC;IACF,CAAC,GAAGxB,WAAW,CAACe,kBAAkB,CAAC;IACnC,MAAMU,WAAW,GAAIC,KAAK,IAAKL,IAAI,CAAC,mBAAmB,EAAEK,KAAK,CAAC;IAC/D,MAAMC,SAAS,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;MAClC,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;QAChCA,OAAO,GAAG;UACRC,OAAO,EAAED;QACX,CAAC;MACH;MACA,MAAM;QACJC,OAAO;QACPC;MACF,CAAC,GAAGF,OAAO;MACX,MAAMG,eAAe,GAAGR,QAAQ,CAACS,MAAM,CAAEC,IAAI,IAAK;QAChD,IAAI,CAACA,IAAI,CAACf,KAAK,CAACgB,SAAS,EAAE;UACzB,OAAO,KAAK;QACd;QACA,IAAID,IAAI,CAACf,KAAK,CAACV,QAAQ,IAAIsB,YAAY,EAAE;UACvC,OAAOG,IAAI,CAACJ,OAAO,CAACH,KAAK;QAC3B;QACA,OAAOG,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,CAACI,IAAI,CAACJ,OAAO,CAACH,KAAK;MACxD,CAAC,CAAC;MACF,MAAMS,KAAK,GAAGJ,eAAe,CAACK,GAAG,CAAEH,IAAI,IAAKA,IAAI,CAAC9B,IAAI,CAAC;MACtDsB,WAAW,CAACU,KAAK,CAAC;IACpB,CAAC;IACD3C,KAAK,CAAC,MAAM0B,KAAK,CAACL,UAAU,EAAGa,KAAK,IAAKL,IAAI,CAAC,QAAQ,EAAEK,KAAK,CAAC,CAAC;IAC/DxB,SAAS,CAAC;MACRyB;IACF,CAAC,CAAC;IACF1B,mBAAmB,CAAC,MAAMiB,KAAK,CAACL,UAAU,CAAC;IAC3CW,YAAY,CAAC;MACXN,KAAK;MACLO;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIY,EAAE;MACN,OAAO1C,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAES,GAAG,CAAC,CAACc,KAAK,CAACP,SAAS,CAAC;MAChC,CAAC,EAAE,CAAC,CAAC0B,EAAE,GAAGf,KAAK,CAACgB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACjB,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEP,kBAAkB,EAClBV,kBAAkB,EAClBY,aAAa,IAAIqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}