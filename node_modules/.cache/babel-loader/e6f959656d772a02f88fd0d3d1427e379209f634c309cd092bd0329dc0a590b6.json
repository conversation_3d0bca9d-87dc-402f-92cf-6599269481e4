{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, watch, computed, onActivated, onMounted, defineComponent, nextTick, createVNode as _createVNode } from \"vue\";\nimport { makeNumericProp, makeStringProp, createNamespace, windowWidth } from \"../utils/index.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"text-ellipsis\");\nconst textEllipsisProps = {\n  rows: makeNumericProp(1),\n  dots: makeStringProp(\"...\"),\n  content: makeStringProp(\"\"),\n  expandText: makeStringProp(\"\"),\n  collapseText: makeStringProp(\"\"),\n  position: makeStringProp(\"end\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: textEllipsisProps,\n  emits: [\"clickAction\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const text = ref(props.content);\n    const expanded = ref(false);\n    const hasAction = ref(false);\n    const root = ref();\n    const actionRef = ref();\n    let needRecalculate = false;\n    const actionText = computed(() => expanded.value ? props.collapseText : props.expandText);\n    const pxToNum = value => {\n      if (!value) return 0;\n      const match = value.match(/^\\d*(\\.\\d*)?/);\n      return match ? Number(match[0]) : 0;\n    };\n    const cloneContainer = () => {\n      if (!root.value || !root.value.isConnected) return;\n      const originStyle = window.getComputedStyle(root.value);\n      const container = document.createElement(\"div\");\n      const styleNames = Array.prototype.slice.apply(originStyle);\n      styleNames.forEach(name2 => {\n        container.style.setProperty(name2, originStyle.getPropertyValue(name2));\n      });\n      container.style.position = \"fixed\";\n      container.style.zIndex = \"-9999\";\n      container.style.top = \"-9999px\";\n      container.style.height = \"auto\";\n      container.style.minHeight = \"auto\";\n      container.style.maxHeight = \"auto\";\n      container.innerText = props.content;\n      document.body.appendChild(container);\n      return container;\n    };\n    const calcEllipsisText = (container, maxHeight) => {\n      var _a, _b;\n      const {\n        content,\n        position,\n        dots\n      } = props;\n      const end = content.length;\n      const middle = 0 + end >> 1;\n      const actionHTML = slots.action ? (_b = (_a = actionRef.value) == null ? void 0 : _a.outerHTML) != null ? _b : \"\" : props.expandText;\n      const calcEllipse = () => {\n        const tail = (left, right) => {\n          if (right - left <= 1) {\n            if (position === \"end\") {\n              return content.slice(0, left) + dots;\n            }\n            return dots + content.slice(right, end);\n          }\n          const middle2 = Math.round((left + right) / 2);\n          if (position === \"end\") {\n            container.innerText = content.slice(0, middle2) + dots;\n          } else {\n            container.innerText = dots + content.slice(middle2, end);\n          }\n          container.innerHTML += actionHTML;\n          if (container.offsetHeight > maxHeight) {\n            if (position === \"end\") {\n              return tail(left, middle2);\n            }\n            return tail(middle2, right);\n          }\n          if (position === \"end\") {\n            return tail(middle2, right);\n          }\n          return tail(left, middle2);\n        };\n        return tail(0, end);\n      };\n      const middleTail = (leftPart, rightPart) => {\n        if (leftPart[1] - leftPart[0] <= 1 && rightPart[1] - rightPart[0] <= 1) {\n          return content.slice(0, leftPart[0]) + dots + content.slice(rightPart[1], end);\n        }\n        const leftMiddle = Math.floor((leftPart[0] + leftPart[1]) / 2);\n        const rightMiddle = Math.ceil((rightPart[0] + rightPart[1]) / 2);\n        container.innerText = props.content.slice(0, leftMiddle) + props.dots + props.content.slice(rightMiddle, end);\n        container.innerHTML += actionHTML;\n        if (container.offsetHeight >= maxHeight) {\n          return middleTail([leftPart[0], leftMiddle], [rightMiddle, rightPart[1]]);\n        }\n        return middleTail([leftMiddle, leftPart[1]], [rightPart[0], rightMiddle]);\n      };\n      return props.position === \"middle\" ? middleTail([0, middle], [middle, end]) : calcEllipse();\n    };\n    const calcEllipsised = () => {\n      const container = cloneContainer();\n      if (!container) {\n        needRecalculate = true;\n        return;\n      }\n      const {\n        paddingBottom,\n        paddingTop,\n        lineHeight\n      } = container.style;\n      const maxHeight = Math.ceil((Number(props.rows) + 0.5) * pxToNum(lineHeight) + pxToNum(paddingTop) + pxToNum(paddingBottom));\n      if (maxHeight < container.offsetHeight) {\n        hasAction.value = true;\n        text.value = calcEllipsisText(container, maxHeight);\n      } else {\n        hasAction.value = false;\n        text.value = props.content;\n      }\n      document.body.removeChild(container);\n    };\n    const toggle = (isExpanded = !expanded.value) => {\n      expanded.value = isExpanded;\n    };\n    const onClickAction = event => {\n      toggle();\n      emit(\"clickAction\", event);\n    };\n    const renderAction = () => {\n      const action = slots.action ? slots.action({\n        expanded: expanded.value\n      }) : actionText.value;\n      return _createVNode(\"span\", {\n        \"ref\": actionRef,\n        \"class\": bem(\"action\"),\n        \"onClick\": onClickAction\n      }, [action]);\n    };\n    onMounted(() => {\n      calcEllipsised();\n      if (slots.action) {\n        nextTick(calcEllipsised);\n      }\n    });\n    onActivated(() => {\n      if (needRecalculate) {\n        needRecalculate = false;\n        calcEllipsised();\n      }\n    });\n    watch([windowWidth, () => [props.content, props.rows, props.position]], calcEllipsised);\n    useExpose({\n      toggle\n    });\n    return () => _createVNode(\"div\", {\n      \"ref\": root,\n      \"class\": bem()\n    }, [expanded.value ? props.content : text.value, hasAction.value ? renderAction() : null]);\n  }\n});\nexport { stdin_default as default, textEllipsisProps };", "map": {"version": 3, "names": ["ref", "watch", "computed", "onActivated", "onMounted", "defineComponent", "nextTick", "createVNode", "_createVNode", "makeNumericProp", "makeStringProp", "createNamespace", "windowWidth", "useExpose", "name", "bem", "textEllipsisProps", "rows", "dots", "content", "expandText", "collapseText", "position", "stdin_default", "props", "emits", "setup", "emit", "slots", "text", "expanded", "hasAction", "root", "actionRef", "needRecalculate", "actionText", "value", "pxToNum", "match", "Number", "cloneContainer", "isConnected", "originStyle", "window", "getComputedStyle", "container", "document", "createElement", "styleNames", "Array", "prototype", "slice", "apply", "for<PERSON>ach", "name2", "style", "setProperty", "getPropertyValue", "zIndex", "top", "height", "minHeight", "maxHeight", "innerText", "body", "append<PERSON><PERSON><PERSON>", "calcEllipsisText", "_a", "_b", "end", "length", "middle", "actionHTML", "action", "outerHTML", "calcEllipse", "tail", "left", "right", "middle2", "Math", "round", "innerHTML", "offsetHeight", "middleTail", "leftPart", "rightPart", "leftMiddle", "floor", "rightMiddle", "ceil", "calcEllipsised", "paddingBottom", "paddingTop", "lineHeight", "<PERSON><PERSON><PERSON><PERSON>", "toggle", "isExpanded", "onClickAction", "event", "renderAction", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/text-ellipsis/TextEllipsis.mjs"], "sourcesContent": ["import { ref, watch, computed, onActivated, onMounted, defineComponent, nextTick, createVNode as _createVNode } from \"vue\";\nimport { makeNumericProp, makeStringProp, createNamespace, windowWidth } from \"../utils/index.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"text-ellipsis\");\nconst textEllipsisProps = {\n  rows: makeNumericProp(1),\n  dots: makeStringProp(\"...\"),\n  content: makeStringProp(\"\"),\n  expandText: makeStringProp(\"\"),\n  collapseText: makeStringProp(\"\"),\n  position: makeStringProp(\"end\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: textEllipsisProps,\n  emits: [\"clickAction\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const text = ref(props.content);\n    const expanded = ref(false);\n    const hasAction = ref(false);\n    const root = ref();\n    const actionRef = ref();\n    let needRecalculate = false;\n    const actionText = computed(() => expanded.value ? props.collapseText : props.expandText);\n    const pxToNum = (value) => {\n      if (!value) return 0;\n      const match = value.match(/^\\d*(\\.\\d*)?/);\n      return match ? Number(match[0]) : 0;\n    };\n    const cloneContainer = () => {\n      if (!root.value || !root.value.isConnected) return;\n      const originStyle = window.getComputedStyle(root.value);\n      const container = document.createElement(\"div\");\n      const styleNames = Array.prototype.slice.apply(originStyle);\n      styleNames.forEach((name2) => {\n        container.style.setProperty(name2, originStyle.getPropertyValue(name2));\n      });\n      container.style.position = \"fixed\";\n      container.style.zIndex = \"-9999\";\n      container.style.top = \"-9999px\";\n      container.style.height = \"auto\";\n      container.style.minHeight = \"auto\";\n      container.style.maxHeight = \"auto\";\n      container.innerText = props.content;\n      document.body.appendChild(container);\n      return container;\n    };\n    const calcEllipsisText = (container, maxHeight) => {\n      var _a, _b;\n      const {\n        content,\n        position,\n        dots\n      } = props;\n      const end = content.length;\n      const middle = 0 + end >> 1;\n      const actionHTML = slots.action ? (_b = (_a = actionRef.value) == null ? void 0 : _a.outerHTML) != null ? _b : \"\" : props.expandText;\n      const calcEllipse = () => {\n        const tail = (left, right) => {\n          if (right - left <= 1) {\n            if (position === \"end\") {\n              return content.slice(0, left) + dots;\n            }\n            return dots + content.slice(right, end);\n          }\n          const middle2 = Math.round((left + right) / 2);\n          if (position === \"end\") {\n            container.innerText = content.slice(0, middle2) + dots;\n          } else {\n            container.innerText = dots + content.slice(middle2, end);\n          }\n          container.innerHTML += actionHTML;\n          if (container.offsetHeight > maxHeight) {\n            if (position === \"end\") {\n              return tail(left, middle2);\n            }\n            return tail(middle2, right);\n          }\n          if (position === \"end\") {\n            return tail(middle2, right);\n          }\n          return tail(left, middle2);\n        };\n        return tail(0, end);\n      };\n      const middleTail = (leftPart, rightPart) => {\n        if (leftPart[1] - leftPart[0] <= 1 && rightPart[1] - rightPart[0] <= 1) {\n          return content.slice(0, leftPart[0]) + dots + content.slice(rightPart[1], end);\n        }\n        const leftMiddle = Math.floor((leftPart[0] + leftPart[1]) / 2);\n        const rightMiddle = Math.ceil((rightPart[0] + rightPart[1]) / 2);\n        container.innerText = props.content.slice(0, leftMiddle) + props.dots + props.content.slice(rightMiddle, end);\n        container.innerHTML += actionHTML;\n        if (container.offsetHeight >= maxHeight) {\n          return middleTail([leftPart[0], leftMiddle], [rightMiddle, rightPart[1]]);\n        }\n        return middleTail([leftMiddle, leftPart[1]], [rightPart[0], rightMiddle]);\n      };\n      return props.position === \"middle\" ? middleTail([0, middle], [middle, end]) : calcEllipse();\n    };\n    const calcEllipsised = () => {\n      const container = cloneContainer();\n      if (!container) {\n        needRecalculate = true;\n        return;\n      }\n      const {\n        paddingBottom,\n        paddingTop,\n        lineHeight\n      } = container.style;\n      const maxHeight = Math.ceil((Number(props.rows) + 0.5) * pxToNum(lineHeight) + pxToNum(paddingTop) + pxToNum(paddingBottom));\n      if (maxHeight < container.offsetHeight) {\n        hasAction.value = true;\n        text.value = calcEllipsisText(container, maxHeight);\n      } else {\n        hasAction.value = false;\n        text.value = props.content;\n      }\n      document.body.removeChild(container);\n    };\n    const toggle = (isExpanded = !expanded.value) => {\n      expanded.value = isExpanded;\n    };\n    const onClickAction = (event) => {\n      toggle();\n      emit(\"clickAction\", event);\n    };\n    const renderAction = () => {\n      const action = slots.action ? slots.action({\n        expanded: expanded.value\n      }) : actionText.value;\n      return _createVNode(\"span\", {\n        \"ref\": actionRef,\n        \"class\": bem(\"action\"),\n        \"onClick\": onClickAction\n      }, [action]);\n    };\n    onMounted(() => {\n      calcEllipsised();\n      if (slots.action) {\n        nextTick(calcEllipsised);\n      }\n    });\n    onActivated(() => {\n      if (needRecalculate) {\n        needRecalculate = false;\n        calcEllipsised();\n      }\n    });\n    watch([windowWidth, () => [props.content, props.rows, props.position]], calcEllipsised);\n    useExpose({\n      toggle\n    });\n    return () => _createVNode(\"div\", {\n      \"ref\": root,\n      \"class\": bem()\n    }, [expanded.value ? props.content : text.value, hasAction.value ? renderAction() : null]);\n  }\n});\nexport {\n  stdin_default as default,\n  textEllipsisProps\n};\n"], "mappings": ";;AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC1H,SAASC,eAAe,EAAEC,cAAc,EAAEC,eAAe,EAAEC,WAAW,QAAQ,oBAAoB;AAClG,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,eAAe,CAAC;AACpD,MAAMK,iBAAiB,GAAG;EACxBC,IAAI,EAAER,eAAe,CAAC,CAAC,CAAC;EACxBS,IAAI,EAAER,cAAc,CAAC,KAAK,CAAC;EAC3BS,OAAO,EAAET,cAAc,CAAC,EAAE,CAAC;EAC3BU,UAAU,EAAEV,cAAc,CAAC,EAAE,CAAC;EAC9BW,YAAY,EAAEX,cAAc,CAAC,EAAE,CAAC;EAChCY,QAAQ,EAAEZ,cAAc,CAAC,KAAK;AAChC,CAAC;AACD,IAAIa,aAAa,GAAGlB,eAAe,CAAC;EAClCS,IAAI;EACJU,KAAK,EAAER,iBAAiB;EACxBS,KAAK,EAAE,CAAC,aAAa,CAAC;EACtBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAG7B,GAAG,CAACwB,KAAK,CAACL,OAAO,CAAC;IAC/B,MAAMW,QAAQ,GAAG9B,GAAG,CAAC,KAAK,CAAC;IAC3B,MAAM+B,SAAS,GAAG/B,GAAG,CAAC,KAAK,CAAC;IAC5B,MAAMgC,IAAI,GAAGhC,GAAG,CAAC,CAAC;IAClB,MAAMiC,SAAS,GAAGjC,GAAG,CAAC,CAAC;IACvB,IAAIkC,eAAe,GAAG,KAAK;IAC3B,MAAMC,UAAU,GAAGjC,QAAQ,CAAC,MAAM4B,QAAQ,CAACM,KAAK,GAAGZ,KAAK,CAACH,YAAY,GAAGG,KAAK,CAACJ,UAAU,CAAC;IACzF,MAAMiB,OAAO,GAAID,KAAK,IAAK;MACzB,IAAI,CAACA,KAAK,EAAE,OAAO,CAAC;MACpB,MAAME,KAAK,GAAGF,KAAK,CAACE,KAAK,CAAC,cAAc,CAAC;MACzC,OAAOA,KAAK,GAAGC,MAAM,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACrC,CAAC;IACD,MAAME,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAI,CAACR,IAAI,CAACI,KAAK,IAAI,CAACJ,IAAI,CAACI,KAAK,CAACK,WAAW,EAAE;MAC5C,MAAMC,WAAW,GAAGC,MAAM,CAACC,gBAAgB,CAACZ,IAAI,CAACI,KAAK,CAAC;MACvD,MAAMS,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/C,MAAMC,UAAU,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,KAAK,CAACV,WAAW,CAAC;MAC3DM,UAAU,CAACK,OAAO,CAAEC,KAAK,IAAK;QAC5BT,SAAS,CAACU,KAAK,CAACC,WAAW,CAACF,KAAK,EAAEZ,WAAW,CAACe,gBAAgB,CAACH,KAAK,CAAC,CAAC;MACzE,CAAC,CAAC;MACFT,SAAS,CAACU,KAAK,CAACjC,QAAQ,GAAG,OAAO;MAClCuB,SAAS,CAACU,KAAK,CAACG,MAAM,GAAG,OAAO;MAChCb,SAAS,CAACU,KAAK,CAACI,GAAG,GAAG,SAAS;MAC/Bd,SAAS,CAACU,KAAK,CAACK,MAAM,GAAG,MAAM;MAC/Bf,SAAS,CAACU,KAAK,CAACM,SAAS,GAAG,MAAM;MAClChB,SAAS,CAACU,KAAK,CAACO,SAAS,GAAG,MAAM;MAClCjB,SAAS,CAACkB,SAAS,GAAGvC,KAAK,CAACL,OAAO;MACnC2B,QAAQ,CAACkB,IAAI,CAACC,WAAW,CAACpB,SAAS,CAAC;MACpC,OAAOA,SAAS;IAClB,CAAC;IACD,MAAMqB,gBAAgB,GAAGA,CAACrB,SAAS,EAAEiB,SAAS,KAAK;MACjD,IAAIK,EAAE,EAAEC,EAAE;MACV,MAAM;QACJjD,OAAO;QACPG,QAAQ;QACRJ;MACF,CAAC,GAAGM,KAAK;MACT,MAAM6C,GAAG,GAAGlD,OAAO,CAACmD,MAAM;MAC1B,MAAMC,MAAM,GAAG,CAAC,GAAGF,GAAG,IAAI,CAAC;MAC3B,MAAMG,UAAU,GAAG5C,KAAK,CAAC6C,MAAM,GAAG,CAACL,EAAE,GAAG,CAACD,EAAE,GAAGlC,SAAS,CAACG,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+B,EAAE,CAACO,SAAS,KAAK,IAAI,GAAGN,EAAE,GAAG,EAAE,GAAG5C,KAAK,CAACJ,UAAU;MACpI,MAAMuD,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,IAAI,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;UAC5B,IAAIA,KAAK,GAAGD,IAAI,IAAI,CAAC,EAAE;YACrB,IAAIvD,QAAQ,KAAK,KAAK,EAAE;cACtB,OAAOH,OAAO,CAACgC,KAAK,CAAC,CAAC,EAAE0B,IAAI,CAAC,GAAG3D,IAAI;YACtC;YACA,OAAOA,IAAI,GAAGC,OAAO,CAACgC,KAAK,CAAC2B,KAAK,EAAET,GAAG,CAAC;UACzC;UACA,MAAMU,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,IAAI,GAAGC,KAAK,IAAI,CAAC,CAAC;UAC9C,IAAIxD,QAAQ,KAAK,KAAK,EAAE;YACtBuB,SAAS,CAACkB,SAAS,GAAG5C,OAAO,CAACgC,KAAK,CAAC,CAAC,EAAE4B,OAAO,CAAC,GAAG7D,IAAI;UACxD,CAAC,MAAM;YACL2B,SAAS,CAACkB,SAAS,GAAG7C,IAAI,GAAGC,OAAO,CAACgC,KAAK,CAAC4B,OAAO,EAAEV,GAAG,CAAC;UAC1D;UACAxB,SAAS,CAACqC,SAAS,IAAIV,UAAU;UACjC,IAAI3B,SAAS,CAACsC,YAAY,GAAGrB,SAAS,EAAE;YACtC,IAAIxC,QAAQ,KAAK,KAAK,EAAE;cACtB,OAAOsD,IAAI,CAACC,IAAI,EAAEE,OAAO,CAAC;YAC5B;YACA,OAAOH,IAAI,CAACG,OAAO,EAAED,KAAK,CAAC;UAC7B;UACA,IAAIxD,QAAQ,KAAK,KAAK,EAAE;YACtB,OAAOsD,IAAI,CAACG,OAAO,EAAED,KAAK,CAAC;UAC7B;UACA,OAAOF,IAAI,CAACC,IAAI,EAAEE,OAAO,CAAC;QAC5B,CAAC;QACD,OAAOH,IAAI,CAAC,CAAC,EAAEP,GAAG,CAAC;MACrB,CAAC;MACD,MAAMe,UAAU,GAAGA,CAACC,QAAQ,EAAEC,SAAS,KAAK;QAC1C,IAAID,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;UACtE,OAAOnE,OAAO,CAACgC,KAAK,CAAC,CAAC,EAAEkC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGnE,IAAI,GAAGC,OAAO,CAACgC,KAAK,CAACmC,SAAS,CAAC,CAAC,CAAC,EAAEjB,GAAG,CAAC;QAChF;QACA,MAAMkB,UAAU,GAAGP,IAAI,CAACQ,KAAK,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC9D,MAAMI,WAAW,GAAGT,IAAI,CAACU,IAAI,CAAC,CAACJ,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAChEzC,SAAS,CAACkB,SAAS,GAAGvC,KAAK,CAACL,OAAO,CAACgC,KAAK,CAAC,CAAC,EAAEoC,UAAU,CAAC,GAAG/D,KAAK,CAACN,IAAI,GAAGM,KAAK,CAACL,OAAO,CAACgC,KAAK,CAACsC,WAAW,EAAEpB,GAAG,CAAC;QAC7GxB,SAAS,CAACqC,SAAS,IAAIV,UAAU;QACjC,IAAI3B,SAAS,CAACsC,YAAY,IAAIrB,SAAS,EAAE;UACvC,OAAOsB,UAAU,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAEE,UAAU,CAAC,EAAE,CAACE,WAAW,EAAEH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E;QACA,OAAOF,UAAU,CAAC,CAACG,UAAU,EAAEF,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAACC,SAAS,CAAC,CAAC,CAAC,EAAEG,WAAW,CAAC,CAAC;MAC3E,CAAC;MACD,OAAOjE,KAAK,CAACF,QAAQ,KAAK,QAAQ,GAAG8D,UAAU,CAAC,CAAC,CAAC,EAAEb,MAAM,CAAC,EAAE,CAACA,MAAM,EAAEF,GAAG,CAAC,CAAC,GAAGM,WAAW,CAAC,CAAC;IAC7F,CAAC;IACD,MAAMgB,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAM9C,SAAS,GAAGL,cAAc,CAAC,CAAC;MAClC,IAAI,CAACK,SAAS,EAAE;QACdX,eAAe,GAAG,IAAI;QACtB;MACF;MACA,MAAM;QACJ0D,aAAa;QACbC,UAAU;QACVC;MACF,CAAC,GAAGjD,SAAS,CAACU,KAAK;MACnB,MAAMO,SAAS,GAAGkB,IAAI,CAACU,IAAI,CAAC,CAACnD,MAAM,CAACf,KAAK,CAACP,IAAI,CAAC,GAAG,GAAG,IAAIoB,OAAO,CAACyD,UAAU,CAAC,GAAGzD,OAAO,CAACwD,UAAU,CAAC,GAAGxD,OAAO,CAACuD,aAAa,CAAC,CAAC;MAC5H,IAAI9B,SAAS,GAAGjB,SAAS,CAACsC,YAAY,EAAE;QACtCpD,SAAS,CAACK,KAAK,GAAG,IAAI;QACtBP,IAAI,CAACO,KAAK,GAAG8B,gBAAgB,CAACrB,SAAS,EAAEiB,SAAS,CAAC;MACrD,CAAC,MAAM;QACL/B,SAAS,CAACK,KAAK,GAAG,KAAK;QACvBP,IAAI,CAACO,KAAK,GAAGZ,KAAK,CAACL,OAAO;MAC5B;MACA2B,QAAQ,CAACkB,IAAI,CAAC+B,WAAW,CAAClD,SAAS,CAAC;IACtC,CAAC;IACD,MAAMmD,MAAM,GAAGA,CAACC,UAAU,GAAG,CAACnE,QAAQ,CAACM,KAAK,KAAK;MAC/CN,QAAQ,CAACM,KAAK,GAAG6D,UAAU;IAC7B,CAAC;IACD,MAAMC,aAAa,GAAIC,KAAK,IAAK;MAC/BH,MAAM,CAAC,CAAC;MACRrE,IAAI,CAAC,aAAa,EAAEwE,KAAK,CAAC;IAC5B,CAAC;IACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAM3B,MAAM,GAAG7C,KAAK,CAAC6C,MAAM,GAAG7C,KAAK,CAAC6C,MAAM,CAAC;QACzC3C,QAAQ,EAAEA,QAAQ,CAACM;MACrB,CAAC,CAAC,GAAGD,UAAU,CAACC,KAAK;MACrB,OAAO5B,YAAY,CAAC,MAAM,EAAE;QAC1B,KAAK,EAAEyB,SAAS;QAChB,OAAO,EAAElB,GAAG,CAAC,QAAQ,CAAC;QACtB,SAAS,EAAEmF;MACb,CAAC,EAAE,CAACzB,MAAM,CAAC,CAAC;IACd,CAAC;IACDrE,SAAS,CAAC,MAAM;MACduF,cAAc,CAAC,CAAC;MAChB,IAAI/D,KAAK,CAAC6C,MAAM,EAAE;QAChBnE,QAAQ,CAACqF,cAAc,CAAC;MAC1B;IACF,CAAC,CAAC;IACFxF,WAAW,CAAC,MAAM;MAChB,IAAI+B,eAAe,EAAE;QACnBA,eAAe,GAAG,KAAK;QACvByD,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACF1F,KAAK,CAAC,CAACW,WAAW,EAAE,MAAM,CAACY,KAAK,CAACL,OAAO,EAAEK,KAAK,CAACP,IAAI,EAAEO,KAAK,CAACF,QAAQ,CAAC,CAAC,EAAEqE,cAAc,CAAC;IACvF9E,SAAS,CAAC;MACRmF;IACF,CAAC,CAAC;IACF,OAAO,MAAMxF,YAAY,CAAC,KAAK,EAAE;MAC/B,KAAK,EAAEwB,IAAI;MACX,OAAO,EAAEjB,GAAG,CAAC;IACf,CAAC,EAAE,CAACe,QAAQ,CAACM,KAAK,GAAGZ,KAAK,CAACL,OAAO,GAAGU,IAAI,CAACO,KAAK,EAAEL,SAAS,CAACK,KAAK,GAAGgE,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;EAC5F;AACF,CAAC,CAAC;AACF,SACE7E,aAAa,IAAI8E,OAAO,EACxBrF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}