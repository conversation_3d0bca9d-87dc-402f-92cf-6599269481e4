{"ast": null, "code": "import { createVNode as _createVNode } from \"vue\";\nimport { useHeight } from \"./use-height.mjs\";\nfunction usePlaceholder(contentRef, bem) {\n  const height = useHeight(contentRef, true);\n  return renderContent => _createVNode(\"div\", {\n    \"class\": bem(\"placeholder\"),\n    \"style\": {\n      height: height.value ? `${height.value}px` : void 0\n    }\n  }, [renderContent()]);\n}\nexport { usePlaceholder };", "map": {"version": 3, "names": ["createVNode", "_createVNode", "useHeight", "usePlaceholder", "contentRef", "bem", "height", "renderContent", "value"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-placeholder.mjs"], "sourcesContent": ["import { createVNode as _createVNode } from \"vue\";\nimport { useHeight } from \"./use-height.mjs\";\nfunction usePlaceholder(contentRef, bem) {\n  const height = useHeight(contentRef, true);\n  return (renderContent) => _createVNode(\"div\", {\n    \"class\": bem(\"placeholder\"),\n    \"style\": {\n      height: height.value ? `${height.value}px` : void 0\n    }\n  }, [renderContent()]);\n}\nexport {\n  usePlaceholder\n};\n"], "mappings": "AAAA,SAASA,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,cAAcA,CAACC,UAAU,EAAEC,GAAG,EAAE;EACvC,MAAMC,MAAM,GAAGJ,SAAS,CAACE,UAAU,EAAE,IAAI,CAAC;EAC1C,OAAQG,aAAa,IAAKN,YAAY,CAAC,KAAK,EAAE;IAC5C,OAAO,EAAEI,GAAG,CAAC,aAAa,CAAC;IAC3B,OAAO,EAAE;MACPC,MAAM,EAAEA,MAAM,CAACE,KAAK,GAAG,GAAGF,MAAM,CAACE,KAAK,IAAI,GAAG,KAAK;IACpD;EACF,CAAC,EAAE,CAACD,aAAa,CAAC,CAAC,CAAC,CAAC;AACvB;AACA,SACEJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}