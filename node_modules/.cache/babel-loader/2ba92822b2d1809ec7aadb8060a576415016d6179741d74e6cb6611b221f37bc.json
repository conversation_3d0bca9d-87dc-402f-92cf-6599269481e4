{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _SubmitBar from \"./SubmitBar.mjs\";\nconst SubmitBar = withInstall(_SubmitBar);\nvar stdin_default = SubmitBar;\nimport { submitBarProps } from \"./SubmitBar.mjs\";\nexport { SubmitBar, stdin_default as default, submitBarProps };", "map": {"version": 3, "names": ["withInstall", "_SubmitBar", "SubmitBar", "stdin_default", "submitBarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/submit-bar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _SubmitBar from \"./SubmitBar.mjs\";\nconst SubmitBar = withInstall(_SubmitBar);\nvar stdin_default = SubmitBar;\nimport { submitBarProps } from \"./SubmitBar.mjs\";\nexport {\n  SubmitBar,\n  stdin_default as default,\n  submitBarProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SAASE,cAAc,QAAQ,iBAAiB;AAChD,SACEF,SAAS,EACTC,aAAa,IAAIE,OAAO,EACxBD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}