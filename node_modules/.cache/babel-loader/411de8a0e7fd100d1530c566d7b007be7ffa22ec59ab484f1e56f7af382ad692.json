{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton-image\");\nconst skeletonImageProps = {\n  imageSize: numericProp,\n  imageShape: makeStringProp(\"square\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: skeletonImageProps,\n  setup(props) {\n    return () => _createVNode(\"div\", {\n      \"class\": bem([props.imageShape]),\n      \"style\": getSizeStyle(props.imageSize)\n    }, [_createVNode(Icon, {\n      \"name\": \"photo\",\n      \"class\": bem(\"icon\")\n    }, null)]);\n  }\n});\nexport { stdin_default as default, skeletonImageProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "numericProp", "getSizeStyle", "makeStringProp", "createNamespace", "Icon", "name", "bem", "skeletonImageProps", "imageSize", "imageShape", "stdin_default", "props", "setup", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/skeleton-image/SkeletonImage.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton-image\");\nconst skeletonImageProps = {\n  imageSize: numericProp,\n  imageShape: makeStringProp(\"square\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: skeletonImageProps,\n  setup(props) {\n    return () => _createVNode(\"div\", {\n      \"class\": bem([props.imageShape]),\n      \"style\": getSizeStyle(props.imageSize)\n    }, [_createVNode(Icon, {\n      \"name\": \"photo\",\n      \"class\": bem(\"icon\")\n    }, null)]);\n  }\n});\nexport {\n  stdin_default as default,\n  skeletonImageProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC/F,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGH,eAAe,CAAC,gBAAgB,CAAC;AACrD,MAAMI,kBAAkB,GAAG;EACzBC,SAAS,EAAER,WAAW;EACtBS,UAAU,EAAEP,cAAc,CAAC,QAAQ;AACrC,CAAC;AACD,IAAIQ,aAAa,GAAGb,eAAe,CAAC;EAClCQ,IAAI;EACJM,KAAK,EAAEJ,kBAAkB;EACzBK,KAAKA,CAACD,KAAK,EAAE;IACX,OAAO,MAAMZ,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEO,GAAG,CAAC,CAACK,KAAK,CAACF,UAAU,CAAC,CAAC;MAChC,OAAO,EAAER,YAAY,CAACU,KAAK,CAACH,SAAS;IACvC,CAAC,EAAE,CAACT,YAAY,CAACK,IAAI,EAAE;MACrB,MAAM,EAAE,OAAO;MACf,OAAO,EAAEE,GAAG,CAAC,MAAM;IACrB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;AACF,CAAC,CAAC;AACF,SACEI,aAAa,IAAIG,OAAO,EACxBN,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}