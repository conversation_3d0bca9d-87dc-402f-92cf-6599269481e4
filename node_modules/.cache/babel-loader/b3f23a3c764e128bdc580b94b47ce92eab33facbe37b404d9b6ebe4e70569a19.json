{"ast": null, "code": "import { ref, watch, computed, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { pick, extend, makeArrayProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { pickerSharedProps } from \"../picker/Picker.mjs\";\nimport { INHERIT_PROPS, INHERIT_SLOTS, formatDataForCascade } from \"./utils.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Picker } from \"../picker/index.mjs\";\nconst [name, bem] = createNamespace(\"area\");\nconst areaProps = extend({}, pick(pickerSharedProps, INHERIT_PROPS), {\n  modelValue: String,\n  columnsNum: makeNumericProp(3),\n  columnsPlaceholder: makeArrayProp(),\n  areaList: {\n    type: Object,\n    default: () => ({})\n  }\n});\nvar stdin_default = defineComponent({\n  name,\n  props: areaProps,\n  emits: [\"change\", \"confirm\", \"cancel\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const codes = ref([]);\n    const picker = ref();\n    const columns = computed(() => formatDataForCascade(props));\n    const onChange = (...args) => emit(\"change\", ...args);\n    const onCancel = (...args) => emit(\"cancel\", ...args);\n    const onConfirm = (...args) => emit(\"confirm\", ...args);\n    watch(codes, newCodes => {\n      const lastCode = newCodes.length ? newCodes[newCodes.length - 1] : \"\";\n      if (lastCode && lastCode !== props.modelValue) {\n        emit(\"update:modelValue\", lastCode);\n      }\n    }, {\n      deep: true\n    });\n    watch(() => props.modelValue, newCode => {\n      if (newCode) {\n        const lastCode = codes.value.length ? codes.value[codes.value.length - 1] : \"\";\n        if (newCode !== lastCode) {\n          codes.value = [`${newCode.slice(0, 2)}0000`, `${newCode.slice(0, 4)}00`, newCode].slice(0, +props.columnsNum);\n        }\n      } else {\n        codes.value = [];\n      }\n    }, {\n      immediate: true\n    });\n    useExpose({\n      confirm: () => {\n        var _a;\n        return (_a = picker.value) == null ? void 0 : _a.confirm();\n      },\n      getSelectedOptions: () => {\n        var _a;\n        return ((_a = picker.value) == null ? void 0 : _a.getSelectedOptions()) || [];\n      }\n    });\n    return () => _createVNode(Picker, _mergeProps({\n      \"ref\": picker,\n      \"modelValue\": codes.value,\n      \"onUpdate:modelValue\": $event => codes.value = $event,\n      \"class\": bem(),\n      \"columns\": columns.value,\n      \"onChange\": onChange,\n      \"onCancel\": onCancel,\n      \"onConfirm\": onConfirm\n    }, pick(props, INHERIT_PROPS)), pick(slots, INHERIT_SLOTS));\n  }\n});\nexport { areaProps, stdin_default as default };", "map": {"version": 3, "names": ["ref", "watch", "computed", "defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "pick", "extend", "makeArrayProp", "makeNumericProp", "createNamespace", "pickerSharedProps", "INHERIT_PROPS", "INHERIT_SLOTS", "formatDataForCascade", "useExpose", "Picker", "name", "bem", "areaProps", "modelValue", "String", "columnsNum", "columnsPlaceholder", "areaList", "type", "Object", "default", "stdin_default", "props", "emits", "setup", "emit", "slots", "codes", "picker", "columns", "onChange", "args", "onCancel", "onConfirm", "newCodes", "lastCode", "length", "deep", "newCode", "value", "slice", "immediate", "confirm", "_a", "getSelectedOptions", "$event"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/area/Area.mjs"], "sourcesContent": ["import { ref, watch, computed, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { pick, extend, makeArrayProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { pickerSharedProps } from \"../picker/Picker.mjs\";\nimport { INHERIT_PROPS, INHERIT_SLOTS, formatDataForCascade } from \"./utils.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Picker } from \"../picker/index.mjs\";\nconst [name, bem] = createNamespace(\"area\");\nconst areaProps = extend({}, pick(pickerSharedProps, INHERIT_PROPS), {\n  modelValue: String,\n  columnsNum: makeNumericProp(3),\n  columnsPlaceholder: makeArrayProp(),\n  areaList: {\n    type: Object,\n    default: () => ({})\n  }\n});\nvar stdin_default = defineComponent({\n  name,\n  props: areaProps,\n  emits: [\"change\", \"confirm\", \"cancel\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const codes = ref([]);\n    const picker = ref();\n    const columns = computed(() => formatDataForCascade(props));\n    const onChange = (...args) => emit(\"change\", ...args);\n    const onCancel = (...args) => emit(\"cancel\", ...args);\n    const onConfirm = (...args) => emit(\"confirm\", ...args);\n    watch(codes, (newCodes) => {\n      const lastCode = newCodes.length ? newCodes[newCodes.length - 1] : \"\";\n      if (lastCode && lastCode !== props.modelValue) {\n        emit(\"update:modelValue\", lastCode);\n      }\n    }, {\n      deep: true\n    });\n    watch(() => props.modelValue, (newCode) => {\n      if (newCode) {\n        const lastCode = codes.value.length ? codes.value[codes.value.length - 1] : \"\";\n        if (newCode !== lastCode) {\n          codes.value = [`${newCode.slice(0, 2)}0000`, `${newCode.slice(0, 4)}00`, newCode].slice(0, +props.columnsNum);\n        }\n      } else {\n        codes.value = [];\n      }\n    }, {\n      immediate: true\n    });\n    useExpose({\n      confirm: () => {\n        var _a;\n        return (_a = picker.value) == null ? void 0 : _a.confirm();\n      },\n      getSelectedOptions: () => {\n        var _a;\n        return ((_a = picker.value) == null ? void 0 : _a.getSelectedOptions()) || [];\n      }\n    });\n    return () => _createVNode(Picker, _mergeProps({\n      \"ref\": picker,\n      \"modelValue\": codes.value,\n      \"onUpdate:modelValue\": ($event) => codes.value = $event,\n      \"class\": bem(),\n      \"columns\": columns.value,\n      \"onChange\": onChange,\n      \"onCancel\": onCancel,\n      \"onConfirm\": onConfirm\n    }, pick(props, INHERIT_PROPS)), pick(slots, INHERIT_SLOTS));\n  }\n});\nexport {\n  areaProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACnH,SAASC,IAAI,EAAEC,MAAM,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAClG,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,aAAa,EAAEC,aAAa,EAAEC,oBAAoB,QAAQ,aAAa;AAChF,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGR,eAAe,CAAC,MAAM,CAAC;AAC3C,MAAMS,SAAS,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAED,IAAI,CAACK,iBAAiB,EAAEC,aAAa,CAAC,EAAE;EACnEQ,UAAU,EAAEC,MAAM;EAClBC,UAAU,EAAEb,eAAe,CAAC,CAAC,CAAC;EAC9Bc,kBAAkB,EAAEf,aAAa,CAAC,CAAC;EACnCgB,QAAQ,EAAE;IACRC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;EACpB;AACF,CAAC,CAAC;AACF,IAAIC,aAAa,GAAG3B,eAAe,CAAC;EAClCgB,IAAI;EACJY,KAAK,EAAEV,SAAS;EAChBW,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,mBAAmB,CAAC;EAC3DC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGpC,GAAG,CAAC,EAAE,CAAC;IACrB,MAAMqC,MAAM,GAAGrC,GAAG,CAAC,CAAC;IACpB,MAAMsC,OAAO,GAAGpC,QAAQ,CAAC,MAAMc,oBAAoB,CAACe,KAAK,CAAC,CAAC;IAC3D,MAAMQ,QAAQ,GAAGA,CAAC,GAAGC,IAAI,KAAKN,IAAI,CAAC,QAAQ,EAAE,GAAGM,IAAI,CAAC;IACrD,MAAMC,QAAQ,GAAGA,CAAC,GAAGD,IAAI,KAAKN,IAAI,CAAC,QAAQ,EAAE,GAAGM,IAAI,CAAC;IACrD,MAAME,SAAS,GAAGA,CAAC,GAAGF,IAAI,KAAKN,IAAI,CAAC,SAAS,EAAE,GAAGM,IAAI,CAAC;IACvDvC,KAAK,CAACmC,KAAK,EAAGO,QAAQ,IAAK;MACzB,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,MAAM,GAAGF,QAAQ,CAACA,QAAQ,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;MACrE,IAAID,QAAQ,IAAIA,QAAQ,KAAKb,KAAK,CAACT,UAAU,EAAE;QAC7CY,IAAI,CAAC,mBAAmB,EAAEU,QAAQ,CAAC;MACrC;IACF,CAAC,EAAE;MACDE,IAAI,EAAE;IACR,CAAC,CAAC;IACF7C,KAAK,CAAC,MAAM8B,KAAK,CAACT,UAAU,EAAGyB,OAAO,IAAK;MACzC,IAAIA,OAAO,EAAE;QACX,MAAMH,QAAQ,GAAGR,KAAK,CAACY,KAAK,CAACH,MAAM,GAAGT,KAAK,CAACY,KAAK,CAACZ,KAAK,CAACY,KAAK,CAACH,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;QAC9E,IAAIE,OAAO,KAAKH,QAAQ,EAAE;UACxBR,KAAK,CAACY,KAAK,GAAG,CAAC,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAGF,OAAO,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAEF,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAClB,KAAK,CAACP,UAAU,CAAC;QAC/G;MACF,CAAC,MAAM;QACLY,KAAK,CAACY,KAAK,GAAG,EAAE;MAClB;IACF,CAAC,EAAE;MACDE,SAAS,EAAE;IACb,CAAC,CAAC;IACFjC,SAAS,CAAC;MACRkC,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIC,EAAE;QACN,OAAO,CAACA,EAAE,GAAGf,MAAM,CAACW,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,EAAE,CAACD,OAAO,CAAC,CAAC;MAC5D,CAAC;MACDE,kBAAkB,EAAEA,CAAA,KAAM;QACxB,IAAID,EAAE;QACN,OAAO,CAAC,CAACA,EAAE,GAAGf,MAAM,CAACW,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,EAAE,CAACC,kBAAkB,CAAC,CAAC,KAAK,EAAE;MAC/E;IACF,CAAC,CAAC;IACF,OAAO,MAAM9C,YAAY,CAACW,MAAM,EAAEb,WAAW,CAAC;MAC5C,KAAK,EAAEgC,MAAM;MACb,YAAY,EAAED,KAAK,CAACY,KAAK;MACzB,qBAAqB,EAAGM,MAAM,IAAKlB,KAAK,CAACY,KAAK,GAAGM,MAAM;MACvD,OAAO,EAAElC,GAAG,CAAC,CAAC;MACd,SAAS,EAAEkB,OAAO,CAACU,KAAK;MACxB,UAAU,EAAET,QAAQ;MACpB,UAAU,EAAEE,QAAQ;MACpB,WAAW,EAAEC;IACf,CAAC,EAAElC,IAAI,CAACuB,KAAK,EAAEjB,aAAa,CAAC,CAAC,EAAEN,IAAI,CAAC2B,KAAK,EAAEpB,aAAa,CAAC,CAAC;EAC7D;AACF,CAAC,CAAC;AACF,SACEM,SAAS,EACTS,aAAa,IAAID,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}