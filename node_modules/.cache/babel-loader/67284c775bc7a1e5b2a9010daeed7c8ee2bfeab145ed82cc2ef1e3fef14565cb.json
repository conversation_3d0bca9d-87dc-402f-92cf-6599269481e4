{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ContactList from \"./ContactList.mjs\";\nconst ContactList = withInstall(_ContactList);\nvar stdin_default = ContactList;\nimport { contactListProps } from \"./ContactList.mjs\";\nexport { ContactList, contactListProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ContactList", "ContactList", "stdin_default", "contactListProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/contact-list/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ContactList from \"./ContactList.mjs\";\nconst ContactList = withInstall(_ContactList);\nvar stdin_default = ContactList;\nimport { contactListProps } from \"./ContactList.mjs\";\nexport {\n  ContactList,\n  contactListProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXE,gBAAgB,EAChBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}