{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Switch from \"./Switch.mjs\";\nconst Switch = withInstall(_Switch);\nvar stdin_default = Switch;\nimport { switchProps } from \"./Switch.mjs\";\nexport { Switch, stdin_default as default, switchProps };", "map": {"version": 3, "names": ["withInstall", "_Switch", "Switch", "stdin_default", "switchProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/switch/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Switch from \"./Switch.mjs\";\nconst Switch = withInstall(_Switch);\nvar stdin_default = Switch;\nimport { switchProps } from \"./Switch.mjs\";\nexport {\n  Switch,\n  stdin_default as default,\n  switchProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEF,MAAM,EACNC,aAAa,IAAIE,OAAO,EACxBD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}