{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Tag from \"./Tag.mjs\";\nconst Tag = withInstall(_Tag);\nvar stdin_default = Tag;\nimport { tagProps } from \"./Tag.mjs\";\nexport { Tag, stdin_default as default, tagProps };", "map": {"version": 3, "names": ["withInstall", "_Tag", "Tag", "stdin_default", "tagProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tag/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Tag from \"./Tag.mjs\";\nconst Tag = withInstall(_Tag);\nvar stdin_default = Tag;\nimport { tagProps } from \"./Tag.mjs\";\nexport {\n  Tag,\n  stdin_default as default,\n  tagProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,IAAI,MAAM,WAAW;AAC5B,MAAMC,GAAG,GAAGF,WAAW,CAACC,IAAI,CAAC;AAC7B,IAAIE,aAAa,GAAGD,GAAG;AACvB,SAASE,QAAQ,QAAQ,WAAW;AACpC,SACEF,GAAG,EACHC,aAAa,IAAIE,OAAO,EACxBD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}