{"ast": null, "code": "import \"core-js/modules/es.suppressed-error.constructor.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\n/*!\n * qrcode.vue v3.6.0\n * A Vue.js component to generate QRCode. Both support Vue 2 and Vue 3\n * © 2017-PRESENT @scopewu(https://github.com/scopewu)\n * MIT License.\n */\nimport { defineComponent, ref, onUpdated, h, onMounted, Fragment } from 'vue';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/*\n * QR Code generator library (TypeScript)\n *\n * Copyright (c) Project Nayuki. (MIT License)\n * https://www.nayuki.io/page/qr-code-generator-library\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of\n * this software and associated documentation files (the \"Software\"), to deal in\n * the Software without restriction, including without limitation the rights to\n * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n * the Software, and to permit persons to whom the Software is furnished to do so,\n * subject to the following conditions:\n * - The above copyright notice and this permission notice shall be included in\n *   all copies or substantial portions of the Software.\n * - The Software is provided \"as is\", without warranty of any kind, express or\n *   implied, including but not limited to the warranties of merchantability,\n *   fitness for a particular purpose and noninfringement. In no event shall the\n *   authors or copyright holders be liable for any claim, damages or other\n *   liability, whether in an action of contract, tort or otherwise, arising from,\n *   out of or in connection with the Software or the use or other dealings in the\n *   Software.\n */\nvar qrcodegen;\n(function (qrcodegen) {\n  /*---- QR Code symbol class ----*/\n  /*\n   * A QR Code symbol, which is a type of two-dimension barcode.\n   * Invented by Denso Wave and described in the ISO/IEC 18004 standard.\n   * Instances of this class represent an immutable square grid of dark and light cells.\n   * The class provides static factory functions to create a QR Code from text or binary data.\n   * The class covers the QR Code Model 2 specification, supporting all versions (sizes)\n   * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.\n   *\n   * Ways to create a QR Code object:\n   * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().\n   * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().\n   * - Low level: Custom-make the array of data codeword bytes (including\n   *   segment headers and final padding, excluding error correction codewords),\n   *   supply the appropriate version number, and call the QrCode() constructor.\n   * (Note that all ways require supplying the desired error correction level.)\n   */\n  var QrCode = /** @class */function () {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code with the given version number,\n    // error correction level, data codeword bytes, and mask number.\n    // This is a low-level API that most users should not use directly.\n    // A mid-level API is the encodeSegments() function.\n    function QrCode(\n    // The version number of this QR Code, which is between 1 and 40 (inclusive).\n    // This determines the size of this barcode.\n    version,\n    // The error correction level used in this QR Code.\n    errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      // The modules of this QR Code (false = light, true = dark).\n      // Immutable after constructor finishes. Accessed through getModule().\n      this.modules = [];\n      // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n      this.isFunction = [];\n      // Check scalar arguments\n      if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION) throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7) throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      // Initialize both grids to be size*size arrays of Boolean false\n      var row = [];\n      for (var i = 0; i < this.size; i++) row.push(false);\n      for (var i = 0; i < this.size; i++) {\n        this.modules.push(row.slice()); // Initially all light\n        this.isFunction.push(row.slice());\n      }\n      // Compute ECC, draw modules\n      this.drawFunctionPatterns();\n      var allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      // Do masking\n      if (msk == -1) {\n        // Automatically choose best mask\n        var minPenalty = 1000000000;\n        for (var i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          var penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i); // Undoes the mask due to XOR\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk); // Apply the final choice of mask\n      this.drawFormatBits(msk); // Overwrite old format bits\n      this.isFunction = [];\n    }\n    /*-- Static factory functions (high level) --*/\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    QrCode.encodeText = function (text, ecl) {\n      var segs = qrcodegen.QrSegment.makeSegments(text);\n      return QrCode.encodeSegments(segs, ecl);\n    };\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n    QrCode.encodeBinary = function (data, ecl) {\n      var seg = qrcodegen.QrSegment.makeBytes(data);\n      return QrCode.encodeSegments([seg], ecl);\n    };\n    /*-- Static factory functions (mid level) --*/\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n    QrCode.encodeSegments = function (segs, ecl, minVersion, maxVersion, mask, boostEcl) {\n      if (minVersion === void 0) {\n        minVersion = 1;\n      }\n      if (maxVersion === void 0) {\n        maxVersion = 40;\n      }\n      if (mask === void 0) {\n        mask = -1;\n      }\n      if (boostEcl === void 0) {\n        boostEcl = true;\n      }\n      if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION) || mask < -1 || mask > 7) throw new RangeError(\"Invalid value\");\n      // Find the minimal version number to use\n      var version;\n      var dataUsedBits;\n      for (version = minVersion;; version++) {\n        var dataCapacityBits_1 = QrCode.getNumDataCodewords(version, ecl) * 8; // Number of data bits available\n        var usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits_1) {\n          dataUsedBits = usedBits;\n          break; // This version number is found to be suitable\n        }\n        if (version >= maxVersion)\n          // All versions in the range could not fit the given data\n          throw new RangeError(\"Data too long\");\n      }\n      // Increase the error correction level while the data still fits in the current version number\n      for (var _i = 0, _a = [QrCode.Ecc.MEDIUM, QrCode.Ecc.QUARTILE, QrCode.Ecc.HIGH]; _i < _a.length; _i++) {\n        // From low to high\n        var newEcl = _a[_i];\n        if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8) ecl = newEcl;\n      }\n      // Concatenate all segments to create the data bit string\n      var bb = [];\n      for (var _b = 0, segs_1 = segs; _b < segs_1.length; _b++) {\n        var seg = segs_1[_b];\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (var _c = 0, _d = seg.getData(); _c < _d.length; _c++) {\n          var b = _d[_c];\n          bb.push(b);\n        }\n      }\n      assert(bb.length == dataUsedBits);\n      // Add terminator and pad up to a byte if applicable\n      var dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      // Pad with alternating bytes until data capacity is reached\n      for (var padByte = 0xEC; bb.length < dataCapacityBits; padByte ^= 0xEC ^ 0x11) appendBits(padByte, 8, bb);\n      // Pack bits into bytes in big endian\n      var dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length) dataCodewords.push(0);\n      bb.forEach(function (b, i) {\n        return dataCodewords[i >>> 3] |= b << 7 - (i & 7);\n      });\n      // Create the QR Code object\n      return new QrCode(version, ecl, dataCodewords, mask);\n    };\n    /*-- Accessor methods --*/\n    // Returns the color of the module (pixel) at the given coordinates, which is false\n    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n    // If the given coordinates are out of bounds, then false (light) is returned.\n    QrCode.prototype.getModule = function (x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    };\n    QrCode.prototype.getModules = function () {\n      return this.modules;\n    };\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n    // Reads this object's version field, and draws and marks all function modules.\n    QrCode.prototype.drawFunctionPatterns = function () {\n      // Draw horizontal and vertical timing patterns\n      for (var i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      // Draw numerous alignment patterns\n      var alignPatPos = this.getAlignmentPatternPositions();\n      var numAlign = alignPatPos.length;\n      for (var i = 0; i < numAlign; i++) {\n        for (var j = 0; j < numAlign; j++) {\n          // Don't draw on the three finder corners\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0)) this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      // Draw configuration data\n      this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor\n      this.drawVersion();\n    };\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n    QrCode.prototype.drawFormatBits = function (mask) {\n      // Calculate error correction code and pack bits\n      var data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is uint2, mask is uint3\n      var rem = data;\n      for (var i = 0; i < 10; i++) rem = rem << 1 ^ (rem >>> 9) * 0x537;\n      var bits = (data << 10 | rem) ^ 0x5412; // uint15\n      assert(bits >>> 15 == 0);\n      // Draw first copy\n      for (var i = 0; i <= 5; i++) this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (var i = 9; i < 15; i++) this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      // Draw second copy\n      for (var i = 0; i < 8; i++) this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (var i = 8; i < 15; i++) this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true); // Always dark\n    };\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n    QrCode.prototype.drawVersion = function () {\n      if (this.version < 7) return;\n      // Calculate error correction code and pack bits\n      var rem = this.version; // version is uint6, in the range [7, 40]\n      for (var i = 0; i < 12; i++) rem = rem << 1 ^ (rem >>> 11) * 0x1F25;\n      var bits = this.version << 12 | rem; // uint18\n      assert(bits >>> 18 == 0);\n      // Draw two copies\n      for (var i = 0; i < 18; i++) {\n        var color = getBit(bits, i);\n        var a = this.size - 11 + i % 3;\n        var b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    };\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n    QrCode.prototype.drawFinderPattern = function (x, y) {\n      for (var dy = -4; dy <= 4; dy++) {\n        for (var dx = -4; dx <= 4; dx++) {\n          var dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm\n          var xx = x + dx;\n          var yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size) this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    };\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n    QrCode.prototype.drawAlignmentPattern = function (x, y) {\n      for (var dy = -2; dy <= 2; dy++) {\n        for (var dx = -2; dx <= 2; dx++) this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    };\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n    QrCode.prototype.setFunctionModule = function (x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    };\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n    QrCode.prototype.addEccAndInterleave = function (data) {\n      var ver = this.version;\n      var ecl = this.errorCorrectionLevel;\n      if (data.length != QrCode.getNumDataCodewords(ver, ecl)) throw new RangeError(\"Invalid argument\");\n      // Calculate parameter numbers\n      var numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      var blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      var rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);\n      var numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      var shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      // Split data into blocks and append ECC to each block\n      var blocks = [];\n      var rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (var i = 0, k = 0; i < numBlocks; i++) {\n        var dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        var ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks) dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      // Interleave (not concatenate) the bytes from every block into a single sequence\n      var result = [];\n      var _loop_1 = function (i) {\n        blocks.forEach(function (block, j) {\n          // Skip the padding byte in short blocks\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks) result.push(block[i]);\n        });\n      };\n      for (var i = 0; i < blocks[0].length; i++) {\n        _loop_1(i);\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    };\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n    QrCode.prototype.drawCodewords = function (data) {\n      if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8)) throw new RangeError(\"Invalid argument\");\n      var i = 0; // Bit index into the data\n      // Do the funny zigzag scan\n      for (var right = this.size - 1; right >= 1; right -= 2) {\n        // Index of right column in each column pair\n        if (right == 6) right = 5;\n        for (var vert = 0; vert < this.size; vert++) {\n          // Vertical counter\n          for (var j = 0; j < 2; j++) {\n            var x = right - j; // Actual x coordinate\n            var upward = (right + 1 & 2) == 0;\n            var y = upward ? this.size - 1 - vert : vert; // Actual y coordinate\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n            // If this QR Code has any remainder bits (0 to 7), they were assigned as\n            // 0/false/light by the constructor and are left unchanged by this method\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    };\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n    QrCode.prototype.applyMask = function (mask) {\n      if (mask < 0 || mask > 7) throw new RangeError(\"Mask value out of range\");\n      for (var y = 0; y < this.size; y++) {\n        for (var x = 0; x < this.size; x++) {\n          var invert = void 0;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert) this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    };\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n    QrCode.prototype.getPenaltyScore = function () {\n      var result = 0;\n      // Adjacent modules in row having same color, and finder-like patterns\n      for (var y = 0; y < this.size; y++) {\n        var runColor = false;\n        var runX = 0;\n        var runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (var x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5) result += QrCode.PENALTY_N1;else if (runX > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;\n      }\n      // Adjacent modules in column having same color, and finder-like patterns\n      for (var x = 0; x < this.size; x++) {\n        var runColor = false;\n        var runY = 0;\n        var runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (var y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5) result += QrCode.PENALTY_N1;else if (runY > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * QrCode.PENALTY_N3;\n      }\n      // 2*2 blocks of modules having same color\n      for (var y = 0; y < this.size - 1; y++) {\n        for (var x = 0; x < this.size - 1; x++) {\n          var color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1]) result += QrCode.PENALTY_N2;\n        }\n      }\n      // Balance of dark and light modules\n      var dark = 0;\n      for (var _i = 0, _a = this.modules; _i < _a.length; _i++) {\n        var row = _a[_i];\n        dark = row.reduce(function (sum, color) {\n          return sum + (color ? 1 : 0);\n        }, dark);\n      }\n      var total = this.size * this.size; // Note that size is odd, so dark/total != 1/2\n      // Compute the smallest integer k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%\n      var k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4\n      return result;\n    };\n    /*-- Private helper functions --*/\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of integers.\n    QrCode.prototype.getAlignmentPatternPositions = function () {\n      if (this.version == 1) return [];else {\n        var numAlign = Math.floor(this.version / 7) + 2;\n        var step = Math.floor((this.version * 8 + numAlign * 3 + 5) / (numAlign * 4 - 4)) * 2;\n        var result = [6];\n        for (var pos = this.size - 7; result.length < numAlign; pos -= step) result.splice(1, 0, pos);\n        return result;\n      }\n    };\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n    QrCode.getNumRawDataModules = function (ver) {\n      if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION) throw new RangeError(\"Version number out of range\");\n      var result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        var numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7) result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    };\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n    QrCode.getNumDataCodewords = function (ver, ecl) {\n      return Math.floor(QrCode.getNumRawDataModules(ver) / 8) - QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    };\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n    QrCode.reedSolomonComputeDivisor = function (degree) {\n      if (degree < 1 || degree > 255) throw new RangeError(\"Degree out of range\");\n      // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.\n      // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the uint8 array [255, 8, 93].\n      var result = [];\n      for (var i = 0; i < degree - 1; i++) result.push(0);\n      result.push(1); // Start off with the monomial x^0\n      // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),\n      // and drop the highest monomial term which is always 1x^degree.\n      // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).\n      var root = 1;\n      for (var i = 0; i < degree; i++) {\n        // Multiply the current product by (x - r^i)\n        for (var j = 0; j < result.length; j++) {\n          result[j] = QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length) result[j] ^= result[j + 1];\n        }\n        root = QrCode.reedSolomonMultiply(root, 0x02);\n      }\n      return result;\n    };\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n    QrCode.reedSolomonComputeRemainder = function (data, divisor) {\n      var result = divisor.map(function (_) {\n        return 0;\n      });\n      var _loop_2 = function (b) {\n        var factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach(function (coef, i) {\n          return result[i] ^= QrCode.reedSolomonMultiply(coef, factor);\n        });\n      };\n      for (var _i = 0, data_1 = data; _i < data_1.length; _i++) {\n        var b = data_1[_i];\n        _loop_2(b);\n      }\n      return result;\n    };\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n    QrCode.reedSolomonMultiply = function (x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0) throw new RangeError(\"Byte out of range\");\n      // Russian peasant multiplication\n      var z = 0;\n      for (var i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 0x11D;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    };\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    QrCode.prototype.finderPenaltyCountPatterns = function (runHistory) {\n      var n = runHistory[1];\n      assert(n <= this.size * 3);\n      var core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    };\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n    QrCode.prototype.finderPenaltyTerminateAndCount = function (currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        // Terminate dark run\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size; // Add light border to final run\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    };\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n    QrCode.prototype.finderPenaltyAddHistory = function (currentRunLength, runHistory) {\n      if (runHistory[0] == 0) currentRunLength += this.size; // Add light border to initial run\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    };\n    /*-- Constants and tables --*/\n    // The minimum version number supported in the QR Code Model 2 standard.\n    QrCode.MIN_VERSION = 1;\n    // The maximum version number supported in the QR Code Model 2 standard.\n    QrCode.MAX_VERSION = 40;\n    // For use in getPenaltyScore(), when evaluating which mask is best.\n    QrCode.PENALTY_N1 = 3;\n    QrCode.PENALTY_N2 = 3;\n    QrCode.PENALTY_N3 = 40;\n    QrCode.PENALTY_N4 = 10;\n    QrCode.ECC_CODEWORDS_PER_BLOCK = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Low\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    // Medium\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Quartile\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30] // High\n    ];\n    QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    // Low\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    // Medium\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    // Quartile\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81] // High\n    ];\n    return QrCode;\n  }();\n  qrcodegen.QrCode = QrCode;\n  // Appends the given number of low-order bits of the given value\n  // to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0) throw new RangeError(\"Value out of range\");\n    for (var i = len - 1; i >= 0; i--)\n    // Append bit by bit\n    bb.push(val >>> i & 1);\n  }\n  // Returns true iff the i'th bit of x is set to 1.\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  // Throws an exception if the given condition is false.\n  function assert(cond) {\n    if (!cond) throw new Error(\"Assertion error\");\n  }\n  /*---- Data segment class ----*/\n  /*\n   * A segment of character/binary/control data in a QR Code symbol.\n   * Instances of this class are immutable.\n   * The mid-level way to create a segment is to take the payload data\n   * and call a static factory function such as QrSegment.makeNumeric().\n   * The low-level way to create a segment is to custom-make the bit buffer\n   * and call the QrSegment() constructor with appropriate values.\n   * This segment class imposes no length restrictions, but QR Codes have restrictions.\n   * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.\n   * Any segment longer than this is meaningless for the purpose of generating QR Codes.\n   */\n  var QrSegment = /** @class */function () {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code segment with the given attributes and data.\n    // The character count (numChars) must agree with the mode and the bit buffer length,\n    // but the constraint isn't checked. The given bit buffer is cloned and stored.\n    function QrSegment(\n    // The mode indicator of this segment.\n    mode,\n    // The length of this segment's unencoded data. Measured in characters for\n    // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.\n    // Always zero or positive. Not the same as the data's bit length.\n    numChars,\n    // The data bits of this segment. Accessed through getData().\n    bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0) throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice(); // Make defensive copy\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    QrSegment.makeBytes = function (data) {\n      var bb = [];\n      for (var _i = 0, data_2 = data; _i < data_2.length; _i++) {\n        var b = data_2[_i];\n        appendBits(b, 8, bb);\n      }\n      return new QrSegment(QrSegment.Mode.BYTE, data.length, bb);\n    };\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n    QrSegment.makeNumeric = function (digits) {\n      if (!QrSegment.isNumeric(digits)) throw new RangeError(\"String contains non-numeric characters\");\n      var bb = [];\n      for (var i = 0; i < digits.length;) {\n        // Consume up to 3 digits per iteration\n        var n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new QrSegment(QrSegment.Mode.NUMERIC, digits.length, bb);\n    };\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    QrSegment.makeAlphanumeric = function (text) {\n      if (!QrSegment.isAlphanumeric(text)) throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      var bb = [];\n      var i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        // Process groups of 2\n        var temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        // 1 character remaining\n        appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new QrSegment(QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    };\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n    QrSegment.makeSegments = function (text) {\n      // Select the most efficient segment encoding automatically\n      if (text == \"\") return [];else if (QrSegment.isNumeric(text)) return [QrSegment.makeNumeric(text)];else if (QrSegment.isAlphanumeric(text)) return [QrSegment.makeAlphanumeric(text)];else return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];\n    };\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n    QrSegment.makeEci = function (assignVal) {\n      var bb = [];\n      if (assignVal < 0) throw new RangeError(\"ECI assignment value out of range\");else if (assignVal < 1 << 7) appendBits(assignVal, 8, bb);else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1000000) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else throw new RangeError(\"ECI assignment value out of range\");\n      return new QrSegment(QrSegment.Mode.ECI, 0, bb);\n    };\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n    QrSegment.isNumeric = function (text) {\n      return QrSegment.NUMERIC_REGEX.test(text);\n    };\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    QrSegment.isAlphanumeric = function (text) {\n      return QrSegment.ALPHANUMERIC_REGEX.test(text);\n    };\n    /*-- Methods --*/\n    // Returns a new copy of the data bits of this segment.\n    QrSegment.prototype.getData = function () {\n      return this.bitData.slice(); // Make defensive copy\n    };\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n    QrSegment.getTotalBits = function (segs, version) {\n      var result = 0;\n      for (var _i = 0, segs_2 = segs; _i < segs_2.length; _i++) {\n        var seg = segs_2[_i];\n        var ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits) return Infinity; // The segment's length doesn't fit the field's bit width\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    };\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n    QrSegment.toUtf8ByteArray = function (str) {\n      str = encodeURI(str);\n      var result = [];\n      for (var i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\") result.push(str.charCodeAt(i));else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    };\n    /*-- Constants --*/\n    // Describes precisely all strings that are encodable in numeric mode.\n    QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n    // Describes precisely all strings that are encodable in alphanumeric mode.\n    QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n    // The set of all legal characters in alphanumeric mode,\n    // where each character value maps to the index in the string.\n    QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n    return QrSegment;\n  }();\n  qrcodegen.QrSegment = QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n/*---- Public helper enumeration ----*/\n(function (qrcodegen) {\n  (function (QrCode) {\n    /*\n     * The error correction level in a QR Code symbol. Immutable.\n     */\n    var Ecc = /** @class */function () {\n      /*-- Constructor and fields --*/\n      function Ecc(\n      // In the range 0 to 3 (unsigned 2-bit integer).\n      ordinal,\n      // (Package-private) In the range 0 to 3 (unsigned 2-bit integer).\n      formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n      /*-- Constants --*/\n      Ecc.LOW = new Ecc(0, 1); // The QR Code can tolerate about  7% erroneous codewords\n      Ecc.MEDIUM = new Ecc(1, 0); // The QR Code can tolerate about 15% erroneous codewords\n      Ecc.QUARTILE = new Ecc(2, 3); // The QR Code can tolerate about 25% erroneous codewords\n      Ecc.HIGH = new Ecc(3, 2); // The QR Code can tolerate about 30% erroneous codewords\n      return Ecc;\n    }();\n    QrCode.Ecc = Ecc;\n  })(qrcodegen.QrCode || (qrcodegen.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n/*---- Public helper enumeration ----*/\n(function (qrcodegen) {\n  (function (QrSegment) {\n    /*\n     * Describes how a segment's data bits are interpreted. Immutable.\n     */\n    var Mode = /** @class */function () {\n      /*-- Constructor and fields --*/\n      function Mode(\n      // The mode indicator bits, which is a uint4 value (range 0 to 15).\n      modeBits,\n      // Number of character count bits for three different version ranges.\n      numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      /*-- Method --*/\n      // (Package-private) Returns the bit width of the character count field for a segment in\n      // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n      Mode.prototype.numCharCountBits = function (ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      };\n      /*-- Constants --*/\n      Mode.NUMERIC = new Mode(0x1, [10, 12, 14]);\n      Mode.ALPHANUMERIC = new Mode(0x2, [9, 11, 13]);\n      Mode.BYTE = new Mode(0x4, [8, 16, 16]);\n      Mode.KANJI = new Mode(0x8, [8, 10, 12]);\n      Mode.ECI = new Mode(0x7, [0, 0, 0]);\n      return Mode;\n    }();\n    QrSegment.Mode = Mode;\n  })(qrcodegen.QrSegment || (qrcodegen.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar QR = qrcodegen;\nvar defaultErrorCorrectLevel = 'L';\nvar ErrorCorrectLevelMap = {\n  L: QR.QrCode.Ecc.LOW,\n  M: QR.QrCode.Ecc.MEDIUM,\n  Q: QR.QrCode.Ecc.QUARTILE,\n  H: QR.QrCode.Ecc.HIGH\n};\n// Thanks the `qrcode.react`\nvar SUPPORTS_PATH2D = function () {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nfunction validErrorCorrectLevel(level) {\n  return level in ErrorCorrectLevelMap;\n}\nfunction generatePath(modules, margin) {\n  if (margin === void 0) {\n    margin = 0;\n  }\n  var ops = [];\n  modules.forEach(function (row, y) {\n    var start = null;\n    row.forEach(function (cell, x) {\n      if (!cell && start !== null) {\n        // M0 0h7v1H0z injects the space with the move and drops the comma,\n        // saving a char per operation\n        ops.push(\"M\".concat(start + margin, \" \").concat(y + margin, \"h\").concat(x - start, \"v1H\").concat(start + margin, \"z\"));\n        start = null;\n        return;\n      }\n      // end of row, clean up or skip\n      if (x === row.length - 1) {\n        if (!cell) {\n          // We would have closed the op above already so this can only mean\n          // 2+ light modules in a row.\n          return;\n        }\n        if (start === null) {\n          // Just a single dark module.\n          ops.push(\"M\".concat(x + margin, \",\").concat(y + margin, \" h1v1H\").concat(x + margin, \"z\"));\n        } else {\n          // Otherwise finish the current line.\n          ops.push(\"M\".concat(start + margin, \",\").concat(y + margin, \" h\").concat(x + 1 - start, \"v1H\").concat(start + margin, \"z\"));\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join('');\n}\nfunction getImageSettings(cells, size, margin, imageSettings) {\n  var width = imageSettings.width,\n    height = imageSettings.height,\n    imageX = imageSettings.x,\n    imageY = imageSettings.y;\n  var numCells = cells.length + margin * 2;\n  var defaultSize = Math.floor(size * 0.1);\n  var scale = numCells / size;\n  var w = (width || defaultSize) * scale;\n  var h = (height || defaultSize) * scale;\n  var x = imageX == null ? cells.length / 2 - w / 2 : imageX * scale;\n  var y = imageY == null ? cells.length / 2 - h / 2 : imageY * scale;\n  var excavation = null;\n  if (imageSettings.excavate) {\n    var floorX = Math.floor(x);\n    var floorY = Math.floor(y);\n    var ceilW = Math.ceil(w + x - floorX);\n    var ceilH = Math.ceil(h + y - floorY);\n    excavation = {\n      x: floorX,\n      y: floorY,\n      w: ceilW,\n      h: ceilH\n    };\n  }\n  return {\n    x: x,\n    y: y,\n    h: h,\n    w: w,\n    excavation: excavation\n  };\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map(function (row, y) {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map(function (cell, x) {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nvar QRCodeProps = {\n  value: {\n    type: String,\n    required: true,\n    default: ''\n  },\n  size: {\n    type: Number,\n    default: 100\n  },\n  level: {\n    type: String,\n    default: defaultErrorCorrectLevel,\n    validator: function (l) {\n      return validErrorCorrectLevel(l);\n    }\n  },\n  background: {\n    type: String,\n    default: '#fff'\n  },\n  foreground: {\n    type: String,\n    default: '#000'\n  },\n  margin: {\n    type: Number,\n    required: false,\n    default: 0\n  },\n  imageSettings: {\n    type: Object,\n    required: false,\n    default: function () {\n      return {};\n    }\n  },\n  gradient: {\n    type: Boolean,\n    required: false,\n    default: false\n  },\n  gradientType: {\n    type: String,\n    required: false,\n    default: 'linear',\n    validator: function (t) {\n      return ['linear', 'radial'].indexOf(t) > -1;\n    }\n  },\n  gradientStartColor: {\n    type: String,\n    required: false,\n    default: '#000'\n  },\n  gradientEndColor: {\n    type: String,\n    required: false,\n    default: '#fff'\n  }\n};\nvar QRCodeVueProps = __assign(__assign({}, QRCodeProps), {\n  renderAs: {\n    type: String,\n    required: false,\n    default: 'canvas',\n    validator: function (as) {\n      return ['canvas', 'svg'].indexOf(as) > -1;\n    }\n  }\n});\nvar QrcodeSvg = defineComponent({\n  name: 'QRCodeSvg',\n  props: QRCodeProps,\n  setup: function (props) {\n    var numCells = ref(0);\n    var fgPath = ref('');\n    var imageProps;\n    var generate = function () {\n      var value = props.value,\n        _level = props.level,\n        _margin = props.margin;\n      var margin = _margin >>> 0;\n      var level = validErrorCorrectLevel(_level) ? _level : defaultErrorCorrectLevel;\n      var cells = QR.QrCode.encodeText(value, ErrorCorrectLevelMap[level]).getModules();\n      numCells.value = cells.length + margin * 2;\n      if (props.imageSettings.src) {\n        var imageSettings = getImageSettings(cells, props.size, margin, props.imageSettings);\n        imageProps = {\n          x: imageSettings.x + margin,\n          y: imageSettings.y + margin,\n          width: imageSettings.w,\n          height: imageSettings.h\n        };\n        if (imageSettings.excavation) {\n          cells = excavateModules(cells, imageSettings.excavation);\n        }\n      }\n      // Drawing strategy: instead of a rect per module, we're going to create a\n      // single path for the dark modules and layer that on top of a light rect,\n      // for a total of 2 DOM nodes. We pay a bit more in string concat but that's\n      // way faster than DOM ops.\n      // For level 1, 441 nodes -> 2\n      // For level 40, 31329 -> 2\n      fgPath.value = generatePath(cells, margin);\n    };\n    var renderGradient = function () {\n      if (!props.gradient) return null;\n      var gradientProps = props.gradientType === 'linear' ? {\n        x1: '0%',\n        y1: '0%',\n        x2: '100%',\n        y2: '100%'\n      } : {\n        cx: '50%',\n        cy: '50%',\n        r: '50%',\n        fx: '50%',\n        fy: '50%'\n      };\n      return h(props.gradientType === 'linear' ? 'linearGradient' : 'radialGradient', __assign({\n        id: 'qr-gradient'\n      }, gradientProps), [h('stop', {\n        offset: '0%',\n        style: {\n          stopColor: props.gradientStartColor\n        }\n      }), h('stop', {\n        offset: '100%',\n        style: {\n          stopColor: props.gradientEndColor\n        }\n      })]);\n    };\n    generate();\n    onUpdated(generate);\n    return function () {\n      return h('svg', {\n        width: props.size,\n        height: props.size,\n        'shape-rendering': \"crispEdges\",\n        xmlns: 'http://www.w3.org/2000/svg',\n        viewBox: \"0 0 \".concat(numCells.value, \" \").concat(numCells.value)\n      }, [h('defs', {}, [renderGradient()]), h('rect', {\n        width: '100%',\n        height: '100%',\n        fill: props.background\n      }), h('path', {\n        fill: props.gradient ? 'url(#qr-gradient)' : props.foreground,\n        d: fgPath.value\n      }), props.imageSettings.src && h('image', __assign({\n        href: props.imageSettings.src\n      }, imageProps))]);\n    };\n  }\n});\nvar QrcodeCanvas = defineComponent({\n  name: 'QRCodeCanvas',\n  props: QRCodeProps,\n  setup: function (props, ctx) {\n    var canvasEl = ref(null);\n    var imageRef = ref(null);\n    var generate = function () {\n      var value = props.value,\n        _level = props.level,\n        size = props.size,\n        _margin = props.margin,\n        background = props.background,\n        foreground = props.foreground,\n        gradient = props.gradient,\n        gradientType = props.gradientType,\n        gradientStartColor = props.gradientStartColor,\n        gradientEndColor = props.gradientEndColor;\n      var margin = _margin >>> 0;\n      var level = validErrorCorrectLevel(_level) ? _level : defaultErrorCorrectLevel;\n      var canvas = canvasEl.value;\n      if (!canvas) {\n        return;\n      }\n      var ctx = canvas.getContext('2d');\n      if (!ctx) {\n        return;\n      }\n      var cells = QR.QrCode.encodeText(value, ErrorCorrectLevelMap[level]).getModules();\n      var numCells = cells.length + margin * 2;\n      var image = imageRef.value;\n      var imageProps = {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n      var showImage = props.imageSettings.src && image != null && image.naturalWidth !== 0 && image.naturalHeight !== 0;\n      if (showImage) {\n        var imageSettings = getImageSettings(cells, props.size, margin, props.imageSettings);\n        imageProps = {\n          x: imageSettings.x + margin,\n          y: imageSettings.y + margin,\n          width: imageSettings.w,\n          height: imageSettings.h\n        };\n        if (imageSettings.excavation) {\n          cells = excavateModules(cells, imageSettings.excavation);\n        }\n      }\n      var devicePixelRatio = window.devicePixelRatio || 1;\n      var scale = size / numCells * devicePixelRatio;\n      canvas.height = canvas.width = size * devicePixelRatio;\n      ctx.scale(scale, scale);\n      ctx.fillStyle = background;\n      ctx.fillRect(0, 0, numCells, numCells);\n      if (gradient) {\n        var grad = void 0;\n        if (gradientType === 'linear') {\n          grad = ctx.createLinearGradient(0, 0, numCells, numCells);\n        } else {\n          grad = ctx.createRadialGradient(numCells / 2, numCells / 2, 0, numCells / 2, numCells / 2, numCells / 2);\n        }\n        grad.addColorStop(0, gradientStartColor);\n        grad.addColorStop(1, gradientEndColor);\n        ctx.fillStyle = grad;\n      } else {\n        ctx.fillStyle = foreground;\n      }\n      if (SUPPORTS_PATH2D) {\n        ctx.fill(new Path2D(generatePath(cells, margin)));\n      } else {\n        cells.forEach(function (row, rdx) {\n          row.forEach(function (cell, cdx) {\n            if (cell) {\n              ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n            }\n          });\n        });\n      }\n      if (showImage) {\n        ctx.drawImage(image, imageProps.x, imageProps.y, imageProps.width, imageProps.height);\n      }\n    };\n    onMounted(generate);\n    onUpdated(generate);\n    var style = ctx.attrs.style;\n    return function () {\n      return h(Fragment, [h('canvas', __assign(__assign({}, ctx.attrs), {\n        ref: canvasEl,\n        style: __assign(__assign({}, style), {\n          width: \"\".concat(props.size, \"px\"),\n          height: \"\".concat(props.size, \"px\")\n        })\n      })), props.imageSettings.src && h('img', {\n        ref: imageRef,\n        src: props.imageSettings.src,\n        style: {\n          display: 'none'\n        },\n        onLoad: generate\n      })]);\n    };\n  }\n});\nvar QrcodeVue = defineComponent({\n  name: 'Qrcode',\n  render: function () {\n    var _a = this.$props,\n      renderAs = _a.renderAs,\n      value = _a.value,\n      size = _a.size,\n      margin = _a.margin,\n      level = _a.level,\n      background = _a.background,\n      foreground = _a.foreground,\n      imageSettings = _a.imageSettings,\n      gradient = _a.gradient,\n      gradientType = _a.gradientType,\n      gradientStartColor = _a.gradientStartColor,\n      gradientEndColor = _a.gradientEndColor;\n    return h(renderAs === 'svg' ? QrcodeSvg : QrcodeCanvas, {\n      value: value,\n      size: size,\n      margin: margin,\n      level: level,\n      background: background,\n      foreground: foreground,\n      imageSettings: imageSettings,\n      gradient: gradient,\n      gradientType: gradientType,\n      gradientStartColor: gradientStartColor,\n      gradientEndColor: gradientEndColor\n    });\n  },\n  props: QRCodeVueProps\n});\nexport { QrcodeCanvas, QrcodeSvg, QrcodeVue as default };", "map": {"version": 3, "names": ["defineComponent", "ref", "onUpdated", "h", "onMounted", "Fragment", "__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "SuppressedError", "error", "suppressed", "message", "e", "Error", "name", "qrcodegen", "QrCode", "version", "errorCorrectionLevel", "dataCodewords", "msk", "modules", "isFunction", "MIN_VERSION", "MAX_VERSION", "RangeError", "size", "row", "push", "slice", "drawFunctionPatterns", "allCodewords", "addEccAndInterleave", "drawCodewords", "min<PERSON><PERSON><PERSON><PERSON>", "applyMask", "drawFormatBits", "penalty", "getPenaltyScore", "assert", "mask", "encodeText", "text", "ecl", "segs", "QrSegment", "makeSegments", "encodeSegments", "encodeBinary", "data", "seg", "makeBytes", "minVersion", "maxVersion", "boostEcl", "dataUsedBits", "dataCapacityBits_1", "getNumDataCodewords", "usedBits", "getTotalBits", "_i", "_a", "Ecc", "MEDIUM", "QUARTILE", "HIGH", "newEcl", "bb", "_b", "segs_1", "appendBits", "mode", "modeBits", "numChars", "numCharCountBits", "_c", "_d", "getData", "b", "dataCapacityBits", "Math", "min", "padByte", "for<PERSON>ach", "getModule", "x", "y", "getModules", "setFunctionModule", "drawFinderPattern", "alignPatPos", "getAlignmentPatternPositions", "numAlign", "j", "drawAlignmentPattern", "drawVersion", "formatBits", "rem", "bits", "getBit", "color", "a", "floor", "dy", "dx", "dist", "max", "abs", "xx", "yy", "isDark", "ver", "numBlocks", "NUM_ERROR_CORRECTION_BLOCKS", "ordinal", "blockEccLen", "ECC_CODEWORDS_PER_BLOCK", "rawCodewords", "getNumRawDataModules", "numShortBlocks", "shortBlockLen", "blocks", "rsDiv", "reedSolomonComputeDivisor", "k", "dat", "ecc", "reedSolomonComputeRemainder", "concat", "result", "_loop_1", "block", "right", "vert", "upward", "invert", "runColor", "runX", "runHistory", "PENALTY_N1", "finderPenaltyAddHistory", "finderPenaltyCountPatterns", "PENALTY_N3", "finderPenaltyTerminateAndCount", "runY", "PENALTY_N2", "dark", "reduce", "sum", "total", "ceil", "PENALTY_N4", "step", "pos", "splice", "degree", "root", "reedSolomonMultiply", "divisor", "map", "_", "_loop_2", "factor", "shift", "coef", "data_1", "z", "core", "currentRunColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pop", "unshift", "val", "len", "cond", "bitData", "data_2", "Mode", "BYTE", "makeNumeric", "digits", "isNumeric", "parseInt", "substring", "NUMERIC", "makeAlphanumeric", "isAlphanumeric", "temp", "ALPHANUMERIC_CHARSET", "indexOf", "char<PERSON>t", "ALPHANUMERIC", "toUtf8ByteArray", "makeEci", "assignVal", "ECI", "NUMERIC_REGEX", "test", "ALPHANUMERIC_REGEX", "segs_2", "ccbits", "Infinity", "str", "encodeURI", "charCodeAt", "LOW", "numBitsCharCount", "KANJI", "QR", "defaultErrorCorrectLevel", "ErrorCorrectLevelMap", "L", "M", "Q", "H", "SUPPORTS_PATH2D", "Path2D", "addPath", "validErrorCorrectLevel", "level", "generatePath", "margin", "ops", "start", "cell", "join", "getImageSettings", "cells", "imageSettings", "width", "height", "imageX", "imageY", "num<PERSON>ells", "defaultSize", "scale", "w", "excavation", "excavate", "floorX", "floorY", "ceilW", "ceilH", "excavateModules", "QRCodeProps", "value", "type", "String", "required", "default", "Number", "validator", "l", "background", "foreground", "gradient", "Boolean", "gradientType", "gradientStartColor", "gradientEndColor", "QRCodeVueProps", "renderAs", "as", "QrcodeSvg", "props", "setup", "fgPath", "imageProps", "generate", "_level", "_margin", "src", "renderGradient", "gradientProps", "x1", "y1", "x2", "y2", "cx", "cy", "r", "fx", "fy", "id", "offset", "style", "stopColor", "xmlns", "viewBox", "fill", "d", "href", "QrcodeCanvas", "ctx", "canvasEl", "imageRef", "canvas", "getContext", "image", "showImage", "naturalWidth", "naturalHeight", "devicePixelRatio", "window", "fillStyle", "fillRect", "grad", "createLinearGradient", "createRadialGradient", "addColorStop", "rdx", "cdx", "drawImage", "attrs", "display", "onLoad", "QrcodeVue", "render", "$props"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/qrcode.vue/dist/qrcode.vue.esm.js"], "sourcesContent": ["/*!\n * qrcode.vue v3.6.0\n * A Vue.js component to generate QRCode. Both support Vue 2 and Vue 3\n * © 2017-PRESENT @scopewu(https://github.com/scopewu)\n * MIT License.\n */\nimport { defineComponent, ref, onUpdated, h, onMounted, Fragment } from 'vue';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/*\n * QR Code generator library (TypeScript)\n *\n * Copyright (c) Project Nayuki. (MIT License)\n * https://www.nayuki.io/page/qr-code-generator-library\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of\n * this software and associated documentation files (the \"Software\"), to deal in\n * the Software without restriction, including without limitation the rights to\n * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n * the Software, and to permit persons to whom the Software is furnished to do so,\n * subject to the following conditions:\n * - The above copyright notice and this permission notice shall be included in\n *   all copies or substantial portions of the Software.\n * - The Software is provided \"as is\", without warranty of any kind, express or\n *   implied, including but not limited to the warranties of merchantability,\n *   fitness for a particular purpose and noninfringement. In no event shall the\n *   authors or copyright holders be liable for any claim, damages or other\n *   liability, whether in an action of contract, tort or otherwise, arising from,\n *   out of or in connection with the Software or the use or other dealings in the\n *   Software.\n */\nvar qrcodegen;\n(function (qrcodegen) {\n    /*---- QR Code symbol class ----*/\n    /*\n     * A QR Code symbol, which is a type of two-dimension barcode.\n     * Invented by Denso Wave and described in the ISO/IEC 18004 standard.\n     * Instances of this class represent an immutable square grid of dark and light cells.\n     * The class provides static factory functions to create a QR Code from text or binary data.\n     * The class covers the QR Code Model 2 specification, supporting all versions (sizes)\n     * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.\n     *\n     * Ways to create a QR Code object:\n     * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().\n     * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().\n     * - Low level: Custom-make the array of data codeword bytes (including\n     *   segment headers and final padding, excluding error correction codewords),\n     *   supply the appropriate version number, and call the QrCode() constructor.\n     * (Note that all ways require supplying the desired error correction level.)\n     */\n    var QrCode = /** @class */ (function () {\n        /*-- Constructor (low level) and fields --*/\n        // Creates a new QR Code with the given version number,\n        // error correction level, data codeword bytes, and mask number.\n        // This is a low-level API that most users should not use directly.\n        // A mid-level API is the encodeSegments() function.\n        function QrCode(\n        // The version number of this QR Code, which is between 1 and 40 (inclusive).\n        // This determines the size of this barcode.\n        version, \n        // The error correction level used in this QR Code.\n        errorCorrectionLevel, dataCodewords, msk) {\n            this.version = version;\n            this.errorCorrectionLevel = errorCorrectionLevel;\n            // The modules of this QR Code (false = light, true = dark).\n            // Immutable after constructor finishes. Accessed through getModule().\n            this.modules = [];\n            // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n            this.isFunction = [];\n            // Check scalar arguments\n            if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION)\n                throw new RangeError(\"Version value out of range\");\n            if (msk < -1 || msk > 7)\n                throw new RangeError(\"Mask value out of range\");\n            this.size = version * 4 + 17;\n            // Initialize both grids to be size*size arrays of Boolean false\n            var row = [];\n            for (var i = 0; i < this.size; i++)\n                row.push(false);\n            for (var i = 0; i < this.size; i++) {\n                this.modules.push(row.slice()); // Initially all light\n                this.isFunction.push(row.slice());\n            }\n            // Compute ECC, draw modules\n            this.drawFunctionPatterns();\n            var allCodewords = this.addEccAndInterleave(dataCodewords);\n            this.drawCodewords(allCodewords);\n            // Do masking\n            if (msk == -1) { // Automatically choose best mask\n                var minPenalty = 1000000000;\n                for (var i = 0; i < 8; i++) {\n                    this.applyMask(i);\n                    this.drawFormatBits(i);\n                    var penalty = this.getPenaltyScore();\n                    if (penalty < minPenalty) {\n                        msk = i;\n                        minPenalty = penalty;\n                    }\n                    this.applyMask(i); // Undoes the mask due to XOR\n                }\n            }\n            assert(0 <= msk && msk <= 7);\n            this.mask = msk;\n            this.applyMask(msk); // Apply the final choice of mask\n            this.drawFormatBits(msk); // Overwrite old format bits\n            this.isFunction = [];\n        }\n        /*-- Static factory functions (high level) --*/\n        // Returns a QR Code representing the given Unicode text string at the given error correction level.\n        // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n        // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n        // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n        // ecl argument if it can be done without increasing the version.\n        QrCode.encodeText = function (text, ecl) {\n            var segs = qrcodegen.QrSegment.makeSegments(text);\n            return QrCode.encodeSegments(segs, ecl);\n        };\n        // Returns a QR Code representing the given binary data at the given error correction level.\n        // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n        // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n        // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n        QrCode.encodeBinary = function (data, ecl) {\n            var seg = qrcodegen.QrSegment.makeBytes(data);\n            return QrCode.encodeSegments([seg], ecl);\n        };\n        /*-- Static factory functions (mid level) --*/\n        // Returns a QR Code representing the given segments with the given encoding parameters.\n        // The smallest possible QR Code version within the given range is automatically\n        // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n        // may be higher than the ecl argument if it can be done without increasing the\n        // version. The mask number is either between 0 to 7 (inclusive) to force that\n        // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n        // This function allows the user to create a custom sequence of segments that switches\n        // between modes (such as alphanumeric and byte) to encode text in less space.\n        // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n        QrCode.encodeSegments = function (segs, ecl, minVersion, maxVersion, mask, boostEcl) {\n            if (minVersion === void 0) { minVersion = 1; }\n            if (maxVersion === void 0) { maxVersion = 40; }\n            if (mask === void 0) { mask = -1; }\n            if (boostEcl === void 0) { boostEcl = true; }\n            if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION)\n                || mask < -1 || mask > 7)\n                throw new RangeError(\"Invalid value\");\n            // Find the minimal version number to use\n            var version;\n            var dataUsedBits;\n            for (version = minVersion;; version++) {\n                var dataCapacityBits_1 = QrCode.getNumDataCodewords(version, ecl) * 8; // Number of data bits available\n                var usedBits = QrSegment.getTotalBits(segs, version);\n                if (usedBits <= dataCapacityBits_1) {\n                    dataUsedBits = usedBits;\n                    break; // This version number is found to be suitable\n                }\n                if (version >= maxVersion) // All versions in the range could not fit the given data\n                    throw new RangeError(\"Data too long\");\n            }\n            // Increase the error correction level while the data still fits in the current version number\n            for (var _i = 0, _a = [QrCode.Ecc.MEDIUM, QrCode.Ecc.QUARTILE, QrCode.Ecc.HIGH]; _i < _a.length; _i++) { // From low to high\n                var newEcl = _a[_i];\n                if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8)\n                    ecl = newEcl;\n            }\n            // Concatenate all segments to create the data bit string\n            var bb = [];\n            for (var _b = 0, segs_1 = segs; _b < segs_1.length; _b++) {\n                var seg = segs_1[_b];\n                appendBits(seg.mode.modeBits, 4, bb);\n                appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n                for (var _c = 0, _d = seg.getData(); _c < _d.length; _c++) {\n                    var b = _d[_c];\n                    bb.push(b);\n                }\n            }\n            assert(bb.length == dataUsedBits);\n            // Add terminator and pad up to a byte if applicable\n            var dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;\n            assert(bb.length <= dataCapacityBits);\n            appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n            appendBits(0, (8 - bb.length % 8) % 8, bb);\n            assert(bb.length % 8 == 0);\n            // Pad with alternating bytes until data capacity is reached\n            for (var padByte = 0xEC; bb.length < dataCapacityBits; padByte ^= 0xEC ^ 0x11)\n                appendBits(padByte, 8, bb);\n            // Pack bits into bytes in big endian\n            var dataCodewords = [];\n            while (dataCodewords.length * 8 < bb.length)\n                dataCodewords.push(0);\n            bb.forEach(function (b, i) {\n                return dataCodewords[i >>> 3] |= b << (7 - (i & 7));\n            });\n            // Create the QR Code object\n            return new QrCode(version, ecl, dataCodewords, mask);\n        };\n        /*-- Accessor methods --*/\n        // Returns the color of the module (pixel) at the given coordinates, which is false\n        // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n        // If the given coordinates are out of bounds, then false (light) is returned.\n        QrCode.prototype.getModule = function (x, y) {\n            return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n        };\n        QrCode.prototype.getModules = function () {\n            return this.modules;\n        };\n        /*-- Private helper methods for constructor: Drawing function modules --*/\n        // Reads this object's version field, and draws and marks all function modules.\n        QrCode.prototype.drawFunctionPatterns = function () {\n            // Draw horizontal and vertical timing patterns\n            for (var i = 0; i < this.size; i++) {\n                this.setFunctionModule(6, i, i % 2 == 0);\n                this.setFunctionModule(i, 6, i % 2 == 0);\n            }\n            // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)\n            this.drawFinderPattern(3, 3);\n            this.drawFinderPattern(this.size - 4, 3);\n            this.drawFinderPattern(3, this.size - 4);\n            // Draw numerous alignment patterns\n            var alignPatPos = this.getAlignmentPatternPositions();\n            var numAlign = alignPatPos.length;\n            for (var i = 0; i < numAlign; i++) {\n                for (var j = 0; j < numAlign; j++) {\n                    // Don't draw on the three finder corners\n                    if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n                        this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n                }\n            }\n            // Draw configuration data\n            this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor\n            this.drawVersion();\n        };\n        // Draws two copies of the format bits (with its own error correction code)\n        // based on the given mask and this object's error correction level field.\n        QrCode.prototype.drawFormatBits = function (mask) {\n            // Calculate error correction code and pack bits\n            var data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is uint2, mask is uint3\n            var rem = data;\n            for (var i = 0; i < 10; i++)\n                rem = (rem << 1) ^ ((rem >>> 9) * 0x537);\n            var bits = (data << 10 | rem) ^ 0x5412; // uint15\n            assert(bits >>> 15 == 0);\n            // Draw first copy\n            for (var i = 0; i <= 5; i++)\n                this.setFunctionModule(8, i, getBit(bits, i));\n            this.setFunctionModule(8, 7, getBit(bits, 6));\n            this.setFunctionModule(8, 8, getBit(bits, 7));\n            this.setFunctionModule(7, 8, getBit(bits, 8));\n            for (var i = 9; i < 15; i++)\n                this.setFunctionModule(14 - i, 8, getBit(bits, i));\n            // Draw second copy\n            for (var i = 0; i < 8; i++)\n                this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n            for (var i = 8; i < 15; i++)\n                this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n            this.setFunctionModule(8, this.size - 8, true); // Always dark\n        };\n        // Draws two copies of the version bits (with its own error correction code),\n        // based on this object's version field, iff 7 <= version <= 40.\n        QrCode.prototype.drawVersion = function () {\n            if (this.version < 7)\n                return;\n            // Calculate error correction code and pack bits\n            var rem = this.version; // version is uint6, in the range [7, 40]\n            for (var i = 0; i < 12; i++)\n                rem = (rem << 1) ^ ((rem >>> 11) * 0x1F25);\n            var bits = this.version << 12 | rem; // uint18\n            assert(bits >>> 18 == 0);\n            // Draw two copies\n            for (var i = 0; i < 18; i++) {\n                var color = getBit(bits, i);\n                var a = this.size - 11 + i % 3;\n                var b = Math.floor(i / 3);\n                this.setFunctionModule(a, b, color);\n                this.setFunctionModule(b, a, color);\n            }\n        };\n        // Draws a 9*9 finder pattern including the border separator,\n        // with the center module at (x, y). Modules can be out of bounds.\n        QrCode.prototype.drawFinderPattern = function (x, y) {\n            for (var dy = -4; dy <= 4; dy++) {\n                for (var dx = -4; dx <= 4; dx++) {\n                    var dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm\n                    var xx = x + dx;\n                    var yy = y + dy;\n                    if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n                        this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n                }\n            }\n        };\n        // Draws a 5*5 alignment pattern, with the center module\n        // at (x, y). All modules must be in bounds.\n        QrCode.prototype.drawAlignmentPattern = function (x, y) {\n            for (var dy = -2; dy <= 2; dy++) {\n                for (var dx = -2; dx <= 2; dx++)\n                    this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n            }\n        };\n        // Sets the color of a module and marks it as a function module.\n        // Only used by the constructor. Coordinates must be in bounds.\n        QrCode.prototype.setFunctionModule = function (x, y, isDark) {\n            this.modules[y][x] = isDark;\n            this.isFunction[y][x] = true;\n        };\n        /*-- Private helper methods for constructor: Codewords and masking --*/\n        // Returns a new byte string representing the given data with the appropriate error correction\n        // codewords appended to it, based on this object's version and error correction level.\n        QrCode.prototype.addEccAndInterleave = function (data) {\n            var ver = this.version;\n            var ecl = this.errorCorrectionLevel;\n            if (data.length != QrCode.getNumDataCodewords(ver, ecl))\n                throw new RangeError(\"Invalid argument\");\n            // Calculate parameter numbers\n            var numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n            var blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n            var rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);\n            var numShortBlocks = numBlocks - rawCodewords % numBlocks;\n            var shortBlockLen = Math.floor(rawCodewords / numBlocks);\n            // Split data into blocks and append ECC to each block\n            var blocks = [];\n            var rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);\n            for (var i = 0, k = 0; i < numBlocks; i++) {\n                var dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n                k += dat.length;\n                var ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n                if (i < numShortBlocks)\n                    dat.push(0);\n                blocks.push(dat.concat(ecc));\n            }\n            // Interleave (not concatenate) the bytes from every block into a single sequence\n            var result = [];\n            var _loop_1 = function (i) {\n                blocks.forEach(function (block, j) {\n                    // Skip the padding byte in short blocks\n                    if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n                        result.push(block[i]);\n                });\n            };\n            for (var i = 0; i < blocks[0].length; i++) {\n                _loop_1(i);\n            }\n            assert(result.length == rawCodewords);\n            return result;\n        };\n        // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n        // data area of this QR Code. Function modules need to be marked off before this is called.\n        QrCode.prototype.drawCodewords = function (data) {\n            if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8))\n                throw new RangeError(\"Invalid argument\");\n            var i = 0; // Bit index into the data\n            // Do the funny zigzag scan\n            for (var right = this.size - 1; right >= 1; right -= 2) { // Index of right column in each column pair\n                if (right == 6)\n                    right = 5;\n                for (var vert = 0; vert < this.size; vert++) { // Vertical counter\n                    for (var j = 0; j < 2; j++) {\n                        var x = right - j; // Actual x coordinate\n                        var upward = ((right + 1) & 2) == 0;\n                        var y = upward ? this.size - 1 - vert : vert; // Actual y coordinate\n                        if (!this.isFunction[y][x] && i < data.length * 8) {\n                            this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n                            i++;\n                        }\n                        // If this QR Code has any remainder bits (0 to 7), they were assigned as\n                        // 0/false/light by the constructor and are left unchanged by this method\n                    }\n                }\n            }\n            assert(i == data.length * 8);\n        };\n        // XORs the codeword modules in this QR Code with the given mask pattern.\n        // The function modules must be marked and the codeword bits must be drawn\n        // before masking. Due to the arithmetic of XOR, calling applyMask() with\n        // the same mask value a second time will undo the mask. A final well-formed\n        // QR Code needs exactly one (not zero, two, etc.) mask applied.\n        QrCode.prototype.applyMask = function (mask) {\n            if (mask < 0 || mask > 7)\n                throw new RangeError(\"Mask value out of range\");\n            for (var y = 0; y < this.size; y++) {\n                for (var x = 0; x < this.size; x++) {\n                    var invert = void 0;\n                    switch (mask) {\n                        case 0:\n                            invert = (x + y) % 2 == 0;\n                            break;\n                        case 1:\n                            invert = y % 2 == 0;\n                            break;\n                        case 2:\n                            invert = x % 3 == 0;\n                            break;\n                        case 3:\n                            invert = (x + y) % 3 == 0;\n                            break;\n                        case 4:\n                            invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n                            break;\n                        case 5:\n                            invert = x * y % 2 + x * y % 3 == 0;\n                            break;\n                        case 6:\n                            invert = (x * y % 2 + x * y % 3) % 2 == 0;\n                            break;\n                        case 7:\n                            invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n                            break;\n                        default: throw new Error(\"Unreachable\");\n                    }\n                    if (!this.isFunction[y][x] && invert)\n                        this.modules[y][x] = !this.modules[y][x];\n                }\n            }\n        };\n        // Calculates and returns the penalty score based on state of this QR Code's current modules.\n        // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n        QrCode.prototype.getPenaltyScore = function () {\n            var result = 0;\n            // Adjacent modules in row having same color, and finder-like patterns\n            for (var y = 0; y < this.size; y++) {\n                var runColor = false;\n                var runX = 0;\n                var runHistory = [0, 0, 0, 0, 0, 0, 0];\n                for (var x = 0; x < this.size; x++) {\n                    if (this.modules[y][x] == runColor) {\n                        runX++;\n                        if (runX == 5)\n                            result += QrCode.PENALTY_N1;\n                        else if (runX > 5)\n                            result++;\n                    }\n                    else {\n                        this.finderPenaltyAddHistory(runX, runHistory);\n                        if (!runColor)\n                            result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n                        runColor = this.modules[y][x];\n                        runX = 1;\n                    }\n                }\n                result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;\n            }\n            // Adjacent modules in column having same color, and finder-like patterns\n            for (var x = 0; x < this.size; x++) {\n                var runColor = false;\n                var runY = 0;\n                var runHistory = [0, 0, 0, 0, 0, 0, 0];\n                for (var y = 0; y < this.size; y++) {\n                    if (this.modules[y][x] == runColor) {\n                        runY++;\n                        if (runY == 5)\n                            result += QrCode.PENALTY_N1;\n                        else if (runY > 5)\n                            result++;\n                    }\n                    else {\n                        this.finderPenaltyAddHistory(runY, runHistory);\n                        if (!runColor)\n                            result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;\n                        runColor = this.modules[y][x];\n                        runY = 1;\n                    }\n                }\n                result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * QrCode.PENALTY_N3;\n            }\n            // 2*2 blocks of modules having same color\n            for (var y = 0; y < this.size - 1; y++) {\n                for (var x = 0; x < this.size - 1; x++) {\n                    var color = this.modules[y][x];\n                    if (color == this.modules[y][x + 1] &&\n                        color == this.modules[y + 1][x] &&\n                        color == this.modules[y + 1][x + 1])\n                        result += QrCode.PENALTY_N2;\n                }\n            }\n            // Balance of dark and light modules\n            var dark = 0;\n            for (var _i = 0, _a = this.modules; _i < _a.length; _i++) {\n                var row = _a[_i];\n                dark = row.reduce(function (sum, color) { return sum + (color ? 1 : 0); }, dark);\n            }\n            var total = this.size * this.size; // Note that size is odd, so dark/total != 1/2\n            // Compute the smallest integer k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%\n            var k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n            assert(0 <= k && k <= 9);\n            result += k * QrCode.PENALTY_N4;\n            assert(0 <= result && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4\n            return result;\n        };\n        /*-- Private helper functions --*/\n        // Returns an ascending list of positions of alignment patterns for this version number.\n        // Each position is in the range [0,177), and are used on both the x and y axes.\n        // This could be implemented as lookup table of 40 variable-length lists of integers.\n        QrCode.prototype.getAlignmentPatternPositions = function () {\n            if (this.version == 1)\n                return [];\n            else {\n                var numAlign = Math.floor(this.version / 7) + 2;\n                var step = Math.floor((this.version * 8 + numAlign * 3 + 5) / (numAlign * 4 - 4)) * 2;\n                var result = [6];\n                for (var pos = this.size - 7; result.length < numAlign; pos -= step)\n                    result.splice(1, 0, pos);\n                return result;\n            }\n        };\n        // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n        // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n        // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n        QrCode.getNumRawDataModules = function (ver) {\n            if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION)\n                throw new RangeError(\"Version number out of range\");\n            var result = (16 * ver + 128) * ver + 64;\n            if (ver >= 2) {\n                var numAlign = Math.floor(ver / 7) + 2;\n                result -= (25 * numAlign - 10) * numAlign - 55;\n                if (ver >= 7)\n                    result -= 36;\n            }\n            assert(208 <= result && result <= 29648);\n            return result;\n        };\n        // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n        // QR Code of the given version number and error correction level, with remainder bits discarded.\n        // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n        QrCode.getNumDataCodewords = function (ver, ecl) {\n            return Math.floor(QrCode.getNumRawDataModules(ver) / 8) -\n                QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] *\n                    QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n        };\n        // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n        // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n        QrCode.reedSolomonComputeDivisor = function (degree) {\n            if (degree < 1 || degree > 255)\n                throw new RangeError(\"Degree out of range\");\n            // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.\n            // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the uint8 array [255, 8, 93].\n            var result = [];\n            for (var i = 0; i < degree - 1; i++)\n                result.push(0);\n            result.push(1); // Start off with the monomial x^0\n            // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),\n            // and drop the highest monomial term which is always 1x^degree.\n            // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).\n            var root = 1;\n            for (var i = 0; i < degree; i++) {\n                // Multiply the current product by (x - r^i)\n                for (var j = 0; j < result.length; j++) {\n                    result[j] = QrCode.reedSolomonMultiply(result[j], root);\n                    if (j + 1 < result.length)\n                        result[j] ^= result[j + 1];\n                }\n                root = QrCode.reedSolomonMultiply(root, 0x02);\n            }\n            return result;\n        };\n        // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n        QrCode.reedSolomonComputeRemainder = function (data, divisor) {\n            var result = divisor.map(function (_) { return 0; });\n            var _loop_2 = function (b) {\n                var factor = b ^ result.shift();\n                result.push(0);\n                divisor.forEach(function (coef, i) {\n                    return result[i] ^= QrCode.reedSolomonMultiply(coef, factor);\n                });\n            };\n            for (var _i = 0, data_1 = data; _i < data_1.length; _i++) {\n                var b = data_1[_i];\n                _loop_2(b);\n            }\n            return result;\n        };\n        // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n        // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n        QrCode.reedSolomonMultiply = function (x, y) {\n            if (x >>> 8 != 0 || y >>> 8 != 0)\n                throw new RangeError(\"Byte out of range\");\n            // Russian peasant multiplication\n            var z = 0;\n            for (var i = 7; i >= 0; i--) {\n                z = (z << 1) ^ ((z >>> 7) * 0x11D);\n                z ^= ((y >>> i) & 1) * x;\n            }\n            assert(z >>> 8 == 0);\n            return z;\n        };\n        // Can only be called immediately after a light run is added, and\n        // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n        QrCode.prototype.finderPenaltyCountPatterns = function (runHistory) {\n            var n = runHistory[1];\n            assert(n <= this.size * 3);\n            var core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n            return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0)\n                + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n        };\n        // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n        QrCode.prototype.finderPenaltyTerminateAndCount = function (currentRunColor, currentRunLength, runHistory) {\n            if (currentRunColor) { // Terminate dark run\n                this.finderPenaltyAddHistory(currentRunLength, runHistory);\n                currentRunLength = 0;\n            }\n            currentRunLength += this.size; // Add light border to final run\n            this.finderPenaltyAddHistory(currentRunLength, runHistory);\n            return this.finderPenaltyCountPatterns(runHistory);\n        };\n        // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n        QrCode.prototype.finderPenaltyAddHistory = function (currentRunLength, runHistory) {\n            if (runHistory[0] == 0)\n                currentRunLength += this.size; // Add light border to initial run\n            runHistory.pop();\n            runHistory.unshift(currentRunLength);\n        };\n        /*-- Constants and tables --*/\n        // The minimum version number supported in the QR Code Model 2 standard.\n        QrCode.MIN_VERSION = 1;\n        // The maximum version number supported in the QR Code Model 2 standard.\n        QrCode.MAX_VERSION = 40;\n        // For use in getPenaltyScore(), when evaluating which mask is best.\n        QrCode.PENALTY_N1 = 3;\n        QrCode.PENALTY_N2 = 3;\n        QrCode.PENALTY_N3 = 40;\n        QrCode.PENALTY_N4 = 10;\n        QrCode.ECC_CODEWORDS_PER_BLOCK = [\n            // Version: (note that index 0 is for padding, and is set to an illegal value)\n            //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n            [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], // Low\n            [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28], // Medium\n            [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], // Quartile\n            [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], // High\n        ];\n        QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n            // Version: (note that index 0 is for padding, and is set to an illegal value)\n            //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n            [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25], // Low\n            [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49], // Medium\n            [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68], // Quartile\n            [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81], // High\n        ];\n        return QrCode;\n    }());\n    qrcodegen.QrCode = QrCode;\n    // Appends the given number of low-order bits of the given value\n    // to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.\n    function appendBits(val, len, bb) {\n        if (len < 0 || len > 31 || val >>> len != 0)\n            throw new RangeError(\"Value out of range\");\n        for (var i = len - 1; i >= 0; i--) // Append bit by bit\n            bb.push((val >>> i) & 1);\n    }\n    // Returns true iff the i'th bit of x is set to 1.\n    function getBit(x, i) {\n        return ((x >>> i) & 1) != 0;\n    }\n    // Throws an exception if the given condition is false.\n    function assert(cond) {\n        if (!cond)\n            throw new Error(\"Assertion error\");\n    }\n    /*---- Data segment class ----*/\n    /*\n     * A segment of character/binary/control data in a QR Code symbol.\n     * Instances of this class are immutable.\n     * The mid-level way to create a segment is to take the payload data\n     * and call a static factory function such as QrSegment.makeNumeric().\n     * The low-level way to create a segment is to custom-make the bit buffer\n     * and call the QrSegment() constructor with appropriate values.\n     * This segment class imposes no length restrictions, but QR Codes have restrictions.\n     * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.\n     * Any segment longer than this is meaningless for the purpose of generating QR Codes.\n     */\n    var QrSegment = /** @class */ (function () {\n        /*-- Constructor (low level) and fields --*/\n        // Creates a new QR Code segment with the given attributes and data.\n        // The character count (numChars) must agree with the mode and the bit buffer length,\n        // but the constraint isn't checked. The given bit buffer is cloned and stored.\n        function QrSegment(\n        // The mode indicator of this segment.\n        mode, \n        // The length of this segment's unencoded data. Measured in characters for\n        // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.\n        // Always zero or positive. Not the same as the data's bit length.\n        numChars, \n        // The data bits of this segment. Accessed through getData().\n        bitData) {\n            this.mode = mode;\n            this.numChars = numChars;\n            this.bitData = bitData;\n            if (numChars < 0)\n                throw new RangeError(\"Invalid argument\");\n            this.bitData = bitData.slice(); // Make defensive copy\n        }\n        /*-- Static factory functions (mid level) --*/\n        // Returns a segment representing the given binary data encoded in\n        // byte mode. All input byte arrays are acceptable. Any text string\n        // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n        QrSegment.makeBytes = function (data) {\n            var bb = [];\n            for (var _i = 0, data_2 = data; _i < data_2.length; _i++) {\n                var b = data_2[_i];\n                appendBits(b, 8, bb);\n            }\n            return new QrSegment(QrSegment.Mode.BYTE, data.length, bb);\n        };\n        // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n        QrSegment.makeNumeric = function (digits) {\n            if (!QrSegment.isNumeric(digits))\n                throw new RangeError(\"String contains non-numeric characters\");\n            var bb = [];\n            for (var i = 0; i < digits.length;) { // Consume up to 3 digits per iteration\n                var n = Math.min(digits.length - i, 3);\n                appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n                i += n;\n            }\n            return new QrSegment(QrSegment.Mode.NUMERIC, digits.length, bb);\n        };\n        // Returns a segment representing the given text string encoded in alphanumeric mode.\n        // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n        // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n        QrSegment.makeAlphanumeric = function (text) {\n            if (!QrSegment.isAlphanumeric(text))\n                throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n            var bb = [];\n            var i;\n            for (i = 0; i + 2 <= text.length; i += 2) { // Process groups of 2\n                var temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n                temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n                appendBits(temp, 11, bb);\n            }\n            if (i < text.length) // 1 character remaining\n                appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n            return new QrSegment(QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n        };\n        // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n        // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n        QrSegment.makeSegments = function (text) {\n            // Select the most efficient segment encoding automatically\n            if (text == \"\")\n                return [];\n            else if (QrSegment.isNumeric(text))\n                return [QrSegment.makeNumeric(text)];\n            else if (QrSegment.isAlphanumeric(text))\n                return [QrSegment.makeAlphanumeric(text)];\n            else\n                return [QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))];\n        };\n        // Returns a segment representing an Extended Channel Interpretation\n        // (ECI) designator with the given assignment value.\n        QrSegment.makeEci = function (assignVal) {\n            var bb = [];\n            if (assignVal < 0)\n                throw new RangeError(\"ECI assignment value out of range\");\n            else if (assignVal < (1 << 7))\n                appendBits(assignVal, 8, bb);\n            else if (assignVal < (1 << 14)) {\n                appendBits(2, 2, bb);\n                appendBits(assignVal, 14, bb);\n            }\n            else if (assignVal < 1000000) {\n                appendBits(6, 3, bb);\n                appendBits(assignVal, 21, bb);\n            }\n            else\n                throw new RangeError(\"ECI assignment value out of range\");\n            return new QrSegment(QrSegment.Mode.ECI, 0, bb);\n        };\n        // Tests whether the given string can be encoded as a segment in numeric mode.\n        // A string is encodable iff each character is in the range 0 to 9.\n        QrSegment.isNumeric = function (text) {\n            return QrSegment.NUMERIC_REGEX.test(text);\n        };\n        // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n        // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n        // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n        QrSegment.isAlphanumeric = function (text) {\n            return QrSegment.ALPHANUMERIC_REGEX.test(text);\n        };\n        /*-- Methods --*/\n        // Returns a new copy of the data bits of this segment.\n        QrSegment.prototype.getData = function () {\n            return this.bitData.slice(); // Make defensive copy\n        };\n        // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n        // the given version. The result is infinity if a segment has too many characters to fit its length field.\n        QrSegment.getTotalBits = function (segs, version) {\n            var result = 0;\n            for (var _i = 0, segs_2 = segs; _i < segs_2.length; _i++) {\n                var seg = segs_2[_i];\n                var ccbits = seg.mode.numCharCountBits(version);\n                if (seg.numChars >= (1 << ccbits))\n                    return Infinity; // The segment's length doesn't fit the field's bit width\n                result += 4 + ccbits + seg.bitData.length;\n            }\n            return result;\n        };\n        // Returns a new array of bytes representing the given string encoded in UTF-8.\n        QrSegment.toUtf8ByteArray = function (str) {\n            str = encodeURI(str);\n            var result = [];\n            for (var i = 0; i < str.length; i++) {\n                if (str.charAt(i) != \"%\")\n                    result.push(str.charCodeAt(i));\n                else {\n                    result.push(parseInt(str.substring(i + 1, i + 3), 16));\n                    i += 2;\n                }\n            }\n            return result;\n        };\n        /*-- Constants --*/\n        // Describes precisely all strings that are encodable in numeric mode.\n        QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n        // Describes precisely all strings that are encodable in alphanumeric mode.\n        QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n        // The set of all legal characters in alphanumeric mode,\n        // where each character value maps to the index in the string.\n        QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n        return QrSegment;\n    }());\n    qrcodegen.QrSegment = QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n/*---- Public helper enumeration ----*/\n(function (qrcodegen) {\n    (function (QrCode) {\n        /*\n         * The error correction level in a QR Code symbol. Immutable.\n         */\n        var Ecc = /** @class */ (function () {\n            /*-- Constructor and fields --*/\n            function Ecc(\n            // In the range 0 to 3 (unsigned 2-bit integer).\n            ordinal, \n            // (Package-private) In the range 0 to 3 (unsigned 2-bit integer).\n            formatBits) {\n                this.ordinal = ordinal;\n                this.formatBits = formatBits;\n            }\n            /*-- Constants --*/\n            Ecc.LOW = new Ecc(0, 1); // The QR Code can tolerate about  7% erroneous codewords\n            Ecc.MEDIUM = new Ecc(1, 0); // The QR Code can tolerate about 15% erroneous codewords\n            Ecc.QUARTILE = new Ecc(2, 3); // The QR Code can tolerate about 25% erroneous codewords\n            Ecc.HIGH = new Ecc(3, 2); // The QR Code can tolerate about 30% erroneous codewords\n            return Ecc;\n        }());\n        QrCode.Ecc = Ecc;\n    })(qrcodegen.QrCode || (qrcodegen.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n/*---- Public helper enumeration ----*/\n(function (qrcodegen) {\n    (function (QrSegment) {\n        /*\n         * Describes how a segment's data bits are interpreted. Immutable.\n         */\n        var Mode = /** @class */ (function () {\n            /*-- Constructor and fields --*/\n            function Mode(\n            // The mode indicator bits, which is a uint4 value (range 0 to 15).\n            modeBits, \n            // Number of character count bits for three different version ranges.\n            numBitsCharCount) {\n                this.modeBits = modeBits;\n                this.numBitsCharCount = numBitsCharCount;\n            }\n            /*-- Method --*/\n            // (Package-private) Returns the bit width of the character count field for a segment in\n            // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n            Mode.prototype.numCharCountBits = function (ver) {\n                return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n            };\n            /*-- Constants --*/\n            Mode.NUMERIC = new Mode(0x1, [10, 12, 14]);\n            Mode.ALPHANUMERIC = new Mode(0x2, [9, 11, 13]);\n            Mode.BYTE = new Mode(0x4, [8, 16, 16]);\n            Mode.KANJI = new Mode(0x8, [8, 10, 12]);\n            Mode.ECI = new Mode(0x7, [0, 0, 0]);\n            return Mode;\n        }());\n        QrSegment.Mode = Mode;\n    })(qrcodegen.QrSegment || (qrcodegen.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar QR = qrcodegen;\n\nvar defaultErrorCorrectLevel = 'L';\nvar ErrorCorrectLevelMap = {\n    L: QR.QrCode.Ecc.LOW,\n    M: QR.QrCode.Ecc.MEDIUM,\n    Q: QR.QrCode.Ecc.QUARTILE,\n    H: QR.QrCode.Ecc.HIGH,\n};\n// Thanks the `qrcode.react`\nvar SUPPORTS_PATH2D = (function () {\n    try {\n        new Path2D().addPath(new Path2D());\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n})();\nfunction validErrorCorrectLevel(level) {\n    return level in ErrorCorrectLevelMap;\n}\nfunction generatePath(modules, margin) {\n    if (margin === void 0) { margin = 0; }\n    var ops = [];\n    modules.forEach(function (row, y) {\n        var start = null;\n        row.forEach(function (cell, x) {\n            if (!cell && start !== null) {\n                // M0 0h7v1H0z injects the space with the move and drops the comma,\n                // saving a char per operation\n                ops.push(\"M\".concat(start + margin, \" \").concat(y + margin, \"h\").concat(x - start, \"v1H\").concat(start + margin, \"z\"));\n                start = null;\n                return;\n            }\n            // end of row, clean up or skip\n            if (x === row.length - 1) {\n                if (!cell) {\n                    // We would have closed the op above already so this can only mean\n                    // 2+ light modules in a row.\n                    return;\n                }\n                if (start === null) {\n                    // Just a single dark module.\n                    ops.push(\"M\".concat(x + margin, \",\").concat(y + margin, \" h1v1H\").concat(x + margin, \"z\"));\n                }\n                else {\n                    // Otherwise finish the current line.\n                    ops.push(\"M\".concat(start + margin, \",\").concat(y + margin, \" h\").concat(x + 1 - start, \"v1H\").concat(start + margin, \"z\"));\n                }\n                return;\n            }\n            if (cell && start === null) {\n                start = x;\n            }\n        });\n    });\n    return ops.join('');\n}\nfunction getImageSettings(cells, size, margin, imageSettings) {\n    var width = imageSettings.width, height = imageSettings.height, imageX = imageSettings.x, imageY = imageSettings.y;\n    var numCells = cells.length + margin * 2;\n    var defaultSize = Math.floor(size * 0.1);\n    var scale = numCells / size;\n    var w = (width || defaultSize) * scale;\n    var h = (height || defaultSize) * scale;\n    var x = imageX == null ? cells.length / 2 - w / 2 : imageX * scale;\n    var y = imageY == null ? cells.length / 2 - h / 2 : imageY * scale;\n    var excavation = null;\n    if (imageSettings.excavate) {\n        var floorX = Math.floor(x);\n        var floorY = Math.floor(y);\n        var ceilW = Math.ceil(w + x - floorX);\n        var ceilH = Math.ceil(h + y - floorY);\n        excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };\n    }\n    return { x: x, y: y, h: h, w: w, excavation: excavation };\n}\nfunction excavateModules(modules, excavation) {\n    return modules.slice().map(function (row, y) {\n        if (y < excavation.y || y >= excavation.y + excavation.h) {\n            return row;\n        }\n        return row.map(function (cell, x) {\n            if (x < excavation.x || x >= excavation.x + excavation.w) {\n                return cell;\n            }\n            return false;\n        });\n    });\n}\nvar QRCodeProps = {\n    value: {\n        type: String,\n        required: true,\n        default: '',\n    },\n    size: {\n        type: Number,\n        default: 100,\n    },\n    level: {\n        type: String,\n        default: defaultErrorCorrectLevel,\n        validator: function (l) { return validErrorCorrectLevel(l); },\n    },\n    background: {\n        type: String,\n        default: '#fff',\n    },\n    foreground: {\n        type: String,\n        default: '#000',\n    },\n    margin: {\n        type: Number,\n        required: false,\n        default: 0,\n    },\n    imageSettings: {\n        type: Object,\n        required: false,\n        default: function () { return ({}); },\n    },\n    gradient: {\n        type: Boolean,\n        required: false,\n        default: false,\n    },\n    gradientType: {\n        type: String,\n        required: false,\n        default: 'linear',\n        validator: function (t) { return ['linear', 'radial'].indexOf(t) > -1; },\n    },\n    gradientStartColor: {\n        type: String,\n        required: false,\n        default: '#000',\n    },\n    gradientEndColor: {\n        type: String,\n        required: false,\n        default: '#fff',\n    },\n};\nvar QRCodeVueProps = __assign(__assign({}, QRCodeProps), { renderAs: {\n        type: String,\n        required: false,\n        default: 'canvas',\n        validator: function (as) { return ['canvas', 'svg'].indexOf(as) > -1; },\n    } });\nvar QrcodeSvg = defineComponent({\n    name: 'QRCodeSvg',\n    props: QRCodeProps,\n    setup: function (props) {\n        var numCells = ref(0);\n        var fgPath = ref('');\n        var imageProps;\n        var generate = function () {\n            var value = props.value, _level = props.level, _margin = props.margin;\n            var margin = _margin >>> 0;\n            var level = validErrorCorrectLevel(_level) ? _level : defaultErrorCorrectLevel;\n            var cells = QR.QrCode.encodeText(value, ErrorCorrectLevelMap[level]).getModules();\n            numCells.value = cells.length + margin * 2;\n            if (props.imageSettings.src) {\n                var imageSettings = getImageSettings(cells, props.size, margin, props.imageSettings);\n                imageProps = {\n                    x: imageSettings.x + margin,\n                    y: imageSettings.y + margin,\n                    width: imageSettings.w,\n                    height: imageSettings.h,\n                };\n                if (imageSettings.excavation) {\n                    cells = excavateModules(cells, imageSettings.excavation);\n                }\n            }\n            // Drawing strategy: instead of a rect per module, we're going to create a\n            // single path for the dark modules and layer that on top of a light rect,\n            // for a total of 2 DOM nodes. We pay a bit more in string concat but that's\n            // way faster than DOM ops.\n            // For level 1, 441 nodes -> 2\n            // For level 40, 31329 -> 2\n            fgPath.value = generatePath(cells, margin);\n        };\n        var renderGradient = function () {\n            if (!props.gradient)\n                return null;\n            var gradientProps = props.gradientType === 'linear'\n                ? {\n                    x1: '0%',\n                    y1: '0%',\n                    x2: '100%',\n                    y2: '100%',\n                }\n                : {\n                    cx: '50%',\n                    cy: '50%',\n                    r: '50%',\n                    fx: '50%',\n                    fy: '50%',\n                };\n            return h(props.gradientType === 'linear' ? 'linearGradient' : 'radialGradient', __assign({ id: 'qr-gradient' }, gradientProps), [\n                h('stop', {\n                    offset: '0%',\n                    style: { stopColor: props.gradientStartColor },\n                }),\n                h('stop', {\n                    offset: '100%',\n                    style: { stopColor: props.gradientEndColor },\n                }),\n            ]);\n        };\n        generate();\n        onUpdated(generate);\n        return function () { return h('svg', {\n            width: props.size,\n            height: props.size,\n            'shape-rendering': \"crispEdges\",\n            xmlns: 'http://www.w3.org/2000/svg',\n            viewBox: \"0 0 \".concat(numCells.value, \" \").concat(numCells.value),\n        }, [\n            h('defs', {}, [renderGradient()]),\n            h('rect', {\n                width: '100%',\n                height: '100%',\n                fill: props.background,\n            }),\n            h('path', {\n                fill: props.gradient ? 'url(#qr-gradient)' : props.foreground,\n                d: fgPath.value,\n            }),\n            props.imageSettings.src && h('image', __assign({ href: props.imageSettings.src }, imageProps)),\n        ]); };\n    },\n});\nvar QrcodeCanvas = defineComponent({\n    name: 'QRCodeCanvas',\n    props: QRCodeProps,\n    setup: function (props, ctx) {\n        var canvasEl = ref(null);\n        var imageRef = ref(null);\n        var generate = function () {\n            var value = props.value, _level = props.level, size = props.size, _margin = props.margin, background = props.background, foreground = props.foreground, gradient = props.gradient, gradientType = props.gradientType, gradientStartColor = props.gradientStartColor, gradientEndColor = props.gradientEndColor;\n            var margin = _margin >>> 0;\n            var level = validErrorCorrectLevel(_level) ? _level : defaultErrorCorrectLevel;\n            var canvas = canvasEl.value;\n            if (!canvas) {\n                return;\n            }\n            var ctx = canvas.getContext('2d');\n            if (!ctx) {\n                return;\n            }\n            var cells = QR.QrCode.encodeText(value, ErrorCorrectLevelMap[level]).getModules();\n            var numCells = cells.length + margin * 2;\n            var image = imageRef.value;\n            var imageProps = { x: 0, y: 0, width: 0, height: 0 };\n            var showImage = props.imageSettings.src && image != null && image.naturalWidth !== 0 && image.naturalHeight !== 0;\n            if (showImage) {\n                var imageSettings = getImageSettings(cells, props.size, margin, props.imageSettings);\n                imageProps = {\n                    x: imageSettings.x + margin,\n                    y: imageSettings.y + margin,\n                    width: imageSettings.w,\n                    height: imageSettings.h,\n                };\n                if (imageSettings.excavation) {\n                    cells = excavateModules(cells, imageSettings.excavation);\n                }\n            }\n            var devicePixelRatio = window.devicePixelRatio || 1;\n            var scale = (size / numCells) * devicePixelRatio;\n            canvas.height = canvas.width = size * devicePixelRatio;\n            ctx.scale(scale, scale);\n            ctx.fillStyle = background;\n            ctx.fillRect(0, 0, numCells, numCells);\n            if (gradient) {\n                var grad = void 0;\n                if (gradientType === 'linear') {\n                    grad = ctx.createLinearGradient(0, 0, numCells, numCells);\n                }\n                else {\n                    grad = ctx.createRadialGradient(numCells / 2, numCells / 2, 0, numCells / 2, numCells / 2, numCells / 2);\n                }\n                grad.addColorStop(0, gradientStartColor);\n                grad.addColorStop(1, gradientEndColor);\n                ctx.fillStyle = grad;\n            }\n            else {\n                ctx.fillStyle = foreground;\n            }\n            if (SUPPORTS_PATH2D) {\n                ctx.fill(new Path2D(generatePath(cells, margin)));\n            }\n            else {\n                cells.forEach(function (row, rdx) {\n                    row.forEach(function (cell, cdx) {\n                        if (cell) {\n                            ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n                        }\n                    });\n                });\n            }\n            if (showImage) {\n                ctx.drawImage(image, imageProps.x, imageProps.y, imageProps.width, imageProps.height);\n            }\n        };\n        onMounted(generate);\n        onUpdated(generate);\n        var style = ctx.attrs.style;\n        return function () { return h(Fragment, [\n            h('canvas', __assign(__assign({}, ctx.attrs), { ref: canvasEl, style: __assign(__assign({}, style), { width: \"\".concat(props.size, \"px\"), height: \"\".concat(props.size, \"px\") }) })),\n            props.imageSettings.src && h('img', {\n                ref: imageRef,\n                src: props.imageSettings.src,\n                style: { display: 'none' },\n                onLoad: generate,\n            })\n        ]); };\n    },\n});\nvar QrcodeVue = defineComponent({\n    name: 'Qrcode',\n    render: function () {\n        var _a = this.$props, renderAs = _a.renderAs, value = _a.value, size = _a.size, margin = _a.margin, level = _a.level, background = _a.background, foreground = _a.foreground, imageSettings = _a.imageSettings, gradient = _a.gradient, gradientType = _a.gradientType, gradientStartColor = _a.gradientStartColor, gradientEndColor = _a.gradientEndColor;\n        return h(renderAs === 'svg' ? QrcodeSvg : QrcodeCanvas, {\n            value: value,\n            size: size,\n            margin: margin,\n            level: level,\n            background: background,\n            foreground: foreground,\n            imageSettings: imageSettings,\n            gradient: gradient,\n            gradientType: gradientType,\n            gradientStartColor: gradientStartColor,\n            gradientEndColor: gradientEndColor,\n        });\n    },\n    props: QRCodeVueProps,\n});\n\nexport { QrcodeCanvas, QrcodeSvg, QrcodeVue as default };\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAe,EAAEC,GAAG,EAAEC,SAAS,EAAEC,CAAC,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,IAAIC,QAAQ,GAAG,SAAAA,CAAA,EAAW;EACtBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,SAASF,QAAQA,CAACG,CAAC,EAAE;IAC7C,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAChF;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AAED,OAAOO,eAAe,KAAK,UAAU,GAAGA,eAAe,GAAG,UAAUC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAE;EAC5F,IAAIC,CAAC,GAAG,IAAIC,KAAK,CAACF,OAAO,CAAC;EAC1B,OAAOC,CAAC,CAACE,IAAI,GAAG,iBAAiB,EAAEF,CAAC,CAACH,KAAK,GAAGA,KAAK,EAAEG,CAAC,CAACF,UAAU,GAAGA,UAAU,EAAEE,CAAC;AACpF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,SAAS;AACb,CAAC,UAAUA,SAAS,EAAE;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,MAAM,GAAG,aAAe,YAAY;IACpC;IACA;IACA;IACA;IACA;IACA,SAASA,MAAMA;IACf;IACA;IACAC,OAAO;IACP;IACAC,oBAAoB,EAAEC,aAAa,EAAEC,GAAG,EAAE;MACtC,IAAI,CAACH,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;MAChD;MACA;MACA,IAAI,CAACG,OAAO,GAAG,EAAE;MACjB;MACA,IAAI,CAACC,UAAU,GAAG,EAAE;MACpB;MACA,IAAIL,OAAO,GAAGD,MAAM,CAACO,WAAW,IAAIN,OAAO,GAAGD,MAAM,CAACQ,WAAW,EAC5D,MAAM,IAAIC,UAAU,CAAC,4BAA4B,CAAC;MACtD,IAAIL,GAAG,GAAG,CAAC,CAAC,IAAIA,GAAG,GAAG,CAAC,EACnB,MAAM,IAAIK,UAAU,CAAC,yBAAyB,CAAC;MACnD,IAAI,CAACC,IAAI,GAAGT,OAAO,GAAG,CAAC,GAAG,EAAE;MAC5B;MACA,IAAIU,GAAG,GAAG,EAAE;MACZ,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2B,IAAI,EAAE3B,CAAC,EAAE,EAC9B4B,GAAG,CAACC,IAAI,CAAC,KAAK,CAAC;MACnB,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2B,IAAI,EAAE3B,CAAC,EAAE,EAAE;QAChC,IAAI,CAACsB,OAAO,CAACO,IAAI,CAACD,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAACP,UAAU,CAACM,IAAI,CAACD,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC;MACrC;MACA;MACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC3B,IAAIC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACb,aAAa,CAAC;MAC1D,IAAI,CAACc,aAAa,CAACF,YAAY,CAAC;MAChC;MACA,IAAIX,GAAG,IAAI,CAAC,CAAC,EAAE;QAAE;QACb,IAAIc,UAAU,GAAG,UAAU;QAC3B,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UACxB,IAAI,CAACoC,SAAS,CAACpC,CAAC,CAAC;UACjB,IAAI,CAACqC,cAAc,CAACrC,CAAC,CAAC;UACtB,IAAIsC,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;UACpC,IAAID,OAAO,GAAGH,UAAU,EAAE;YACtBd,GAAG,GAAGrB,CAAC;YACPmC,UAAU,GAAGG,OAAO;UACxB;UACA,IAAI,CAACF,SAAS,CAACpC,CAAC,CAAC,CAAC,CAAC;QACvB;MACJ;MACAwC,MAAM,CAAC,CAAC,IAAInB,GAAG,IAAIA,GAAG,IAAI,CAAC,CAAC;MAC5B,IAAI,CAACoB,IAAI,GAAGpB,GAAG;MACf,IAAI,CAACe,SAAS,CAACf,GAAG,CAAC,CAAC,CAAC;MACrB,IAAI,CAACgB,cAAc,CAAChB,GAAG,CAAC,CAAC,CAAC;MAC1B,IAAI,CAACE,UAAU,GAAG,EAAE;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACAN,MAAM,CAACyB,UAAU,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;MACrC,IAAIC,IAAI,GAAG7B,SAAS,CAAC8B,SAAS,CAACC,YAAY,CAACJ,IAAI,CAAC;MACjD,OAAO1B,MAAM,CAAC+B,cAAc,CAACH,IAAI,EAAED,GAAG,CAAC;IAC3C,CAAC;IACD;IACA;IACA;IACA;IACA3B,MAAM,CAACgC,YAAY,GAAG,UAAUC,IAAI,EAAEN,GAAG,EAAE;MACvC,IAAIO,GAAG,GAAGnC,SAAS,CAAC8B,SAAS,CAACM,SAAS,CAACF,IAAI,CAAC;MAC7C,OAAOjC,MAAM,CAAC+B,cAAc,CAAC,CAACG,GAAG,CAAC,EAAEP,GAAG,CAAC;IAC5C,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA3B,MAAM,CAAC+B,cAAc,GAAG,UAAUH,IAAI,EAAED,GAAG,EAAES,UAAU,EAAEC,UAAU,EAAEb,IAAI,EAAEc,QAAQ,EAAE;MACjF,IAAIF,UAAU,KAAK,KAAK,CAAC,EAAE;QAAEA,UAAU,GAAG,CAAC;MAAE;MAC7C,IAAIC,UAAU,KAAK,KAAK,CAAC,EAAE;QAAEA,UAAU,GAAG,EAAE;MAAE;MAC9C,IAAIb,IAAI,KAAK,KAAK,CAAC,EAAE;QAAEA,IAAI,GAAG,CAAC,CAAC;MAAE;MAClC,IAAIc,QAAQ,KAAK,KAAK,CAAC,EAAE;QAAEA,QAAQ,GAAG,IAAI;MAAE;MAC5C,IAAI,EAAEtC,MAAM,CAACO,WAAW,IAAI6B,UAAU,IAAIA,UAAU,IAAIC,UAAU,IAAIA,UAAU,IAAIrC,MAAM,CAACQ,WAAW,CAAC,IAChGgB,IAAI,GAAG,CAAC,CAAC,IAAIA,IAAI,GAAG,CAAC,EACxB,MAAM,IAAIf,UAAU,CAAC,eAAe,CAAC;MACzC;MACA,IAAIR,OAAO;MACX,IAAIsC,YAAY;MAChB,KAAKtC,OAAO,GAAGmC,UAAU,GAAGnC,OAAO,EAAE,EAAE;QACnC,IAAIuC,kBAAkB,GAAGxC,MAAM,CAACyC,mBAAmB,CAACxC,OAAO,EAAE0B,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACvE,IAAIe,QAAQ,GAAGb,SAAS,CAACc,YAAY,CAACf,IAAI,EAAE3B,OAAO,CAAC;QACpD,IAAIyC,QAAQ,IAAIF,kBAAkB,EAAE;UAChCD,YAAY,GAAGG,QAAQ;UACvB,MAAM,CAAC;QACX;QACA,IAAIzC,OAAO,IAAIoC,UAAU;UAAE;UACvB,MAAM,IAAI5B,UAAU,CAAC,eAAe,CAAC;MAC7C;MACA;MACA,KAAK,IAAImC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC7C,MAAM,CAAC8C,GAAG,CAACC,MAAM,EAAE/C,MAAM,CAAC8C,GAAG,CAACE,QAAQ,EAAEhD,MAAM,CAAC8C,GAAG,CAACG,IAAI,CAAC,EAAEL,EAAE,GAAGC,EAAE,CAAC3D,MAAM,EAAE0D,EAAE,EAAE,EAAE;QAAE;QACrG,IAAIM,MAAM,GAAGL,EAAE,CAACD,EAAE,CAAC;QACnB,IAAIN,QAAQ,IAAIC,YAAY,IAAIvC,MAAM,CAACyC,mBAAmB,CAACxC,OAAO,EAAEiD,MAAM,CAAC,GAAG,CAAC,EAC3EvB,GAAG,GAAGuB,MAAM;MACpB;MACA;MACA,IAAIC,EAAE,GAAG,EAAE;MACX,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,MAAM,GAAGzB,IAAI,EAAEwB,EAAE,GAAGC,MAAM,CAACnE,MAAM,EAAEkE,EAAE,EAAE,EAAE;QACtD,IAAIlB,GAAG,GAAGmB,MAAM,CAACD,EAAE,CAAC;QACpBE,UAAU,CAACpB,GAAG,CAACqB,IAAI,CAACC,QAAQ,EAAE,CAAC,EAAEL,EAAE,CAAC;QACpCG,UAAU,CAACpB,GAAG,CAACuB,QAAQ,EAAEvB,GAAG,CAACqB,IAAI,CAACG,gBAAgB,CAACzD,OAAO,CAAC,EAAEkD,EAAE,CAAC;QAChE,KAAK,IAAIQ,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG1B,GAAG,CAAC2B,OAAO,CAAC,CAAC,EAAEF,EAAE,GAAGC,EAAE,CAAC1E,MAAM,EAAEyE,EAAE,EAAE,EAAE;UACvD,IAAIG,CAAC,GAAGF,EAAE,CAACD,EAAE,CAAC;UACdR,EAAE,CAACvC,IAAI,CAACkD,CAAC,CAAC;QACd;MACJ;MACAvC,MAAM,CAAC4B,EAAE,CAACjE,MAAM,IAAIqD,YAAY,CAAC;MACjC;MACA,IAAIwB,gBAAgB,GAAG/D,MAAM,CAACyC,mBAAmB,CAACxC,OAAO,EAAE0B,GAAG,CAAC,GAAG,CAAC;MACnEJ,MAAM,CAAC4B,EAAE,CAACjE,MAAM,IAAI6E,gBAAgB,CAAC;MACrCT,UAAU,CAAC,CAAC,EAAEU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,gBAAgB,GAAGZ,EAAE,CAACjE,MAAM,CAAC,EAAEiE,EAAE,CAAC;MAC5DG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGH,EAAE,CAACjE,MAAM,GAAG,CAAC,IAAI,CAAC,EAAEiE,EAAE,CAAC;MAC1C5B,MAAM,CAAC4B,EAAE,CAACjE,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;MAC1B;MACA,KAAK,IAAIgF,OAAO,GAAG,IAAI,EAAEf,EAAE,CAACjE,MAAM,GAAG6E,gBAAgB,EAAEG,OAAO,IAAI,IAAI,GAAG,IAAI,EACzEZ,UAAU,CAACY,OAAO,EAAE,CAAC,EAAEf,EAAE,CAAC;MAC9B;MACA,IAAIhD,aAAa,GAAG,EAAE;MACtB,OAAOA,aAAa,CAACjB,MAAM,GAAG,CAAC,GAAGiE,EAAE,CAACjE,MAAM,EACvCiB,aAAa,CAACS,IAAI,CAAC,CAAC,CAAC;MACzBuC,EAAE,CAACgB,OAAO,CAAC,UAAUL,CAAC,EAAE/E,CAAC,EAAE;QACvB,OAAOoB,aAAa,CAACpB,CAAC,KAAK,CAAC,CAAC,IAAI+E,CAAC,IAAK,CAAC,IAAI/E,CAAC,GAAG,CAAC,CAAE;MACvD,CAAC,CAAC;MACF;MACA,OAAO,IAAIiB,MAAM,CAACC,OAAO,EAAE0B,GAAG,EAAExB,aAAa,EAAEqB,IAAI,CAAC;IACxD,CAAC;IACD;IACA;IACA;IACA;IACAxB,MAAM,CAACZ,SAAS,CAACgF,SAAS,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACzC,OAAO,CAAC,IAAID,CAAC,IAAIA,CAAC,GAAG,IAAI,CAAC3D,IAAI,IAAI,CAAC,IAAI4D,CAAC,IAAIA,CAAC,GAAG,IAAI,CAAC5D,IAAI,IAAI,IAAI,CAACL,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC;IACnF,CAAC;IACDrE,MAAM,CAACZ,SAAS,CAACmF,UAAU,GAAG,YAAY;MACtC,OAAO,IAAI,CAAClE,OAAO;IACvB,CAAC;IACD;IACA;IACAL,MAAM,CAACZ,SAAS,CAAC0B,oBAAoB,GAAG,YAAY;MAChD;MACA,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2B,IAAI,EAAE3B,CAAC,EAAE,EAAE;QAChC,IAAI,CAACyF,iBAAiB,CAAC,CAAC,EAAEzF,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAACyF,iBAAiB,CAACzF,CAAC,EAAE,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MAC5C;MACA;MACA,IAAI,CAAC0F,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B,IAAI,CAACA,iBAAiB,CAAC,IAAI,CAAC/D,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;MACxC,IAAI,CAAC+D,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC/D,IAAI,GAAG,CAAC,CAAC;MACxC;MACA,IAAIgE,WAAW,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAAC;MACrD,IAAIC,QAAQ,GAAGF,WAAW,CAACxF,MAAM;MACjC,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,QAAQ,EAAE7F,CAAC,EAAE,EAAE;QAC/B,KAAK,IAAI8F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;UAC/B;UACA,IAAI,EAAE9F,CAAC,IAAI,CAAC,IAAI8F,CAAC,IAAI,CAAC,IAAI9F,CAAC,IAAI,CAAC,IAAI8F,CAAC,IAAID,QAAQ,GAAG,CAAC,IAAI7F,CAAC,IAAI6F,QAAQ,GAAG,CAAC,IAAIC,CAAC,IAAI,CAAC,CAAC,EACjF,IAAI,CAACC,oBAAoB,CAACJ,WAAW,CAAC3F,CAAC,CAAC,EAAE2F,WAAW,CAACG,CAAC,CAAC,CAAC;QACjE;MACJ;MACA;MACA,IAAI,CAACzD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,IAAI,CAAC2D,WAAW,CAAC,CAAC;IACtB,CAAC;IACD;IACA;IACA/E,MAAM,CAACZ,SAAS,CAACgC,cAAc,GAAG,UAAUI,IAAI,EAAE;MAC9C;MACA,IAAIS,IAAI,GAAG,IAAI,CAAC/B,oBAAoB,CAAC8E,UAAU,IAAI,CAAC,GAAGxD,IAAI,CAAC,CAAC;MAC7D,IAAIyD,GAAG,GAAGhD,IAAI;MACd,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EACvBkG,GAAG,GAAIA,GAAG,IAAI,CAAC,GAAK,CAACA,GAAG,KAAK,CAAC,IAAI,KAAM;MAC5C,IAAIC,IAAI,GAAG,CAACjD,IAAI,IAAI,EAAE,GAAGgD,GAAG,IAAI,MAAM,CAAC,CAAC;MACxC1D,MAAM,CAAC2D,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC;MACxB;MACA,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EACvB,IAAI,CAACyF,iBAAiB,CAAC,CAAC,EAAEzF,CAAC,EAAEoG,MAAM,CAACD,IAAI,EAAEnG,CAAC,CAAC,CAAC;MACjD,IAAI,CAACyF,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAEW,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACV,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAEW,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACV,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAEW,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EACvB,IAAI,CAACyF,iBAAiB,CAAC,EAAE,GAAGzF,CAAC,EAAE,CAAC,EAAEoG,MAAM,CAACD,IAAI,EAAEnG,CAAC,CAAC,CAAC;MACtD;MACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EACtB,IAAI,CAACyF,iBAAiB,CAAC,IAAI,CAAC9D,IAAI,GAAG,CAAC,GAAG3B,CAAC,EAAE,CAAC,EAAEoG,MAAM,CAACD,IAAI,EAAEnG,CAAC,CAAC,CAAC;MACjE,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EACvB,IAAI,CAACyF,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC9D,IAAI,GAAG,EAAE,GAAG3B,CAAC,EAAEoG,MAAM,CAACD,IAAI,EAAEnG,CAAC,CAAC,CAAC;MAClE,IAAI,CAACyF,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC9D,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACpD,CAAC;IACD;IACA;IACAV,MAAM,CAACZ,SAAS,CAAC2F,WAAW,GAAG,YAAY;MACvC,IAAI,IAAI,CAAC9E,OAAO,GAAG,CAAC,EAChB;MACJ;MACA,IAAIgF,GAAG,GAAG,IAAI,CAAChF,OAAO,CAAC,CAAC;MACxB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EACvBkG,GAAG,GAAIA,GAAG,IAAI,CAAC,GAAK,CAACA,GAAG,KAAK,EAAE,IAAI,MAAO;MAC9C,IAAIC,IAAI,GAAG,IAAI,CAACjF,OAAO,IAAI,EAAE,GAAGgF,GAAG,CAAC,CAAC;MACrC1D,MAAM,CAAC2D,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC;MACxB;MACA,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACzB,IAAIqG,KAAK,GAAGD,MAAM,CAACD,IAAI,EAAEnG,CAAC,CAAC;QAC3B,IAAIsG,CAAC,GAAG,IAAI,CAAC3E,IAAI,GAAG,EAAE,GAAG3B,CAAC,GAAG,CAAC;QAC9B,IAAI+E,CAAC,GAAGE,IAAI,CAACsB,KAAK,CAACvG,CAAC,GAAG,CAAC,CAAC;QACzB,IAAI,CAACyF,iBAAiB,CAACa,CAAC,EAAEvB,CAAC,EAAEsB,KAAK,CAAC;QACnC,IAAI,CAACZ,iBAAiB,CAACV,CAAC,EAAEuB,CAAC,EAAED,KAAK,CAAC;MACvC;IACJ,CAAC;IACD;IACA;IACApF,MAAM,CAACZ,SAAS,CAACqF,iBAAiB,GAAG,UAAUJ,CAAC,EAAEC,CAAC,EAAE;MACjD,KAAK,IAAIiB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC7B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;UAC7B,IAAIC,IAAI,GAAGzB,IAAI,CAAC0B,GAAG,CAAC1B,IAAI,CAAC2B,GAAG,CAACH,EAAE,CAAC,EAAExB,IAAI,CAAC2B,GAAG,CAACJ,EAAE,CAAC,CAAC,CAAC,CAAC;UACjD,IAAIK,EAAE,GAAGvB,CAAC,GAAGmB,EAAE;UACf,IAAIK,EAAE,GAAGvB,CAAC,GAAGiB,EAAE;UACf,IAAI,CAAC,IAAIK,EAAE,IAAIA,EAAE,GAAG,IAAI,CAAClF,IAAI,IAAI,CAAC,IAAImF,EAAE,IAAIA,EAAE,GAAG,IAAI,CAACnF,IAAI,EACtD,IAAI,CAAC8D,iBAAiB,CAACoB,EAAE,EAAEC,EAAE,EAAEJ,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC;QAC9D;MACJ;IACJ,CAAC;IACD;IACA;IACAzF,MAAM,CAACZ,SAAS,CAAC0F,oBAAoB,GAAG,UAAUT,CAAC,EAAEC,CAAC,EAAE;MACpD,KAAK,IAAIiB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC7B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAC3B,IAAI,CAAChB,iBAAiB,CAACH,CAAC,GAAGmB,EAAE,EAAElB,CAAC,GAAGiB,EAAE,EAAEvB,IAAI,CAAC0B,GAAG,CAAC1B,IAAI,CAAC2B,GAAG,CAACH,EAAE,CAAC,EAAExB,IAAI,CAAC2B,GAAG,CAACJ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;MACzF;IACJ,CAAC;IACD;IACA;IACAvF,MAAM,CAACZ,SAAS,CAACoF,iBAAiB,GAAG,UAAUH,CAAC,EAAEC,CAAC,EAAEwB,MAAM,EAAE;MACzD,IAAI,CAACzF,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC,GAAGyB,MAAM;MAC3B,IAAI,CAACxF,UAAU,CAACgE,CAAC,CAAC,CAACD,CAAC,CAAC,GAAG,IAAI;IAChC,CAAC;IACD;IACA;IACA;IACArE,MAAM,CAACZ,SAAS,CAAC4B,mBAAmB,GAAG,UAAUiB,IAAI,EAAE;MACnD,IAAI8D,GAAG,GAAG,IAAI,CAAC9F,OAAO;MACtB,IAAI0B,GAAG,GAAG,IAAI,CAACzB,oBAAoB;MACnC,IAAI+B,IAAI,CAAC/C,MAAM,IAAIc,MAAM,CAACyC,mBAAmB,CAACsD,GAAG,EAAEpE,GAAG,CAAC,EACnD,MAAM,IAAIlB,UAAU,CAAC,kBAAkB,CAAC;MAC5C;MACA,IAAIuF,SAAS,GAAGhG,MAAM,CAACiG,2BAA2B,CAACtE,GAAG,CAACuE,OAAO,CAAC,CAACH,GAAG,CAAC;MACpE,IAAII,WAAW,GAAGnG,MAAM,CAACoG,uBAAuB,CAACzE,GAAG,CAACuE,OAAO,CAAC,CAACH,GAAG,CAAC;MAClE,IAAIM,YAAY,GAAGrC,IAAI,CAACsB,KAAK,CAACtF,MAAM,CAACsG,oBAAoB,CAACP,GAAG,CAAC,GAAG,CAAC,CAAC;MACnE,IAAIQ,cAAc,GAAGP,SAAS,GAAGK,YAAY,GAAGL,SAAS;MACzD,IAAIQ,aAAa,GAAGxC,IAAI,CAACsB,KAAK,CAACe,YAAY,GAAGL,SAAS,CAAC;MACxD;MACA,IAAIS,MAAM,GAAG,EAAE;MACf,IAAIC,KAAK,GAAG1G,MAAM,CAAC2G,yBAAyB,CAACR,WAAW,CAAC;MACzD,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAE6H,CAAC,GAAG,CAAC,EAAE7H,CAAC,GAAGiH,SAAS,EAAEjH,CAAC,EAAE,EAAE;QACvC,IAAI8H,GAAG,GAAG5E,IAAI,CAACpB,KAAK,CAAC+F,CAAC,EAAEA,CAAC,GAAGJ,aAAa,GAAGL,WAAW,IAAIpH,CAAC,GAAGwH,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACvFK,CAAC,IAAIC,GAAG,CAAC3H,MAAM;QACf,IAAI4H,GAAG,GAAG9G,MAAM,CAAC+G,2BAA2B,CAACF,GAAG,EAAEH,KAAK,CAAC;QACxD,IAAI3H,CAAC,GAAGwH,cAAc,EAClBM,GAAG,CAACjG,IAAI,CAAC,CAAC,CAAC;QACf6F,MAAM,CAAC7F,IAAI,CAACiG,GAAG,CAACG,MAAM,CAACF,GAAG,CAAC,CAAC;MAChC;MACA;MACA,IAAIG,MAAM,GAAG,EAAE;MACf,IAAIC,OAAO,GAAG,SAAAA,CAAUnI,CAAC,EAAE;QACvB0H,MAAM,CAACtC,OAAO,CAAC,UAAUgD,KAAK,EAAEtC,CAAC,EAAE;UAC/B;UACA,IAAI9F,CAAC,IAAIyH,aAAa,GAAGL,WAAW,IAAItB,CAAC,IAAI0B,cAAc,EACvDU,MAAM,CAACrG,IAAI,CAACuG,KAAK,CAACpI,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC;MACN,CAAC;MACD,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0H,MAAM,CAAC,CAAC,CAAC,CAACvH,MAAM,EAAEH,CAAC,EAAE,EAAE;QACvCmI,OAAO,CAACnI,CAAC,CAAC;MACd;MACAwC,MAAM,CAAC0F,MAAM,CAAC/H,MAAM,IAAImH,YAAY,CAAC;MACrC,OAAOY,MAAM;IACjB,CAAC;IACD;IACA;IACAjH,MAAM,CAACZ,SAAS,CAAC6B,aAAa,GAAG,UAAUgB,IAAI,EAAE;MAC7C,IAAIA,IAAI,CAAC/C,MAAM,IAAI8E,IAAI,CAACsB,KAAK,CAACtF,MAAM,CAACsG,oBAAoB,CAAC,IAAI,CAACrG,OAAO,CAAC,GAAG,CAAC,CAAC,EACxE,MAAM,IAAIQ,UAAU,CAAC,kBAAkB,CAAC;MAC5C,IAAI1B,CAAC,GAAG,CAAC,CAAC,CAAC;MACX;MACA,KAAK,IAAIqI,KAAK,GAAG,IAAI,CAAC1G,IAAI,GAAG,CAAC,EAAE0G,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC,EAAE;QAAE;QACtD,IAAIA,KAAK,IAAI,CAAC,EACVA,KAAK,GAAG,CAAC;QACb,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAAC3G,IAAI,EAAE2G,IAAI,EAAE,EAAE;UAAE;UAC3C,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;YACxB,IAAIR,CAAC,GAAG+C,KAAK,GAAGvC,CAAC,CAAC,CAAC;YACnB,IAAIyC,MAAM,GAAG,CAAEF,KAAK,GAAG,CAAC,GAAI,CAAC,KAAK,CAAC;YACnC,IAAI9C,CAAC,GAAGgD,MAAM,GAAG,IAAI,CAAC5G,IAAI,GAAG,CAAC,GAAG2G,IAAI,GAAGA,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC/G,UAAU,CAACgE,CAAC,CAAC,CAACD,CAAC,CAAC,IAAItF,CAAC,GAAGkD,IAAI,CAAC/C,MAAM,GAAG,CAAC,EAAE;cAC/C,IAAI,CAACmB,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC,GAAGc,MAAM,CAAClD,IAAI,CAAClD,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAAC;cACvDA,CAAC,EAAE;YACP;YACA;YACA;UACJ;QACJ;MACJ;MACAwC,MAAM,CAACxC,CAAC,IAAIkD,IAAI,CAAC/C,MAAM,GAAG,CAAC,CAAC;IAChC,CAAC;IACD;IACA;IACA;IACA;IACA;IACAc,MAAM,CAACZ,SAAS,CAAC+B,SAAS,GAAG,UAAUK,IAAI,EAAE;MACzC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,CAAC,EACpB,MAAM,IAAIf,UAAU,CAAC,yBAAyB,CAAC;MACnD,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,IAAI,EAAE4D,CAAC,EAAE,EAAE;QAChC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3D,IAAI,EAAE2D,CAAC,EAAE,EAAE;UAChC,IAAIkD,MAAM,GAAG,KAAK,CAAC;UACnB,QAAQ/F,IAAI;YACR,KAAK,CAAC;cACF+F,MAAM,GAAG,CAAClD,CAAC,GAAGC,CAAC,IAAI,CAAC,IAAI,CAAC;cACzB;YACJ,KAAK,CAAC;cACFiD,MAAM,GAAGjD,CAAC,GAAG,CAAC,IAAI,CAAC;cACnB;YACJ,KAAK,CAAC;cACFiD,MAAM,GAAGlD,CAAC,GAAG,CAAC,IAAI,CAAC;cACnB;YACJ,KAAK,CAAC;cACFkD,MAAM,GAAG,CAAClD,CAAC,GAAGC,CAAC,IAAI,CAAC,IAAI,CAAC;cACzB;YACJ,KAAK,CAAC;cACFiD,MAAM,GAAG,CAACvD,IAAI,CAACsB,KAAK,CAACjB,CAAC,GAAG,CAAC,CAAC,GAAGL,IAAI,CAACsB,KAAK,CAAChB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;cACzD;YACJ,KAAK,CAAC;cACFiD,MAAM,GAAGlD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,IAAI,CAAC;cACnC;YACJ,KAAK,CAAC;cACFiD,MAAM,GAAG,CAAClD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;cACzC;YACJ,KAAK,CAAC;cACFiD,MAAM,GAAG,CAAC,CAAClD,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;cAC3C;YACJ;cAAS,MAAM,IAAIzE,KAAK,CAAC,aAAa,CAAC;UAC3C;UACA,IAAI,CAAC,IAAI,CAACS,UAAU,CAACgE,CAAC,CAAC,CAACD,CAAC,CAAC,IAAIkD,MAAM,EAChC,IAAI,CAAClH,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC,GAAG,CAAC,IAAI,CAAChE,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC;QAChD;MACJ;IACJ,CAAC;IACD;IACA;IACArE,MAAM,CAACZ,SAAS,CAACkC,eAAe,GAAG,YAAY;MAC3C,IAAI2F,MAAM,GAAG,CAAC;MACd;MACA,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,IAAI,EAAE4D,CAAC,EAAE,EAAE;QAChC,IAAIkD,QAAQ,GAAG,KAAK;QACpB,IAAIC,IAAI,GAAG,CAAC;QACZ,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtC,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3D,IAAI,EAAE2D,CAAC,EAAE,EAAE;UAChC,IAAI,IAAI,CAAChE,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC,IAAImD,QAAQ,EAAE;YAChCC,IAAI,EAAE;YACN,IAAIA,IAAI,IAAI,CAAC,EACTR,MAAM,IAAIjH,MAAM,CAAC2H,UAAU,CAAC,KAC3B,IAAIF,IAAI,GAAG,CAAC,EACbR,MAAM,EAAE;UAChB,CAAC,MACI;YACD,IAAI,CAACW,uBAAuB,CAACH,IAAI,EAAEC,UAAU,CAAC;YAC9C,IAAI,CAACF,QAAQ,EACTP,MAAM,IAAI,IAAI,CAACY,0BAA0B,CAACH,UAAU,CAAC,GAAG1H,MAAM,CAAC8H,UAAU;YAC7EN,QAAQ,GAAG,IAAI,CAACnH,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC;YAC7BoD,IAAI,GAAG,CAAC;UACZ;QACJ;QACAR,MAAM,IAAI,IAAI,CAACc,8BAA8B,CAACP,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAAC,GAAG1H,MAAM,CAAC8H,UAAU;MACjG;MACA;MACA,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3D,IAAI,EAAE2D,CAAC,EAAE,EAAE;QAChC,IAAImD,QAAQ,GAAG,KAAK;QACpB,IAAIQ,IAAI,GAAG,CAAC;QACZ,IAAIN,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtC,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,IAAI,EAAE4D,CAAC,EAAE,EAAE;UAChC,IAAI,IAAI,CAACjE,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC,IAAImD,QAAQ,EAAE;YAChCQ,IAAI,EAAE;YACN,IAAIA,IAAI,IAAI,CAAC,EACTf,MAAM,IAAIjH,MAAM,CAAC2H,UAAU,CAAC,KAC3B,IAAIK,IAAI,GAAG,CAAC,EACbf,MAAM,EAAE;UAChB,CAAC,MACI;YACD,IAAI,CAACW,uBAAuB,CAACI,IAAI,EAAEN,UAAU,CAAC;YAC9C,IAAI,CAACF,QAAQ,EACTP,MAAM,IAAI,IAAI,CAACY,0BAA0B,CAACH,UAAU,CAAC,GAAG1H,MAAM,CAAC8H,UAAU;YAC7EN,QAAQ,GAAG,IAAI,CAACnH,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC;YAC7B2D,IAAI,GAAG,CAAC;UACZ;QACJ;QACAf,MAAM,IAAI,IAAI,CAACc,8BAA8B,CAACP,QAAQ,EAAEQ,IAAI,EAAEN,UAAU,CAAC,GAAG1H,MAAM,CAAC8H,UAAU;MACjG;MACA;MACA,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,IAAI,GAAG,CAAC,EAAE4D,CAAC,EAAE,EAAE;QACpC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3D,IAAI,GAAG,CAAC,EAAE2D,CAAC,EAAE,EAAE;UACpC,IAAIe,KAAK,GAAG,IAAI,CAAC/E,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,CAAC;UAC9B,IAAIe,KAAK,IAAI,IAAI,CAAC/E,OAAO,CAACiE,CAAC,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,IAC/Be,KAAK,IAAI,IAAI,CAAC/E,OAAO,CAACiE,CAAC,GAAG,CAAC,CAAC,CAACD,CAAC,CAAC,IAC/Be,KAAK,IAAI,IAAI,CAAC/E,OAAO,CAACiE,CAAC,GAAG,CAAC,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,EACnC4C,MAAM,IAAIjH,MAAM,CAACiI,UAAU;QACnC;MACJ;MACA;MACA,IAAIC,IAAI,GAAG,CAAC;MACZ,KAAK,IAAItF,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,IAAI,CAACxC,OAAO,EAAEuC,EAAE,GAAGC,EAAE,CAAC3D,MAAM,EAAE0D,EAAE,EAAE,EAAE;QACtD,IAAIjC,GAAG,GAAGkC,EAAE,CAACD,EAAE,CAAC;QAChBsF,IAAI,GAAGvH,GAAG,CAACwH,MAAM,CAAC,UAAUC,GAAG,EAAEhD,KAAK,EAAE;UAAE,OAAOgD,GAAG,IAAIhD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QAAE,CAAC,EAAE8C,IAAI,CAAC;MACpF;MACA,IAAIG,KAAK,GAAG,IAAI,CAAC3H,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;MACnC;MACA,IAAIkG,CAAC,GAAG5C,IAAI,CAACsE,IAAI,CAACtE,IAAI,CAAC2B,GAAG,CAACuC,IAAI,GAAG,EAAE,GAAGG,KAAK,GAAG,EAAE,CAAC,GAAGA,KAAK,CAAC,GAAG,CAAC;MAC/D9G,MAAM,CAAC,CAAC,IAAIqF,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC;MACxBK,MAAM,IAAIL,CAAC,GAAG5G,MAAM,CAACuI,UAAU;MAC/BhH,MAAM,CAAC,CAAC,IAAI0F,MAAM,IAAIA,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC;MAC1C,OAAOA,MAAM;IACjB,CAAC;IACD;IACA;IACA;IACA;IACAjH,MAAM,CAACZ,SAAS,CAACuF,4BAA4B,GAAG,YAAY;MACxD,IAAI,IAAI,CAAC1E,OAAO,IAAI,CAAC,EACjB,OAAO,EAAE,CAAC,KACT;QACD,IAAI2E,QAAQ,GAAGZ,IAAI,CAACsB,KAAK,CAAC,IAAI,CAACrF,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;QAC/C,IAAIuI,IAAI,GAAGxE,IAAI,CAACsB,KAAK,CAAC,CAAC,IAAI,CAACrF,OAAO,GAAG,CAAC,GAAG2E,QAAQ,GAAG,CAAC,GAAG,CAAC,KAAKA,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACrF,IAAIqC,MAAM,GAAG,CAAC,CAAC,CAAC;QAChB,KAAK,IAAIwB,GAAG,GAAG,IAAI,CAAC/H,IAAI,GAAG,CAAC,EAAEuG,MAAM,CAAC/H,MAAM,GAAG0F,QAAQ,EAAE6D,GAAG,IAAID,IAAI,EAC/DvB,MAAM,CAACyB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAED,GAAG,CAAC;QAC5B,OAAOxB,MAAM;MACjB;IACJ,CAAC;IACD;IACA;IACA;IACAjH,MAAM,CAACsG,oBAAoB,GAAG,UAAUP,GAAG,EAAE;MACzC,IAAIA,GAAG,GAAG/F,MAAM,CAACO,WAAW,IAAIwF,GAAG,GAAG/F,MAAM,CAACQ,WAAW,EACpD,MAAM,IAAIC,UAAU,CAAC,6BAA6B,CAAC;MACvD,IAAIwG,MAAM,GAAG,CAAC,EAAE,GAAGlB,GAAG,GAAG,GAAG,IAAIA,GAAG,GAAG,EAAE;MACxC,IAAIA,GAAG,IAAI,CAAC,EAAE;QACV,IAAInB,QAAQ,GAAGZ,IAAI,CAACsB,KAAK,CAACS,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;QACtCkB,MAAM,IAAI,CAAC,EAAE,GAAGrC,QAAQ,GAAG,EAAE,IAAIA,QAAQ,GAAG,EAAE;QAC9C,IAAImB,GAAG,IAAI,CAAC,EACRkB,MAAM,IAAI,EAAE;MACpB;MACA1F,MAAM,CAAC,GAAG,IAAI0F,MAAM,IAAIA,MAAM,IAAI,KAAK,CAAC;MACxC,OAAOA,MAAM;IACjB,CAAC;IACD;IACA;IACA;IACAjH,MAAM,CAACyC,mBAAmB,GAAG,UAAUsD,GAAG,EAAEpE,GAAG,EAAE;MAC7C,OAAOqC,IAAI,CAACsB,KAAK,CAACtF,MAAM,CAACsG,oBAAoB,CAACP,GAAG,CAAC,GAAG,CAAC,CAAC,GACnD/F,MAAM,CAACoG,uBAAuB,CAACzE,GAAG,CAACuE,OAAO,CAAC,CAACH,GAAG,CAAC,GAC5C/F,MAAM,CAACiG,2BAA2B,CAACtE,GAAG,CAACuE,OAAO,CAAC,CAACH,GAAG,CAAC;IAChE,CAAC;IACD;IACA;IACA/F,MAAM,CAAC2G,yBAAyB,GAAG,UAAUgC,MAAM,EAAE;MACjD,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,GAAG,EAC1B,MAAM,IAAIlI,UAAU,CAAC,qBAAqB,CAAC;MAC/C;MACA;MACA,IAAIwG,MAAM,GAAG,EAAE;MACf,KAAK,IAAIlI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4J,MAAM,GAAG,CAAC,EAAE5J,CAAC,EAAE,EAC/BkI,MAAM,CAACrG,IAAI,CAAC,CAAC,CAAC;MAClBqG,MAAM,CAACrG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;MACA;MACA;MACA,IAAIgI,IAAI,GAAG,CAAC;MACZ,KAAK,IAAI7J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4J,MAAM,EAAE5J,CAAC,EAAE,EAAE;QAC7B;QACA,KAAK,IAAI8F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,MAAM,CAAC/H,MAAM,EAAE2F,CAAC,EAAE,EAAE;UACpCoC,MAAM,CAACpC,CAAC,CAAC,GAAG7E,MAAM,CAAC6I,mBAAmB,CAAC5B,MAAM,CAACpC,CAAC,CAAC,EAAE+D,IAAI,CAAC;UACvD,IAAI/D,CAAC,GAAG,CAAC,GAAGoC,MAAM,CAAC/H,MAAM,EACrB+H,MAAM,CAACpC,CAAC,CAAC,IAAIoC,MAAM,CAACpC,CAAC,GAAG,CAAC,CAAC;QAClC;QACA+D,IAAI,GAAG5I,MAAM,CAAC6I,mBAAmB,CAACD,IAAI,EAAE,IAAI,CAAC;MACjD;MACA,OAAO3B,MAAM;IACjB,CAAC;IACD;IACAjH,MAAM,CAAC+G,2BAA2B,GAAG,UAAU9E,IAAI,EAAE6G,OAAO,EAAE;MAC1D,IAAI7B,MAAM,GAAG6B,OAAO,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;QAAE,OAAO,CAAC;MAAE,CAAC,CAAC;MACpD,IAAIC,OAAO,GAAG,SAAAA,CAAUnF,CAAC,EAAE;QACvB,IAAIoF,MAAM,GAAGpF,CAAC,GAAGmD,MAAM,CAACkC,KAAK,CAAC,CAAC;QAC/BlC,MAAM,CAACrG,IAAI,CAAC,CAAC,CAAC;QACdkI,OAAO,CAAC3E,OAAO,CAAC,UAAUiF,IAAI,EAAErK,CAAC,EAAE;UAC/B,OAAOkI,MAAM,CAAClI,CAAC,CAAC,IAAIiB,MAAM,CAAC6I,mBAAmB,CAACO,IAAI,EAAEF,MAAM,CAAC;QAChE,CAAC,CAAC;MACN,CAAC;MACD,KAAK,IAAItG,EAAE,GAAG,CAAC,EAAEyG,MAAM,GAAGpH,IAAI,EAAEW,EAAE,GAAGyG,MAAM,CAACnK,MAAM,EAAE0D,EAAE,EAAE,EAAE;QACtD,IAAIkB,CAAC,GAAGuF,MAAM,CAACzG,EAAE,CAAC;QAClBqG,OAAO,CAACnF,CAAC,CAAC;MACd;MACA,OAAOmD,MAAM;IACjB,CAAC;IACD;IACA;IACAjH,MAAM,CAAC6I,mBAAmB,GAAG,UAAUxE,CAAC,EAAEC,CAAC,EAAE;MACzC,IAAID,CAAC,KAAK,CAAC,IAAI,CAAC,IAAIC,CAAC,KAAK,CAAC,IAAI,CAAC,EAC5B,MAAM,IAAI7D,UAAU,CAAC,mBAAmB,CAAC;MAC7C;MACA,IAAI6I,CAAC,GAAG,CAAC;MACT,KAAK,IAAIvK,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzBuK,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAK,CAACA,CAAC,KAAK,CAAC,IAAI,KAAM;QAClCA,CAAC,IAAI,CAAEhF,CAAC,KAAKvF,CAAC,GAAI,CAAC,IAAIsF,CAAC;MAC5B;MACA9C,MAAM,CAAC+H,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;MACpB,OAAOA,CAAC;IACZ,CAAC;IACD;IACA;IACAtJ,MAAM,CAACZ,SAAS,CAACyI,0BAA0B,GAAG,UAAUH,UAAU,EAAE;MAChE,IAAI1I,CAAC,GAAG0I,UAAU,CAAC,CAAC,CAAC;MACrBnG,MAAM,CAACvC,CAAC,IAAI,IAAI,CAAC0B,IAAI,GAAG,CAAC,CAAC;MAC1B,IAAI6I,IAAI,GAAGvK,CAAC,GAAG,CAAC,IAAI0I,UAAU,CAAC,CAAC,CAAC,IAAI1I,CAAC,IAAI0I,UAAU,CAAC,CAAC,CAAC,IAAI1I,CAAC,GAAG,CAAC,IAAI0I,UAAU,CAAC,CAAC,CAAC,IAAI1I,CAAC,IAAI0I,UAAU,CAAC,CAAC,CAAC,IAAI1I,CAAC;MAC5G,OAAO,CAACuK,IAAI,IAAI7B,UAAU,CAAC,CAAC,CAAC,IAAI1I,CAAC,GAAG,CAAC,IAAI0I,UAAU,CAAC,CAAC,CAAC,IAAI1I,CAAC,GAAG,CAAC,GAAG,CAAC,KAC7DuK,IAAI,IAAI7B,UAAU,CAAC,CAAC,CAAC,IAAI1I,CAAC,GAAG,CAAC,IAAI0I,UAAU,CAAC,CAAC,CAAC,IAAI1I,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxE,CAAC;IACD;IACAgB,MAAM,CAACZ,SAAS,CAAC2I,8BAA8B,GAAG,UAAUyB,eAAe,EAAEC,gBAAgB,EAAE/B,UAAU,EAAE;MACvG,IAAI8B,eAAe,EAAE;QAAE;QACnB,IAAI,CAAC5B,uBAAuB,CAAC6B,gBAAgB,EAAE/B,UAAU,CAAC;QAC1D+B,gBAAgB,GAAG,CAAC;MACxB;MACAA,gBAAgB,IAAI,IAAI,CAAC/I,IAAI,CAAC,CAAC;MAC/B,IAAI,CAACkH,uBAAuB,CAAC6B,gBAAgB,EAAE/B,UAAU,CAAC;MAC1D,OAAO,IAAI,CAACG,0BAA0B,CAACH,UAAU,CAAC;IACtD,CAAC;IACD;IACA1H,MAAM,CAACZ,SAAS,CAACwI,uBAAuB,GAAG,UAAU6B,gBAAgB,EAAE/B,UAAU,EAAE;MAC/E,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAClB+B,gBAAgB,IAAI,IAAI,CAAC/I,IAAI,CAAC,CAAC;MACnCgH,UAAU,CAACgC,GAAG,CAAC,CAAC;MAChBhC,UAAU,CAACiC,OAAO,CAACF,gBAAgB,CAAC;IACxC,CAAC;IACD;IACA;IACAzJ,MAAM,CAACO,WAAW,GAAG,CAAC;IACtB;IACAP,MAAM,CAACQ,WAAW,GAAG,EAAE;IACvB;IACAR,MAAM,CAAC2H,UAAU,GAAG,CAAC;IACrB3H,MAAM,CAACiI,UAAU,GAAG,CAAC;IACrBjI,MAAM,CAAC8H,UAAU,GAAG,EAAE;IACtB9H,MAAM,CAACuI,UAAU,GAAG,EAAE;IACtBvI,MAAM,CAACoG,uBAAuB,GAAG;IAC7B;IACA;IACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAAE;IACrK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAAE;IACtK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAAE;IACtK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAE;IAAA,CACzK;IACDpG,MAAM,CAACiG,2BAA2B,GAAG;IACjC;IACA;IACA,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAAE;IAC/I,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAAE;IACxJ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAAE;IAC3J,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAE;IAAA,CAC/J;IACD,OAAOjG,MAAM;EACjB,CAAC,CAAC,CAAE;EACJD,SAAS,CAACC,MAAM,GAAGA,MAAM;EACzB;EACA;EACA,SAASsD,UAAUA,CAACsG,GAAG,EAAEC,GAAG,EAAE1G,EAAE,EAAE;IAC9B,IAAI0G,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,EAAE,IAAID,GAAG,KAAKC,GAAG,IAAI,CAAC,EACvC,MAAM,IAAIpJ,UAAU,CAAC,oBAAoB,CAAC;IAC9C,KAAK,IAAI1B,CAAC,GAAG8K,GAAG,GAAG,CAAC,EAAE9K,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE;IAAE;IAC/BoE,EAAE,CAACvC,IAAI,CAAEgJ,GAAG,KAAK7K,CAAC,GAAI,CAAC,CAAC;EAChC;EACA;EACA,SAASoG,MAAMA,CAACd,CAAC,EAAEtF,CAAC,EAAE;IAClB,OAAO,CAAEsF,CAAC,KAAKtF,CAAC,GAAI,CAAC,KAAK,CAAC;EAC/B;EACA;EACA,SAASwC,MAAMA,CAACuI,IAAI,EAAE;IAClB,IAAI,CAACA,IAAI,EACL,MAAM,IAAIjK,KAAK,CAAC,iBAAiB,CAAC;EAC1C;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIgC,SAAS,GAAG,aAAe,YAAY;IACvC;IACA;IACA;IACA;IACA,SAASA,SAASA;IAClB;IACA0B,IAAI;IACJ;IACA;IACA;IACAE,QAAQ;IACR;IACAsG,OAAO,EAAE;MACL,IAAI,CAACxG,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACE,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACsG,OAAO,GAAGA,OAAO;MACtB,IAAItG,QAAQ,GAAG,CAAC,EACZ,MAAM,IAAIhD,UAAU,CAAC,kBAAkB,CAAC;MAC5C,IAAI,CAACsJ,OAAO,GAAGA,OAAO,CAAClJ,KAAK,CAAC,CAAC,CAAC,CAAC;IACpC;IACA;IACA;IACA;IACA;IACAgB,SAAS,CAACM,SAAS,GAAG,UAAUF,IAAI,EAAE;MAClC,IAAIkB,EAAE,GAAG,EAAE;MACX,KAAK,IAAIP,EAAE,GAAG,CAAC,EAAEoH,MAAM,GAAG/H,IAAI,EAAEW,EAAE,GAAGoH,MAAM,CAAC9K,MAAM,EAAE0D,EAAE,EAAE,EAAE;QACtD,IAAIkB,CAAC,GAAGkG,MAAM,CAACpH,EAAE,CAAC;QAClBU,UAAU,CAACQ,CAAC,EAAE,CAAC,EAAEX,EAAE,CAAC;MACxB;MACA,OAAO,IAAItB,SAAS,CAACA,SAAS,CAACoI,IAAI,CAACC,IAAI,EAAEjI,IAAI,CAAC/C,MAAM,EAAEiE,EAAE,CAAC;IAC9D,CAAC;IACD;IACAtB,SAAS,CAACsI,WAAW,GAAG,UAAUC,MAAM,EAAE;MACtC,IAAI,CAACvI,SAAS,CAACwI,SAAS,CAACD,MAAM,CAAC,EAC5B,MAAM,IAAI3J,UAAU,CAAC,wCAAwC,CAAC;MAClE,IAAI0C,EAAE,GAAG,EAAE;MACX,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqL,MAAM,CAAClL,MAAM,GAAG;QAAE;QAClC,IAAIF,CAAC,GAAGgF,IAAI,CAACC,GAAG,CAACmG,MAAM,CAAClL,MAAM,GAAGH,CAAC,EAAE,CAAC,CAAC;QACtCuE,UAAU,CAACgH,QAAQ,CAACF,MAAM,CAACG,SAAS,CAACxL,CAAC,EAAEA,CAAC,GAAGC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEmE,EAAE,CAAC;QACnEpE,CAAC,IAAIC,CAAC;MACV;MACA,OAAO,IAAI6C,SAAS,CAACA,SAAS,CAACoI,IAAI,CAACO,OAAO,EAAEJ,MAAM,CAAClL,MAAM,EAAEiE,EAAE,CAAC;IACnE,CAAC;IACD;IACA;IACA;IACAtB,SAAS,CAAC4I,gBAAgB,GAAG,UAAU/I,IAAI,EAAE;MACzC,IAAI,CAACG,SAAS,CAAC6I,cAAc,CAAChJ,IAAI,CAAC,EAC/B,MAAM,IAAIjB,UAAU,CAAC,6DAA6D,CAAC;MACvF,IAAI0C,EAAE,GAAG,EAAE;MACX,IAAIpE,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI2C,IAAI,CAACxC,MAAM,EAAEH,CAAC,IAAI,CAAC,EAAE;QAAE;QACxC,IAAI4L,IAAI,GAAG9I,SAAS,CAAC+I,oBAAoB,CAACC,OAAO,CAACnJ,IAAI,CAACoJ,MAAM,CAAC/L,CAAC,CAAC,CAAC,GAAG,EAAE;QACtE4L,IAAI,IAAI9I,SAAS,CAAC+I,oBAAoB,CAACC,OAAO,CAACnJ,IAAI,CAACoJ,MAAM,CAAC/L,CAAC,GAAG,CAAC,CAAC,CAAC;QAClEuE,UAAU,CAACqH,IAAI,EAAE,EAAE,EAAExH,EAAE,CAAC;MAC5B;MACA,IAAIpE,CAAC,GAAG2C,IAAI,CAACxC,MAAM;QAAE;QACjBoE,UAAU,CAACzB,SAAS,CAAC+I,oBAAoB,CAACC,OAAO,CAACnJ,IAAI,CAACoJ,MAAM,CAAC/L,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEoE,EAAE,CAAC;MAC7E,OAAO,IAAItB,SAAS,CAACA,SAAS,CAACoI,IAAI,CAACc,YAAY,EAAErJ,IAAI,CAACxC,MAAM,EAAEiE,EAAE,CAAC;IACtE,CAAC;IACD;IACA;IACAtB,SAAS,CAACC,YAAY,GAAG,UAAUJ,IAAI,EAAE;MACrC;MACA,IAAIA,IAAI,IAAI,EAAE,EACV,OAAO,EAAE,CAAC,KACT,IAAIG,SAAS,CAACwI,SAAS,CAAC3I,IAAI,CAAC,EAC9B,OAAO,CAACG,SAAS,CAACsI,WAAW,CAACzI,IAAI,CAAC,CAAC,CAAC,KACpC,IAAIG,SAAS,CAAC6I,cAAc,CAAChJ,IAAI,CAAC,EACnC,OAAO,CAACG,SAAS,CAAC4I,gBAAgB,CAAC/I,IAAI,CAAC,CAAC,CAAC,KAE1C,OAAO,CAACG,SAAS,CAACM,SAAS,CAACN,SAAS,CAACmJ,eAAe,CAACtJ,IAAI,CAAC,CAAC,CAAC;IACrE,CAAC;IACD;IACA;IACAG,SAAS,CAACoJ,OAAO,GAAG,UAAUC,SAAS,EAAE;MACrC,IAAI/H,EAAE,GAAG,EAAE;MACX,IAAI+H,SAAS,GAAG,CAAC,EACb,MAAM,IAAIzK,UAAU,CAAC,mCAAmC,CAAC,CAAC,KACzD,IAAIyK,SAAS,GAAI,CAAC,IAAI,CAAE,EACzB5H,UAAU,CAAC4H,SAAS,EAAE,CAAC,EAAE/H,EAAE,CAAC,CAAC,KAC5B,IAAI+H,SAAS,GAAI,CAAC,IAAI,EAAG,EAAE;QAC5B5H,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEH,EAAE,CAAC;QACpBG,UAAU,CAAC4H,SAAS,EAAE,EAAE,EAAE/H,EAAE,CAAC;MACjC,CAAC,MACI,IAAI+H,SAAS,GAAG,OAAO,EAAE;QAC1B5H,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEH,EAAE,CAAC;QACpBG,UAAU,CAAC4H,SAAS,EAAE,EAAE,EAAE/H,EAAE,CAAC;MACjC,CAAC,MAEG,MAAM,IAAI1C,UAAU,CAAC,mCAAmC,CAAC;MAC7D,OAAO,IAAIoB,SAAS,CAACA,SAAS,CAACoI,IAAI,CAACkB,GAAG,EAAE,CAAC,EAAEhI,EAAE,CAAC;IACnD,CAAC;IACD;IACA;IACAtB,SAAS,CAACwI,SAAS,GAAG,UAAU3I,IAAI,EAAE;MAClC,OAAOG,SAAS,CAACuJ,aAAa,CAACC,IAAI,CAAC3J,IAAI,CAAC;IAC7C,CAAC;IACD;IACA;IACA;IACAG,SAAS,CAAC6I,cAAc,GAAG,UAAUhJ,IAAI,EAAE;MACvC,OAAOG,SAAS,CAACyJ,kBAAkB,CAACD,IAAI,CAAC3J,IAAI,CAAC;IAClD,CAAC;IACD;IACA;IACAG,SAAS,CAACzC,SAAS,CAACyE,OAAO,GAAG,YAAY;MACtC,OAAO,IAAI,CAACkG,OAAO,CAAClJ,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD;IACA;IACAgB,SAAS,CAACc,YAAY,GAAG,UAAUf,IAAI,EAAE3B,OAAO,EAAE;MAC9C,IAAIgH,MAAM,GAAG,CAAC;MACd,KAAK,IAAIrE,EAAE,GAAG,CAAC,EAAE2I,MAAM,GAAG3J,IAAI,EAAEgB,EAAE,GAAG2I,MAAM,CAACrM,MAAM,EAAE0D,EAAE,EAAE,EAAE;QACtD,IAAIV,GAAG,GAAGqJ,MAAM,CAAC3I,EAAE,CAAC;QACpB,IAAI4I,MAAM,GAAGtJ,GAAG,CAACqB,IAAI,CAACG,gBAAgB,CAACzD,OAAO,CAAC;QAC/C,IAAIiC,GAAG,CAACuB,QAAQ,IAAK,CAAC,IAAI+H,MAAO,EAC7B,OAAOC,QAAQ,CAAC,CAAC;QACrBxE,MAAM,IAAI,CAAC,GAAGuE,MAAM,GAAGtJ,GAAG,CAAC6H,OAAO,CAAC7K,MAAM;MAC7C;MACA,OAAO+H,MAAM;IACjB,CAAC;IACD;IACApF,SAAS,CAACmJ,eAAe,GAAG,UAAUU,GAAG,EAAE;MACvCA,GAAG,GAAGC,SAAS,CAACD,GAAG,CAAC;MACpB,IAAIzE,MAAM,GAAG,EAAE;MACf,KAAK,IAAIlI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2M,GAAG,CAACxM,MAAM,EAAEH,CAAC,EAAE,EAAE;QACjC,IAAI2M,GAAG,CAACZ,MAAM,CAAC/L,CAAC,CAAC,IAAI,GAAG,EACpBkI,MAAM,CAACrG,IAAI,CAAC8K,GAAG,CAACE,UAAU,CAAC7M,CAAC,CAAC,CAAC,CAAC,KAC9B;UACDkI,MAAM,CAACrG,IAAI,CAAC0J,QAAQ,CAACoB,GAAG,CAACnB,SAAS,CAACxL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACtDA,CAAC,IAAI,CAAC;QACV;MACJ;MACA,OAAOkI,MAAM;IACjB,CAAC;IACD;IACA;IACApF,SAAS,CAACuJ,aAAa,GAAG,UAAU;IACpC;IACAvJ,SAAS,CAACyJ,kBAAkB,GAAG,uBAAuB;IACtD;IACA;IACAzJ,SAAS,CAAC+I,oBAAoB,GAAG,+CAA+C;IAChF,OAAO/I,SAAS;EACpB,CAAC,CAAC,CAAE;EACJ9B,SAAS,CAAC8B,SAAS,GAAGA,SAAS;AACnC,CAAC,EAAE9B,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC;AACA,CAAC,UAAUA,SAAS,EAAE;EAClB,CAAC,UAAUC,MAAM,EAAE;IACf;AACR;AACA;IACQ,IAAI8C,GAAG,GAAG,aAAe,YAAY;MACjC;MACA,SAASA,GAAGA;MACZ;MACAoD,OAAO;MACP;MACAlB,UAAU,EAAE;QACR,IAAI,CAACkB,OAAO,GAAGA,OAAO;QACtB,IAAI,CAAClB,UAAU,GAAGA,UAAU;MAChC;MACA;MACAlC,GAAG,CAAC+I,GAAG,GAAG,IAAI/I,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzBA,GAAG,CAACC,MAAM,GAAG,IAAID,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5BA,GAAG,CAACE,QAAQ,GAAG,IAAIF,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9BA,GAAG,CAACG,IAAI,GAAG,IAAIH,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,OAAOA,GAAG;IACd,CAAC,CAAC,CAAE;IACJ9C,MAAM,CAAC8C,GAAG,GAAGA,GAAG;EACpB,CAAC,EAAE/C,SAAS,CAACC,MAAM,KAAKD,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,EAAED,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC;AACA,CAAC,UAAUA,SAAS,EAAE;EAClB,CAAC,UAAU8B,SAAS,EAAE;IAClB;AACR;AACA;IACQ,IAAIoI,IAAI,GAAG,aAAe,YAAY;MAClC;MACA,SAASA,IAAIA;MACb;MACAzG,QAAQ;MACR;MACAsI,gBAAgB,EAAE;QACd,IAAI,CAACtI,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACsI,gBAAgB,GAAGA,gBAAgB;MAC5C;MACA;MACA;MACA;MACA7B,IAAI,CAAC7K,SAAS,CAACsE,gBAAgB,GAAG,UAAUqC,GAAG,EAAE;QAC7C,OAAO,IAAI,CAAC+F,gBAAgB,CAAC9H,IAAI,CAACsB,KAAK,CAAC,CAACS,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;MAC5D,CAAC;MACD;MACAkE,IAAI,CAACO,OAAO,GAAG,IAAIP,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC1CA,IAAI,CAACc,YAAY,GAAG,IAAId,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC9CA,IAAI,CAACC,IAAI,GAAG,IAAID,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MACtCA,IAAI,CAAC8B,KAAK,GAAG,IAAI9B,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MACvCA,IAAI,CAACkB,GAAG,GAAG,IAAIlB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACnC,OAAOA,IAAI;IACf,CAAC,CAAC,CAAE;IACJpI,SAAS,CAACoI,IAAI,GAAGA,IAAI;EACzB,CAAC,EAAElK,SAAS,CAAC8B,SAAS,KAAK9B,SAAS,CAAC8B,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,EAAE9B,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,IAAIiM,EAAE,GAAGjM,SAAS;AAElB,IAAIkM,wBAAwB,GAAG,GAAG;AAClC,IAAIC,oBAAoB,GAAG;EACvBC,CAAC,EAAEH,EAAE,CAAChM,MAAM,CAAC8C,GAAG,CAAC+I,GAAG;EACpBO,CAAC,EAAEJ,EAAE,CAAChM,MAAM,CAAC8C,GAAG,CAACC,MAAM;EACvBsJ,CAAC,EAAEL,EAAE,CAAChM,MAAM,CAAC8C,GAAG,CAACE,QAAQ;EACzBsJ,CAAC,EAAEN,EAAE,CAAChM,MAAM,CAAC8C,GAAG,CAACG;AACrB,CAAC;AACD;AACA,IAAIsJ,eAAe,GAAI,YAAY;EAC/B,IAAI;IACA,IAAIC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,IAAID,MAAM,CAAC,CAAC,CAAC;EACtC,CAAC,CACD,OAAO5M,CAAC,EAAE;IACN,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC,CAAE,CAAC;AACJ,SAAS8M,sBAAsBA,CAACC,KAAK,EAAE;EACnC,OAAOA,KAAK,IAAIT,oBAAoB;AACxC;AACA,SAASU,YAAYA,CAACvM,OAAO,EAAEwM,MAAM,EAAE;EACnC,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;IAAEA,MAAM,GAAG,CAAC;EAAE;EACrC,IAAIC,GAAG,GAAG,EAAE;EACZzM,OAAO,CAAC8D,OAAO,CAAC,UAAUxD,GAAG,EAAE2D,CAAC,EAAE;IAC9B,IAAIyI,KAAK,GAAG,IAAI;IAChBpM,GAAG,CAACwD,OAAO,CAAC,UAAU6I,IAAI,EAAE3I,CAAC,EAAE;MAC3B,IAAI,CAAC2I,IAAI,IAAID,KAAK,KAAK,IAAI,EAAE;QACzB;QACA;QACAD,GAAG,CAAClM,IAAI,CAAC,GAAG,CAACoG,MAAM,CAAC+F,KAAK,GAAGF,MAAM,EAAE,GAAG,CAAC,CAAC7F,MAAM,CAAC1C,CAAC,GAAGuI,MAAM,EAAE,GAAG,CAAC,CAAC7F,MAAM,CAAC3C,CAAC,GAAG0I,KAAK,EAAE,KAAK,CAAC,CAAC/F,MAAM,CAAC+F,KAAK,GAAGF,MAAM,EAAE,GAAG,CAAC,CAAC;QACtHE,KAAK,GAAG,IAAI;QACZ;MACJ;MACA;MACA,IAAI1I,CAAC,KAAK1D,GAAG,CAACzB,MAAM,GAAG,CAAC,EAAE;QACtB,IAAI,CAAC8N,IAAI,EAAE;UACP;UACA;UACA;QACJ;QACA,IAAID,KAAK,KAAK,IAAI,EAAE;UAChB;UACAD,GAAG,CAAClM,IAAI,CAAC,GAAG,CAACoG,MAAM,CAAC3C,CAAC,GAAGwI,MAAM,EAAE,GAAG,CAAC,CAAC7F,MAAM,CAAC1C,CAAC,GAAGuI,MAAM,EAAE,QAAQ,CAAC,CAAC7F,MAAM,CAAC3C,CAAC,GAAGwI,MAAM,EAAE,GAAG,CAAC,CAAC;QAC9F,CAAC,MACI;UACD;UACAC,GAAG,CAAClM,IAAI,CAAC,GAAG,CAACoG,MAAM,CAAC+F,KAAK,GAAGF,MAAM,EAAE,GAAG,CAAC,CAAC7F,MAAM,CAAC1C,CAAC,GAAGuI,MAAM,EAAE,IAAI,CAAC,CAAC7F,MAAM,CAAC3C,CAAC,GAAG,CAAC,GAAG0I,KAAK,EAAE,KAAK,CAAC,CAAC/F,MAAM,CAAC+F,KAAK,GAAGF,MAAM,EAAE,GAAG,CAAC,CAAC;QAC/H;QACA;MACJ;MACA,IAAIG,IAAI,IAAID,KAAK,KAAK,IAAI,EAAE;QACxBA,KAAK,GAAG1I,CAAC;MACb;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOyI,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;AACvB;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEzM,IAAI,EAAEmM,MAAM,EAAEO,aAAa,EAAE;EAC1D,IAAIC,KAAK,GAAGD,aAAa,CAACC,KAAK;IAAEC,MAAM,GAAGF,aAAa,CAACE,MAAM;IAAEC,MAAM,GAAGH,aAAa,CAAC/I,CAAC;IAAEmJ,MAAM,GAAGJ,aAAa,CAAC9I,CAAC;EAClH,IAAImJ,QAAQ,GAAGN,KAAK,CAACjO,MAAM,GAAG2N,MAAM,GAAG,CAAC;EACxC,IAAIa,WAAW,GAAG1J,IAAI,CAACsB,KAAK,CAAC5E,IAAI,GAAG,GAAG,CAAC;EACxC,IAAIiN,KAAK,GAAGF,QAAQ,GAAG/M,IAAI;EAC3B,IAAIkN,CAAC,GAAG,CAACP,KAAK,IAAIK,WAAW,IAAIC,KAAK;EACtC,IAAIpP,CAAC,GAAG,CAAC+O,MAAM,IAAII,WAAW,IAAIC,KAAK;EACvC,IAAItJ,CAAC,GAAGkJ,MAAM,IAAI,IAAI,GAAGJ,KAAK,CAACjO,MAAM,GAAG,CAAC,GAAG0O,CAAC,GAAG,CAAC,GAAGL,MAAM,GAAGI,KAAK;EAClE,IAAIrJ,CAAC,GAAGkJ,MAAM,IAAI,IAAI,GAAGL,KAAK,CAACjO,MAAM,GAAG,CAAC,GAAGX,CAAC,GAAG,CAAC,GAAGiP,MAAM,GAAGG,KAAK;EAClE,IAAIE,UAAU,GAAG,IAAI;EACrB,IAAIT,aAAa,CAACU,QAAQ,EAAE;IACxB,IAAIC,MAAM,GAAG/J,IAAI,CAACsB,KAAK,CAACjB,CAAC,CAAC;IAC1B,IAAI2J,MAAM,GAAGhK,IAAI,CAACsB,KAAK,CAAChB,CAAC,CAAC;IAC1B,IAAI2J,KAAK,GAAGjK,IAAI,CAACsE,IAAI,CAACsF,CAAC,GAAGvJ,CAAC,GAAG0J,MAAM,CAAC;IACrC,IAAIG,KAAK,GAAGlK,IAAI,CAACsE,IAAI,CAAC/J,CAAC,GAAG+F,CAAC,GAAG0J,MAAM,CAAC;IACrCH,UAAU,GAAG;MAAExJ,CAAC,EAAE0J,MAAM;MAAEzJ,CAAC,EAAE0J,MAAM;MAAEJ,CAAC,EAAEK,KAAK;MAAE1P,CAAC,EAAE2P;IAAM,CAAC;EAC7D;EACA,OAAO;IAAE7J,CAAC,EAAEA,CAAC;IAAEC,CAAC,EAAEA,CAAC;IAAE/F,CAAC,EAAEA,CAAC;IAAEqP,CAAC,EAAEA,CAAC;IAAEC,UAAU,EAAEA;EAAW,CAAC;AAC7D;AACA,SAASM,eAAeA,CAAC9N,OAAO,EAAEwN,UAAU,EAAE;EAC1C,OAAOxN,OAAO,CAACQ,KAAK,CAAC,CAAC,CAACkI,GAAG,CAAC,UAAUpI,GAAG,EAAE2D,CAAC,EAAE;IACzC,IAAIA,CAAC,GAAGuJ,UAAU,CAACvJ,CAAC,IAAIA,CAAC,IAAIuJ,UAAU,CAACvJ,CAAC,GAAGuJ,UAAU,CAACtP,CAAC,EAAE;MACtD,OAAOoC,GAAG;IACd;IACA,OAAOA,GAAG,CAACoI,GAAG,CAAC,UAAUiE,IAAI,EAAE3I,CAAC,EAAE;MAC9B,IAAIA,CAAC,GAAGwJ,UAAU,CAACxJ,CAAC,IAAIA,CAAC,IAAIwJ,UAAU,CAACxJ,CAAC,GAAGwJ,UAAU,CAACD,CAAC,EAAE;QACtD,OAAOZ,IAAI;MACf;MACA,OAAO,KAAK;IAChB,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,IAAIoB,WAAW,GAAG;EACdC,KAAK,EAAE;IACHC,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE;EACb,CAAC;EACD/N,IAAI,EAAE;IACF4N,IAAI,EAAEI,MAAM;IACZD,OAAO,EAAE;EACb,CAAC;EACD9B,KAAK,EAAE;IACH2B,IAAI,EAAEC,MAAM;IACZE,OAAO,EAAExC,wBAAwB;IACjC0C,SAAS,EAAE,SAAAA,CAAUC,CAAC,EAAE;MAAE,OAAOlC,sBAAsB,CAACkC,CAAC,CAAC;IAAE;EAChE,CAAC;EACDC,UAAU,EAAE;IACRP,IAAI,EAAEC,MAAM;IACZE,OAAO,EAAE;EACb,CAAC;EACDK,UAAU,EAAE;IACRR,IAAI,EAAEC,MAAM;IACZE,OAAO,EAAE;EACb,CAAC;EACD5B,MAAM,EAAE;IACJyB,IAAI,EAAEI,MAAM;IACZF,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;EACb,CAAC;EACDrB,aAAa,EAAE;IACXkB,IAAI,EAAE3P,MAAM;IACZ6P,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAQ,CAAC,CAAC;IAAG;EACxC,CAAC;EACDM,QAAQ,EAAE;IACNT,IAAI,EAAEU,OAAO;IACbR,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;EACb,CAAC;EACDQ,YAAY,EAAE;IACVX,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,QAAQ;IACjBE,SAAS,EAAE,SAAAA,CAAU9P,CAAC,EAAE;MAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACgM,OAAO,CAAChM,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE;EAC3E,CAAC;EACDqQ,kBAAkB,EAAE;IAChBZ,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;EACb,CAAC;EACDU,gBAAgB,EAAE;IACdb,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;EACb;AACJ,CAAC;AACD,IAAIW,cAAc,GAAG1Q,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0P,WAAW,CAAC,EAAE;EAAEiB,QAAQ,EAAE;IAC7Df,IAAI,EAAEC,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,QAAQ;IACjBE,SAAS,EAAE,SAAAA,CAAUW,EAAE,EAAE;MAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAACzE,OAAO,CAACyE,EAAE,CAAC,GAAG,CAAC,CAAC;IAAE;EAC1E;AAAE,CAAC,CAAC;AACR,IAAIC,SAAS,GAAGnR,eAAe,CAAC;EAC5B0B,IAAI,EAAE,WAAW;EACjB0P,KAAK,EAAEpB,WAAW;EAClBqB,KAAK,EAAE,SAAAA,CAAUD,KAAK,EAAE;IACpB,IAAI/B,QAAQ,GAAGpP,GAAG,CAAC,CAAC,CAAC;IACrB,IAAIqR,MAAM,GAAGrR,GAAG,CAAC,EAAE,CAAC;IACpB,IAAIsR,UAAU;IACd,IAAIC,QAAQ,GAAG,SAAAA,CAAA,EAAY;MACvB,IAAIvB,KAAK,GAAGmB,KAAK,CAACnB,KAAK;QAAEwB,MAAM,GAAGL,KAAK,CAAC7C,KAAK;QAAEmD,OAAO,GAAGN,KAAK,CAAC3C,MAAM;MACrE,IAAIA,MAAM,GAAGiD,OAAO,KAAK,CAAC;MAC1B,IAAInD,KAAK,GAAGD,sBAAsB,CAACmD,MAAM,CAAC,GAAGA,MAAM,GAAG5D,wBAAwB;MAC9E,IAAIkB,KAAK,GAAGnB,EAAE,CAAChM,MAAM,CAACyB,UAAU,CAAC4M,KAAK,EAAEnC,oBAAoB,CAACS,KAAK,CAAC,CAAC,CAACpI,UAAU,CAAC,CAAC;MACjFkJ,QAAQ,CAACY,KAAK,GAAGlB,KAAK,CAACjO,MAAM,GAAG2N,MAAM,GAAG,CAAC;MAC1C,IAAI2C,KAAK,CAACpC,aAAa,CAAC2C,GAAG,EAAE;QACzB,IAAI3C,aAAa,GAAGF,gBAAgB,CAACC,KAAK,EAAEqC,KAAK,CAAC9O,IAAI,EAAEmM,MAAM,EAAE2C,KAAK,CAACpC,aAAa,CAAC;QACpFuC,UAAU,GAAG;UACTtL,CAAC,EAAE+I,aAAa,CAAC/I,CAAC,GAAGwI,MAAM;UAC3BvI,CAAC,EAAE8I,aAAa,CAAC9I,CAAC,GAAGuI,MAAM;UAC3BQ,KAAK,EAAED,aAAa,CAACQ,CAAC;UACtBN,MAAM,EAAEF,aAAa,CAAC7O;QAC1B,CAAC;QACD,IAAI6O,aAAa,CAACS,UAAU,EAAE;UAC1BV,KAAK,GAAGgB,eAAe,CAAChB,KAAK,EAAEC,aAAa,CAACS,UAAU,CAAC;QAC5D;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA6B,MAAM,CAACrB,KAAK,GAAGzB,YAAY,CAACO,KAAK,EAAEN,MAAM,CAAC;IAC9C,CAAC;IACD,IAAImD,cAAc,GAAG,SAAAA,CAAA,EAAY;MAC7B,IAAI,CAACR,KAAK,CAACT,QAAQ,EACf,OAAO,IAAI;MACf,IAAIkB,aAAa,GAAGT,KAAK,CAACP,YAAY,KAAK,QAAQ,GAC7C;QACEiB,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACR,CAAC,GACC;QACEC,EAAE,EAAE,KAAK;QACTC,EAAE,EAAE,KAAK;QACTC,CAAC,EAAE,KAAK;QACRC,EAAE,EAAE,KAAK;QACTC,EAAE,EAAE;MACR,CAAC;MACL,OAAOnS,CAAC,CAACiR,KAAK,CAACP,YAAY,KAAK,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB,EAAEvQ,QAAQ,CAAC;QAAEiS,EAAE,EAAE;MAAc,CAAC,EAAEV,aAAa,CAAC,EAAE,CAC5H1R,CAAC,CAAC,MAAM,EAAE;QACNqS,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE;UAAEC,SAAS,EAAEtB,KAAK,CAACN;QAAmB;MACjD,CAAC,CAAC,EACF3Q,CAAC,CAAC,MAAM,EAAE;QACNqS,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE;UAAEC,SAAS,EAAEtB,KAAK,CAACL;QAAiB;MAC/C,CAAC,CAAC,CACL,CAAC;IACN,CAAC;IACDS,QAAQ,CAAC,CAAC;IACVtR,SAAS,CAACsR,QAAQ,CAAC;IACnB,OAAO,YAAY;MAAE,OAAOrR,CAAC,CAAC,KAAK,EAAE;QACjC8O,KAAK,EAAEmC,KAAK,CAAC9O,IAAI;QACjB4M,MAAM,EAAEkC,KAAK,CAAC9O,IAAI;QAClB,iBAAiB,EAAE,YAAY;QAC/BqQ,KAAK,EAAE,4BAA4B;QACnCC,OAAO,EAAE,MAAM,CAAChK,MAAM,CAACyG,QAAQ,CAACY,KAAK,EAAE,GAAG,CAAC,CAACrH,MAAM,CAACyG,QAAQ,CAACY,KAAK;MACrE,CAAC,EAAE,CACC9P,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAACyR,cAAc,CAAC,CAAC,CAAC,CAAC,EACjCzR,CAAC,CAAC,MAAM,EAAE;QACN8O,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACd2D,IAAI,EAAEzB,KAAK,CAACX;MAChB,CAAC,CAAC,EACFtQ,CAAC,CAAC,MAAM,EAAE;QACN0S,IAAI,EAAEzB,KAAK,CAACT,QAAQ,GAAG,mBAAmB,GAAGS,KAAK,CAACV,UAAU;QAC7DoC,CAAC,EAAExB,MAAM,CAACrB;MACd,CAAC,CAAC,EACFmB,KAAK,CAACpC,aAAa,CAAC2C,GAAG,IAAIxR,CAAC,CAAC,OAAO,EAAEG,QAAQ,CAAC;QAAEyS,IAAI,EAAE3B,KAAK,CAACpC,aAAa,CAAC2C;MAAI,CAAC,EAAEJ,UAAU,CAAC,CAAC,CACjG,CAAC;IAAE,CAAC;EACT;AACJ,CAAC,CAAC;AACF,IAAIyB,YAAY,GAAGhT,eAAe,CAAC;EAC/B0B,IAAI,EAAE,cAAc;EACpB0P,KAAK,EAAEpB,WAAW;EAClBqB,KAAK,EAAE,SAAAA,CAAUD,KAAK,EAAE6B,GAAG,EAAE;IACzB,IAAIC,QAAQ,GAAGjT,GAAG,CAAC,IAAI,CAAC;IACxB,IAAIkT,QAAQ,GAAGlT,GAAG,CAAC,IAAI,CAAC;IACxB,IAAIuR,QAAQ,GAAG,SAAAA,CAAA,EAAY;MACvB,IAAIvB,KAAK,GAAGmB,KAAK,CAACnB,KAAK;QAAEwB,MAAM,GAAGL,KAAK,CAAC7C,KAAK;QAAEjM,IAAI,GAAG8O,KAAK,CAAC9O,IAAI;QAAEoP,OAAO,GAAGN,KAAK,CAAC3C,MAAM;QAAEgC,UAAU,GAAGW,KAAK,CAACX,UAAU;QAAEC,UAAU,GAAGU,KAAK,CAACV,UAAU;QAAEC,QAAQ,GAAGS,KAAK,CAACT,QAAQ;QAAEE,YAAY,GAAGO,KAAK,CAACP,YAAY;QAAEC,kBAAkB,GAAGM,KAAK,CAACN,kBAAkB;QAAEC,gBAAgB,GAAGK,KAAK,CAACL,gBAAgB;MAC9S,IAAItC,MAAM,GAAGiD,OAAO,KAAK,CAAC;MAC1B,IAAInD,KAAK,GAAGD,sBAAsB,CAACmD,MAAM,CAAC,GAAGA,MAAM,GAAG5D,wBAAwB;MAC9E,IAAIuF,MAAM,GAAGF,QAAQ,CAACjD,KAAK;MAC3B,IAAI,CAACmD,MAAM,EAAE;QACT;MACJ;MACA,IAAIH,GAAG,GAAGG,MAAM,CAACC,UAAU,CAAC,IAAI,CAAC;MACjC,IAAI,CAACJ,GAAG,EAAE;QACN;MACJ;MACA,IAAIlE,KAAK,GAAGnB,EAAE,CAAChM,MAAM,CAACyB,UAAU,CAAC4M,KAAK,EAAEnC,oBAAoB,CAACS,KAAK,CAAC,CAAC,CAACpI,UAAU,CAAC,CAAC;MACjF,IAAIkJ,QAAQ,GAAGN,KAAK,CAACjO,MAAM,GAAG2N,MAAM,GAAG,CAAC;MACxC,IAAI6E,KAAK,GAAGH,QAAQ,CAAClD,KAAK;MAC1B,IAAIsB,UAAU,GAAG;QAAEtL,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAE+I,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACpD,IAAIqE,SAAS,GAAGnC,KAAK,CAACpC,aAAa,CAAC2C,GAAG,IAAI2B,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACE,YAAY,KAAK,CAAC,IAAIF,KAAK,CAACG,aAAa,KAAK,CAAC;MACjH,IAAIF,SAAS,EAAE;QACX,IAAIvE,aAAa,GAAGF,gBAAgB,CAACC,KAAK,EAAEqC,KAAK,CAAC9O,IAAI,EAAEmM,MAAM,EAAE2C,KAAK,CAACpC,aAAa,CAAC;QACpFuC,UAAU,GAAG;UACTtL,CAAC,EAAE+I,aAAa,CAAC/I,CAAC,GAAGwI,MAAM;UAC3BvI,CAAC,EAAE8I,aAAa,CAAC9I,CAAC,GAAGuI,MAAM;UAC3BQ,KAAK,EAAED,aAAa,CAACQ,CAAC;UACtBN,MAAM,EAAEF,aAAa,CAAC7O;QAC1B,CAAC;QACD,IAAI6O,aAAa,CAACS,UAAU,EAAE;UAC1BV,KAAK,GAAGgB,eAAe,CAAChB,KAAK,EAAEC,aAAa,CAACS,UAAU,CAAC;QAC5D;MACJ;MACA,IAAIiE,gBAAgB,GAAGC,MAAM,CAACD,gBAAgB,IAAI,CAAC;MACnD,IAAInE,KAAK,GAAIjN,IAAI,GAAG+M,QAAQ,GAAIqE,gBAAgB;MAChDN,MAAM,CAAClE,MAAM,GAAGkE,MAAM,CAACnE,KAAK,GAAG3M,IAAI,GAAGoR,gBAAgB;MACtDT,GAAG,CAAC1D,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC;MACvB0D,GAAG,CAACW,SAAS,GAAGnD,UAAU;MAC1BwC,GAAG,CAACY,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAExE,QAAQ,EAAEA,QAAQ,CAAC;MACtC,IAAIsB,QAAQ,EAAE;QACV,IAAImD,IAAI,GAAG,KAAK,CAAC;QACjB,IAAIjD,YAAY,KAAK,QAAQ,EAAE;UAC3BiD,IAAI,GAAGb,GAAG,CAACc,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE1E,QAAQ,EAAEA,QAAQ,CAAC;QAC7D,CAAC,MACI;UACDyE,IAAI,GAAGb,GAAG,CAACe,oBAAoB,CAAC3E,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAE,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,CAAC;QAC5G;QACAyE,IAAI,CAACG,YAAY,CAAC,CAAC,EAAEnD,kBAAkB,CAAC;QACxCgD,IAAI,CAACG,YAAY,CAAC,CAAC,EAAElD,gBAAgB,CAAC;QACtCkC,GAAG,CAACW,SAAS,GAAGE,IAAI;MACxB,CAAC,MACI;QACDb,GAAG,CAACW,SAAS,GAAGlD,UAAU;MAC9B;MACA,IAAIvC,eAAe,EAAE;QACjB8E,GAAG,CAACJ,IAAI,CAAC,IAAIzE,MAAM,CAACI,YAAY,CAACO,KAAK,EAAEN,MAAM,CAAC,CAAC,CAAC;MACrD,CAAC,MACI;QACDM,KAAK,CAAChJ,OAAO,CAAC,UAAUxD,GAAG,EAAE2R,GAAG,EAAE;UAC9B3R,GAAG,CAACwD,OAAO,CAAC,UAAU6I,IAAI,EAAEuF,GAAG,EAAE;YAC7B,IAAIvF,IAAI,EAAE;cACNqE,GAAG,CAACY,QAAQ,CAACM,GAAG,GAAG1F,MAAM,EAAEyF,GAAG,GAAGzF,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;YAClD;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN;MACA,IAAI8E,SAAS,EAAE;QACXN,GAAG,CAACmB,SAAS,CAACd,KAAK,EAAE/B,UAAU,CAACtL,CAAC,EAAEsL,UAAU,CAACrL,CAAC,EAAEqL,UAAU,CAACtC,KAAK,EAAEsC,UAAU,CAACrC,MAAM,CAAC;MACzF;IACJ,CAAC;IACD9O,SAAS,CAACoR,QAAQ,CAAC;IACnBtR,SAAS,CAACsR,QAAQ,CAAC;IACnB,IAAIiB,KAAK,GAAGQ,GAAG,CAACoB,KAAK,CAAC5B,KAAK;IAC3B,OAAO,YAAY;MAAE,OAAOtS,CAAC,CAACE,QAAQ,EAAE,CACpCF,CAAC,CAAC,QAAQ,EAAEG,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE2S,GAAG,CAACoB,KAAK,CAAC,EAAE;QAAEpU,GAAG,EAAEiT,QAAQ;QAAET,KAAK,EAAEnS,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEmS,KAAK,CAAC,EAAE;UAAExD,KAAK,EAAE,EAAE,CAACrG,MAAM,CAACwI,KAAK,CAAC9O,IAAI,EAAE,IAAI,CAAC;UAAE4M,MAAM,EAAE,EAAE,CAACtG,MAAM,CAACwI,KAAK,CAAC9O,IAAI,EAAE,IAAI;QAAE,CAAC;MAAE,CAAC,CAAC,CAAC,EACpL8O,KAAK,CAACpC,aAAa,CAAC2C,GAAG,IAAIxR,CAAC,CAAC,KAAK,EAAE;QAChCF,GAAG,EAAEkT,QAAQ;QACbxB,GAAG,EAAEP,KAAK,CAACpC,aAAa,CAAC2C,GAAG;QAC5Bc,KAAK,EAAE;UAAE6B,OAAO,EAAE;QAAO,CAAC;QAC1BC,MAAM,EAAE/C;MACZ,CAAC,CAAC,CACL,CAAC;IAAE,CAAC;EACT;AACJ,CAAC,CAAC;AACF,IAAIgD,SAAS,GAAGxU,eAAe,CAAC;EAC5B0B,IAAI,EAAE,QAAQ;EACd+S,MAAM,EAAE,SAAAA,CAAA,EAAY;IAChB,IAAIhQ,EAAE,GAAG,IAAI,CAACiQ,MAAM;MAAEzD,QAAQ,GAAGxM,EAAE,CAACwM,QAAQ;MAAEhB,KAAK,GAAGxL,EAAE,CAACwL,KAAK;MAAE3N,IAAI,GAAGmC,EAAE,CAACnC,IAAI;MAAEmM,MAAM,GAAGhK,EAAE,CAACgK,MAAM;MAAEF,KAAK,GAAG9J,EAAE,CAAC8J,KAAK;MAAEkC,UAAU,GAAGhM,EAAE,CAACgM,UAAU;MAAEC,UAAU,GAAGjM,EAAE,CAACiM,UAAU;MAAE1B,aAAa,GAAGvK,EAAE,CAACuK,aAAa;MAAE2B,QAAQ,GAAGlM,EAAE,CAACkM,QAAQ;MAAEE,YAAY,GAAGpM,EAAE,CAACoM,YAAY;MAAEC,kBAAkB,GAAGrM,EAAE,CAACqM,kBAAkB;MAAEC,gBAAgB,GAAGtM,EAAE,CAACsM,gBAAgB;IAC1V,OAAO5Q,CAAC,CAAC8Q,QAAQ,KAAK,KAAK,GAAGE,SAAS,GAAG6B,YAAY,EAAE;MACpD/C,KAAK,EAAEA,KAAK;MACZ3N,IAAI,EAAEA,IAAI;MACVmM,MAAM,EAAEA,MAAM;MACdF,KAAK,EAAEA,KAAK;MACZkC,UAAU,EAAEA,UAAU;MACtBC,UAAU,EAAEA,UAAU;MACtB1B,aAAa,EAAEA,aAAa;MAC5B2B,QAAQ,EAAEA,QAAQ;MAClBE,YAAY,EAAEA,YAAY;MAC1BC,kBAAkB,EAAEA,kBAAkB;MACtCC,gBAAgB,EAAEA;IACtB,CAAC,CAAC;EACN,CAAC;EACDK,KAAK,EAAEJ;AACX,CAAC,CAAC;AAEF,SAASgC,YAAY,EAAE7B,SAAS,EAAEqD,SAAS,IAAInE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}