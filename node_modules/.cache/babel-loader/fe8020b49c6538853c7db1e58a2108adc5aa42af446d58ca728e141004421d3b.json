{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, addUnit, truthProp, numericProp, makeNumericProp } from \"../utils/index.mjs\";\nimport { BORDER_TOP } from \"../utils/constant.mjs\";\nimport { useChildren } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"grid\");\nconst gridProps = {\n  square: <PERSON>olean,\n  center: truthProp,\n  border: truthProp,\n  gutter: numericProp,\n  reverse: Boolean,\n  iconSize: numericProp,\n  direction: String,\n  clickable: Boolean,\n  columnNum: makeNumericProp(4)\n};\nconst GRID_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: gridProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      linkChildren\n    } = useChildren(GRID_KEY);\n    linkChildren({\n      props\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"style\": {\n          paddingLeft: addUnit(props.gutter)\n        },\n        \"class\": [bem(), {\n          [BORDER_TOP]: props.border && !props.gutter\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { GRID_KEY, stdin_default as default, gridProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "createNamespace", "addUnit", "truthProp", "numericProp", "makeNumericProp", "BORDER_TOP", "useChildren", "name", "bem", "gridProps", "square", "Boolean", "center", "border", "gutter", "reverse", "iconSize", "direction", "String", "clickable", "columnNum", "GRID_KEY", "Symbol", "stdin_default", "props", "setup", "slots", "linkChildren", "_a", "paddingLeft", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/grid/Grid.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, addUnit, truthProp, numericProp, makeNumericProp } from \"../utils/index.mjs\";\nimport { BORDER_TOP } from \"../utils/constant.mjs\";\nimport { useChildren } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"grid\");\nconst gridProps = {\n  square: <PERSON>olean,\n  center: truthProp,\n  border: truthProp,\n  gutter: numericProp,\n  reverse: Boolean,\n  iconSize: numericProp,\n  direction: String,\n  clickable: Boolean,\n  columnNum: makeNumericProp(4)\n};\nconst GRID_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: gridProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      linkChildren\n    } = useChildren(GRID_KEY);\n    linkChildren({\n      props\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"style\": {\n          paddingLeft: addUnit(props.gutter)\n        },\n        \"class\": [bem(), {\n          [BORDER_TOP]: props.border && !props.gutter\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  GRID_KEY,\n  stdin_default as default,\n  gridProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,eAAe,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AACtG,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,WAAW,QAAQ,WAAW;AACvC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGR,eAAe,CAAC,MAAM,CAAC;AAC3C,MAAMS,SAAS,GAAG;EAChBC,MAAM,EAAEC,OAAO;EACfC,MAAM,EAAEV,SAAS;EACjBW,MAAM,EAAEX,SAAS;EACjBY,MAAM,EAAEX,WAAW;EACnBY,OAAO,EAAEJ,OAAO;EAChBK,QAAQ,EAAEb,WAAW;EACrBc,SAAS,EAAEC,MAAM;EACjBC,SAAS,EAAER,OAAO;EAClBS,SAAS,EAAEhB,eAAe,CAAC,CAAC;AAC9B,CAAC;AACD,MAAMiB,QAAQ,GAAGC,MAAM,CAACf,IAAI,CAAC;AAC7B,IAAIgB,aAAa,GAAG1B,eAAe,CAAC;EAClCU,IAAI;EACJiB,KAAK,EAAEf,SAAS;EAChBgB,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAM;MACJC;IACF,CAAC,GAAGrB,WAAW,CAACe,QAAQ,CAAC;IACzBM,YAAY,CAAC;MACXH;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAII,EAAE;MACN,OAAO7B,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAE;UACP8B,WAAW,EAAE5B,OAAO,CAACuB,KAAK,CAACV,MAAM;QACnC,CAAC;QACD,OAAO,EAAE,CAACN,GAAG,CAAC,CAAC,EAAE;UACf,CAACH,UAAU,GAAGmB,KAAK,CAACX,MAAM,IAAI,CAACW,KAAK,CAACV;QACvC,CAAC;MACH,CAAC,EAAE,CAAC,CAACc,EAAE,GAAGF,KAAK,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,IAAI,CAACL,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEL,QAAQ,EACRE,aAAa,IAAIO,OAAO,EACxBrB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}