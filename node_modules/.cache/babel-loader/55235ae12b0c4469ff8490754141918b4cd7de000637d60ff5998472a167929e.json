{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Coupon from \"./Coupon.mjs\";\nconst Coupon = withInstall(_Coupon);\nvar stdin_default = Coupon;\nexport { Coupon, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Coupon", "Coupon", "stdin_default", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/coupon/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Coupon from \"./Coupon.mjs\";\nconst Coupon = withInstall(_Coupon);\nvar stdin_default = Coupon;\nexport {\n  Coupon,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SACEA,MAAM,EACNC,aAAa,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}