{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ActionSheet from \"./ActionSheet.mjs\";\nconst ActionSheet = withInstall(_ActionSheet);\nvar stdin_default = ActionSheet;\nimport { actionSheetProps } from \"./ActionSheet.mjs\";\nexport { ActionSheet, actionSheetProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ActionSheet", "ActionSheet", "stdin_default", "actionSheetProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/action-sheet/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ActionSheet from \"./ActionSheet.mjs\";\nconst ActionSheet = withInstall(_ActionSheet);\nvar stdin_default = ActionSheet;\nimport { actionSheetProps } from \"./ActionSheet.mjs\";\nexport {\n  ActionSheet,\n  actionSheetProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXE,gBAAgB,EAChBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}