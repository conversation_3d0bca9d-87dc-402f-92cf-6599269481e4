{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Tabs from \"./Tabs.mjs\";\nconst Tabs = withInstall(_Tabs);\nvar stdin_default = Tabs;\nimport { tabsProps } from \"./Tabs.mjs\";\nexport { Tabs, stdin_default as default, tabsProps };", "map": {"version": 3, "names": ["withInstall", "_Tabs", "Tabs", "stdin_default", "tabsProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tabs/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Tabs from \"./Tabs.mjs\";\nconst Tabs = withInstall(_Tabs);\nvar stdin_default = Tabs;\nimport { tabsProps } from \"./Tabs.mjs\";\nexport {\n  Tabs,\n  stdin_default as default,\n  tabsProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SAASE,SAAS,QAAQ,YAAY;AACtC,SACEF,IAAI,EACJC,aAAa,IAAIE,OAAO,EACxBD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}