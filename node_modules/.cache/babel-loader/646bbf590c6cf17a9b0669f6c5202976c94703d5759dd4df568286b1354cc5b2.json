{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed, Comment, defineComponent, Fragment, Text, createVNode as _createVNode } from \"vue\";\nimport { createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"space\");\nconst spaceProps = {\n  align: String,\n  direction: {\n    type: String,\n    default: \"horizontal\"\n  },\n  size: {\n    type: [Number, String, Array],\n    default: 8\n  },\n  wrap: Boolean,\n  fill: Boolean\n};\nfunction filterEmpty(children = []) {\n  const nodes = [];\n  children.forEach(child => {\n    if (Array.isArray(child)) {\n      nodes.push(...child);\n    } else if (child.type === Fragment) {\n      nodes.push(...filterEmpty(child.children));\n    } else {\n      nodes.push(child);\n    }\n  });\n  return nodes.filter(c => {\n    var _a;\n    return !(c && (c.type === Comment || c.type === Fragment && ((_a = c.children) == null ? void 0 : _a.length) === 0 || c.type === Text && c.children.trim() === \"\"));\n  });\n}\nvar stdin_default = defineComponent({\n  name,\n  props: spaceProps,\n  setup(props, {\n    slots\n  }) {\n    const mergedAlign = computed(() => {\n      var _a;\n      return (_a = props.align) != null ? _a : props.direction === \"horizontal\" ? \"center\" : \"\";\n    });\n    const getMargin = size => {\n      if (typeof size === \"number\") {\n        return size + \"px\";\n      }\n      return size;\n    };\n    const getMarginStyle = isLast => {\n      const style = {};\n      const marginRight = `${getMargin(Array.isArray(props.size) ? props.size[0] : props.size)}`;\n      const marginBottom = `${getMargin(Array.isArray(props.size) ? props.size[1] : props.size)}`;\n      if (isLast) {\n        return props.wrap ? {\n          marginBottom\n        } : {};\n      }\n      if (props.direction === \"horizontal\") {\n        style.marginRight = marginRight;\n      }\n      if (props.direction === \"vertical\" || props.wrap) {\n        style.marginBottom = marginBottom;\n      }\n      return style;\n    };\n    return () => {\n      var _a;\n      const children = filterEmpty((_a = slots.default) == null ? void 0 : _a.call(slots));\n      return _createVNode(\"div\", {\n        \"class\": [bem({\n          [props.direction]: props.direction,\n          [`align-${mergedAlign.value}`]: mergedAlign.value,\n          wrap: props.wrap,\n          fill: props.fill\n        })]\n      }, [children.map((c, i) => _createVNode(\"div\", {\n        \"key\": `item-${i}`,\n        \"class\": `${name}-item`,\n        \"style\": getMarginStyle(i === children.length - 1)\n      }, [c]))]);\n    };\n  }\n});\nexport { stdin_default as default, spaceProps };", "map": {"version": 3, "names": ["computed", "Comment", "defineComponent", "Fragment", "Text", "createVNode", "_createVNode", "createNamespace", "name", "bem", "spaceProps", "align", "String", "direction", "type", "default", "size", "Number", "Array", "wrap", "Boolean", "fill", "filterEmpty", "children", "nodes", "for<PERSON>ach", "child", "isArray", "push", "filter", "c", "_a", "length", "trim", "stdin_default", "props", "setup", "slots", "mergedAlign", "<PERSON><PERSON><PERSON><PERSON>", "getMarginStyle", "isLast", "style", "marginRight", "marginBottom", "call", "value", "map", "i"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/space/Space.mjs"], "sourcesContent": ["import { computed, Comment, defineComponent, Fragment, Text, createVNode as _createVNode } from \"vue\";\nimport { createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"space\");\nconst spaceProps = {\n  align: String,\n  direction: {\n    type: String,\n    default: \"horizontal\"\n  },\n  size: {\n    type: [Number, String, Array],\n    default: 8\n  },\n  wrap: <PERSON><PERSON><PERSON>,\n  fill: <PERSON>ole<PERSON>\n};\nfunction filterEmpty(children = []) {\n  const nodes = [];\n  children.forEach((child) => {\n    if (Array.isArray(child)) {\n      nodes.push(...child);\n    } else if (child.type === Fragment) {\n      nodes.push(...filterEmpty(child.children));\n    } else {\n      nodes.push(child);\n    }\n  });\n  return nodes.filter((c) => {\n    var _a;\n    return !(c && (c.type === Comment || c.type === Fragment && ((_a = c.children) == null ? void 0 : _a.length) === 0 || c.type === Text && c.children.trim() === \"\"));\n  });\n}\nvar stdin_default = defineComponent({\n  name,\n  props: spaceProps,\n  setup(props, {\n    slots\n  }) {\n    const mergedAlign = computed(() => {\n      var _a;\n      return (_a = props.align) != null ? _a : props.direction === \"horizontal\" ? \"center\" : \"\";\n    });\n    const getMargin = (size) => {\n      if (typeof size === \"number\") {\n        return size + \"px\";\n      }\n      return size;\n    };\n    const getMarginStyle = (isLast) => {\n      const style = {};\n      const marginRight = `${getMargin(Array.isArray(props.size) ? props.size[0] : props.size)}`;\n      const marginBottom = `${getMargin(Array.isArray(props.size) ? props.size[1] : props.size)}`;\n      if (isLast) {\n        return props.wrap ? {\n          marginBottom\n        } : {};\n      }\n      if (props.direction === \"horizontal\") {\n        style.marginRight = marginRight;\n      }\n      if (props.direction === \"vertical\" || props.wrap) {\n        style.marginBottom = marginBottom;\n      }\n      return style;\n    };\n    return () => {\n      var _a;\n      const children = filterEmpty((_a = slots.default) == null ? void 0 : _a.call(slots));\n      return _createVNode(\"div\", {\n        \"class\": [bem({\n          [props.direction]: props.direction,\n          [`align-${mergedAlign.value}`]: mergedAlign.value,\n          wrap: props.wrap,\n          fill: props.fill\n        })]\n      }, [children.map((c, i) => _createVNode(\"div\", {\n        \"key\": `item-${i}`,\n        \"class\": `${name}-item`,\n        \"style\": getMarginStyle(i === children.length - 1)\n      }, [c]))]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  spaceProps\n};\n"], "mappings": ";;;;AAAA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACrG,SAASC,eAAe,QAAQ,oBAAoB;AACpD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGF,eAAe,CAAC,OAAO,CAAC;AAC5C,MAAMG,UAAU,GAAG;EACjBC,KAAK,EAAEC,MAAM;EACbC,SAAS,EAAE;IACTC,IAAI,EAAEF,MAAM;IACZG,OAAO,EAAE;EACX,CAAC;EACDC,IAAI,EAAE;IACJF,IAAI,EAAE,CAACG,MAAM,EAAEL,MAAM,EAAEM,KAAK,CAAC;IAC7BH,OAAO,EAAE;EACX,CAAC;EACDI,IAAI,EAAEC,OAAO;EACbC,IAAI,EAAED;AACR,CAAC;AACD,SAASE,WAAWA,CAACC,QAAQ,GAAG,EAAE,EAAE;EAClC,MAAMC,KAAK,GAAG,EAAE;EAChBD,QAAQ,CAACE,OAAO,CAAEC,KAAK,IAAK;IAC1B,IAAIR,KAAK,CAACS,OAAO,CAACD,KAAK,CAAC,EAAE;MACxBF,KAAK,CAACI,IAAI,CAAC,GAAGF,KAAK,CAAC;IACtB,CAAC,MAAM,IAAIA,KAAK,CAACZ,IAAI,KAAKX,QAAQ,EAAE;MAClCqB,KAAK,CAACI,IAAI,CAAC,GAAGN,WAAW,CAACI,KAAK,CAACH,QAAQ,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLC,KAAK,CAACI,IAAI,CAACF,KAAK,CAAC;IACnB;EACF,CAAC,CAAC;EACF,OAAOF,KAAK,CAACK,MAAM,CAAEC,CAAC,IAAK;IACzB,IAAIC,EAAE;IACN,OAAO,EAAED,CAAC,KAAKA,CAAC,CAAChB,IAAI,KAAKb,OAAO,IAAI6B,CAAC,CAAChB,IAAI,KAAKX,QAAQ,IAAI,CAAC,CAAC4B,EAAE,GAAGD,CAAC,CAACP,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGQ,EAAE,CAACC,MAAM,MAAM,CAAC,IAAIF,CAAC,CAAChB,IAAI,KAAKV,IAAI,IAAI0B,CAAC,CAACP,QAAQ,CAACU,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;EACrK,CAAC,CAAC;AACJ;AACA,IAAIC,aAAa,GAAGhC,eAAe,CAAC;EAClCM,IAAI;EACJ2B,KAAK,EAAEzB,UAAU;EACjB0B,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,WAAW,GAAGtC,QAAQ,CAAC,MAAM;MACjC,IAAI+B,EAAE;MACN,OAAO,CAACA,EAAE,GAAGI,KAAK,CAACxB,KAAK,KAAK,IAAI,GAAGoB,EAAE,GAAGI,KAAK,CAACtB,SAAS,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE;IAC3F,CAAC,CAAC;IACF,MAAM0B,SAAS,GAAIvB,IAAI,IAAK;MAC1B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOA,IAAI,GAAG,IAAI;MACpB;MACA,OAAOA,IAAI;IACb,CAAC;IACD,MAAMwB,cAAc,GAAIC,MAAM,IAAK;MACjC,MAAMC,KAAK,GAAG,CAAC,CAAC;MAChB,MAAMC,WAAW,GAAG,GAAGJ,SAAS,CAACrB,KAAK,CAACS,OAAO,CAACQ,KAAK,CAACnB,IAAI,CAAC,GAAGmB,KAAK,CAACnB,IAAI,CAAC,CAAC,CAAC,GAAGmB,KAAK,CAACnB,IAAI,CAAC,EAAE;MAC1F,MAAM4B,YAAY,GAAG,GAAGL,SAAS,CAACrB,KAAK,CAACS,OAAO,CAACQ,KAAK,CAACnB,IAAI,CAAC,GAAGmB,KAAK,CAACnB,IAAI,CAAC,CAAC,CAAC,GAAGmB,KAAK,CAACnB,IAAI,CAAC,EAAE;MAC3F,IAAIyB,MAAM,EAAE;QACV,OAAON,KAAK,CAAChB,IAAI,GAAG;UAClByB;QACF,CAAC,GAAG,CAAC,CAAC;MACR;MACA,IAAIT,KAAK,CAACtB,SAAS,KAAK,YAAY,EAAE;QACpC6B,KAAK,CAACC,WAAW,GAAGA,WAAW;MACjC;MACA,IAAIR,KAAK,CAACtB,SAAS,KAAK,UAAU,IAAIsB,KAAK,CAAChB,IAAI,EAAE;QAChDuB,KAAK,CAACE,YAAY,GAAGA,YAAY;MACnC;MACA,OAAOF,KAAK;IACd,CAAC;IACD,OAAO,MAAM;MACX,IAAIX,EAAE;MACN,MAAMR,QAAQ,GAAGD,WAAW,CAAC,CAACS,EAAE,GAAGM,KAAK,CAACtB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,EAAE,CAACc,IAAI,CAACR,KAAK,CAAC,CAAC;MACpF,OAAO/B,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAE,CAACG,GAAG,CAAC;UACZ,CAAC0B,KAAK,CAACtB,SAAS,GAAGsB,KAAK,CAACtB,SAAS;UAClC,CAAC,SAASyB,WAAW,CAACQ,KAAK,EAAE,GAAGR,WAAW,CAACQ,KAAK;UACjD3B,IAAI,EAAEgB,KAAK,CAAChB,IAAI;UAChBE,IAAI,EAAEc,KAAK,CAACd;QACd,CAAC,CAAC;MACJ,CAAC,EAAE,CAACE,QAAQ,CAACwB,GAAG,CAAC,CAACjB,CAAC,EAAEkB,CAAC,KAAK1C,YAAY,CAAC,KAAK,EAAE;QAC7C,KAAK,EAAE,QAAQ0C,CAAC,EAAE;QAClB,OAAO,EAAE,GAAGxC,IAAI,OAAO;QACvB,OAAO,EAAEgC,cAAc,CAACQ,CAAC,KAAKzB,QAAQ,CAACS,MAAM,GAAG,CAAC;MACnD,CAAC,EAAE,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEI,aAAa,IAAInB,OAAO,EACxBL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}