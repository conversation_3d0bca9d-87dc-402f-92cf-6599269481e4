{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem, t] = createNamespace(\"contact-card\");\nconst contactCardProps = {\n  tel: String,\n  name: String,\n  type: makeStringProp(\"add\"),\n  addText: String,\n  editable: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: contactCardProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit\n  }) {\n    const onClick = event => {\n      if (props.editable) {\n        emit(\"click\", event);\n      }\n    };\n    const renderContent = () => {\n      if (props.type === \"add\") {\n        return props.addText || t(\"addContact\");\n      }\n      return [_createVNode(\"div\", null, [`${t(\"name\")}\\uFF1A${props.name}`]), _createVNode(\"div\", null, [`${t(\"tel\")}\\uFF1A${props.tel}`])];\n    };\n    return () => _createVNode(Cell, {\n      \"center\": true,\n      \"icon\": props.type === \"edit\" ? \"contact\" : \"add-square\",\n      \"class\": bem([props.type]),\n      \"border\": false,\n      \"isLink\": props.editable,\n      \"titleClass\": bem(\"title\"),\n      \"onClick\": onClick\n    }, {\n      title: renderContent\n    });\n  }\n});\nexport { contactCardProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "truthProp", "makeStringProp", "createNamespace", "Cell", "name", "bem", "t", "contactCardProps", "tel", "String", "type", "addText", "editable", "stdin_default", "props", "emits", "setup", "emit", "onClick", "event", "renderContent", "title", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/contact-card/ContactCard.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem, t] = createNamespace(\"contact-card\");\nconst contactCardProps = {\n  tel: String,\n  name: String,\n  type: makeStringProp(\"add\"),\n  addText: String,\n  editable: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: contactCardProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit\n  }) {\n    const onClick = (event) => {\n      if (props.editable) {\n        emit(\"click\", event);\n      }\n    };\n    const renderContent = () => {\n      if (props.type === \"add\") {\n        return props.addText || t(\"addContact\");\n      }\n      return [_createVNode(\"div\", null, [`${t(\"name\")}\\uFF1A${props.name}`]), _createVNode(\"div\", null, [`${t(\"tel\")}\\uFF1A${props.tel}`])];\n    };\n    return () => _createVNode(Cell, {\n      \"center\": true,\n      \"icon\": props.type === \"edit\" ? \"contact\" : \"add-square\",\n      \"class\": bem([props.type]),\n      \"border\": false,\n      \"isLink\": props.editable,\n      \"titleClass\": bem(\"title\"),\n      \"onClick\": onClick\n    }, {\n      title: renderContent\n    });\n  }\n});\nexport {\n  contactCardProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC/E,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGJ,eAAe,CAAC,cAAc,CAAC;AACtD,MAAMK,gBAAgB,GAAG;EACvBC,GAAG,EAAEC,MAAM;EACXL,IAAI,EAAEK,MAAM;EACZC,IAAI,EAAET,cAAc,CAAC,KAAK,CAAC;EAC3BU,OAAO,EAAEF,MAAM;EACfG,QAAQ,EAAEZ;AACZ,CAAC;AACD,IAAIa,aAAa,GAAGhB,eAAe,CAAC;EAClCO,IAAI;EACJU,KAAK,EAAEP,gBAAgB;EACvBQ,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAKA,CAACF,KAAK,EAAE;IACXG;EACF,CAAC,EAAE;IACD,MAAMC,OAAO,GAAIC,KAAK,IAAK;MACzB,IAAIL,KAAK,CAACF,QAAQ,EAAE;QAClBK,IAAI,CAAC,OAAO,EAAEE,KAAK,CAAC;MACtB;IACF,CAAC;IACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIN,KAAK,CAACJ,IAAI,KAAK,KAAK,EAAE;QACxB,OAAOI,KAAK,CAACH,OAAO,IAAIL,CAAC,CAAC,YAAY,CAAC;MACzC;MACA,OAAO,CAACP,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAGO,CAAC,CAAC,MAAM,CAAC,SAASQ,KAAK,CAACV,IAAI,EAAE,CAAC,CAAC,EAAEL,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,GAAGO,CAAC,CAAC,KAAK,CAAC,SAASQ,KAAK,CAACN,GAAG,EAAE,CAAC,CAAC,CAAC;IACvI,CAAC;IACD,OAAO,MAAMT,YAAY,CAACI,IAAI,EAAE;MAC9B,QAAQ,EAAE,IAAI;MACd,MAAM,EAAEW,KAAK,CAACJ,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,YAAY;MACxD,OAAO,EAAEL,GAAG,CAAC,CAACS,KAAK,CAACJ,IAAI,CAAC,CAAC;MAC1B,QAAQ,EAAE,KAAK;MACf,QAAQ,EAAEI,KAAK,CAACF,QAAQ;MACxB,YAAY,EAAEP,GAAG,CAAC,OAAO,CAAC;MAC1B,SAAS,EAAEa;IACb,CAAC,EAAE;MACDG,KAAK,EAAED;IACT,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AACF,SACEb,gBAAgB,EAChBM,aAAa,IAAIS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}