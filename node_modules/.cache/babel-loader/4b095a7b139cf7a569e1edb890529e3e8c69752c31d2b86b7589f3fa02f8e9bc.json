{"ast": null, "code": "import { defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { pick, extend, numericProp, unknownProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { popupSharedProps } from \"../popup/shared.mjs\";\nconst [name, bem] = createNamespace(\"notify\");\nconst popupInheritProps = [\"lockScroll\", \"position\", \"show\", \"teleport\", \"zIndex\"];\nconst notifyProps = extend({}, popupSharedProps, {\n  type: makeStringProp(\"danger\"),\n  color: String,\n  message: numericProp,\n  position: makeStringProp(\"top\"),\n  className: unknownProp,\n  background: String,\n  lockScroll: Boolean\n});\nvar stdin_default = defineComponent({\n  name,\n  props: notifyProps,\n  emits: [\"update:show\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const updateShow = show => emit(\"update:show\", show);\n    return () => _createVNode(Popup, _mergeProps({\n      \"class\": [bem([props.type]), props.className],\n      \"style\": {\n        color: props.color,\n        background: props.background\n      },\n      \"overlay\": false,\n      \"duration\": 0.2,\n      \"onUpdate:show\": updateShow\n    }, pick(props, popupInheritProps)), {\n      default: () => [slots.default ? slots.default() : props.message]\n    });\n  }\n});\nexport { stdin_default as default, notifyProps };", "map": {"version": 3, "names": ["defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "pick", "extend", "numericProp", "unknownProp", "makeStringProp", "createNamespace", "Popup", "popupSharedProps", "name", "bem", "popupInheritProps", "notifyProps", "type", "color", "String", "message", "position", "className", "background", "lockScroll", "Boolean", "stdin_default", "props", "emits", "setup", "emit", "slots", "updateShow", "show", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/notify/Notify.mjs"], "sourcesContent": ["import { defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { pick, extend, numericProp, unknownProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { popupSharedProps } from \"../popup/shared.mjs\";\nconst [name, bem] = createNamespace(\"notify\");\nconst popupInheritProps = [\"lockScroll\", \"position\", \"show\", \"teleport\", \"zIndex\"];\nconst notifyProps = extend({}, popupSharedProps, {\n  type: makeStringProp(\"danger\"),\n  color: String,\n  message: numericProp,\n  position: makeStringProp(\"top\"),\n  className: unknownProp,\n  background: String,\n  lockScroll: Boolean\n});\nvar stdin_default = defineComponent({\n  name,\n  props: notifyProps,\n  emits: [\"update:show\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const updateShow = (show) => emit(\"update:show\", show);\n    return () => _createVNode(Popup, _mergeProps({\n      \"class\": [bem([props.type]), props.className],\n      \"style\": {\n        color: props.color,\n        background: props.background\n      },\n      \"overlay\": false,\n      \"duration\": 0.2,\n      \"onUpdate:show\": updateShow\n    }, pick(props, popupInheritProps)), {\n      default: () => [slots.default ? slots.default() : props.message]\n    });\n  }\n});\nexport {\n  stdin_default as default,\n  notifyProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC7F,SAASC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC5G,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,QAAQ,CAAC;AAC7C,MAAMK,iBAAiB,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;AAClF,MAAMC,WAAW,GAAGV,MAAM,CAAC,CAAC,CAAC,EAAEM,gBAAgB,EAAE;EAC/CK,IAAI,EAAER,cAAc,CAAC,QAAQ,CAAC;EAC9BS,KAAK,EAAEC,MAAM;EACbC,OAAO,EAAEb,WAAW;EACpBc,QAAQ,EAAEZ,cAAc,CAAC,KAAK,CAAC;EAC/Ba,SAAS,EAAEd,WAAW;EACtBe,UAAU,EAAEJ,MAAM;EAClBK,UAAU,EAAEC;AACd,CAAC,CAAC;AACF,IAAIC,aAAa,GAAG1B,eAAe,CAAC;EAClCa,IAAI;EACJc,KAAK,EAAEX,WAAW;EAClBY,KAAK,EAAE,CAAC,aAAa,CAAC;EACtBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,UAAU,GAAIC,IAAI,IAAKH,IAAI,CAAC,aAAa,EAAEG,IAAI,CAAC;IACtD,OAAO,MAAM7B,YAAY,CAACO,KAAK,EAAET,WAAW,CAAC;MAC3C,OAAO,EAAE,CAACY,GAAG,CAAC,CAACa,KAAK,CAACV,IAAI,CAAC,CAAC,EAAEU,KAAK,CAACL,SAAS,CAAC;MAC7C,OAAO,EAAE;QACPJ,KAAK,EAAES,KAAK,CAACT,KAAK;QAClBK,UAAU,EAAEI,KAAK,CAACJ;MACpB,CAAC;MACD,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,GAAG;MACf,eAAe,EAAES;IACnB,CAAC,EAAE3B,IAAI,CAACsB,KAAK,EAAEZ,iBAAiB,CAAC,CAAC,EAAE;MAClCmB,OAAO,EAAEA,CAAA,KAAM,CAACH,KAAK,CAACG,OAAO,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGP,KAAK,CAACP,OAAO;IACjE,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AACF,SACEM,aAAa,IAAIQ,OAAO,EACxBlB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}