{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Notify from \"./Notify.mjs\";\nconst Notify = withInstall(_Notify);\nvar stdin_default = Notify;\nimport { notifyProps } from \"./Notify.mjs\";\nimport { showNotify, closeNotify, setNotifyDefaultOptions, resetNotifyDefaultOptions } from \"./function-call.mjs\";\nexport { Notify, closeNotify, stdin_default as default, notifyProps, resetNotifyDefaultOptions, setNotifyDefaultOptions, showNotify };", "map": {"version": 3, "names": ["withInstall", "_Notify", "Notify", "stdin_default", "notifyProps", "showNotify", "closeNotify", "setNotifyDefaultOptions", "resetNotifyDefaultOptions", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/notify/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Notify from \"./Notify.mjs\";\nconst Notify = withInstall(_Notify);\nvar stdin_default = Notify;\nimport { notifyProps } from \"./Notify.mjs\";\nimport {\n  showNotify,\n  closeNotify,\n  setNotifyDefaultOptions,\n  resetNotifyDefaultOptions\n} from \"./function-call.mjs\";\nexport {\n  Notify,\n  closeNotify,\n  stdin_default as default,\n  notifyProps,\n  resetNotifyDefaultOptions,\n  setNotifyDefaultOptions,\n  showNotify\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEC,UAAU,EACVC,WAAW,EACXC,uBAAuB,EACvBC,yBAAyB,QACpB,qBAAqB;AAC5B,SACEN,MAAM,EACNI,WAAW,EACXH,aAAa,IAAIM,OAAO,EACxBL,WAAW,EACXI,yBAAyB,EACzBD,uBAAuB,EACvBF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}