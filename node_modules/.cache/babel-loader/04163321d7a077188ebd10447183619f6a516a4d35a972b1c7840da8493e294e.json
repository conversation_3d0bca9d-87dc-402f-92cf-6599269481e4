{"ast": null, "code": "import { Teleport, computed, defineComponent, nextTick, onMounted, ref, watch, onActivated, onDeactivated, createVNode as _createVNode, vShow as _vShow, mergeProps as _mergeProps, withDirectives as _withDirectives } from \"vue\";\nimport { pick, addUnit, closest, createNamespace, isObject, makeStringProp, windowWidth, windowHeight } from \"../utils/index.mjs\";\nimport { useRect, useEventListener } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport Icon from \"../icon/index.mjs\";\nconst floatingBubbleProps = {\n  gap: {\n    type: [Number, Object],\n    default: 24\n  },\n  icon: String,\n  axis: makeStringProp(\"y\"),\n  magnetic: String,\n  offset: Object,\n  teleport: {\n    type: [String, Object],\n    default: \"body\"\n  }\n};\nconst [name, bem] = createNamespace(\"floating-bubble\");\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: floatingBubbleProps,\n  emits: [\"click\", \"update:offset\", \"offsetChange\"],\n  setup(props, {\n    slots,\n    emit,\n    attrs\n  }) {\n    const rootRef = ref();\n    const state = ref({\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    });\n    const gapX = computed(() => isObject(props.gap) ? props.gap.x : props.gap);\n    const gapY = computed(() => isObject(props.gap) ? props.gap.y : props.gap);\n    const boundary = computed(() => ({\n      top: gapY.value,\n      right: windowWidth.value - state.value.width - gapX.value,\n      bottom: windowHeight.value - state.value.height - gapY.value,\n      left: gapX.value\n    }));\n    const dragging = ref(false);\n    let initialized = false;\n    const rootStyle = computed(() => {\n      const style = {};\n      const x = addUnit(state.value.x);\n      const y = addUnit(state.value.y);\n      style.transform = `translate3d(${x}, ${y}, 0)`;\n      if (dragging.value || !initialized) {\n        style.transition = \"none\";\n      }\n      return style;\n    });\n    const updateState = () => {\n      if (!show.value) return;\n      const {\n        width,\n        height\n      } = useRect(rootRef.value);\n      const {\n        offset\n      } = props;\n      state.value = {\n        x: offset ? offset.x : windowWidth.value - width - gapX.value,\n        y: offset ? offset.y : windowHeight.value - height - gapY.value,\n        width,\n        height\n      };\n    };\n    const touch = useTouch();\n    let prevX = 0;\n    let prevY = 0;\n    const onTouchStart = e => {\n      touch.start(e);\n      dragging.value = true;\n      prevX = state.value.x;\n      prevY = state.value.y;\n    };\n    const onTouchMove = e => {\n      e.preventDefault();\n      touch.move(e);\n      if (props.axis === \"lock\") return;\n      if (!touch.isTap.value) {\n        if (props.axis === \"x\" || props.axis === \"xy\") {\n          let nextX = prevX + touch.deltaX.value;\n          if (nextX < boundary.value.left) nextX = boundary.value.left;\n          if (nextX > boundary.value.right) nextX = boundary.value.right;\n          state.value.x = nextX;\n        }\n        if (props.axis === \"y\" || props.axis === \"xy\") {\n          let nextY = prevY + touch.deltaY.value;\n          if (nextY < boundary.value.top) nextY = boundary.value.top;\n          if (nextY > boundary.value.bottom) nextY = boundary.value.bottom;\n          state.value.y = nextY;\n        }\n        const offset = pick(state.value, [\"x\", \"y\"]);\n        emit(\"update:offset\", offset);\n      }\n    };\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: rootRef\n    });\n    const onTouchEnd = () => {\n      dragging.value = false;\n      nextTick(() => {\n        if (props.magnetic === \"x\") {\n          const nextX = closest([boundary.value.left, boundary.value.right], state.value.x);\n          state.value.x = nextX;\n        }\n        if (props.magnetic === \"y\") {\n          const nextY = closest([boundary.value.top, boundary.value.bottom], state.value.y);\n          state.value.y = nextY;\n        }\n        if (!touch.isTap.value) {\n          const offset = pick(state.value, [\"x\", \"y\"]);\n          emit(\"update:offset\", offset);\n          if (prevX !== offset.x || prevY !== offset.y) {\n            emit(\"offsetChange\", offset);\n          }\n        }\n      });\n    };\n    const onClick = e => {\n      if (touch.isTap.value) emit(\"click\", e);else e.stopPropagation();\n    };\n    onMounted(() => {\n      updateState();\n      nextTick(() => {\n        initialized = true;\n      });\n    });\n    watch([windowWidth, windowHeight, gapX, gapY, () => props.offset], updateState, {\n      deep: true\n    });\n    const show = ref(true);\n    onActivated(() => {\n      show.value = true;\n    });\n    onDeactivated(() => {\n      if (props.teleport) {\n        show.value = false;\n      }\n    });\n    return () => {\n      const Content = _withDirectives(_createVNode(\"div\", _mergeProps({\n        \"class\": bem(),\n        \"ref\": rootRef,\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd,\n        \"onClickCapture\": onClick,\n        \"style\": rootStyle.value\n      }, attrs), [slots.default ? slots.default() : _createVNode(Icon, {\n        \"name\": props.icon,\n        \"class\": bem(\"icon\")\n      }, null)]), [[_vShow, show.value]]);\n      return props.teleport ? _createVNode(Teleport, {\n        \"to\": props.teleport\n      }, {\n        default: () => [Content]\n      }) : Content;\n    };\n  }\n});\nexport { stdin_default as default, floatingBubbleProps };", "map": {"version": 3, "names": ["Teleport", "computed", "defineComponent", "nextTick", "onMounted", "ref", "watch", "onActivated", "onDeactivated", "createVNode", "_createVNode", "vShow", "_vShow", "mergeProps", "_mergeProps", "withDirectives", "_withDirectives", "pick", "addUnit", "closest", "createNamespace", "isObject", "makeStringProp", "windowWidth", "windowHeight", "useRect", "useEventListener", "useTouch", "Icon", "floatingBubbleProps", "gap", "type", "Number", "Object", "default", "icon", "String", "axis", "magnetic", "offset", "teleport", "name", "bem", "stdin_default", "inheritAttrs", "props", "emits", "setup", "slots", "emit", "attrs", "rootRef", "state", "x", "y", "width", "height", "gapX", "gapY", "boundary", "top", "value", "right", "bottom", "left", "dragging", "initialized", "rootStyle", "style", "transform", "transition", "updateState", "show", "touch", "prevX", "prevY", "onTouchStart", "e", "start", "onTouchMove", "preventDefault", "move", "isTap", "nextX", "deltaX", "nextY", "deltaY", "target", "onTouchEnd", "onClick", "stopPropagation", "deep", "Content"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/floating-bubble/FloatingBubble.mjs"], "sourcesContent": ["import { Teleport, computed, defineComponent, nextTick, onMounted, ref, watch, onActivated, onDeactivated, createVNode as _createVNode, vShow as _vShow, mergeProps as _mergeProps, withDirectives as _withDirectives } from \"vue\";\nimport { pick, addUnit, closest, createNamespace, isObject, makeStringProp, windowWidth, windowHeight } from \"../utils/index.mjs\";\nimport { useRect, useEventListener } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport Icon from \"../icon/index.mjs\";\nconst floatingBubbleProps = {\n  gap: {\n    type: [Number, Object],\n    default: 24\n  },\n  icon: String,\n  axis: makeStringProp(\"y\"),\n  magnetic: String,\n  offset: Object,\n  teleport: {\n    type: [String, Object],\n    default: \"body\"\n  }\n};\nconst [name, bem] = createNamespace(\"floating-bubble\");\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: floatingBubbleProps,\n  emits: [\"click\", \"update:offset\", \"offsetChange\"],\n  setup(props, {\n    slots,\n    emit,\n    attrs\n  }) {\n    const rootRef = ref();\n    const state = ref({\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    });\n    const gapX = computed(() => isObject(props.gap) ? props.gap.x : props.gap);\n    const gapY = computed(() => isObject(props.gap) ? props.gap.y : props.gap);\n    const boundary = computed(() => ({\n      top: gapY.value,\n      right: windowWidth.value - state.value.width - gapX.value,\n      bottom: windowHeight.value - state.value.height - gapY.value,\n      left: gapX.value\n    }));\n    const dragging = ref(false);\n    let initialized = false;\n    const rootStyle = computed(() => {\n      const style = {};\n      const x = addUnit(state.value.x);\n      const y = addUnit(state.value.y);\n      style.transform = `translate3d(${x}, ${y}, 0)`;\n      if (dragging.value || !initialized) {\n        style.transition = \"none\";\n      }\n      return style;\n    });\n    const updateState = () => {\n      if (!show.value) return;\n      const {\n        width,\n        height\n      } = useRect(rootRef.value);\n      const {\n        offset\n      } = props;\n      state.value = {\n        x: offset ? offset.x : windowWidth.value - width - gapX.value,\n        y: offset ? offset.y : windowHeight.value - height - gapY.value,\n        width,\n        height\n      };\n    };\n    const touch = useTouch();\n    let prevX = 0;\n    let prevY = 0;\n    const onTouchStart = (e) => {\n      touch.start(e);\n      dragging.value = true;\n      prevX = state.value.x;\n      prevY = state.value.y;\n    };\n    const onTouchMove = (e) => {\n      e.preventDefault();\n      touch.move(e);\n      if (props.axis === \"lock\") return;\n      if (!touch.isTap.value) {\n        if (props.axis === \"x\" || props.axis === \"xy\") {\n          let nextX = prevX + touch.deltaX.value;\n          if (nextX < boundary.value.left) nextX = boundary.value.left;\n          if (nextX > boundary.value.right) nextX = boundary.value.right;\n          state.value.x = nextX;\n        }\n        if (props.axis === \"y\" || props.axis === \"xy\") {\n          let nextY = prevY + touch.deltaY.value;\n          if (nextY < boundary.value.top) nextY = boundary.value.top;\n          if (nextY > boundary.value.bottom) nextY = boundary.value.bottom;\n          state.value.y = nextY;\n        }\n        const offset = pick(state.value, [\"x\", \"y\"]);\n        emit(\"update:offset\", offset);\n      }\n    };\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: rootRef\n    });\n    const onTouchEnd = () => {\n      dragging.value = false;\n      nextTick(() => {\n        if (props.magnetic === \"x\") {\n          const nextX = closest([boundary.value.left, boundary.value.right], state.value.x);\n          state.value.x = nextX;\n        }\n        if (props.magnetic === \"y\") {\n          const nextY = closest([boundary.value.top, boundary.value.bottom], state.value.y);\n          state.value.y = nextY;\n        }\n        if (!touch.isTap.value) {\n          const offset = pick(state.value, [\"x\", \"y\"]);\n          emit(\"update:offset\", offset);\n          if (prevX !== offset.x || prevY !== offset.y) {\n            emit(\"offsetChange\", offset);\n          }\n        }\n      });\n    };\n    const onClick = (e) => {\n      if (touch.isTap.value) emit(\"click\", e);\n      else e.stopPropagation();\n    };\n    onMounted(() => {\n      updateState();\n      nextTick(() => {\n        initialized = true;\n      });\n    });\n    watch([windowWidth, windowHeight, gapX, gapY, () => props.offset], updateState, {\n      deep: true\n    });\n    const show = ref(true);\n    onActivated(() => {\n      show.value = true;\n    });\n    onDeactivated(() => {\n      if (props.teleport) {\n        show.value = false;\n      }\n    });\n    return () => {\n      const Content = _withDirectives(_createVNode(\"div\", _mergeProps({\n        \"class\": bem(),\n        \"ref\": rootRef,\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd,\n        \"onClickCapture\": onClick,\n        \"style\": rootStyle.value\n      }, attrs), [slots.default ? slots.default() : _createVNode(Icon, {\n        \"name\": props.icon,\n        \"class\": bem(\"icon\")\n      }, null)]), [[_vShow, show.value]]);\n      return props.teleport ? _createVNode(Teleport, {\n        \"to\": props.teleport\n      }, {\n        default: () => [Content]\n      }) : Content;\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  floatingBubbleProps\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,IAAIC,YAAY,EAAEC,KAAK,IAAIC,MAAM,EAAEC,UAAU,IAAIC,WAAW,EAAEC,cAAc,IAAIC,eAAe,QAAQ,KAAK;AAClO,SAASC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,WAAW,EAAEC,YAAY,QAAQ,oBAAoB;AACjI,SAASC,OAAO,EAAEC,gBAAgB,QAAQ,WAAW;AACrD,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,OAAOC,IAAI,MAAM,mBAAmB;AACpC,MAAMC,mBAAmB,GAAG;EAC1BC,GAAG,EAAE;IACHC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;IACtBC,OAAO,EAAE;EACX,CAAC;EACDC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAEf,cAAc,CAAC,GAAG,CAAC;EACzBgB,QAAQ,EAAEF,MAAM;EAChBG,MAAM,EAAEN,MAAM;EACdO,QAAQ,EAAE;IACRT,IAAI,EAAE,CAACK,MAAM,EAAEH,MAAM,CAAC;IACtBC,OAAO,EAAE;EACX;AACF,CAAC;AACD,MAAM,CAACO,IAAI,EAAEC,GAAG,CAAC,GAAGtB,eAAe,CAAC,iBAAiB,CAAC;AACtD,IAAIuB,aAAa,GAAGzC,eAAe,CAAC;EAClCuC,IAAI;EACJG,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAEhB,mBAAmB;EAC1BiB,KAAK,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,cAAc,CAAC;EACjDC,KAAKA,CAACF,KAAK,EAAE;IACXG,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,OAAO,GAAG9C,GAAG,CAAC,CAAC;IACrB,MAAM+C,KAAK,GAAG/C,GAAG,CAAC;MAChBgD,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC,CAAC;IACF,MAAMC,IAAI,GAAGxD,QAAQ,CAAC,MAAMoB,QAAQ,CAACwB,KAAK,CAACf,GAAG,CAAC,GAAGe,KAAK,CAACf,GAAG,CAACuB,CAAC,GAAGR,KAAK,CAACf,GAAG,CAAC;IAC1E,MAAM4B,IAAI,GAAGzD,QAAQ,CAAC,MAAMoB,QAAQ,CAACwB,KAAK,CAACf,GAAG,CAAC,GAAGe,KAAK,CAACf,GAAG,CAACwB,CAAC,GAAGT,KAAK,CAACf,GAAG,CAAC;IAC1E,MAAM6B,QAAQ,GAAG1D,QAAQ,CAAC,OAAO;MAC/B2D,GAAG,EAAEF,IAAI,CAACG,KAAK;MACfC,KAAK,EAAEvC,WAAW,CAACsC,KAAK,GAAGT,KAAK,CAACS,KAAK,CAACN,KAAK,GAAGE,IAAI,CAACI,KAAK;MACzDE,MAAM,EAAEvC,YAAY,CAACqC,KAAK,GAAGT,KAAK,CAACS,KAAK,CAACL,MAAM,GAAGE,IAAI,CAACG,KAAK;MAC5DG,IAAI,EAAEP,IAAI,CAACI;IACb,CAAC,CAAC,CAAC;IACH,MAAMI,QAAQ,GAAG5D,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAI6D,WAAW,GAAG,KAAK;IACvB,MAAMC,SAAS,GAAGlE,QAAQ,CAAC,MAAM;MAC/B,MAAMmE,KAAK,GAAG,CAAC,CAAC;MAChB,MAAMf,CAAC,GAAGnC,OAAO,CAACkC,KAAK,CAACS,KAAK,CAACR,CAAC,CAAC;MAChC,MAAMC,CAAC,GAAGpC,OAAO,CAACkC,KAAK,CAACS,KAAK,CAACP,CAAC,CAAC;MAChCc,KAAK,CAACC,SAAS,GAAG,eAAehB,CAAC,KAAKC,CAAC,MAAM;MAC9C,IAAIW,QAAQ,CAACJ,KAAK,IAAI,CAACK,WAAW,EAAE;QAClCE,KAAK,CAACE,UAAU,GAAG,MAAM;MAC3B;MACA,OAAOF,KAAK;IACd,CAAC,CAAC;IACF,MAAMG,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAI,CAACC,IAAI,CAACX,KAAK,EAAE;MACjB,MAAM;QACJN,KAAK;QACLC;MACF,CAAC,GAAG/B,OAAO,CAAC0B,OAAO,CAACU,KAAK,CAAC;MAC1B,MAAM;QACJtB;MACF,CAAC,GAAGM,KAAK;MACTO,KAAK,CAACS,KAAK,GAAG;QACZR,CAAC,EAAEd,MAAM,GAAGA,MAAM,CAACc,CAAC,GAAG9B,WAAW,CAACsC,KAAK,GAAGN,KAAK,GAAGE,IAAI,CAACI,KAAK;QAC7DP,CAAC,EAAEf,MAAM,GAAGA,MAAM,CAACe,CAAC,GAAG9B,YAAY,CAACqC,KAAK,GAAGL,MAAM,GAAGE,IAAI,CAACG,KAAK;QAC/DN,KAAK;QACLC;MACF,CAAC;IACH,CAAC;IACD,MAAMiB,KAAK,GAAG9C,QAAQ,CAAC,CAAC;IACxB,IAAI+C,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb,MAAMC,YAAY,GAAIC,CAAC,IAAK;MAC1BJ,KAAK,CAACK,KAAK,CAACD,CAAC,CAAC;MACdZ,QAAQ,CAACJ,KAAK,GAAG,IAAI;MACrBa,KAAK,GAAGtB,KAAK,CAACS,KAAK,CAACR,CAAC;MACrBsB,KAAK,GAAGvB,KAAK,CAACS,KAAK,CAACP,CAAC;IACvB,CAAC;IACD,MAAMyB,WAAW,GAAIF,CAAC,IAAK;MACzBA,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBP,KAAK,CAACQ,IAAI,CAACJ,CAAC,CAAC;MACb,IAAIhC,KAAK,CAACR,IAAI,KAAK,MAAM,EAAE;MAC3B,IAAI,CAACoC,KAAK,CAACS,KAAK,CAACrB,KAAK,EAAE;QACtB,IAAIhB,KAAK,CAACR,IAAI,KAAK,GAAG,IAAIQ,KAAK,CAACR,IAAI,KAAK,IAAI,EAAE;UAC7C,IAAI8C,KAAK,GAAGT,KAAK,GAAGD,KAAK,CAACW,MAAM,CAACvB,KAAK;UACtC,IAAIsB,KAAK,GAAGxB,QAAQ,CAACE,KAAK,CAACG,IAAI,EAAEmB,KAAK,GAAGxB,QAAQ,CAACE,KAAK,CAACG,IAAI;UAC5D,IAAImB,KAAK,GAAGxB,QAAQ,CAACE,KAAK,CAACC,KAAK,EAAEqB,KAAK,GAAGxB,QAAQ,CAACE,KAAK,CAACC,KAAK;UAC9DV,KAAK,CAACS,KAAK,CAACR,CAAC,GAAG8B,KAAK;QACvB;QACA,IAAItC,KAAK,CAACR,IAAI,KAAK,GAAG,IAAIQ,KAAK,CAACR,IAAI,KAAK,IAAI,EAAE;UAC7C,IAAIgD,KAAK,GAAGV,KAAK,GAAGF,KAAK,CAACa,MAAM,CAACzB,KAAK;UACtC,IAAIwB,KAAK,GAAG1B,QAAQ,CAACE,KAAK,CAACD,GAAG,EAAEyB,KAAK,GAAG1B,QAAQ,CAACE,KAAK,CAACD,GAAG;UAC1D,IAAIyB,KAAK,GAAG1B,QAAQ,CAACE,KAAK,CAACE,MAAM,EAAEsB,KAAK,GAAG1B,QAAQ,CAACE,KAAK,CAACE,MAAM;UAChEX,KAAK,CAACS,KAAK,CAACP,CAAC,GAAG+B,KAAK;QACvB;QACA,MAAM9C,MAAM,GAAGtB,IAAI,CAACmC,KAAK,CAACS,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5CZ,IAAI,CAAC,eAAe,EAAEV,MAAM,CAAC;MAC/B;IACF,CAAC;IACDb,gBAAgB,CAAC,WAAW,EAAEqD,WAAW,EAAE;MACzCQ,MAAM,EAAEpC;IACV,CAAC,CAAC;IACF,MAAMqC,UAAU,GAAGA,CAAA,KAAM;MACvBvB,QAAQ,CAACJ,KAAK,GAAG,KAAK;MACtB1D,QAAQ,CAAC,MAAM;QACb,IAAI0C,KAAK,CAACP,QAAQ,KAAK,GAAG,EAAE;UAC1B,MAAM6C,KAAK,GAAGhE,OAAO,CAAC,CAACwC,QAAQ,CAACE,KAAK,CAACG,IAAI,EAAEL,QAAQ,CAACE,KAAK,CAACC,KAAK,CAAC,EAAEV,KAAK,CAACS,KAAK,CAACR,CAAC,CAAC;UACjFD,KAAK,CAACS,KAAK,CAACR,CAAC,GAAG8B,KAAK;QACvB;QACA,IAAItC,KAAK,CAACP,QAAQ,KAAK,GAAG,EAAE;UAC1B,MAAM+C,KAAK,GAAGlE,OAAO,CAAC,CAACwC,QAAQ,CAACE,KAAK,CAACD,GAAG,EAAED,QAAQ,CAACE,KAAK,CAACE,MAAM,CAAC,EAAEX,KAAK,CAACS,KAAK,CAACP,CAAC,CAAC;UACjFF,KAAK,CAACS,KAAK,CAACP,CAAC,GAAG+B,KAAK;QACvB;QACA,IAAI,CAACZ,KAAK,CAACS,KAAK,CAACrB,KAAK,EAAE;UACtB,MAAMtB,MAAM,GAAGtB,IAAI,CAACmC,KAAK,CAACS,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;UAC5CZ,IAAI,CAAC,eAAe,EAAEV,MAAM,CAAC;UAC7B,IAAImC,KAAK,KAAKnC,MAAM,CAACc,CAAC,IAAIsB,KAAK,KAAKpC,MAAM,CAACe,CAAC,EAAE;YAC5CL,IAAI,CAAC,cAAc,EAAEV,MAAM,CAAC;UAC9B;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IACD,MAAMkD,OAAO,GAAIZ,CAAC,IAAK;MACrB,IAAIJ,KAAK,CAACS,KAAK,CAACrB,KAAK,EAAEZ,IAAI,CAAC,OAAO,EAAE4B,CAAC,CAAC,CAAC,KACnCA,CAAC,CAACa,eAAe,CAAC,CAAC;IAC1B,CAAC;IACDtF,SAAS,CAAC,MAAM;MACdmE,WAAW,CAAC,CAAC;MACbpE,QAAQ,CAAC,MAAM;QACb+D,WAAW,GAAG,IAAI;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF5D,KAAK,CAAC,CAACiB,WAAW,EAAEC,YAAY,EAAEiC,IAAI,EAAEC,IAAI,EAAE,MAAMb,KAAK,CAACN,MAAM,CAAC,EAAEgC,WAAW,EAAE;MAC9EoB,IAAI,EAAE;IACR,CAAC,CAAC;IACF,MAAMnB,IAAI,GAAGnE,GAAG,CAAC,IAAI,CAAC;IACtBE,WAAW,CAAC,MAAM;MAChBiE,IAAI,CAACX,KAAK,GAAG,IAAI;IACnB,CAAC,CAAC;IACFrD,aAAa,CAAC,MAAM;MAClB,IAAIqC,KAAK,CAACL,QAAQ,EAAE;QAClBgC,IAAI,CAACX,KAAK,GAAG,KAAK;MACpB;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,MAAM+B,OAAO,GAAG5E,eAAe,CAACN,YAAY,CAAC,KAAK,EAAEI,WAAW,CAAC;QAC9D,OAAO,EAAE4B,GAAG,CAAC,CAAC;QACd,KAAK,EAAES,OAAO;QACd,qBAAqB,EAAEyB,YAAY;QACnC,YAAY,EAAEY,UAAU;QACxB,eAAe,EAAEA,UAAU;QAC3B,gBAAgB,EAAEC,OAAO;QACzB,OAAO,EAAEtB,SAAS,CAACN;MACrB,CAAC,EAAEX,KAAK,CAAC,EAAE,CAACF,KAAK,CAACd,OAAO,GAAGc,KAAK,CAACd,OAAO,CAAC,CAAC,GAAGxB,YAAY,CAACkB,IAAI,EAAE;QAC/D,MAAM,EAAEiB,KAAK,CAACV,IAAI;QAClB,OAAO,EAAEO,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC9B,MAAM,EAAE4D,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;MACnC,OAAOhB,KAAK,CAACL,QAAQ,GAAG9B,YAAY,CAACV,QAAQ,EAAE;QAC7C,IAAI,EAAE6C,KAAK,CAACL;MACd,CAAC,EAAE;QACDN,OAAO,EAAEA,CAAA,KAAM,CAAC0D,OAAO;MACzB,CAAC,CAAC,GAAGA,OAAO;IACd,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEjD,aAAa,IAAIT,OAAO,EACxBL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}