{"ast": null, "code": "export const HOOK_SETUP = 'devtools-plugin:setup';\nexport const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';", "map": {"version": 3, "names": ["HOOK_SETUP", "HOOK_PLUGIN_SETTINGS_SET"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/@vue/devtools-api/lib/esm/const.js"], "sourcesContent": ["export const HOOK_SETUP = 'devtools-plugin:setup';\nexport const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAG,uBAAuB;AACjD,OAAO,MAAMC,wBAAwB,GAAG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}