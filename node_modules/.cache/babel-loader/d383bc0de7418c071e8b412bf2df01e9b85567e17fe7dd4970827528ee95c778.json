{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport axios from 'axios';\nimport QrcodeVue from 'qrcode.vue';\nexport default {\n  __name: 'Home',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n\n    // 用户信息\n    const username = ref(localStorage.getItem('username') || 'admin');\n\n    // 原有的数据和方法\n    const items = ref([]);\n    const total = ref(0);\n    const page = ref(1);\n    const pageSize = ref(10);\n    const searchName = ref('');\n    const showAddDialog = ref(false);\n    const addUsername = ref('');\n    const showQrDialog = ref(false);\n    const newUserId = ref(null);\n    const showImageDialog = ref(false);\n    const currentImage = ref('');\n    const showQrDialog2 = ref(false);\n    const currentId = ref(null);\n\n    // 检查登录状态\n    onMounted(() => {\n      const isLoggedIn = localStorage.getItem('isLoggedIn');\n      if (!isLoggedIn) {\n        router.push('/login');\n      }\n    });\n\n    // 退出登录\n    const handleLogout = () => {\n      localStorage.removeItem('isLoggedIn');\n      localStorage.removeItem('username');\n      router.push('/login');\n    };\n\n    // 原有的业务方法\n    const fetchImages = (p = page.value) => {\n      page.value = p;\n      axios.get('http://192.168.100.58:5000/api/images', {\n        params: {\n          page: page.value,\n          page_size: pageSize.value,\n          username: searchName.value\n        }\n      }).then(res => {\n        items.value = res.data.items;\n        total.value = res.data.total;\n        console.log('接口返回数据:', res.data);\n      });\n    };\n    const addUser = () => {\n      if (!addUsername.value) return;\n      axios.post('http://192.168.100.58:5000/api/add_user', {\n        username: addUsername.value\n      }).then(res => {\n        showAddDialog.value = false;\n        newUserId.value = res.data.id;\n        showQrDialog.value = true;\n        addUsername.value = '';\n        fetchImages(1);\n      });\n    };\n    const showImage = row => {\n      currentImage.value = row.image1;\n      showImageDialog.value = true;\n    };\n    const showQr = row => {\n      currentId.value = row.id;\n      showQrDialog2.value = true;\n    };\n    const resetSearch = () => {\n      searchName.value = '';\n      fetchImages(1);\n    };\n\n    // 初始化数据\n    fetchImages();\n    const __returned__ = {\n      router,\n      username,\n      items,\n      total,\n      page,\n      pageSize,\n      searchName,\n      showAddDialog,\n      addUsername,\n      showQrDialog,\n      newUserId,\n      showImageDialog,\n      currentImage,\n      showQrDialog2,\n      currentId,\n      handleLogout,\n      fetchImages,\n      addUser,\n      showImage,\n      showQr,\n      resetSearch,\n      ref,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get axios() {\n        return axios;\n      },\n      QrcodeVue\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "useRouter", "axios", "QrcodeVue", "router", "username", "localStorage", "getItem", "items", "total", "page", "pageSize", "searchName", "showAddDialog", "addUsername", "showQrDialog", "newUserId", "showImageDialog", "currentImage", "showQrDialog2", "currentId", "isLoggedIn", "push", "handleLogout", "removeItem", "fetchImages", "p", "value", "get", "params", "page_size", "then", "res", "data", "console", "log", "addUser", "post", "id", "showImage", "row", "image1", "showQr", "resetSearch"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/src/views/Home.vue"], "sourcesContent": ["<!-- eslint-disable vue/multi-word-component-names -->\n<template>\n  <div>\n    <!-- 系统标题区域 -->\n    <div class=\"system-header\">\n      <div class=\"header-content\">\n        <div class=\"title-section\">\n          <h1 class=\"system-title\">\n            <span class=\"title-icon\">🏥</span>\n            脊柱侧弯筛查系统\n          </h1>\n          <p class=\"system-subtitle\">专业的脊柱健康检测与管理平台</p>\n        </div>\n        <!-- 添加退出登录按钮 -->\n        <div class=\"header-actions\">\n          <span class=\"welcome-text\">欢迎，{{ username }}</span>\n          <van-button \n            type=\"default\" \n            size=\"small\" \n            class=\"logout-btn\"\n            @click=\"handleLogout\"\n          >\n            🚪 退出登录\n          </van-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主内容容器 -->\n    <div class=\"main-container\">\n      <!-- 新增+筛选区 -->\n      <div class=\"search-section\">\n        <div class=\"search-card\">\n          <div class=\"search-header\">\n            <h3 class=\"search-title\">用户管理</h3>\n            <p class=\"search-desc\">管理筛查用户信息，查看检测结果</p>\n          </div>\n          <div class=\"search-controls\">\n            <van-button \n              type=\"primary\" \n              size=\"normal\" \n              class=\"add-btn\"\n              @click=\"showAddDialog = true\"\n            >\n              <span class=\"btn-icon\">➕</span>\n              新增用户\n            </van-button>\n            <div class=\"search-input-group\">\n              <van-field\n                v-model=\"searchName\"\n                label=\"姓名筛选\"\n                placeholder=\"请输入用户姓名\"\n                clearable\n                class=\"search-input\"\n                @keyup.enter=\"fetchImages(1)\"\n              />\n              <van-button \n                type=\"primary\" \n                size=\"normal\" \n                class=\"search-btn\"\n                @click=\"fetchImages(1)\"\n              >\n                🔍 查询\n              </van-button>\n              <van-button \n                type=\"default\" \n                size=\"normal\" \n                class=\"reset-btn\"\n                @click=\"resetSearch\"\n              >\n                🔄 重置\n              </van-button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n    <!-- 新增用户弹窗 -->\n    <van-dialog \n      v-model:show=\"showAddDialog\" \n      title=\"➕ 新增用户\" \n      show-cancel-button \n      @confirm=\"addUser\"\n      class=\"add-user-dialog\"\n    >\n      <div class=\"dialog-content\">\n        <div class=\"dialog-desc\">\n          <p>请输入用户姓名，系统将为其创建筛查档案</p>\n        </div>\n        <van-field \n          v-model=\"addUsername\" \n          label=\"用户姓名\" \n          placeholder=\"请输入真实姓名\"\n          required \n          class=\"dialog-field\"\n        />\n      </div>\n    </van-dialog>\n    \n    <!-- 新增成功后二维码弹窗 -->\n    <van-dialog \n      v-model:show=\"showQrDialog\" \n      title=\"🎉 用户创建成功\" \n      show-cancel-button\n      class=\"qr-dialog\"\n    >\n      <div class=\"qr-content\">\n        <div class=\"success-message\">\n          <p class=\"success-text\">用户档案已创建成功！</p>\n          <p class=\"qr-desc\">请保存下方二维码，用于筛查时快速识别用户</p>\n        </div>\n        <div class=\"qr-display\">\n          <div class=\"qr-card\">\n            <div class=\"qr-header\">\n              <span class=\"qr-title\">用户二维码</span>\n              <span class=\"user-id-badge\">ID: {{ newUserId }}</span>\n            </div>\n            <div class=\"qr-code-wrapper\">\n              <qrcode-vue :value=\"String(newUserId)\" :size=\"180\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </van-dialog>\n\n      <!-- 用户列表 -->\n      <div class=\"table-section\">\n        <div class=\"table-card\">\n          <div class=\"table-header\">\n            <h3 class=\"table-title\">用户列表</h3>\n            <div class=\"table-stats\">\n              <span class=\"stats-text\">共 {{ total }} 位用户</span>\n            </div>\n          </div>\n          <div class=\"table-container\">\n            <table class=\"modern-table\">\n              <thead>\n                <tr>\n                  <th class=\"th-id\">用户ID</th>\n                  <th class=\"th-name\">姓名</th>\n                  <th class=\"th-actions\">操作</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"item in items\" :key=\"item.id\" class=\"table-row\">\n                  <td class=\"td-id\">\n                    <span class=\"id-badge\">{{ item.id }}</span>\n                  </td>\n                  <td class=\"td-name\">\n                    <div class=\"user-info\">\n                      <div class=\"user-avatar\">{{ item.username.charAt(0).toUpperCase() }}</div>\n                      <span class=\"user-name\">{{ item.username }}</span>\n                    </div>\n                  </td>\n                  <td class=\"td-actions\">\n                    <div class=\"action-buttons\">\n                      <van-button \n                        size=\"small\" \n                        type=\"primary\" \n                        class=\"action-btn result-btn\"\n                        @click=\"showImage(item)\"\n                      >\n                        📊 查看结果\n                      </van-button>\n                      <van-button \n                        size=\"small\" \n                        type=\"default\" \n                        class=\"action-btn qr-btn\"\n                        @click=\"showQr(item)\"\n                      >\n                        📱 二维码\n                      </van-button>\n                    </div>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页区域 -->\n      <div class=\"pagination-section\">\n        <van-pagination\n          v-model=\"page\"\n          :total-items=\"total\"\n          :items-per-page=\"pageSize\"\n          @change=\"fetchImages\"\n          mode=\"simple\"\n          class=\"custom-pagination\"\n        />\n      </div>\n\n    <!-- 查看结果弹窗 -->\n    <van-dialog \n      v-model:show=\"showImageDialog\" \n      title=\"📊 筛查结果\" \n      show-cancel-button\n      class=\"result-dialog\"\n    >\n      <div v-if=\"currentImage\" class=\"result-content\">\n        <div class=\"result-header\">\n          <p class=\"result-desc\">以下是用户的脊柱侧弯筛查结果图像</p>\n        </div>\n        <div class=\"image-container\">\n          <van-image \n            :src=\"currentImage\" \n            width=\"100%\" \n            height=\"300\" \n            fit=\"contain\"\n            class=\"result-image\"\n          />\n        </div>\n        <div class=\"result-footer\">\n          <p class=\"result-note\">请专业医师根据图像进行诊断分析</p>\n        </div>\n      </div>\n    </van-dialog>\n\n    <!-- 查看二维码弹窗 -->\n    <van-dialog \n      v-model:show=\"showQrDialog2\" \n      title=\"📱 用户二维码\" \n      show-cancel-button\n      class=\"qr-dialog\"\n    >\n      <div v-if=\"currentId\" class=\"qr-content\">\n        <div class=\"qr-desc-section\">\n          <p class=\"qr-desc\">扫描二维码快速识别用户信息</p>\n        </div>\n        <div class=\"qr-display\">\n          <div class=\"qr-card\">\n            <div class=\"qr-header\">\n              <span class=\"qr-title\">用户识别码</span>\n              <span class=\"user-id-badge\">ID: {{ currentId }}</span>\n            </div>\n            <div class=\"qr-code-wrapper\">\n              <qrcode-vue :value=\"String(currentId)\" :size=\"180\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </van-dialog>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport axios from 'axios'\nimport QrcodeVue from 'qrcode.vue'\n\nconst router = useRouter()\n\n// 用户信息\nconst username = ref(localStorage.getItem('username') || 'admin')\n\n// 原有的数据和方法\nconst items = ref([])\nconst total = ref(0)\nconst page = ref(1)\nconst pageSize = ref(10)\nconst searchName = ref('')\n\nconst showAddDialog = ref(false)\nconst addUsername = ref('')\nconst showQrDialog = ref(false)\nconst newUserId = ref(null)\n\nconst showImageDialog = ref(false)\nconst currentImage = ref('')\nconst showQrDialog2 = ref(false)\nconst currentId = ref(null)\n\n// 检查登录状态\nonMounted(() => {\n  const isLoggedIn = localStorage.getItem('isLoggedIn')\n  if (!isLoggedIn) {\n    router.push('/login')\n  }\n})\n\n// 退出登录\nconst handleLogout = () => {\n  localStorage.removeItem('isLoggedIn')\n  localStorage.removeItem('username')\n  router.push('/login')\n}\n\n// 原有的业务方法\nconst fetchImages = (p = page.value) => {\n  page.value = p\n  axios.get('http://192.168.100.58:5000/api/images', {\n    params: {\n      page: page.value,\n      page_size: pageSize.value,\n      username: searchName.value\n    }\n  }).then(res => {\n    items.value = res.data.items\n    total.value = res.data.total\n    console.log('接口返回数据:', res.data)\n  })\n}\n\nconst addUser = () => {\n  if (!addUsername.value) return\n  axios.post('http://192.168.100.58:5000/api/add_user', { username: addUsername.value })\n    .then(res => {\n      showAddDialog.value = false\n      newUserId.value = res.data.id\n      showQrDialog.value = true\n      addUsername.value = ''\n      fetchImages(1)\n    })\n}\n\nconst showImage = (row) => {\n  currentImage.value = row.image1\n  showImageDialog.value = true\n}\n\nconst showQr = (row) => {\n  currentId.value = row.id\n  showQrDialog2.value = true\n}\n\nconst resetSearch = () => {\n  searchName.value = ''\n  fetchImages(1)\n}\n\n// 初始化数据\nfetchImages()\n</script>\n\n<style>\n/* CSS变量定义 - 医疗主题色彩 */\n:root {\n  --primary-color: #1890ff;\n  --success-color: #52c41a;\n  --warning-color: #faad14;\n  --error-color: #ff4d4f;\n  --bg-color: #f5f7fa;\n  --card-bg: #ffffff;\n  --border-color: #e8e8e8;\n  --text-primary: #2c3e50;\n  --text-secondary: #666666;\n  --shadow-light: 0 2px 8px rgba(0,0,0,0.1);\n  --shadow-hover: 0 4px 12px rgba(0,0,0,0.15);\n  --border-radius: 8px;\n  --border-radius-small: 6px;\n}\n\n/* 全局样式重置 */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  background-color: var(--bg-color);\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\n}\n\n#app {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: var(--text-primary);\n  min-height: 100vh;\n}\n\n/* 系统标题区域样式 */\n.system-header {\n  background: linear-gradient(135deg, var(--primary-color) 0%, #40a9ff 100%);\n  color: white;\n  padding: 24px 0;\n  box-shadow: var(--shadow-light);\n  margin-bottom: 24px;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 16px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.title-section {\n  text-align: left;\n}\n\n.system-title {\n  margin: 0 0 8px 0;\n  font-size: 28px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.title-icon {\n  font-size: 32px;\n}\n\n.system-subtitle {\n  margin: 0;\n  font-size: 16px;\n  opacity: 0.9;\n  font-weight: 400;\n}\n\n/* 头部操作区域 */\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.welcome-text {\n  color: white;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.logout-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  transition: all 0.3s ease;\n}\n\n.logout-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-1px);\n}\n\n/* 主容器样式 */\n.main-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 16px;\n}\n\n/* 搜索区域样式 */\n.search-section {\n  margin-bottom: 24px;\n}\n\n.search-card {\n  background: var(--card-bg);\n  border-radius: var(--border-radius);\n  box-shadow: var(--shadow-light);\n  padding: 24px;\n  border: 1px solid var(--border-color);\n  transition: box-shadow 0.3s ease;\n}\n\n.search-card:hover {\n  box-shadow: var(--shadow-hover);\n}\n\n.search-header {\n  margin-bottom: 20px;\n  text-align: left;\n}\n\n.search-title {\n  margin: 0 0 4px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.search-desc {\n  margin: 0;\n  font-size: 14px;\n  color: var(--text-secondary);\n}\n\n.search-controls {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.search-input-group {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex-wrap: wrap;\n}\n\n.search-input {\n  flex: 1;\n  min-width: 200px;\n}\n\n/* 按钮样式优化 */\n.add-btn {\n  align-self: flex-start;\n  background: linear-gradient(135deg, var(--success-color) 0%, #73d13d 100%);\n  border: none;\n  border-radius: var(--border-radius-small);\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.add-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-hover);\n}\n\n.btn-icon {\n  margin-right: 4px;\n}\n\n.search-btn {\n  background: linear-gradient(135deg, var(--primary-color) 0%, #40a9ff 100%);\n  border: none;\n  border-radius: var(--border-radius-small);\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.search-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-hover);\n}\n\n.reset-btn {\n  border-radius: var(--border-radius-small);\n  transition: all 0.3s ease;\n}\n\n.reset-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-light);\n}\n\n/* 表格区域样式 */\n.table-section {\n  margin-bottom: 24px;\n}\n\n.table-card {\n  background: var(--card-bg);\n  border-radius: var(--border-radius);\n  box-shadow: var(--shadow-light);\n  border: 1px solid var(--border-color);\n  overflow: hidden;\n  transition: box-shadow 0.3s ease;\n}\n\n.table-card:hover {\n  box-shadow: var(--shadow-hover);\n}\n\n.table-header {\n  padding: 20px 24px;\n  border-bottom: 1px solid var(--border-color);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);\n}\n\n.table-title {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: var(--text-primary);\n}\n\n.table-stats {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.stats-text {\n  font-size: 14px;\n  background: var(--primary-color);\n  color: white;\n  padding: 4px 12px;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.table-container {\n  overflow-x: auto;\n}\n\n.modern-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 14px;\n}\n\n.modern-table thead {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n}\n\n.modern-table th {\n  padding: 16px 20px;\n  text-align: left;\n  font-weight: 600;\n  color: var(--text-primary);\n  border-bottom: 2px solid var(--border-color);\n  font-size: 14px;\n}\n\n.th-id {\n  width: 100px;\n  text-align: center;\n}\n\n.th-name {\n  width: auto;\n}\n\n.th-actions {\n  width: 200px;\n  text-align: center;\n}\n\n.table-row {\n  transition: all 0.3s ease;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.table-row:hover {\n  background: linear-gradient(135deg, #f8f9ff 0%, #f0f5ff 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);\n}\n\n.table-row:nth-child(even) {\n  background: #fafbfc;\n}\n\n.table-row:nth-child(even):hover {\n  background: linear-gradient(135deg, #f8f9ff 0%, #f0f5ff 100%);\n}\n\n.modern-table td {\n  padding: 16px 20px;\n  vertical-align: middle;\n}\n\n.td-id {\n  text-align: center;\n}\n\n.id-badge {\n  background: linear-gradient(135deg, var(--primary-color) 0%, #40a9ff 100%);\n  color: white;\n  padding: 4px 12px;\n  border-radius: 12px;\n  font-weight: 500;\n  font-size: 12px;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, var(--success-color) 0%, #73d13d 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.user-name {\n  font-weight: 500;\n  color: var(--text-primary);\n}\n\n.td-actions {\n  text-align: center;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.action-btn {\n  border-radius: var(--border-radius-small);\n  font-weight: 500;\n  transition: all 0.3s ease;\n  min-width: 80px;\n}\n\n.result-btn {\n  background: linear-gradient(135deg, var(--primary-color) 0%, #40a9ff 100%);\n  border: none;\n}\n\n.result-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-hover);\n}\n\n.qr-btn {\n  border: 1px solid var(--border-color);\n  background: white;\n}\n\n.qr-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-light);\n  border-color: var(--primary-color);\n  color: var(--primary-color);\n}\n\n/* 分页区域样式 */\n.pagination-section {\n  display: flex;\n  justify-content: center;\n  padding: 24px 0;\n  background: var(--card-bg);\n  border-radius: var(--border-radius);\n  box-shadow: var(--shadow-light);\n  border: 1px solid var(--border-color);\n  margin-bottom: 24px;\n}\n\n/* 弹窗样式 */\n.add-user-dialog {\n  --van-dialog-border-radius: 16px;\n  --van-dialog-background: var(--card-bg);\n  --van-dialog-header-font-weight: 600;\n  --van-dialog-header-color: var(--text-primary);\n  --van-dialog-message-color: var(--text-primary);\n}\n\n.add-user-dialog .van-dialog__header {\n  background: linear-gradient(135deg, #f8f9ff 0%, #f0f5ff 100%) !important;\n  border-bottom: 1px solid var(--border-color) !important;\n  padding: 20px 24px 16px 24px !important;\n}\n\n.add-user-dialog .van-dialog__content {\n  padding: 0 24px 20px 24px !important;\n}\n\n.add-user-dialog .van-dialog__footer {\n  padding: 16px 24px 20px 24px !important;\n  border-top: 1px solid var(--border-color) !important;\n  background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%) !important;\n}\n\n.dialog-content {\n  padding: 20px 0;\n}\n\n.dialog-desc {\n  margin-bottom: 20px;\n  text-align: center;\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\n  border-radius: 12px;\n  padding: 16px;\n  border: 1px solid #bae6fd;\n}\n\n.dialog-desc p {\n  margin: 0;\n  color: var(--primary-color);\n  font-size: 14px;\n  line-height: 1.6;\n  font-weight: 500;\n}\n\n.dialog-field {\n  margin-bottom: 8px;\n  --van-field-border-color: var(--border-color);\n  --van-field-focus-border-color: var(--primary-color);\n  --van-field-label-color: var(--text-primary);\n  --van-field-input-text-color: var(--text-primary);\n  --van-field-placeholder-text-color: var(--text-secondary);\n}\n\n.dialog-field .van-field__control {\n  border-radius: 8px !important;\n  border: 1px solid var(--border-color) !important;\n  background: #fafbfc !important;\n  transition: all 0.3s ease !important;\n}\n\n.dialog-field .van-field__control:focus-within {\n  border-color: var(--primary-color) !important;\n  background: white !important;\n  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1) !important;\n}\n\n/* 弹窗按钮美化 */\n.add-user-dialog .van-dialog__confirm {\n  background: linear-gradient(135deg, var(--success-color) 0%, #73d13d 100%) !important;\n  border: none !important;\n  border-radius: 8px !important;\n  font-weight: 600 !important;\n  padding: 12px 24px !important;\n  transition: all 0.3s ease !important;\n}\n\n.add-user-dialog .van-dialog__confirm:hover {\n  transform: translateY(-1px) !important;\n  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3) !important;\n}\n\n.add-user-dialog .van-dialog__cancel {\n  background: white !important;\n  border: 1px solid var(--border-color) !important;\n  border-radius: 8px !important;\n  color: var(--text-secondary) !important;\n  font-weight: 500 !important;\n  padding: 12px 24px !important;\n  transition: all 0.3s ease !important;\n}\n\n.add-user-dialog .van-dialog__cancel:hover {\n  border-color: var(--primary-color) !important;\n  color: var(--primary-color) !important;\n  transform: translateY(-1px) !important;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;\n}\n\n.qr-content {\n  padding: 16px 0;\n  text-align: center;\n}\n\n.success-message {\n  margin-bottom: 20px;\n}\n\n.success-text {\n  margin: 0 0 8px 0;\n  color: var(--success-color);\n  font-weight: 600;\n  font-size: 16px;\n}\n\n.qr-desc {\n  margin: 0;\n  color: var(--text-secondary);\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.qr-desc-section {\n  margin-bottom: 16px;\n}\n\n.qr-display {\n  display: flex;\n  justify-content: center;\n}\n\n.qr-card {\n  background: linear-gradient(135deg, #f8f9ff 0%, #f0f5ff 100%);\n  border: 2px solid var(--primary-color);\n  border-radius: var(--border-radius);\n  padding: 20px;\n  box-shadow: var(--shadow-light);\n  max-width: 240px;\n}\n\n.qr-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding-bottom: 12px;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.qr-title {\n  font-weight: 600;\n  color: var(--text-primary);\n  font-size: 14px;\n}\n\n.user-id-badge {\n  background: var(--primary-color);\n  color: white;\n  padding: 2px 8px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.qr-code-wrapper {\n  display: flex;\n  justify-content: center;\n  padding: 8px;\n  background: white;\n  border-radius: var(--border-radius-small);\n  box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    gap: 12px;\n    text-align: center;\n  }\n\n  .header-actions {\n    justify-content: center;\n  }\n\n  .search-card {\n    padding: 20px 16px;\n  }\n\n  .search-controls {\n    gap: 12px;\n  }\n\n  .search-input-group {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .search-input {\n    min-width: auto;\n  }\n\n  .table-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n    padding: 16px 20px;\n  }\n\n  .modern-table th,\n  .modern-table td {\n    padding: 12px 16px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n    gap: 6px;\n  }\n\n  .action-btn {\n    min-width: auto;\n    font-size: 12px;\n    padding: 6px 12px;\n  }\n\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n    font-size: 12px;\n  }\n}\n</style>\n"], "mappings": ";AAwPA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAI;AACnC,SAASC,SAAS,QAAQ,YAAW;AACrC,OAAOC,KAAK,MAAM,OAAM;AACxB,OAAOC,SAAS,MAAM,YAAW;;;;;;;IAEjC,MAAMC,MAAM,GAAGH,SAAS,CAAC;;IAEzB;IACA,MAAMI,QAAQ,GAAGN,GAAG,CAACO,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO;;IAEhE;IACA,MAAMC,KAAK,GAAGT,GAAG,CAAC,EAAE;IACpB,MAAMU,KAAK,GAAGV,GAAG,CAAC,CAAC;IACnB,MAAMW,IAAI,GAAGX,GAAG,CAAC,CAAC;IAClB,MAAMY,QAAQ,GAAGZ,GAAG,CAAC,EAAE;IACvB,MAAMa,UAAU,GAAGb,GAAG,CAAC,EAAE;IAEzB,MAAMc,aAAa,GAAGd,GAAG,CAAC,KAAK;IAC/B,MAAMe,WAAW,GAAGf,GAAG,CAAC,EAAE;IAC1B,MAAMgB,YAAY,GAAGhB,GAAG,CAAC,KAAK;IAC9B,MAAMiB,SAAS,GAAGjB,GAAG,CAAC,IAAI;IAE1B,MAAMkB,eAAe,GAAGlB,GAAG,CAAC,KAAK;IACjC,MAAMmB,YAAY,GAAGnB,GAAG,CAAC,EAAE;IAC3B,MAAMoB,aAAa,GAAGpB,GAAG,CAAC,KAAK;IAC/B,MAAMqB,SAAS,GAAGrB,GAAG,CAAC,IAAI;;IAE1B;IACAC,SAAS,CAAC,MAAM;MACd,MAAMqB,UAAU,GAAGf,YAAY,CAACC,OAAO,CAAC,YAAY;MACpD,IAAI,CAACc,UAAU,EAAE;QACfjB,MAAM,CAACkB,IAAI,CAAC,QAAQ;MACtB;IACF,CAAC;;IAED;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBjB,YAAY,CAACkB,UAAU,CAAC,YAAY;MACpClB,YAAY,CAACkB,UAAU,CAAC,UAAU;MAClCpB,MAAM,CAACkB,IAAI,CAAC,QAAQ;IACtB;;IAEA;IACA,MAAMG,WAAW,GAAGA,CAACC,CAAC,GAAGhB,IAAI,CAACiB,KAAK,KAAK;MACtCjB,IAAI,CAACiB,KAAK,GAAGD,CAAA;MACbxB,KAAK,CAAC0B,GAAG,CAAC,uCAAuC,EAAE;QACjDC,MAAM,EAAE;UACNnB,IAAI,EAAEA,IAAI,CAACiB,KAAK;UAChBG,SAAS,EAAEnB,QAAQ,CAACgB,KAAK;UACzBtB,QAAQ,EAAEO,UAAU,CAACe;QACvB;MACF,CAAC,CAAC,CAACI,IAAI,CAACC,GAAG,IAAI;QACbxB,KAAK,CAACmB,KAAK,GAAGK,GAAG,CAACC,IAAI,CAACzB,KAAI;QAC3BC,KAAK,CAACkB,KAAK,GAAGK,GAAG,CAACC,IAAI,CAACxB,KAAI;QAC3ByB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,GAAG,CAACC,IAAI;MACjC,CAAC;IACH;IAEA,MAAMG,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAACtB,WAAW,CAACa,KAAK,EAAE;MACxBzB,KAAK,CAACmC,IAAI,CAAC,yCAAyC,EAAE;QAAEhC,QAAQ,EAAES,WAAW,CAACa;MAAM,CAAC,EAClFI,IAAI,CAACC,GAAG,IAAI;QACXnB,aAAa,CAACc,KAAK,GAAG,KAAI;QAC1BX,SAAS,CAACW,KAAK,GAAGK,GAAG,CAACC,IAAI,CAACK,EAAC;QAC5BvB,YAAY,CAACY,KAAK,GAAG,IAAG;QACxBb,WAAW,CAACa,KAAK,GAAG,EAAC;QACrBF,WAAW,CAAC,CAAC;MACf,CAAC;IACL;IAEA,MAAMc,SAAS,GAAIC,GAAG,IAAK;MACzBtB,YAAY,CAACS,KAAK,GAAGa,GAAG,CAACC,MAAK;MAC9BxB,eAAe,CAACU,KAAK,GAAG,IAAG;IAC7B;IAEA,MAAMe,MAAM,GAAIF,GAAG,IAAK;MACtBpB,SAAS,CAACO,KAAK,GAAGa,GAAG,CAACF,EAAC;MACvBnB,aAAa,CAACQ,KAAK,GAAG,IAAG;IAC3B;IAEA,MAAMgB,WAAW,GAAGA,CAAA,KAAM;MACxB/B,UAAU,CAACe,KAAK,GAAG,EAAC;MACpBF,WAAW,CAAC,CAAC;IACf;;IAEA;IACAA,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}