{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport LoginPage from '../views/Login.vue';\nimport HomePage from '../views/Home.vue';\nconst routes = [{\n  path: '/',\n  redirect: '/login'\n}, {\n  path: '/login',\n  name: 'LoginPage',\n  component: LoginPage,\n  meta: {\n    title: '登录 - 脊柱侧弯筛查系统'\n  }\n}, {\n  path: '/home',\n  name: 'HomePage',\n  component: HomePage,\n  meta: {\n    title: '用户管理 - 脊柱侧弯筛查系统',\n    requiresAuth: true\n  }\n}];\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n});\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  if (to.meta.title) {\n    document.title = to.meta.title;\n  }\n\n  // 检查是否需要登录\n  if (to.meta.requiresAuth) {\n    const isLoggedIn = localStorage.getItem('isLoggedIn');\n    if (!isLoggedIn) {\n      next('/login');\n      return;\n    }\n  }\n\n  // 如果已登录且访问登录页，重定向到主页\n  if (to.path === '/login') {\n    const isLoggedIn = localStorage.getItem('isLoggedIn');\n    if (isLoggedIn) {\n      next('/home');\n      return;\n    }\n  }\n  next();\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "LoginPage", "HomePage", "routes", "path", "redirect", "name", "component", "meta", "title", "requiresAuth", "router", "history", "beforeEach", "to", "from", "next", "document", "isLoggedIn", "localStorage", "getItem"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\nimport LoginPage from '../views/Login.vue'\nimport HomePage from '../views/Home.vue'\n\nconst routes = [\n  {\n    path: '/',\n    redirect: '/login'\n  },\n  {\n    path: '/login',\n    name: 'LoginPage',\n    component: LoginPage,\n    meta: {\n      title: '登录 - 脊柱侧弯筛查系统'\n    }\n  },\n  {\n    path: '/home',\n    name: 'HomePage',\n    component: HomePage,\n    meta: {\n      title: '用户管理 - 脊柱侧弯筛查系统',\n      requiresAuth: true\n    }\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n})\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  if (to.meta.title) {\n    document.title = to.meta.title\n  }\n\n  // 检查是否需要登录\n  if (to.meta.requiresAuth) {\n    const isLoggedIn = localStorage.getItem('isLoggedIn')\n    if (!isLoggedIn) {\n      next('/login')\n      return\n    }\n  }\n\n  // 如果已登录且访问登录页，重定向到主页\n  if (to.path === '/login') {\n    const isLoggedIn = localStorage.getItem('isLoggedIn')\n    if (isLoggedIn) {\n      next('/home')\n      return\n    }\n  }\n\n  next()\n})\n\nexport default router\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,QAAQ,MAAM,mBAAmB;AAExC,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEN,SAAS;EACpBO,IAAI,EAAE;IACJC,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEL,IAAI,EAAE,OAAO;EACbE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEL,QAAQ;EACnBM,IAAI,EAAE;IACJC,KAAK,EAAE,iBAAiB;IACxBC,YAAY,EAAE;EAChB;AACF,CAAC,CACF;AAED,MAAMC,MAAM,GAAGZ,YAAY,CAAC;EAC1Ba,OAAO,EAAEZ,gBAAgB,CAAC,CAAC;EAC3BG;AACF,CAAC,CAAC;;AAEF;AACAQ,MAAM,CAACE,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC;EACA,IAAIF,EAAE,CAACN,IAAI,CAACC,KAAK,EAAE;IACjBQ,QAAQ,CAACR,KAAK,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK;EAChC;;EAEA;EACA,IAAIK,EAAE,CAACN,IAAI,CAACE,YAAY,EAAE;IACxB,MAAMQ,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,IAAI,CAACF,UAAU,EAAE;MACfF,IAAI,CAAC,QAAQ,CAAC;MACd;IACF;EACF;;EAEA;EACA,IAAIF,EAAE,CAACV,IAAI,KAAK,QAAQ,EAAE;IACxB,MAAMc,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,IAAIF,UAAU,EAAE;MACdF,IAAI,CAAC,OAAO,CAAC;MACb;IACF;EACF;EAEAA,IAAI,CAAC,CAAC;AACR,CAAC,CAAC;AAEF,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}