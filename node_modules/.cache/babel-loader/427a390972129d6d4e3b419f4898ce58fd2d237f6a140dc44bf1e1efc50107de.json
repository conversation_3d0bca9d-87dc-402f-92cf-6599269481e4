{"ast": null, "code": "import { ref, onBeforeUpdate } from \"vue\";\nfunction useRefs() {\n  const refs = ref([]);\n  const cache = [];\n  onBeforeUpdate(() => {\n    refs.value = [];\n  });\n  const setRefs = index => {\n    if (!cache[index]) {\n      cache[index] = el => {\n        refs.value[index] = el;\n      };\n    }\n    return cache[index];\n  };\n  return [refs, setRefs];\n}\nexport { useRefs };", "map": {"version": 3, "names": ["ref", "onBeforeUpdate", "useRefs", "refs", "cache", "value", "setRefs", "index", "el"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-refs.mjs"], "sourcesContent": ["import { ref, onBeforeUpdate } from \"vue\";\nfunction useRefs() {\n  const refs = ref([]);\n  const cache = [];\n  onBeforeUpdate(() => {\n    refs.value = [];\n  });\n  const setRefs = (index) => {\n    if (!cache[index]) {\n      cache[index] = (el) => {\n        refs.value[index] = el;\n      };\n    }\n    return cache[index];\n  };\n  return [refs, setRefs];\n}\nexport {\n  useRefs\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,cAAc,QAAQ,KAAK;AACzC,SAASC,OAAOA,CAAA,EAAG;EACjB,MAAMC,IAAI,GAAGH,GAAG,CAAC,EAAE,CAAC;EACpB,MAAMI,KAAK,GAAG,EAAE;EAChBH,cAAc,CAAC,MAAM;IACnBE,IAAI,CAACE,KAAK,GAAG,EAAE;EACjB,CAAC,CAAC;EACF,MAAMC,OAAO,GAAIC,KAAK,IAAK;IACzB,IAAI,CAACH,KAAK,CAACG,KAAK,CAAC,EAAE;MACjBH,KAAK,CAACG,KAAK,CAAC,GAAIC,EAAE,IAAK;QACrBL,IAAI,CAACE,KAAK,CAACE,KAAK,CAAC,GAAGC,EAAE;MACxB,CAAC;IACH;IACA,OAAOJ,KAAK,CAACG,KAAK,CAAC;EACrB,CAAC;EACD,OAAO,CAACJ,IAAI,EAAEG,OAAO,CAAC;AACxB;AACA,SACEJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}