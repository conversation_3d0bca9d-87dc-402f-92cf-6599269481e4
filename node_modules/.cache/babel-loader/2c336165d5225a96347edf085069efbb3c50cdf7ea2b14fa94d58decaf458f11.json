{"ast": null, "code": "import { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, inBrowser } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport Dialog from \"./Dialog.mjs\";\nlet instance;\nconst DEFAULT_OPTIONS = {\n  title: \"\",\n  width: \"\",\n  theme: null,\n  message: \"\",\n  overlay: true,\n  callback: null,\n  teleport: \"body\",\n  className: \"\",\n  allowHtml: false,\n  lockScroll: true,\n  transition: void 0,\n  beforeClose: null,\n  overlayClass: \"\",\n  overlayStyle: void 0,\n  messageAlign: \"\",\n  cancelButtonText: \"\",\n  cancelButtonColor: null,\n  cancelButtonDisabled: false,\n  confirmButtonText: \"\",\n  confirmButtonColor: null,\n  confirmButtonDisabled: false,\n  showConfirmButton: true,\n  showCancelButton: false,\n  closeOnPopstate: true,\n  closeOnClickOverlay: false,\n  destroyOnClose: false\n};\nlet currentOptions = extend({}, DEFAULT_OPTIONS);\nfunction initInstance() {\n  const Wrapper = {\n    setup() {\n      const {\n        state,\n        toggle\n      } = usePopupState();\n      return () => _createVNode(Dialog, _mergeProps(state, {\n        \"onUpdate:show\": toggle\n      }), null);\n    }\n  };\n  ({\n    instance\n  } = mountComponent(Wrapper));\n}\nfunction showDialog(options) {\n  if (!inBrowser) {\n    return Promise.resolve(void 0);\n  }\n  return new Promise((resolve, reject) => {\n    if (!instance) {\n      initInstance();\n    }\n    instance.open(extend({}, currentOptions, options, {\n      callback: action => {\n        (action === \"confirm\" ? resolve : reject)(action);\n      }\n    }));\n  });\n}\nconst setDialogDefaultOptions = options => {\n  extend(currentOptions, options);\n};\nconst resetDialogDefaultOptions = () => {\n  currentOptions = extend({}, DEFAULT_OPTIONS);\n};\nconst showConfirmDialog = options => showDialog(extend({\n  showCancelButton: true\n}, options));\nconst closeDialog = () => {\n  if (instance) {\n    instance.toggle(false);\n  }\n};\nexport { closeDialog, resetDialogDefaultOptions, setDialogDefaultOptions, showConfirmDialog, showDialog };", "map": {"version": 3, "names": ["mergeProps", "_mergeProps", "createVNode", "_createVNode", "extend", "inBrowser", "mountComponent", "usePopupState", "Dialog", "instance", "DEFAULT_OPTIONS", "title", "width", "theme", "message", "overlay", "callback", "teleport", "className", "allowHtml", "lockScroll", "transition", "beforeClose", "overlayClass", "overlayStyle", "messageAlign", "cancelButtonText", "cancelButtonColor", "cancelButtonDisabled", "confirmButtonText", "confirmButtonColor", "confirmButtonDisabled", "showConfirmButton", "showCancelButton", "closeOnPopstate", "closeOnClickOverlay", "destroyOnClose", "currentOptions", "initInstance", "Wrapper", "setup", "state", "toggle", "showDialog", "options", "Promise", "resolve", "reject", "open", "action", "setDialogDefaultOptions", "resetDialogDefaultOptions", "showConfirmDialog", "closeDialog"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/dialog/function-call.mjs"], "sourcesContent": ["import { mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, inBrowser } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport Dialog from \"./Dialog.mjs\";\nlet instance;\nconst DEFAULT_OPTIONS = {\n  title: \"\",\n  width: \"\",\n  theme: null,\n  message: \"\",\n  overlay: true,\n  callback: null,\n  teleport: \"body\",\n  className: \"\",\n  allowHtml: false,\n  lockScroll: true,\n  transition: void 0,\n  beforeClose: null,\n  overlayClass: \"\",\n  overlayStyle: void 0,\n  messageAlign: \"\",\n  cancelButtonText: \"\",\n  cancelButtonColor: null,\n  cancelButtonDisabled: false,\n  confirmButtonText: \"\",\n  confirmButtonColor: null,\n  confirmButtonDisabled: false,\n  showConfirmButton: true,\n  showCancelButton: false,\n  closeOnPopstate: true,\n  closeOnClickOverlay: false,\n  destroyOnClose: false\n};\nlet currentOptions = extend({}, DEFAULT_OPTIONS);\nfunction initInstance() {\n  const Wrapper = {\n    setup() {\n      const {\n        state,\n        toggle\n      } = usePopupState();\n      return () => _createVNode(Dialog, _mergeProps(state, {\n        \"onUpdate:show\": toggle\n      }), null);\n    }\n  };\n  ({\n    instance\n  } = mountComponent(Wrapper));\n}\nfunction showDialog(options) {\n  if (!inBrowser) {\n    return Promise.resolve(void 0);\n  }\n  return new Promise((resolve, reject) => {\n    if (!instance) {\n      initInstance();\n    }\n    instance.open(extend({}, currentOptions, options, {\n      callback: (action) => {\n        (action === \"confirm\" ? resolve : reject)(action);\n      }\n    }));\n  });\n}\nconst setDialogDefaultOptions = (options) => {\n  extend(currentOptions, options);\n};\nconst resetDialogDefaultOptions = () => {\n  currentOptions = extend({}, DEFAULT_OPTIONS);\n};\nconst showConfirmDialog = (options) => showDialog(extend({\n  showCancelButton: true\n}, options));\nconst closeDialog = () => {\n  if (instance) {\n    instance.toggle(false);\n  }\n};\nexport {\n  closeDialog,\n  resetDialogDefaultOptions,\n  setDialogDefaultOptions,\n  showConfirmDialog,\n  showDialog\n};\n"], "mappings": "AAAA,SAASA,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,MAAM,EAAEC,SAAS,QAAQ,oBAAoB;AACtD,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,OAAOC,MAAM,MAAM,cAAc;AACjC,IAAIC,QAAQ;AACZ,MAAMC,eAAe,GAAG;EACtBC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,KAAK;EAChBC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK,CAAC;EAClBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,EAAE;EAChBC,YAAY,EAAE,KAAK,CAAC;EACpBC,YAAY,EAAE,EAAE;EAChBC,gBAAgB,EAAE,EAAE;EACpBC,iBAAiB,EAAE,IAAI;EACvBC,oBAAoB,EAAE,KAAK;EAC3BC,iBAAiB,EAAE,EAAE;EACrBC,kBAAkB,EAAE,IAAI;EACxBC,qBAAqB,EAAE,KAAK;EAC5BC,iBAAiB,EAAE,IAAI;EACvBC,gBAAgB,EAAE,KAAK;EACvBC,eAAe,EAAE,IAAI;EACrBC,mBAAmB,EAAE,KAAK;EAC1BC,cAAc,EAAE;AAClB,CAAC;AACD,IAAIC,cAAc,GAAGjC,MAAM,CAAC,CAAC,CAAC,EAAEM,eAAe,CAAC;AAChD,SAAS4B,YAAYA,CAAA,EAAG;EACtB,MAAMC,OAAO,GAAG;IACdC,KAAKA,CAAA,EAAG;MACN,MAAM;QACJC,KAAK;QACLC;MACF,CAAC,GAAGnC,aAAa,CAAC,CAAC;MACnB,OAAO,MAAMJ,YAAY,CAACK,MAAM,EAAEP,WAAW,CAACwC,KAAK,EAAE;QACnD,eAAe,EAAEC;MACnB,CAAC,CAAC,EAAE,IAAI,CAAC;IACX;EACF,CAAC;EACD,CAAC;IACCjC;EACF,CAAC,GAAGH,cAAc,CAACiC,OAAO,CAAC;AAC7B;AACA,SAASI,UAAUA,CAACC,OAAO,EAAE;EAC3B,IAAI,CAACvC,SAAS,EAAE;IACd,OAAOwC,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC;EAChC;EACA,OAAO,IAAID,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAI,CAACtC,QAAQ,EAAE;MACb6B,YAAY,CAAC,CAAC;IAChB;IACA7B,QAAQ,CAACuC,IAAI,CAAC5C,MAAM,CAAC,CAAC,CAAC,EAAEiC,cAAc,EAAEO,OAAO,EAAE;MAChD5B,QAAQ,EAAGiC,MAAM,IAAK;QACpB,CAACA,MAAM,KAAK,SAAS,GAAGH,OAAO,GAAGC,MAAM,EAAEE,MAAM,CAAC;MACnD;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,MAAMC,uBAAuB,GAAIN,OAAO,IAAK;EAC3CxC,MAAM,CAACiC,cAAc,EAAEO,OAAO,CAAC;AACjC,CAAC;AACD,MAAMO,yBAAyB,GAAGA,CAAA,KAAM;EACtCd,cAAc,GAAGjC,MAAM,CAAC,CAAC,CAAC,EAAEM,eAAe,CAAC;AAC9C,CAAC;AACD,MAAM0C,iBAAiB,GAAIR,OAAO,IAAKD,UAAU,CAACvC,MAAM,CAAC;EACvD6B,gBAAgB,EAAE;AACpB,CAAC,EAAEW,OAAO,CAAC,CAAC;AACZ,MAAMS,WAAW,GAAGA,CAAA,KAAM;EACxB,IAAI5C,QAAQ,EAAE;IACZA,QAAQ,CAACiC,MAAM,CAAC,KAAK,CAAC;EACxB;AACF,CAAC;AACD,SACEW,WAAW,EACXF,yBAAyB,EACzBD,uBAAuB,EACvBE,iBAAiB,EACjBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}