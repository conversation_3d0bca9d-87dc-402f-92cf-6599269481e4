{"ast": null, "code": "import { ref, watch, computed, nextTick, onMounted, onBeforeUnmount, defineComponent, getCurrentInstance, createVNode as _createVNode, resolveDirective as _resolveDirective, mergeProps as _mergeProps, withDirectives as _withDirectives } from \"vue\";\nimport { isDef, addUnit, inBrowser, truthProp, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"image\");\nconst imageProps = {\n  src: String,\n  alt: String,\n  fit: String,\n  position: String,\n  round: Boolean,\n  block: Boolean,\n  width: numericProp,\n  height: numericProp,\n  radius: numericProp,\n  lazyLoad: Boolean,\n  iconSize: numericProp,\n  showError: truthProp,\n  errorIcon: makeStringProp(\"photo-fail\"),\n  iconPrefix: String,\n  showLoading: truthProp,\n  loadingIcon: makeStringProp(\"photo\"),\n  crossorigin: String,\n  referrerpolicy: String,\n  decoding: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: imageProps,\n  emits: [\"load\", \"error\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const error = ref(false);\n    const loading = ref(true);\n    const imageRef = ref();\n    const {\n      $Lazyload\n    } = getCurrentInstance().proxy;\n    const style = computed(() => {\n      const style2 = {\n        width: addUnit(props.width),\n        height: addUnit(props.height)\n      };\n      if (isDef(props.radius)) {\n        style2.overflow = \"hidden\";\n        style2.borderRadius = addUnit(props.radius);\n      }\n      return style2;\n    });\n    watch(() => props.src, () => {\n      error.value = false;\n      loading.value = true;\n    });\n    const onLoad = event => {\n      if (loading.value) {\n        loading.value = false;\n        emit(\"load\", event);\n      }\n    };\n    const triggerLoad = () => {\n      const loadEvent = new Event(\"load\");\n      Object.defineProperty(loadEvent, \"target\", {\n        value: imageRef.value,\n        enumerable: true\n      });\n      onLoad(loadEvent);\n    };\n    const onError = event => {\n      error.value = true;\n      loading.value = false;\n      emit(\"error\", event);\n    };\n    const renderIcon = (name2, className, slot) => {\n      if (slot) {\n        return slot();\n      }\n      return _createVNode(Icon, {\n        \"name\": name2,\n        \"size\": props.iconSize,\n        \"class\": className,\n        \"classPrefix\": props.iconPrefix\n      }, null);\n    };\n    const renderPlaceholder = () => {\n      if (loading.value && props.showLoading) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"loading\")\n        }, [renderIcon(props.loadingIcon, bem(\"loading-icon\"), slots.loading)]);\n      }\n      if (error.value && props.showError) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"error\")\n        }, [renderIcon(props.errorIcon, bem(\"error-icon\"), slots.error)]);\n      }\n    };\n    const renderImage = () => {\n      if (error.value || !props.src) {\n        return;\n      }\n      const attrs = {\n        alt: props.alt,\n        class: bem(\"img\"),\n        decoding: props.decoding,\n        style: {\n          objectFit: props.fit,\n          objectPosition: props.position\n        },\n        crossorigin: props.crossorigin,\n        referrerpolicy: props.referrerpolicy\n      };\n      if (props.lazyLoad) {\n        return _withDirectives(_createVNode(\"img\", _mergeProps({\n          \"ref\": imageRef\n        }, attrs), null), [[_resolveDirective(\"lazy\"), props.src]]);\n      }\n      return _createVNode(\"img\", _mergeProps({\n        \"ref\": imageRef,\n        \"src\": props.src,\n        \"onLoad\": onLoad,\n        \"onError\": onError\n      }, attrs), null);\n    };\n    const onLazyLoaded = ({\n      el\n    }) => {\n      const check = () => {\n        if (el === imageRef.value && loading.value) {\n          triggerLoad();\n        }\n      };\n      if (imageRef.value) {\n        check();\n      } else {\n        nextTick(check);\n      }\n    };\n    const onLazyLoadError = ({\n      el\n    }) => {\n      if (el === imageRef.value && !error.value) {\n        onError();\n      }\n    };\n    if ($Lazyload && inBrowser) {\n      $Lazyload.$on(\"loaded\", onLazyLoaded);\n      $Lazyload.$on(\"error\", onLazyLoadError);\n      onBeforeUnmount(() => {\n        $Lazyload.$off(\"loaded\", onLazyLoaded);\n        $Lazyload.$off(\"error\", onLazyLoadError);\n      });\n    }\n    onMounted(() => {\n      nextTick(() => {\n        var _a;\n        if (((_a = imageRef.value) == null ? void 0 : _a.complete) && !props.lazyLoad) {\n          triggerLoad();\n        }\n      });\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          round: props.round,\n          block: props.block\n        }),\n        \"style\": style.value\n      }, [renderImage(), renderPlaceholder(), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { stdin_default as default, imageProps };", "map": {"version": 3, "names": ["ref", "watch", "computed", "nextTick", "onMounted", "onBeforeUnmount", "defineComponent", "getCurrentInstance", "createVNode", "_createVNode", "resolveDirective", "_resolveDirective", "mergeProps", "_mergeProps", "withDirectives", "_withDirectives", "isDef", "addUnit", "inBrowser", "truthProp", "numericProp", "makeStringProp", "createNamespace", "Icon", "name", "bem", "imageProps", "src", "String", "alt", "fit", "position", "round", "Boolean", "block", "width", "height", "radius", "lazyLoad", "iconSize", "showError", "errorIcon", "iconPrefix", "showLoading", "loadingIcon", "crossorigin", "referrerpolicy", "decoding", "stdin_default", "props", "emits", "setup", "emit", "slots", "error", "loading", "imageRef", "$Lazyload", "proxy", "style", "style2", "overflow", "borderRadius", "value", "onLoad", "event", "triggerLoad", "loadEvent", "Event", "Object", "defineProperty", "enumerable", "onError", "renderIcon", "name2", "className", "slot", "renderPlaceholder", "renderImage", "attrs", "class", "objectFit", "objectPosition", "onLazyLoaded", "el", "check", "onLazyLoadError", "$on", "$off", "_a", "complete", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/image/Image.mjs"], "sourcesContent": ["import { ref, watch, computed, nextTick, onMounted, onBeforeUnmount, defineComponent, getCurrentInstance, createVNode as _createVNode, resolveDirective as _resolveDirective, mergeProps as _mergeProps, withDirectives as _withDirectives } from \"vue\";\nimport { isDef, addUnit, inBrowser, truthProp, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"image\");\nconst imageProps = {\n  src: String,\n  alt: String,\n  fit: String,\n  position: String,\n  round: Boolean,\n  block: Boolean,\n  width: numericProp,\n  height: numericProp,\n  radius: numericProp,\n  lazyLoad: Boolean,\n  iconSize: numericProp,\n  showError: truthProp,\n  errorIcon: makeStringProp(\"photo-fail\"),\n  iconPrefix: String,\n  showLoading: truthProp,\n  loadingIcon: makeStringProp(\"photo\"),\n  crossorigin: String,\n  referrerpolicy: String,\n  decoding: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: imageProps,\n  emits: [\"load\", \"error\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const error = ref(false);\n    const loading = ref(true);\n    const imageRef = ref();\n    const {\n      $Lazyload\n    } = getCurrentInstance().proxy;\n    const style = computed(() => {\n      const style2 = {\n        width: addUnit(props.width),\n        height: addUnit(props.height)\n      };\n      if (isDef(props.radius)) {\n        style2.overflow = \"hidden\";\n        style2.borderRadius = addUnit(props.radius);\n      }\n      return style2;\n    });\n    watch(() => props.src, () => {\n      error.value = false;\n      loading.value = true;\n    });\n    const onLoad = (event) => {\n      if (loading.value) {\n        loading.value = false;\n        emit(\"load\", event);\n      }\n    };\n    const triggerLoad = () => {\n      const loadEvent = new Event(\"load\");\n      Object.defineProperty(loadEvent, \"target\", {\n        value: imageRef.value,\n        enumerable: true\n      });\n      onLoad(loadEvent);\n    };\n    const onError = (event) => {\n      error.value = true;\n      loading.value = false;\n      emit(\"error\", event);\n    };\n    const renderIcon = (name2, className, slot) => {\n      if (slot) {\n        return slot();\n      }\n      return _createVNode(Icon, {\n        \"name\": name2,\n        \"size\": props.iconSize,\n        \"class\": className,\n        \"classPrefix\": props.iconPrefix\n      }, null);\n    };\n    const renderPlaceholder = () => {\n      if (loading.value && props.showLoading) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"loading\")\n        }, [renderIcon(props.loadingIcon, bem(\"loading-icon\"), slots.loading)]);\n      }\n      if (error.value && props.showError) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"error\")\n        }, [renderIcon(props.errorIcon, bem(\"error-icon\"), slots.error)]);\n      }\n    };\n    const renderImage = () => {\n      if (error.value || !props.src) {\n        return;\n      }\n      const attrs = {\n        alt: props.alt,\n        class: bem(\"img\"),\n        decoding: props.decoding,\n        style: {\n          objectFit: props.fit,\n          objectPosition: props.position\n        },\n        crossorigin: props.crossorigin,\n        referrerpolicy: props.referrerpolicy\n      };\n      if (props.lazyLoad) {\n        return _withDirectives(_createVNode(\"img\", _mergeProps({\n          \"ref\": imageRef\n        }, attrs), null), [[_resolveDirective(\"lazy\"), props.src]]);\n      }\n      return _createVNode(\"img\", _mergeProps({\n        \"ref\": imageRef,\n        \"src\": props.src,\n        \"onLoad\": onLoad,\n        \"onError\": onError\n      }, attrs), null);\n    };\n    const onLazyLoaded = ({\n      el\n    }) => {\n      const check = () => {\n        if (el === imageRef.value && loading.value) {\n          triggerLoad();\n        }\n      };\n      if (imageRef.value) {\n        check();\n      } else {\n        nextTick(check);\n      }\n    };\n    const onLazyLoadError = ({\n      el\n    }) => {\n      if (el === imageRef.value && !error.value) {\n        onError();\n      }\n    };\n    if ($Lazyload && inBrowser) {\n      $Lazyload.$on(\"loaded\", onLazyLoaded);\n      $Lazyload.$on(\"error\", onLazyLoadError);\n      onBeforeUnmount(() => {\n        $Lazyload.$off(\"loaded\", onLazyLoaded);\n        $Lazyload.$off(\"error\", onLazyLoadError);\n      });\n    }\n    onMounted(() => {\n      nextTick(() => {\n        var _a;\n        if (((_a = imageRef.value) == null ? void 0 : _a.complete) && !props.lazyLoad) {\n          triggerLoad();\n        }\n      });\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          round: props.round,\n          block: props.block\n        }),\n        \"style\": style.value\n      }, [renderImage(), renderPlaceholder(), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  imageProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,WAAW,IAAIC,YAAY,EAAEC,gBAAgB,IAAIC,iBAAiB,EAAEC,UAAU,IAAIC,WAAW,EAAEC,cAAc,IAAIC,eAAe,QAAQ,KAAK;AACvP,SAASC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACvH,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGH,eAAe,CAAC,OAAO,CAAC;AAC5C,MAAMI,UAAU,GAAG;EACjBC,GAAG,EAAEC,MAAM;EACXC,GAAG,EAAED,MAAM;EACXE,GAAG,EAAEF,MAAM;EACXG,QAAQ,EAAEH,MAAM;EAChBI,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAED,OAAO;EACdE,KAAK,EAAEf,WAAW;EAClBgB,MAAM,EAAEhB,WAAW;EACnBiB,MAAM,EAAEjB,WAAW;EACnBkB,QAAQ,EAAEL,OAAO;EACjBM,QAAQ,EAAEnB,WAAW;EACrBoB,SAAS,EAAErB,SAAS;EACpBsB,SAAS,EAAEpB,cAAc,CAAC,YAAY,CAAC;EACvCqB,UAAU,EAAEd,MAAM;EAClBe,WAAW,EAAExB,SAAS;EACtByB,WAAW,EAAEvB,cAAc,CAAC,OAAO,CAAC;EACpCwB,WAAW,EAAEjB,MAAM;EACnBkB,cAAc,EAAElB,MAAM;EACtBmB,QAAQ,EAAEnB;AACZ,CAAC;AACD,IAAIoB,aAAa,GAAG1C,eAAe,CAAC;EAClCkB,IAAI;EACJyB,KAAK,EAAEvB,UAAU;EACjBwB,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EACxBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGtD,GAAG,CAAC,KAAK,CAAC;IACxB,MAAMuD,OAAO,GAAGvD,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMwD,QAAQ,GAAGxD,GAAG,CAAC,CAAC;IACtB,MAAM;MACJyD;IACF,CAAC,GAAGlD,kBAAkB,CAAC,CAAC,CAACmD,KAAK;IAC9B,MAAMC,KAAK,GAAGzD,QAAQ,CAAC,MAAM;MAC3B,MAAM0D,MAAM,GAAG;QACbzB,KAAK,EAAElB,OAAO,CAACgC,KAAK,CAACd,KAAK,CAAC;QAC3BC,MAAM,EAAEnB,OAAO,CAACgC,KAAK,CAACb,MAAM;MAC9B,CAAC;MACD,IAAIpB,KAAK,CAACiC,KAAK,CAACZ,MAAM,CAAC,EAAE;QACvBuB,MAAM,CAACC,QAAQ,GAAG,QAAQ;QAC1BD,MAAM,CAACE,YAAY,GAAG7C,OAAO,CAACgC,KAAK,CAACZ,MAAM,CAAC;MAC7C;MACA,OAAOuB,MAAM;IACf,CAAC,CAAC;IACF3D,KAAK,CAAC,MAAMgD,KAAK,CAACtB,GAAG,EAAE,MAAM;MAC3B2B,KAAK,CAACS,KAAK,GAAG,KAAK;MACnBR,OAAO,CAACQ,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC;IACF,MAAMC,MAAM,GAAIC,KAAK,IAAK;MACxB,IAAIV,OAAO,CAACQ,KAAK,EAAE;QACjBR,OAAO,CAACQ,KAAK,GAAG,KAAK;QACrBX,IAAI,CAAC,MAAM,EAAEa,KAAK,CAAC;MACrB;IACF,CAAC;IACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,SAAS,GAAG,IAAIC,KAAK,CAAC,MAAM,CAAC;MACnCC,MAAM,CAACC,cAAc,CAACH,SAAS,EAAE,QAAQ,EAAE;QACzCJ,KAAK,EAAEP,QAAQ,CAACO,KAAK;QACrBQ,UAAU,EAAE;MACd,CAAC,CAAC;MACFP,MAAM,CAACG,SAAS,CAAC;IACnB,CAAC;IACD,MAAMK,OAAO,GAAIP,KAAK,IAAK;MACzBX,KAAK,CAACS,KAAK,GAAG,IAAI;MAClBR,OAAO,CAACQ,KAAK,GAAG,KAAK;MACrBX,IAAI,CAAC,OAAO,EAAEa,KAAK,CAAC;IACtB,CAAC;IACD,MAAMQ,UAAU,GAAGA,CAACC,KAAK,EAAEC,SAAS,EAAEC,IAAI,KAAK;MAC7C,IAAIA,IAAI,EAAE;QACR,OAAOA,IAAI,CAAC,CAAC;MACf;MACA,OAAOnE,YAAY,CAACc,IAAI,EAAE;QACxB,MAAM,EAAEmD,KAAK;QACb,MAAM,EAAEzB,KAAK,CAACV,QAAQ;QACtB,OAAO,EAAEoC,SAAS;QAClB,aAAa,EAAE1B,KAAK,CAACP;MACvB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAItB,OAAO,CAACQ,KAAK,IAAId,KAAK,CAACN,WAAW,EAAE;QACtC,OAAOlC,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEgB,GAAG,CAAC,SAAS;QACxB,CAAC,EAAE,CAACgD,UAAU,CAACxB,KAAK,CAACL,WAAW,EAAEnB,GAAG,CAAC,cAAc,CAAC,EAAE4B,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;MACzE;MACA,IAAID,KAAK,CAACS,KAAK,IAAId,KAAK,CAACT,SAAS,EAAE;QAClC,OAAO/B,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEgB,GAAG,CAAC,OAAO;QACtB,CAAC,EAAE,CAACgD,UAAU,CAACxB,KAAK,CAACR,SAAS,EAAEhB,GAAG,CAAC,YAAY,CAAC,EAAE4B,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;MACnE;IACF,CAAC;IACD,MAAMwB,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIxB,KAAK,CAACS,KAAK,IAAI,CAACd,KAAK,CAACtB,GAAG,EAAE;QAC7B;MACF;MACA,MAAMoD,KAAK,GAAG;QACZlD,GAAG,EAAEoB,KAAK,CAACpB,GAAG;QACdmD,KAAK,EAAEvD,GAAG,CAAC,KAAK,CAAC;QACjBsB,QAAQ,EAAEE,KAAK,CAACF,QAAQ;QACxBY,KAAK,EAAE;UACLsB,SAAS,EAAEhC,KAAK,CAACnB,GAAG;UACpBoD,cAAc,EAAEjC,KAAK,CAAClB;QACxB,CAAC;QACDc,WAAW,EAAEI,KAAK,CAACJ,WAAW;QAC9BC,cAAc,EAAEG,KAAK,CAACH;MACxB,CAAC;MACD,IAAIG,KAAK,CAACX,QAAQ,EAAE;QAClB,OAAOvB,eAAe,CAACN,YAAY,CAAC,KAAK,EAAEI,WAAW,CAAC;UACrD,KAAK,EAAE2C;QACT,CAAC,EAAEuB,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAACpE,iBAAiB,CAAC,MAAM,CAAC,EAAEsC,KAAK,CAACtB,GAAG,CAAC,CAAC,CAAC;MAC7D;MACA,OAAOlB,YAAY,CAAC,KAAK,EAAEI,WAAW,CAAC;QACrC,KAAK,EAAE2C,QAAQ;QACf,KAAK,EAAEP,KAAK,CAACtB,GAAG;QAChB,QAAQ,EAAEqC,MAAM;QAChB,SAAS,EAAEQ;MACb,CAAC,EAAEO,KAAK,CAAC,EAAE,IAAI,CAAC;IAClB,CAAC;IACD,MAAMI,YAAY,GAAGA,CAAC;MACpBC;IACF,CAAC,KAAK;MACJ,MAAMC,KAAK,GAAGA,CAAA,KAAM;QAClB,IAAID,EAAE,KAAK5B,QAAQ,CAACO,KAAK,IAAIR,OAAO,CAACQ,KAAK,EAAE;UAC1CG,WAAW,CAAC,CAAC;QACf;MACF,CAAC;MACD,IAAIV,QAAQ,CAACO,KAAK,EAAE;QAClBsB,KAAK,CAAC,CAAC;MACT,CAAC,MAAM;QACLlF,QAAQ,CAACkF,KAAK,CAAC;MACjB;IACF,CAAC;IACD,MAAMC,eAAe,GAAGA,CAAC;MACvBF;IACF,CAAC,KAAK;MACJ,IAAIA,EAAE,KAAK5B,QAAQ,CAACO,KAAK,IAAI,CAACT,KAAK,CAACS,KAAK,EAAE;QACzCS,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IACD,IAAIf,SAAS,IAAIvC,SAAS,EAAE;MAC1BuC,SAAS,CAAC8B,GAAG,CAAC,QAAQ,EAAEJ,YAAY,CAAC;MACrC1B,SAAS,CAAC8B,GAAG,CAAC,OAAO,EAAED,eAAe,CAAC;MACvCjF,eAAe,CAAC,MAAM;QACpBoD,SAAS,CAAC+B,IAAI,CAAC,QAAQ,EAAEL,YAAY,CAAC;QACtC1B,SAAS,CAAC+B,IAAI,CAAC,OAAO,EAAEF,eAAe,CAAC;MAC1C,CAAC,CAAC;IACJ;IACAlF,SAAS,CAAC,MAAM;MACdD,QAAQ,CAAC,MAAM;QACb,IAAIsF,EAAE;QACN,IAAI,CAAC,CAACA,EAAE,GAAGjC,QAAQ,CAACO,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0B,EAAE,CAACC,QAAQ,KAAK,CAACzC,KAAK,CAACX,QAAQ,EAAE;UAC7E4B,WAAW,CAAC,CAAC;QACf;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIuB,EAAE;MACN,OAAOhF,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEgB,GAAG,CAAC;UACXO,KAAK,EAAEiB,KAAK,CAACjB,KAAK;UAClBE,KAAK,EAAEe,KAAK,CAACf;QACf,CAAC,CAAC;QACF,OAAO,EAAEyB,KAAK,CAACI;MACjB,CAAC,EAAE,CAACe,WAAW,CAAC,CAAC,EAAED,iBAAiB,CAAC,CAAC,EAAE,CAACY,EAAE,GAAGpC,KAAK,CAACsC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,IAAI,CAACvC,KAAK,CAAC,CAAC,CAAC;IAClG,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEL,aAAa,IAAI2C,OAAO,EACxBjE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}