{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, defineComponent, createVNode as _createVNode, Fragment as _Fragment } from \"vue\";\nimport { createNamespace, numericProp } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Field } from \"../field/index.mjs\";\nconst [name, bem] = createNamespace(\"address-edit-detail\");\nconst t = createNamespace(\"address-edit\")[2];\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    show: Boolean,\n    rows: numericProp,\n    value: String,\n    rules: Array,\n    focused: Boolean,\n    maxlength: numericProp,\n    searchResult: Array,\n    showSearchResult: Boolean\n  },\n  emits: [\"blur\", \"focus\", \"input\", \"selectSearch\"],\n  setup(props, {\n    emit\n  }) {\n    const field = ref();\n    const showSearchResult = () => props.focused && props.searchResult && props.showSearchResult;\n    const onSelect = express => {\n      emit(\"selectSearch\", express);\n      emit(\"input\", `${express.address || \"\"} ${express.name || \"\"}`.trim());\n    };\n    const renderSearchResult = () => {\n      if (!showSearchResult()) {\n        return;\n      }\n      const {\n        searchResult\n      } = props;\n      return searchResult.map(express => _createVNode(Cell, {\n        \"clickable\": true,\n        \"key\": (express.name || \"\") + (express.address || \"\"),\n        \"icon\": \"location-o\",\n        \"title\": express.name,\n        \"label\": express.address,\n        \"class\": bem(\"search-item\"),\n        \"border\": false,\n        \"onClick\": () => onSelect(express)\n      }, null));\n    };\n    const onBlur = event => emit(\"blur\", event);\n    const onFocus = event => emit(\"focus\", event);\n    const onInput = value => emit(\"input\", value);\n    return () => {\n      if (props.show) {\n        return _createVNode(_Fragment, null, [_createVNode(Field, {\n          \"autosize\": true,\n          \"clearable\": true,\n          \"ref\": field,\n          \"class\": bem(),\n          \"rows\": props.rows,\n          \"type\": \"textarea\",\n          \"rules\": props.rules,\n          \"label\": t(\"addressDetail\"),\n          \"border\": !showSearchResult(),\n          \"maxlength\": props.maxlength,\n          \"modelValue\": props.value,\n          \"placeholder\": t(\"addressDetail\"),\n          \"onBlur\": onBlur,\n          \"onFocus\": onFocus,\n          \"onUpdate:modelValue\": onInput\n        }, null), renderSearchResult()]);\n      }\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["ref", "defineComponent", "createVNode", "_createVNode", "Fragment", "_Fragment", "createNamespace", "numericProp", "Cell", "Field", "name", "bem", "t", "stdin_default", "props", "show", "Boolean", "rows", "value", "String", "rules", "Array", "focused", "maxlength", "searchResult", "showSearchResult", "emits", "setup", "emit", "field", "onSelect", "express", "address", "trim", "renderSearchResult", "map", "onClick", "onBlur", "event", "onFocus", "onInput", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/address-edit/AddressEditDetail.mjs"], "sourcesContent": ["import { ref, defineComponent, createVNode as _createVNode, Fragment as _Fragment } from \"vue\";\nimport { createNamespace, numericProp } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Field } from \"../field/index.mjs\";\nconst [name, bem] = createNamespace(\"address-edit-detail\");\nconst t = createNamespace(\"address-edit\")[2];\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    show: Boolean,\n    rows: numericProp,\n    value: String,\n    rules: Array,\n    focused: Boolean,\n    maxlength: numericProp,\n    searchResult: Array,\n    showSearchResult: Boolean\n  },\n  emits: [\"blur\", \"focus\", \"input\", \"selectSearch\"],\n  setup(props, {\n    emit\n  }) {\n    const field = ref();\n    const showSearchResult = () => props.focused && props.searchResult && props.showSearchResult;\n    const onSelect = (express) => {\n      emit(\"selectSearch\", express);\n      emit(\"input\", `${express.address || \"\"} ${express.name || \"\"}`.trim());\n    };\n    const renderSearchResult = () => {\n      if (!showSearchResult()) {\n        return;\n      }\n      const {\n        searchResult\n      } = props;\n      return searchResult.map((express) => _createVNode(Cell, {\n        \"clickable\": true,\n        \"key\": (express.name || \"\") + (express.address || \"\"),\n        \"icon\": \"location-o\",\n        \"title\": express.name,\n        \"label\": express.address,\n        \"class\": bem(\"search-item\"),\n        \"border\": false,\n        \"onClick\": () => onSelect(express)\n      }, null));\n    };\n    const onBlur = (event) => emit(\"blur\", event);\n    const onFocus = (event) => emit(\"focus\", event);\n    const onInput = (value) => emit(\"input\", value);\n    return () => {\n      if (props.show) {\n        return _createVNode(_Fragment, null, [_createVNode(Field, {\n          \"autosize\": true,\n          \"clearable\": true,\n          \"ref\": field,\n          \"class\": bem(),\n          \"rows\": props.rows,\n          \"type\": \"textarea\",\n          \"rules\": props.rules,\n          \"label\": t(\"addressDetail\"),\n          \"border\": !showSearchResult(),\n          \"maxlength\": props.maxlength,\n          \"modelValue\": props.value,\n          \"placeholder\": t(\"addressDetail\"),\n          \"onBlur\": onBlur,\n          \"onFocus\": onFocus,\n          \"onUpdate:modelValue\": onInput\n        }, null), renderSearchResult()]);\n      }\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": ";;AAAA,SAASA,GAAG,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,EAAEC,QAAQ,IAAIC,SAAS,QAAQ,KAAK;AAC9F,SAASC,eAAe,EAAEC,WAAW,QAAQ,oBAAoB;AACjE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,qBAAqB,CAAC;AAC1D,MAAMM,CAAC,GAAGN,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAC5C,IAAIO,aAAa,GAAGZ,eAAe,CAAC;EAClCS,IAAI;EACJI,KAAK,EAAE;IACLC,IAAI,EAAEC,OAAO;IACbC,IAAI,EAAEV,WAAW;IACjBW,KAAK,EAAEC,MAAM;IACbC,KAAK,EAAEC,KAAK;IACZC,OAAO,EAAEN,OAAO;IAChBO,SAAS,EAAEhB,WAAW;IACtBiB,YAAY,EAAEH,KAAK;IACnBI,gBAAgB,EAAET;EACpB,CAAC;EACDU,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,CAAC;EACjDC,KAAKA,CAACb,KAAK,EAAE;IACXc;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAG7B,GAAG,CAAC,CAAC;IACnB,MAAMyB,gBAAgB,GAAGA,CAAA,KAAMX,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACU,YAAY,IAAIV,KAAK,CAACW,gBAAgB;IAC5F,MAAMK,QAAQ,GAAIC,OAAO,IAAK;MAC5BH,IAAI,CAAC,cAAc,EAAEG,OAAO,CAAC;MAC7BH,IAAI,CAAC,OAAO,EAAE,GAAGG,OAAO,CAACC,OAAO,IAAI,EAAE,IAAID,OAAO,CAACrB,IAAI,IAAI,EAAE,EAAE,CAACuB,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IACD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAI,CAACT,gBAAgB,CAAC,CAAC,EAAE;QACvB;MACF;MACA,MAAM;QACJD;MACF,CAAC,GAAGV,KAAK;MACT,OAAOU,YAAY,CAACW,GAAG,CAAEJ,OAAO,IAAK5B,YAAY,CAACK,IAAI,EAAE;QACtD,WAAW,EAAE,IAAI;QACjB,KAAK,EAAE,CAACuB,OAAO,CAACrB,IAAI,IAAI,EAAE,KAAKqB,OAAO,CAACC,OAAO,IAAI,EAAE,CAAC;QACrD,MAAM,EAAE,YAAY;QACpB,OAAO,EAAED,OAAO,CAACrB,IAAI;QACrB,OAAO,EAAEqB,OAAO,CAACC,OAAO;QACxB,OAAO,EAAErB,GAAG,CAAC,aAAa,CAAC;QAC3B,QAAQ,EAAE,KAAK;QACf,SAAS,EAAEyB,CAAA,KAAMN,QAAQ,CAACC,OAAO;MACnC,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IACD,MAAMM,MAAM,GAAIC,KAAK,IAAKV,IAAI,CAAC,MAAM,EAAEU,KAAK,CAAC;IAC7C,MAAMC,OAAO,GAAID,KAAK,IAAKV,IAAI,CAAC,OAAO,EAAEU,KAAK,CAAC;IAC/C,MAAME,OAAO,GAAItB,KAAK,IAAKU,IAAI,CAAC,OAAO,EAAEV,KAAK,CAAC;IAC/C,OAAO,MAAM;MACX,IAAIJ,KAAK,CAACC,IAAI,EAAE;QACd,OAAOZ,YAAY,CAACE,SAAS,EAAE,IAAI,EAAE,CAACF,YAAY,CAACM,KAAK,EAAE;UACxD,UAAU,EAAE,IAAI;UAChB,WAAW,EAAE,IAAI;UACjB,KAAK,EAAEoB,KAAK;UACZ,OAAO,EAAElB,GAAG,CAAC,CAAC;UACd,MAAM,EAAEG,KAAK,CAACG,IAAI;UAClB,MAAM,EAAE,UAAU;UAClB,OAAO,EAAEH,KAAK,CAACM,KAAK;UACpB,OAAO,EAAER,CAAC,CAAC,eAAe,CAAC;UAC3B,QAAQ,EAAE,CAACa,gBAAgB,CAAC,CAAC;UAC7B,WAAW,EAAEX,KAAK,CAACS,SAAS;UAC5B,YAAY,EAAET,KAAK,CAACI,KAAK;UACzB,aAAa,EAAEN,CAAC,CAAC,eAAe,CAAC;UACjC,QAAQ,EAAEyB,MAAM;UAChB,SAAS,EAAEE,OAAO;UAClB,qBAAqB,EAAEC;QACzB,CAAC,EAAE,IAAI,CAAC,EAAEN,kBAAkB,CAAC,CAAC,CAAC,CAAC;MAClC;IACF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACErB,aAAa,IAAI4B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}