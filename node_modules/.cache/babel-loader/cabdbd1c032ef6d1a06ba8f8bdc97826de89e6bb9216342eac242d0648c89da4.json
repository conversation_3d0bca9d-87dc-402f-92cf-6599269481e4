{"ast": null, "code": "import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"progress\");\nconst progressProps = {\n  color: String,\n  inactive: Boolean,\n  pivotText: String,\n  textColor: String,\n  showPivot: truthProp,\n  pivotColor: String,\n  trackColor: String,\n  strokeWidth: numericProp,\n  percentage: {\n    type: numericProp,\n    default: 0,\n    validator: value => +value >= 0 && +value <= 100\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: progressProps,\n  setup(props) {\n    const background = computed(() => props.inactive ? void 0 : props.color);\n    const format = rate => Math.min(Math.max(+rate, 0), 100);\n    const renderPivot = () => {\n      const {\n        textColor,\n        pivotText,\n        pivotColor,\n        percentage\n      } = props;\n      const safePercentage = format(percentage);\n      const text = pivotText != null ? pivotText : `${percentage}%`;\n      if (props.showPivot && text) {\n        const style = {\n          color: textColor,\n          left: `${safePercentage}%`,\n          transform: `translate(-${safePercentage}%,-50%)`,\n          background: pivotColor || background.value\n        };\n        return _createVNode(\"span\", {\n          \"style\": style,\n          \"class\": bem(\"pivot\", {\n            inactive: props.inactive\n          })\n        }, [text]);\n      }\n    };\n    return () => {\n      const {\n        trackColor,\n        percentage,\n        strokeWidth\n      } = props;\n      const safePercentage = format(percentage);\n      const rootStyle = {\n        background: trackColor,\n        height: addUnit(strokeWidth)\n      };\n      const portionStyle = {\n        width: `${safePercentage}%`,\n        background: background.value\n      };\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"style\": rootStyle\n      }, [_createVNode(\"span\", {\n        \"class\": bem(\"portion\", {\n          inactive: props.inactive\n        }),\n        \"style\": portionStyle\n      }, null), renderPivot()]);\n    };\n  }\n});\nexport { stdin_default as default, progressProps };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "addUnit", "truthProp", "numericProp", "createNamespace", "name", "bem", "progressProps", "color", "String", "inactive", "Boolean", "pivotText", "textColor", "showPivot", "pivotColor", "trackColor", "strokeWidth", "percentage", "type", "default", "validator", "value", "stdin_default", "props", "setup", "background", "format", "rate", "Math", "min", "max", "renderPivot", "safePercentage", "text", "style", "left", "transform", "rootStyle", "height", "portionStyle", "width"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/progress/Progress.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"progress\");\nconst progressProps = {\n  color: String,\n  inactive: Boolean,\n  pivotText: String,\n  textColor: String,\n  showPivot: truthProp,\n  pivotColor: String,\n  trackColor: String,\n  strokeWidth: numericProp,\n  percentage: {\n    type: numericProp,\n    default: 0,\n    validator: (value) => +value >= 0 && +value <= 100\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: progressProps,\n  setup(props) {\n    const background = computed(() => props.inactive ? void 0 : props.color);\n    const format = (rate) => Math.min(Math.max(+rate, 0), 100);\n    const renderPivot = () => {\n      const {\n        textColor,\n        pivotText,\n        pivotColor,\n        percentage\n      } = props;\n      const safePercentage = format(percentage);\n      const text = pivotText != null ? pivotText : `${percentage}%`;\n      if (props.showPivot && text) {\n        const style = {\n          color: textColor,\n          left: `${safePercentage}%`,\n          transform: `translate(-${safePercentage}%,-50%)`,\n          background: pivotColor || background.value\n        };\n        return _createVNode(\"span\", {\n          \"style\": style,\n          \"class\": bem(\"pivot\", {\n            inactive: props.inactive\n          })\n        }, [text]);\n      }\n    };\n    return () => {\n      const {\n        trackColor,\n        percentage,\n        strokeWidth\n      } = props;\n      const safePercentage = format(percentage);\n      const rootStyle = {\n        background: trackColor,\n        height: addUnit(strokeWidth)\n      };\n      const portionStyle = {\n        width: `${safePercentage}%`,\n        background: background.value\n      };\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"style\": rootStyle\n      }, [_createVNode(\"span\", {\n        \"class\": bem(\"portion\", {\n          inactive: props.inactive\n        }),\n        \"style\": portionStyle\n      }, null), renderPivot()]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  progressProps\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AACrF,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGF,eAAe,CAAC,UAAU,CAAC;AAC/C,MAAMG,aAAa,GAAG;EACpBC,KAAK,EAAEC,MAAM;EACbC,QAAQ,EAAEC,OAAO;EACjBC,SAAS,EAAEH,MAAM;EACjBI,SAAS,EAAEJ,MAAM;EACjBK,SAAS,EAAEZ,SAAS;EACpBa,UAAU,EAAEN,MAAM;EAClBO,UAAU,EAAEP,MAAM;EAClBQ,WAAW,EAAEd,WAAW;EACxBe,UAAU,EAAE;IACVC,IAAI,EAAEhB,WAAW;IACjBiB,OAAO,EAAE,CAAC;IACVC,SAAS,EAAGC,KAAK,IAAK,CAACA,KAAK,IAAI,CAAC,IAAI,CAACA,KAAK,IAAI;EACjD;AACF,CAAC;AACD,IAAIC,aAAa,GAAGzB,eAAe,CAAC;EAClCO,IAAI;EACJmB,KAAK,EAAEjB,aAAa;EACpBkB,KAAKA,CAACD,KAAK,EAAE;IACX,MAAME,UAAU,GAAG7B,QAAQ,CAAC,MAAM2B,KAAK,CAACd,QAAQ,GAAG,KAAK,CAAC,GAAGc,KAAK,CAAChB,KAAK,CAAC;IACxE,MAAMmB,MAAM,GAAIC,IAAI,IAAKC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAACH,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMI,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAM;QACJnB,SAAS;QACTD,SAAS;QACTG,UAAU;QACVG;MACF,CAAC,GAAGM,KAAK;MACT,MAAMS,cAAc,GAAGN,MAAM,CAACT,UAAU,CAAC;MACzC,MAAMgB,IAAI,GAAGtB,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,GAAGM,UAAU,GAAG;MAC7D,IAAIM,KAAK,CAACV,SAAS,IAAIoB,IAAI,EAAE;QAC3B,MAAMC,KAAK,GAAG;UACZ3B,KAAK,EAAEK,SAAS;UAChBuB,IAAI,EAAE,GAAGH,cAAc,GAAG;UAC1BI,SAAS,EAAE,cAAcJ,cAAc,SAAS;UAChDP,UAAU,EAAEX,UAAU,IAAIW,UAAU,CAACJ;QACvC,CAAC;QACD,OAAOtB,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEmC,KAAK;UACd,OAAO,EAAE7B,GAAG,CAAC,OAAO,EAAE;YACpBI,QAAQ,EAAEc,KAAK,CAACd;UAClB,CAAC;QACH,CAAC,EAAE,CAACwB,IAAI,CAAC,CAAC;MACZ;IACF,CAAC;IACD,OAAO,MAAM;MACX,MAAM;QACJlB,UAAU;QACVE,UAAU;QACVD;MACF,CAAC,GAAGO,KAAK;MACT,MAAMS,cAAc,GAAGN,MAAM,CAACT,UAAU,CAAC;MACzC,MAAMoB,SAAS,GAAG;QAChBZ,UAAU,EAAEV,UAAU;QACtBuB,MAAM,EAAEtC,OAAO,CAACgB,WAAW;MAC7B,CAAC;MACD,MAAMuB,YAAY,GAAG;QACnBC,KAAK,EAAE,GAAGR,cAAc,GAAG;QAC3BP,UAAU,EAAEA,UAAU,CAACJ;MACzB,CAAC;MACD,OAAOtB,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEM,GAAG,CAAC,CAAC;QACd,OAAO,EAAEgC;MACX,CAAC,EAAE,CAACtC,YAAY,CAAC,MAAM,EAAE;QACvB,OAAO,EAAEM,GAAG,CAAC,SAAS,EAAE;UACtBI,QAAQ,EAAEc,KAAK,CAACd;QAClB,CAAC,CAAC;QACF,OAAO,EAAE8B;MACX,CAAC,EAAE,IAAI,CAAC,EAAER,WAAW,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACET,aAAa,IAAIH,OAAO,EACxBb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}