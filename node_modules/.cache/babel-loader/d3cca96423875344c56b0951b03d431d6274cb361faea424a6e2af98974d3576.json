{"ast": null, "code": "import { watch, onBeforeUnmount, onDeactivated } from \"vue\";\nimport { getScrollParent, onMountedOrActivated } from \"@vant/use\";\nimport { useTouch } from \"./use-touch.mjs\";\nimport { preventDefault } from \"../utils/index.mjs\";\nlet totalLockCount = 0;\nconst BODY_LOCK_CLASS = \"van-overflow-hidden\";\nfunction useLockScroll(rootRef, shouldLock) {\n  const touch = useTouch();\n  const DIRECTION_UP = \"01\";\n  const DIRECTION_DOWN = \"10\";\n  const onTouchMove = event => {\n    touch.move(event);\n    const direction = touch.deltaY.value > 0 ? DIRECTION_DOWN : DIRECTION_UP;\n    const el = getScrollParent(event.target, rootRef.value);\n    const {\n      scrollHeight,\n      offsetHeight,\n      scrollTop\n    } = el;\n    let status = \"11\";\n    if (scrollTop === 0) {\n      status = offsetHeight >= scrollHeight ? \"00\" : \"01\";\n    } else if (scrollTop + offsetHeight >= scrollHeight) {\n      status = \"10\";\n    }\n    if (status !== \"11\" && touch.isVertical() && !(parseInt(status, 2) & parseInt(direction, 2))) {\n      preventDefault(event, true);\n    }\n  };\n  const lock = () => {\n    document.addEventListener(\"touchstart\", touch.start);\n    document.addEventListener(\"touchmove\", onTouchMove, {\n      passive: false\n    });\n    if (!totalLockCount) {\n      document.body.classList.add(BODY_LOCK_CLASS);\n    }\n    totalLockCount++;\n  };\n  const unlock = () => {\n    if (totalLockCount) {\n      document.removeEventListener(\"touchstart\", touch.start);\n      document.removeEventListener(\"touchmove\", onTouchMove);\n      totalLockCount--;\n      if (!totalLockCount) {\n        document.body.classList.remove(BODY_LOCK_CLASS);\n      }\n    }\n  };\n  const init = () => shouldLock() && lock();\n  const destroy = () => shouldLock() && unlock();\n  onMountedOrActivated(init);\n  onDeactivated(destroy);\n  onBeforeUnmount(destroy);\n  watch(shouldLock, value => {\n    value ? lock() : unlock();\n  });\n}\nexport { useLockScroll };", "map": {"version": 3, "names": ["watch", "onBeforeUnmount", "onDeactivated", "getScrollParent", "onMountedOrActivated", "useTouch", "preventDefault", "totalLockCount", "BODY_LOCK_CLASS", "useLockScroll", "rootRef", "shouldLock", "touch", "DIRECTION_UP", "DIRECTION_DOWN", "onTouchMove", "event", "move", "direction", "deltaY", "value", "el", "target", "scrollHeight", "offsetHeight", "scrollTop", "status", "isVertical", "parseInt", "lock", "document", "addEventListener", "start", "passive", "body", "classList", "add", "unlock", "removeEventListener", "remove", "init", "destroy"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-lock-scroll.mjs"], "sourcesContent": ["import { watch, onBeforeUnmount, onDeactivated } from \"vue\";\nimport { getScrollParent, onMountedOrActivated } from \"@vant/use\";\nimport { useTouch } from \"./use-touch.mjs\";\nimport { preventDefault } from \"../utils/index.mjs\";\nlet totalLockCount = 0;\nconst BODY_LOCK_CLASS = \"van-overflow-hidden\";\nfunction useLockScroll(rootRef, shouldLock) {\n  const touch = useTouch();\n  const DIRECTION_UP = \"01\";\n  const DIRECTION_DOWN = \"10\";\n  const onTouchMove = (event) => {\n    touch.move(event);\n    const direction = touch.deltaY.value > 0 ? DIRECTION_DOWN : DIRECTION_UP;\n    const el = getScrollParent(\n      event.target,\n      rootRef.value\n    );\n    const { scrollHeight, offsetHeight, scrollTop } = el;\n    let status = \"11\";\n    if (scrollTop === 0) {\n      status = offsetHeight >= scrollHeight ? \"00\" : \"01\";\n    } else if (scrollTop + offsetHeight >= scrollHeight) {\n      status = \"10\";\n    }\n    if (status !== \"11\" && touch.isVertical() && !(parseInt(status, 2) & parseInt(direction, 2))) {\n      preventDefault(event, true);\n    }\n  };\n  const lock = () => {\n    document.addEventListener(\"touchstart\", touch.start);\n    document.addEventListener(\"touchmove\", onTouchMove, { passive: false });\n    if (!totalLockCount) {\n      document.body.classList.add(BODY_LOCK_CLASS);\n    }\n    totalLockCount++;\n  };\n  const unlock = () => {\n    if (totalLockCount) {\n      document.removeEventListener(\"touchstart\", touch.start);\n      document.removeEventListener(\"touchmove\", onTouchMove);\n      totalLockCount--;\n      if (!totalLockCount) {\n        document.body.classList.remove(BODY_LOCK_CLASS);\n      }\n    }\n  };\n  const init = () => shouldLock() && lock();\n  const destroy = () => shouldLock() && unlock();\n  onMountedOrActivated(init);\n  onDeactivated(destroy);\n  onBeforeUnmount(destroy);\n  watch(shouldLock, (value) => {\n    value ? lock() : unlock();\n  });\n}\nexport {\n  useLockScroll\n};\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,eAAe,EAAEC,aAAa,QAAQ,KAAK;AAC3D,SAASC,eAAe,EAAEC,oBAAoB,QAAQ,WAAW;AACjE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,IAAIC,cAAc,GAAG,CAAC;AACtB,MAAMC,eAAe,GAAG,qBAAqB;AAC7C,SAASC,aAAaA,CAACC,OAAO,EAAEC,UAAU,EAAE;EAC1C,MAAMC,KAAK,GAAGP,QAAQ,CAAC,CAAC;EACxB,MAAMQ,YAAY,GAAG,IAAI;EACzB,MAAMC,cAAc,GAAG,IAAI;EAC3B,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC7BJ,KAAK,CAACK,IAAI,CAACD,KAAK,CAAC;IACjB,MAAME,SAAS,GAAGN,KAAK,CAACO,MAAM,CAACC,KAAK,GAAG,CAAC,GAAGN,cAAc,GAAGD,YAAY;IACxE,MAAMQ,EAAE,GAAGlB,eAAe,CACxBa,KAAK,CAACM,MAAM,EACZZ,OAAO,CAACU,KACV,CAAC;IACD,MAAM;MAAEG,YAAY;MAAEC,YAAY;MAAEC;IAAU,CAAC,GAAGJ,EAAE;IACpD,IAAIK,MAAM,GAAG,IAAI;IACjB,IAAID,SAAS,KAAK,CAAC,EAAE;MACnBC,MAAM,GAAGF,YAAY,IAAID,YAAY,GAAG,IAAI,GAAG,IAAI;IACrD,CAAC,MAAM,IAAIE,SAAS,GAAGD,YAAY,IAAID,YAAY,EAAE;MACnDG,MAAM,GAAG,IAAI;IACf;IACA,IAAIA,MAAM,KAAK,IAAI,IAAId,KAAK,CAACe,UAAU,CAAC,CAAC,IAAI,EAAEC,QAAQ,CAACF,MAAM,EAAE,CAAC,CAAC,GAAGE,QAAQ,CAACV,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE;MAC5FZ,cAAc,CAACU,KAAK,EAAE,IAAI,CAAC;IAC7B;EACF,CAAC;EACD,MAAMa,IAAI,GAAGA,CAAA,KAAM;IACjBC,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAEnB,KAAK,CAACoB,KAAK,CAAC;IACpDF,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEhB,WAAW,EAAE;MAAEkB,OAAO,EAAE;IAAM,CAAC,CAAC;IACvE,IAAI,CAAC1B,cAAc,EAAE;MACnBuB,QAAQ,CAACI,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC5B,eAAe,CAAC;IAC9C;IACAD,cAAc,EAAE;EAClB,CAAC;EACD,MAAM8B,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI9B,cAAc,EAAE;MAClBuB,QAAQ,CAACQ,mBAAmB,CAAC,YAAY,EAAE1B,KAAK,CAACoB,KAAK,CAAC;MACvDF,QAAQ,CAACQ,mBAAmB,CAAC,WAAW,EAAEvB,WAAW,CAAC;MACtDR,cAAc,EAAE;MAChB,IAAI,CAACA,cAAc,EAAE;QACnBuB,QAAQ,CAACI,IAAI,CAACC,SAAS,CAACI,MAAM,CAAC/B,eAAe,CAAC;MACjD;IACF;EACF,CAAC;EACD,MAAMgC,IAAI,GAAGA,CAAA,KAAM7B,UAAU,CAAC,CAAC,IAAIkB,IAAI,CAAC,CAAC;EACzC,MAAMY,OAAO,GAAGA,CAAA,KAAM9B,UAAU,CAAC,CAAC,IAAI0B,MAAM,CAAC,CAAC;EAC9CjC,oBAAoB,CAACoC,IAAI,CAAC;EAC1BtC,aAAa,CAACuC,OAAO,CAAC;EACtBxC,eAAe,CAACwC,OAAO,CAAC;EACxBzC,KAAK,CAACW,UAAU,EAAGS,KAAK,IAAK;IAC3BA,KAAK,GAAGS,IAAI,CAAC,CAAC,GAAGQ,MAAM,CAAC,CAAC;EAC3B,CAAC,CAAC;AACJ;AACA,SACE5B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}