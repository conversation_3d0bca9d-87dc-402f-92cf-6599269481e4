{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, unknownProp } from \"../utils/index.mjs\";\nimport { Tag } from \"../tag/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Radio } from \"../radio/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { RadioGroup } from \"../radio-group/index.mjs\";\nconst [name, bem, t] = createNamespace(\"contact-list\");\nconst contactListProps = {\n  list: Array,\n  addText: String,\n  modelValue: unknownProp,\n  defaultTagText: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: contactListProps,\n  emits: [\"add\", \"edit\", \"select\", \"update:modelValue\"],\n  setup(props, {\n    emit\n  }) {\n    const renderItem = (item, index) => {\n      const onClick = () => {\n        emit(\"update:modelValue\", item.id);\n        emit(\"select\", item, index);\n      };\n      const renderRightIcon = () => _createVNode(Radio, {\n        \"class\": bem(\"radio\"),\n        \"name\": item.id,\n        \"iconSize\": 18\n      }, null);\n      const renderEditIcon = () => _createVNode(Icon, {\n        \"name\": \"edit\",\n        \"class\": bem(\"edit\"),\n        \"onClick\": event => {\n          event.stopPropagation();\n          emit(\"edit\", item, index);\n        }\n      }, null);\n      const renderContent = () => {\n        const nodes = [`${item.name}\\uFF0C${item.tel}`];\n        if (item.isDefault && props.defaultTagText) {\n          nodes.push(_createVNode(Tag, {\n            \"type\": \"primary\",\n            \"round\": true,\n            \"class\": bem(\"item-tag\")\n          }, {\n            default: () => [props.defaultTagText]\n          }));\n        }\n        return nodes;\n      };\n      return _createVNode(Cell, {\n        \"key\": item.id,\n        \"isLink\": true,\n        \"center\": true,\n        \"class\": bem(\"item\"),\n        \"titleClass\": bem(\"item-title\"),\n        \"onClick\": onClick\n      }, {\n        icon: renderEditIcon,\n        title: renderContent,\n        \"right-icon\": renderRightIcon\n      });\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [_createVNode(RadioGroup, {\n      \"modelValue\": props.modelValue,\n      \"class\": bem(\"group\")\n    }, {\n      default: () => [props.list && props.list.map(renderItem)]\n    }), _createVNode(\"div\", {\n      \"class\": [bem(\"bottom\"), \"van-safe-area-bottom\"]\n    }, [_createVNode(Button, {\n      \"round\": true,\n      \"block\": true,\n      \"type\": \"primary\",\n      \"class\": bem(\"add\"),\n      \"text\": props.addText || t(\"addContact\"),\n      \"onClick\": () => emit(\"add\")\n    }, null)])]);\n  }\n});\nexport { contactListProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "createNamespace", "unknownProp", "Tag", "Icon", "Cell", "Radio", "<PERSON><PERSON>", "RadioGroup", "name", "bem", "t", "contactListProps", "list", "Array", "addText", "String", "modelValue", "defaultTagText", "stdin_default", "props", "emits", "setup", "emit", "renderItem", "item", "index", "onClick", "id", "renderRightIcon", "renderEditIcon", "event", "stopPropagation", "renderContent", "nodes", "tel", "isDefault", "push", "default", "icon", "title", "map"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/contact-list/ContactList.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, unknownProp } from \"../utils/index.mjs\";\nimport { Tag } from \"../tag/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Radio } from \"../radio/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { RadioGroup } from \"../radio-group/index.mjs\";\nconst [name, bem, t] = createNamespace(\"contact-list\");\nconst contactListProps = {\n  list: Array,\n  addText: String,\n  modelValue: unknownProp,\n  defaultTagText: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: contactListProps,\n  emits: [\"add\", \"edit\", \"select\", \"update:modelValue\"],\n  setup(props, {\n    emit\n  }) {\n    const renderItem = (item, index) => {\n      const onClick = () => {\n        emit(\"update:modelValue\", item.id);\n        emit(\"select\", item, index);\n      };\n      const renderRightIcon = () => _createVNode(Radio, {\n        \"class\": bem(\"radio\"),\n        \"name\": item.id,\n        \"iconSize\": 18\n      }, null);\n      const renderEditIcon = () => _createVNode(Icon, {\n        \"name\": \"edit\",\n        \"class\": bem(\"edit\"),\n        \"onClick\": (event) => {\n          event.stopPropagation();\n          emit(\"edit\", item, index);\n        }\n      }, null);\n      const renderContent = () => {\n        const nodes = [`${item.name}\\uFF0C${item.tel}`];\n        if (item.isDefault && props.defaultTagText) {\n          nodes.push(_createVNode(Tag, {\n            \"type\": \"primary\",\n            \"round\": true,\n            \"class\": bem(\"item-tag\")\n          }, {\n            default: () => [props.defaultTagText]\n          }));\n        }\n        return nodes;\n      };\n      return _createVNode(Cell, {\n        \"key\": item.id,\n        \"isLink\": true,\n        \"center\": true,\n        \"class\": bem(\"item\"),\n        \"titleClass\": bem(\"item-title\"),\n        \"onClick\": onClick\n      }, {\n        icon: renderEditIcon,\n        title: renderContent,\n        \"right-icon\": renderRightIcon\n      });\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [_createVNode(RadioGroup, {\n      \"modelValue\": props.modelValue,\n      \"class\": bem(\"group\")\n    }, {\n      default: () => [props.list && props.list.map(renderItem)]\n    }), _createVNode(\"div\", {\n      \"class\": [bem(\"bottom\"), \"van-safe-area-bottom\"]\n    }, [_createVNode(Button, {\n      \"round\": true,\n      \"block\": true,\n      \"type\": \"primary\",\n      \"class\": bem(\"add\"),\n      \"text\": props.addText || t(\"addContact\"),\n      \"onClick\": () => emit(\"add\")\n    }, null)])]);\n  }\n});\nexport {\n  contactListProps,\n  stdin_default as default\n};\n"], "mappings": ";;;AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,eAAe,EAAEC,WAAW,QAAQ,oBAAoB;AACjE,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGV,eAAe,CAAC,cAAc,CAAC;AACtD,MAAMW,gBAAgB,GAAG;EACvBC,IAAI,EAAEC,KAAK;EACXC,OAAO,EAAEC,MAAM;EACfC,UAAU,EAAEf,WAAW;EACvBgB,cAAc,EAAEF;AAClB,CAAC;AACD,IAAIG,aAAa,GAAGrB,eAAe,CAAC;EAClCW,IAAI;EACJW,KAAK,EAAER,gBAAgB;EACvBS,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,mBAAmB,CAAC;EACrDC,KAAKA,CAACF,KAAK,EAAE;IACXG;EACF,CAAC,EAAE;IACD,MAAMC,UAAU,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;MAClC,MAAMC,OAAO,GAAGA,CAAA,KAAM;QACpBJ,IAAI,CAAC,mBAAmB,EAAEE,IAAI,CAACG,EAAE,CAAC;QAClCL,IAAI,CAAC,QAAQ,EAAEE,IAAI,EAAEC,KAAK,CAAC;MAC7B,CAAC;MACD,MAAMG,eAAe,GAAGA,CAAA,KAAM7B,YAAY,CAACM,KAAK,EAAE;QAChD,OAAO,EAAEI,GAAG,CAAC,OAAO,CAAC;QACrB,MAAM,EAAEe,IAAI,CAACG,EAAE;QACf,UAAU,EAAE;MACd,CAAC,EAAE,IAAI,CAAC;MACR,MAAME,cAAc,GAAGA,CAAA,KAAM9B,YAAY,CAACI,IAAI,EAAE;QAC9C,MAAM,EAAE,MAAM;QACd,OAAO,EAAEM,GAAG,CAAC,MAAM,CAAC;QACpB,SAAS,EAAGqB,KAAK,IAAK;UACpBA,KAAK,CAACC,eAAe,CAAC,CAAC;UACvBT,IAAI,CAAC,MAAM,EAAEE,IAAI,EAAEC,KAAK,CAAC;QAC3B;MACF,CAAC,EAAE,IAAI,CAAC;MACR,MAAMO,aAAa,GAAGA,CAAA,KAAM;QAC1B,MAAMC,KAAK,GAAG,CAAC,GAAGT,IAAI,CAAChB,IAAI,SAASgB,IAAI,CAACU,GAAG,EAAE,CAAC;QAC/C,IAAIV,IAAI,CAACW,SAAS,IAAIhB,KAAK,CAACF,cAAc,EAAE;UAC1CgB,KAAK,CAACG,IAAI,CAACrC,YAAY,CAACG,GAAG,EAAE;YAC3B,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,IAAI;YACb,OAAO,EAAEO,GAAG,CAAC,UAAU;UACzB,CAAC,EAAE;YACD4B,OAAO,EAAEA,CAAA,KAAM,CAAClB,KAAK,CAACF,cAAc;UACtC,CAAC,CAAC,CAAC;QACL;QACA,OAAOgB,KAAK;MACd,CAAC;MACD,OAAOlC,YAAY,CAACK,IAAI,EAAE;QACxB,KAAK,EAAEoB,IAAI,CAACG,EAAE;QACd,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI;QACd,OAAO,EAAElB,GAAG,CAAC,MAAM,CAAC;QACpB,YAAY,EAAEA,GAAG,CAAC,YAAY,CAAC;QAC/B,SAAS,EAAEiB;MACb,CAAC,EAAE;QACDY,IAAI,EAAET,cAAc;QACpBU,KAAK,EAAEP,aAAa;QACpB,YAAY,EAAEJ;MAChB,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,MAAM7B,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEU,GAAG,CAAC;IACf,CAAC,EAAE,CAACV,YAAY,CAACQ,UAAU,EAAE;MAC3B,YAAY,EAAEY,KAAK,CAACH,UAAU;MAC9B,OAAO,EAAEP,GAAG,CAAC,OAAO;IACtB,CAAC,EAAE;MACD4B,OAAO,EAAEA,CAAA,KAAM,CAAClB,KAAK,CAACP,IAAI,IAAIO,KAAK,CAACP,IAAI,CAAC4B,GAAG,CAACjB,UAAU,CAAC;IAC1D,CAAC,CAAC,EAAExB,YAAY,CAAC,KAAK,EAAE;MACtB,OAAO,EAAE,CAACU,GAAG,CAAC,QAAQ,CAAC,EAAE,sBAAsB;IACjD,CAAC,EAAE,CAACV,YAAY,CAACO,MAAM,EAAE;MACvB,OAAO,EAAE,IAAI;MACb,OAAO,EAAE,IAAI;MACb,MAAM,EAAE,SAAS;MACjB,OAAO,EAAEG,GAAG,CAAC,KAAK,CAAC;MACnB,MAAM,EAAEU,KAAK,CAACL,OAAO,IAAIJ,CAAC,CAAC,YAAY,CAAC;MACxC,SAAS,EAAEgB,CAAA,KAAMJ,IAAI,CAAC,KAAK;IAC7B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACd;AACF,CAAC,CAAC;AACF,SACEX,gBAAgB,EAChBO,aAAa,IAAImB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}