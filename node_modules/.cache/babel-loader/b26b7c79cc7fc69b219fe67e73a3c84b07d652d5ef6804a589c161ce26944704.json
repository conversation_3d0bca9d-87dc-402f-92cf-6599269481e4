{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-card\"\n};\nconst _hoisted_3 = {\n  class: \"login-form\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"error-message\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_van_field = _resolveComponent(\"van-field\");\n  const _component_van_button = _resolveComponent(\"van-button\");\n  const _component_van_form = _resolveComponent(\"van-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 背景装饰 \"), _cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n    class: \"login-background\"\n  }, [_createElementVNode(\"div\", {\n    class: \"bg-shape shape-1\"\n  }), _createElementVNode(\"div\", {\n    class: \"bg-shape shape-2\"\n  }), _createElementVNode(\"div\", {\n    class: \"bg-shape shape-3\"\n  })], -1 /* CACHED */)), _createCommentVNode(\" 登录卡片 \"), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 系统标题 \"), _cache[9] || (_cache[9] = _createStaticVNode(\"<div class=\\\"login-header\\\" data-v-26084dc2><div class=\\\"logo-section\\\" data-v-26084dc2><span class=\\\"logo-icon\\\" data-v-26084dc2>🏥</span><h1 class=\\\"system-title\\\" data-v-26084dc2>脊柱侧弯筛查系统</h1></div><p class=\\\"system-subtitle\\\" data-v-26084dc2>专业的脊柱健康检测与管理平台</p></div>\", 1)), _createCommentVNode(\" 登录表单 \"), _createElementVNode(\"div\", _hoisted_3, [_cache[6] || (_cache[6] = _createElementVNode(\"h2\", {\n    class: \"form-title\"\n  }, \"系统登录\", -1 /* CACHED */)), _cache[7] || (_cache[7] = _createElementVNode(\"p\", {\n    class: \"form-subtitle\"\n  }, \"请输入您的账号信息\", -1 /* CACHED */)), _createVNode(_component_van_form, {\n    onSubmit: $setup.handleLogin\n  }, {\n    default: _withCtx(() => [_createVNode(_component_van_field, {\n      modelValue: $setup.loginForm.username,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.loginForm.username = $event),\n      name: \"username\",\n      label: \"用户名\",\n      placeholder: \"请输入用户名\",\n      rules: [{\n        required: true,\n        message: '请输入用户名'\n      }],\n      class: \"login-field\"\n    }, {\n      \"left-icon\": _withCtx(() => _cache[2] || (_cache[2] = [_createElementVNode(\"span\", {\n        class: \"field-icon\"\n      }, \"👤\", -1 /* CACHED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_van_field, {\n      modelValue: $setup.loginForm.password,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.loginForm.password = $event),\n      type: \"password\",\n      name: \"password\",\n      label: \"密码\",\n      placeholder: \"请输入密码\",\n      rules: [{\n        required: true,\n        message: '请输入密码'\n      }],\n      class: \"login-field\"\n    }, {\n      \"left-icon\": _withCtx(() => _cache[3] || (_cache[3] = [_createElementVNode(\"span\", {\n        class: \"field-icon\"\n      }, \"🔒\", -1 /* CACHED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 错误提示 \"), $setup.errorMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_cache[4] || (_cache[4] = _createElementVNode(\"span\", {\n      class: \"error-icon\"\n    }, \"⚠️\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($setup.errorMessage), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 登录按钮 \"), _createVNode(_component_van_button, {\n      round: \"\",\n      block: \"\",\n      type: \"primary\",\n      \"native-type\": \"submit\",\n      class: \"login-button\",\n      loading: $setup.loading,\n      \"loading-text\": \"登录中...\"\n    }, {\n      default: _withCtx(() => _cache[5] || (_cache[5] = [_createElementVNode(\"span\", {\n        class: \"btn-text\"\n      }, \"立即登录\", -1 /* CACHED */)])),\n      _: 1 /* STABLE */,\n      __: [5]\n    }, 8 /* PROPS */, [\"loading\"])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 提示信息 \"), _cache[8] || (_cache[8] = _createStaticVNode(\"<div class=\\\"login-tips\\\" data-v-26084dc2><div class=\\\"tip-item\\\" data-v-26084dc2><span class=\\\"tip-icon\\\" data-v-26084dc2>💡</span><span class=\\\"tip-text\\\" data-v-26084dc2>测试账号：admin</span></div><div class=\\\"tip-item\\\" data-v-26084dc2><span class=\\\"tip-icon\\\" data-v-26084dc2>🔑</span><span class=\\\"tip-text\\\" data-v-26084dc2>测试密码：123456</span></div></div>\", 1))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_van_form", "onSubmit", "$setup", "handleLogin", "_component_van_field", "loginForm", "username", "$event", "name", "label", "placeholder", "rules", "required", "message", "_withCtx", "_cache", "password", "type", "errorMessage", "_hoisted_4", "_toDisplayString", "_component_van_button", "round", "block", "loading"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/src/views/Login.vue"], "sourcesContent": ["<!-- eslint-disable vue/multi-word-component-names -->\n<template>\n  <div class=\"login-container\">\n    <!-- 背景装饰 -->\n    <div class=\"login-background\">\n      <div class=\"bg-shape shape-1\"></div>\n      <div class=\"bg-shape shape-2\"></div>\n      <div class=\"bg-shape shape-3\"></div>\n    </div>\n    \n    <!-- 登录卡片 -->\n    <div class=\"login-card\">\n      <!-- 系统标题 -->\n      <div class=\"login-header\">\n        <div class=\"logo-section\">\n          <span class=\"logo-icon\">🏥</span>\n          <h1 class=\"system-title\">脊柱侧弯筛查系统</h1>\n        </div>\n        <p class=\"system-subtitle\">专业的脊柱健康检测与管理平台</p>\n      </div>\n      \n      <!-- 登录表单 -->\n      <div class=\"login-form\">\n        <h2 class=\"form-title\">系统登录</h2>\n        <p class=\"form-subtitle\">请输入您的账号信息</p>\n        \n        <van-form @submit=\"handleLogin\">\n          <van-field\n            v-model=\"loginForm.username\"\n            name=\"username\"\n            label=\"用户名\"\n            placeholder=\"请输入用户名\"\n            :rules=\"[{ required: true, message: '请输入用户名' }]\"\n            class=\"login-field\"\n          >\n            <template #left-icon>\n              <span class=\"field-icon\">👤</span>\n            </template>\n          </van-field>\n          \n          <van-field\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            name=\"password\"\n            label=\"密码\"\n            placeholder=\"请输入密码\"\n            :rules=\"[{ required: true, message: '请输入密码' }]\"\n            class=\"login-field\"\n          >\n            <template #left-icon>\n              <span class=\"field-icon\">🔒</span>\n            </template>\n          </van-field>\n          \n          <!-- 错误提示 -->\n          <div v-if=\"errorMessage\" class=\"error-message\">\n            <span class=\"error-icon\">⚠️</span>\n            {{ errorMessage }}\n          </div>\n          \n          <!-- 登录按钮 -->\n          <van-button \n            round \n            block \n            type=\"primary\" \n            native-type=\"submit\"\n            class=\"login-button\"\n            :loading=\"loading\"\n            loading-text=\"登录中...\"\n          >\n            <span class=\"btn-text\">立即登录</span>\n          </van-button>\n        </van-form>\n        \n        <!-- 提示信息 -->\n        <div class=\"login-tips\">\n          <div class=\"tip-item\">\n            <span class=\"tip-icon\">💡</span>\n            <span class=\"tip-text\">测试账号：admin</span>\n          </div>\n          <div class=\"tip-item\">\n            <span class=\"tip-icon\">🔑</span>\n            <span class=\"tip-text\">测试密码：123456</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useRouter } from 'vue-router'\n\nconst router = useRouter()\n\n// 表单数据\nconst loginForm = ref({\n  username: '',\n  password: ''\n})\n\n// 状态管理\nconst loading = ref(false)\nconst errorMessage = ref('')\n\n// 登录处理\nconst handleLogin = async () => {\n  loading.value = true\n  errorMessage.value = ''\n  \n  // 模拟登录延迟\n  setTimeout(() => {\n    const { username, password } = loginForm.value\n    \n    // 验证用户名和密码\n    if (username === 'admin' && password === '123456') {\n      // 登录成功\n      localStorage.setItem('isLoggedIn', 'true')\n      localStorage.setItem('username', username)\n      \n      // 跳转到主页\n      router.push('/home')\n    } else {\n      // 登录失败\n      errorMessage.value = '用户名或密码错误，请重新输入'\n      \n      // 清空密码\n      loginForm.value.password = ''\n    }\n    \n    loading.value = false\n  }, 1000)\n}\n</script>\n\n<style scoped>\n/* 登录容器 */\n.login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 背景装饰 */\n.login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n}\n\n.bg-shape {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.1);\n  animation: float 6s ease-in-out infinite;\n}\n\n.shape-1 {\n  width: 200px;\n  height: 200px;\n  top: 10%;\n  left: 10%;\n  animation-delay: 0s;\n}\n\n.shape-2 {\n  width: 150px;\n  height: 150px;\n  top: 60%;\n  right: 10%;\n  animation-delay: 2s;\n}\n\n.shape-3 {\n  width: 100px;\n  height: 100px;\n  bottom: 20%;\n  left: 20%;\n  animation-delay: 4s;\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n/* 登录卡片 */\n.login-card {\n  background: white;\n  border-radius: 20px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 40px;\n  width: 100%;\n  max-width: 450px;\n  position: relative;\n  animation: slideInUp 0.8s ease-out;\n}\n\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 系统标题区域 */\n.login-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.logo-section {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  margin-bottom: 8px;\n}\n\n.logo-icon {\n  font-size: 36px;\n}\n\n.system-title {\n  margin: 0;\n  font-size: 24px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.system-subtitle {\n  margin: 0;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 表单区域 */\n.login-form {\n  width: 100%;\n}\n\n.form-title {\n  margin: 0 0 8px 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #2c3e50;\n  text-align: center;\n}\n\n.form-subtitle {\n  margin: 0 0 30px 0;\n  font-size: 14px;\n  color: #666;\n  text-align: center;\n}\n\n/* 输入框样式 */\n.login-field {\n  margin-bottom: 20px;\n  --van-field-border-color: #e8e8e8;\n  --van-field-focus-border-color: #1890ff;\n}\n\n.field-icon {\n  font-size: 16px;\n  margin-right: 8px;\n}\n\n/* 错误提示 */\n.error-message {\n  background: #fff2f0;\n  border: 1px solid #ffccc7;\n  border-radius: 8px;\n  padding: 12px 16px;\n  margin-bottom: 20px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #ff4d4f;\n  font-size: 14px;\n  animation: shake 0.5s ease-in-out;\n}\n\n@keyframes shake {\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-5px); }\n  75% { transform: translateX(5px); }\n}\n\n.error-icon {\n  font-size: 16px;\n}\n\n/* 登录按钮 */\n.login-button {\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n  border: none;\n  height: 50px;\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 30px;\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n  transition: all 0.3s ease;\n}\n\n.login-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);\n}\n\n.btn-text {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n/* 提示信息 */\n.login-tips {\n  background: #f8f9ff;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #e8e8ff;\n}\n\n.tip-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n.tip-item:last-child {\n  margin-bottom: 0;\n}\n\n.tip-icon {\n  font-size: 16px;\n}\n\n/* 响应式设计 */\n@media (max-width: 480px) {\n  .login-card {\n    padding: 30px 20px;\n    margin: 10px;\n    border-radius: 16px;\n  }\n  \n  .system-title {\n    font-size: 20px;\n  }\n  \n  .logo-icon {\n    font-size: 28px;\n  }\n  \n  .form-title {\n    font-size: 18px;\n  }\n}\n</style>\n"], "mappings": ";;EAEOA,KAAK,EAAC;AAAiB;;EASrBA,KAAK,EAAC;AAAY;;EAWhBA,KAAK,EAAC;AAAY;;;EAiCMA,KAAK,EAAC;;;;;;uBArDvCC,mBAAA,CAqFM,OArFNC,UAqFM,GApFJC,mBAAA,UAAa,E,4BACbC,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAkB,IAC3BI,mBAAA,CAAoC;IAA/BJ,KAAK,EAAC;EAAkB,IAC7BI,mBAAA,CAAoC;IAA/BJ,KAAK,EAAC;EAAkB,IAC7BI,mBAAA,CAAoC;IAA/BJ,KAAK,EAAC;EAAkB,G,qBAG/BG,mBAAA,UAAa,EACbC,mBAAA,CA2EM,OA3ENC,UA2EM,GA1EJF,mBAAA,UAAa,E,oUASbA,mBAAA,UAAa,EACbC,mBAAA,CA+DM,OA/DNE,UA+DM,G,0BA9DJF,mBAAA,CAAgC;IAA5BJ,KAAK,EAAC;EAAY,GAAC,MAAI,qB,0BAC3BI,mBAAA,CAAsC;IAAnCJ,KAAK,EAAC;EAAe,GAAC,WAAS,qBAElCO,YAAA,CA8CWC,mBAAA;IA9CAC,QAAM,EAAEC,MAAA,CAAAC;EAAW;sBAC5B,MAWY,CAXZJ,YAAA,CAWYK,oBAAA;kBAVDF,MAAA,CAAAG,SAAS,CAACC,QAAQ;iEAAlBJ,MAAA,CAAAG,SAAS,CAACC,QAAQ,GAAAC,MAAA;MAC3BC,IAAI,EAAC,UAAU;MACfC,KAAK,EAAC,KAAK;MACXC,WAAW,EAAC,QAAQ;MACnBC,KAAK,EAAE;QAAAC,QAAA;QAAAC,OAAA;MAAA,EAAuC;MAC/CrB,KAAK,EAAC;;MAEK,WAAS,EAAAsB,QAAA,CAClB,MAAkCC,MAAA,QAAAA,MAAA,OAAlCnB,mBAAA,CAAkC;QAA5BJ,KAAK,EAAC;MAAY,GAAC,IAAE,mB;;uCAI/BO,YAAA,CAYYK,oBAAA;kBAXDF,MAAA,CAAAG,SAAS,CAACW,QAAQ;iEAAlBd,MAAA,CAAAG,SAAS,CAACW,QAAQ,GAAAT,MAAA;MAC3BU,IAAI,EAAC,UAAU;MACfT,IAAI,EAAC,UAAU;MACfC,KAAK,EAAC,IAAI;MACVC,WAAW,EAAC,OAAO;MAClBC,KAAK,EAAE;QAAAC,QAAA;QAAAC,OAAA;MAAA,EAAsC;MAC9CrB,KAAK,EAAC;;MAEK,WAAS,EAAAsB,QAAA,CAClB,MAAkCC,MAAA,QAAAA,MAAA,OAAlCnB,mBAAA,CAAkC;QAA5BJ,KAAK,EAAC;MAAY,GAAC,IAAE,mB;;uCAI/BG,mBAAA,UAAa,EACFO,MAAA,CAAAgB,YAAY,I,cAAvBzB,mBAAA,CAGM,OAHN0B,UAGM,G,0BAFJvB,mBAAA,CAAkC;MAA5BJ,KAAK,EAAC;IAAY,GAAC,IAAE,qB,iBAAO,GAClC,GAAA4B,gBAAA,CAAGlB,MAAA,CAAAgB,YAAY,iB,wCAGjBvB,mBAAA,UAAa,EACbI,YAAA,CAUasB,qBAAA;MATXC,KAAK,EAAL,EAAK;MACLC,KAAK,EAAL,EAAK;MACLN,IAAI,EAAC,SAAS;MACd,aAAW,EAAC,QAAQ;MACpBzB,KAAK,EAAC,cAAc;MACnBgC,OAAO,EAAEtB,MAAA,CAAAsB,OAAO;MACjB,cAAY,EAAC;;wBAEb,MAAkCT,MAAA,QAAAA,MAAA,OAAlCnB,mBAAA,CAAkC;QAA5BJ,KAAK,EAAC;MAAU,GAAC,MAAI,mB;;;;;MAI/BG,mBAAA,UAAa,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}