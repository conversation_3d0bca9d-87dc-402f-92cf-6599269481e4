{"ast": null, "code": "import { getCurrentInstance } from \"vue\";\nconst routeProps = {\n  to: [String, Object],\n  url: String,\n  replace: Boolean\n};\nfunction route({\n  to,\n  url,\n  replace,\n  $router: router\n}) {\n  if (to && router) {\n    router[replace ? \"replace\" : \"push\"](to);\n  } else if (url) {\n    replace ? location.replace(url) : location.href = url;\n  }\n}\nfunction useRoute() {\n  const vm = getCurrentInstance().proxy;\n  return () => route(vm);\n}\nexport { route, routeProps, useRoute };", "map": {"version": 3, "names": ["getCurrentInstance", "routeProps", "to", "String", "Object", "url", "replace", "Boolean", "route", "$router", "router", "location", "href", "useRoute", "vm", "proxy"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-route.mjs"], "sourcesContent": ["import {\n  getCurrentInstance\n} from \"vue\";\nconst routeProps = {\n  to: [String, Object],\n  url: String,\n  replace: Boolean\n};\nfunction route({\n  to,\n  url,\n  replace,\n  $router: router\n}) {\n  if (to && router) {\n    router[replace ? \"replace\" : \"push\"](to);\n  } else if (url) {\n    replace ? location.replace(url) : location.href = url;\n  }\n}\nfunction useRoute() {\n  const vm = getCurrentInstance().proxy;\n  return () => route(vm);\n}\nexport {\n  route,\n  routeProps,\n  useRoute\n};\n"], "mappings": "AAAA,SACEA,kBAAkB,QACb,KAAK;AACZ,MAAMC,UAAU,GAAG;EACjBC,EAAE,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;EACpBC,GAAG,EAAEF,MAAM;EACXG,OAAO,EAAEC;AACX,CAAC;AACD,SAASC,KAAKA,CAAC;EACbN,EAAE;EACFG,GAAG;EACHC,OAAO;EACPG,OAAO,EAAEC;AACX,CAAC,EAAE;EACD,IAAIR,EAAE,IAAIQ,MAAM,EAAE;IAChBA,MAAM,CAACJ,OAAO,GAAG,SAAS,GAAG,MAAM,CAAC,CAACJ,EAAE,CAAC;EAC1C,CAAC,MAAM,IAAIG,GAAG,EAAE;IACdC,OAAO,GAAGK,QAAQ,CAACL,OAAO,CAACD,GAAG,CAAC,GAAGM,QAAQ,CAACC,IAAI,GAAGP,GAAG;EACvD;AACF;AACA,SAASQ,QAAQA,CAAA,EAAG;EAClB,MAAMC,EAAE,GAAGd,kBAAkB,CAAC,CAAC,CAACe,KAAK;EACrC,OAAO,MAAMP,KAAK,CAACM,EAAE,CAAC;AACxB;AACA,SACEN,KAAK,EACLP,UAAU,EACVY,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}