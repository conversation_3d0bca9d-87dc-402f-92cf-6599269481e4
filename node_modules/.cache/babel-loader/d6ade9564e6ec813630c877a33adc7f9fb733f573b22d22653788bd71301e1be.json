{"ast": null, "code": "import { Lazyload } from \"./vue-lazyload/index.mjs\";\nvar stdin_default = Lazyload;\nexport { Lazyload, stdin_default as default };", "map": {"version": 3, "names": ["Lazyload", "stdin_default", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/lazyload/index.mjs"], "sourcesContent": ["import { Lazyload } from \"./vue-lazyload/index.mjs\";\nvar stdin_default = Lazyload;\nexport {\n  Lazyload,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,IAAIC,aAAa,GAAGD,QAAQ;AAC5B,SACEA,QAAQ,EACRC,aAAa,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}