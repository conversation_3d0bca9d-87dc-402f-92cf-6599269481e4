{"ast": null, "code": "import { ref, watch } from \"vue\";\nconst useSyncPropRef = (getProp, setProp) => {\n  const propRef = ref(getProp());\n  watch(getProp, value => {\n    if (value !== propRef.value) {\n      propRef.value = value;\n    }\n  });\n  watch(propRef, value => {\n    if (value !== getProp()) {\n      setProp(value);\n    }\n  });\n  return propRef;\n};\nexport { useSyncPropRef };", "map": {"version": 3, "names": ["ref", "watch", "useSyncPropRef", "getProp", "setProp", "propRef", "value"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-sync-prop-ref.mjs"], "sourcesContent": ["import { ref, watch } from \"vue\";\nconst useSyncPropRef = (getProp, setProp) => {\n  const propRef = ref(getProp());\n  watch(getProp, (value) => {\n    if (value !== propRef.value) {\n      propRef.value = value;\n    }\n  });\n  watch(propRef, (value) => {\n    if (value !== getProp()) {\n      setProp(value);\n    }\n  });\n  return propRef;\n};\nexport {\n  useSyncPropRef\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAChC,MAAMC,cAAc,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;EAC3C,MAAMC,OAAO,GAAGL,GAAG,CAACG,OAAO,CAAC,CAAC,CAAC;EAC9BF,KAAK,CAACE,OAAO,EAAGG,KAAK,IAAK;IACxB,IAAIA,KAAK,KAAKD,OAAO,CAACC,KAAK,EAAE;MAC3BD,OAAO,CAACC,KAAK,GAAGA,KAAK;IACvB;EACF,CAAC,CAAC;EACFL,KAAK,CAACI,OAAO,EAAGC,KAAK,IAAK;IACxB,IAAIA,KAAK,KAAKH,OAAO,CAAC,CAAC,EAAE;MACvBC,OAAO,CAACE,KAAK,CAAC;IAChB;EACF,CAAC,CAAC;EACF,OAAOD,OAAO;AAChB,CAAC;AACD,SACEH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}