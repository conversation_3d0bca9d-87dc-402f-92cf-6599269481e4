{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nconst AREA_EMPTY_CODE = \"000000\";\nconst INHERIT_SLOTS = [\"title\", \"cancel\", \"confirm\", \"toolbar\", \"columns-top\", \"columns-bottom\"];\nconst INHERIT_PROPS = [\"title\", \"loading\", \"readonly\", \"optionHeight\", \"swipeDuration\", \"visibleOptionNum\", \"cancelButtonText\", \"confirmButtonText\"];\nconst makeOption = (text = \"\", value = AREA_EMPTY_CODE, children = void 0) => ({\n  text,\n  value,\n  children\n});\nfunction formatDataForCascade({\n  areaList,\n  columnsNum,\n  columnsPlaceholder: placeholder\n}) {\n  const {\n    city_list: city = {},\n    county_list: county = {},\n    province_list: province = {}\n  } = areaList;\n  const showCity = +columnsNum > 1;\n  const showCounty = +columnsNum > 2;\n  const getProvinceChildren = () => {\n    if (showCity) {\n      return placeholder.length > 1 ? [makeOption(placeholder[1], AREA_EMPTY_CODE, showCounty ? [] : void 0)] : [];\n    }\n  };\n  const provinceMap = /* @__PURE__ */new Map();\n  Object.keys(province).forEach(code => {\n    provinceMap.set(code.slice(0, 2), makeOption(province[code], code, getProvinceChildren()));\n  });\n  const cityMap = /* @__PURE__ */new Map();\n  if (showCity) {\n    const getCityChildren = () => {\n      if (showCounty) {\n        return placeholder.length > 2 ? [makeOption(placeholder[2])] : [];\n      }\n    };\n    Object.keys(city).forEach(code => {\n      const option = makeOption(city[code], code, getCityChildren());\n      cityMap.set(code.slice(0, 4), option);\n      const province2 = provinceMap.get(code.slice(0, 2));\n      if (province2) {\n        province2.children.push(option);\n      }\n    });\n  }\n  if (showCounty) {\n    Object.keys(county).forEach(code => {\n      const city2 = cityMap.get(code.slice(0, 4));\n      if (city2) {\n        city2.children.push(makeOption(county[code], code));\n      }\n    });\n  }\n  const options = Array.from(provinceMap.values());\n  if (placeholder.length) {\n    const county2 = showCounty ? [makeOption(placeholder[2])] : void 0;\n    const city2 = showCity ? [makeOption(placeholder[1], AREA_EMPTY_CODE, county2)] : void 0;\n    options.unshift(makeOption(placeholder[0], AREA_EMPTY_CODE, city2));\n  }\n  return options;\n}\nexport { AREA_EMPTY_CODE, INHERIT_PROPS, INHERIT_SLOTS, formatDataForCascade };", "map": {"version": 3, "names": ["AREA_EMPTY_CODE", "INHERIT_SLOTS", "INHERIT_PROPS", "makeOption", "text", "value", "children", "formatDataForCascade", "areaList", "columnsNum", "columnsPlaceholder", "placeholder", "city_list", "city", "county_list", "county", "province_list", "province", "showCity", "showCounty", "getProvinceC<PERSON><PERSON>n", "length", "provinceMap", "Map", "Object", "keys", "for<PERSON>ach", "code", "set", "slice", "cityMap", "getCityChildren", "option", "province2", "get", "push", "city2", "options", "Array", "from", "values", "county2", "unshift"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/area/utils.mjs"], "sourcesContent": ["const AREA_EMPTY_CODE = \"000000\";\nconst INHERIT_SLOTS = [\n  \"title\",\n  \"cancel\",\n  \"confirm\",\n  \"toolbar\",\n  \"columns-top\",\n  \"columns-bottom\"\n];\nconst INHERIT_PROPS = [\n  \"title\",\n  \"loading\",\n  \"readonly\",\n  \"optionHeight\",\n  \"swipeDuration\",\n  \"visibleOptionNum\",\n  \"cancelButtonText\",\n  \"confirmButtonText\"\n];\nconst makeOption = (text = \"\", value = AREA_EMPTY_CODE, children = void 0) => ({\n  text,\n  value,\n  children\n});\nfunction formatDataForCascade({\n  areaList,\n  columnsNum,\n  columnsPlaceholder: placeholder\n}) {\n  const {\n    city_list: city = {},\n    county_list: county = {},\n    province_list: province = {}\n  } = areaList;\n  const showCity = +columnsNum > 1;\n  const showCounty = +columnsNum > 2;\n  const getProvinceChildren = () => {\n    if (showCity) {\n      return placeholder.length > 1 ? [\n        makeOption(\n          placeholder[1],\n          AREA_EMPTY_CODE,\n          showCounty ? [] : void 0\n        )\n      ] : [];\n    }\n  };\n  const provinceMap = /* @__PURE__ */ new Map();\n  Object.keys(province).forEach((code) => {\n    provinceMap.set(\n      code.slice(0, 2),\n      makeOption(province[code], code, getProvinceChildren())\n    );\n  });\n  const cityMap = /* @__PURE__ */ new Map();\n  if (showCity) {\n    const getCityChildren = () => {\n      if (showCounty) {\n        return placeholder.length > 2 ? [makeOption(placeholder[2])] : [];\n      }\n    };\n    Object.keys(city).forEach((code) => {\n      const option = makeOption(city[code], code, getCityChildren());\n      cityMap.set(code.slice(0, 4), option);\n      const province2 = provinceMap.get(code.slice(0, 2));\n      if (province2) {\n        province2.children.push(option);\n      }\n    });\n  }\n  if (showCounty) {\n    Object.keys(county).forEach((code) => {\n      const city2 = cityMap.get(code.slice(0, 4));\n      if (city2) {\n        city2.children.push(makeOption(county[code], code));\n      }\n    });\n  }\n  const options = Array.from(provinceMap.values());\n  if (placeholder.length) {\n    const county2 = showCounty ? [makeOption(placeholder[2])] : void 0;\n    const city2 = showCity ? [makeOption(placeholder[1], AREA_EMPTY_CODE, county2)] : void 0;\n    options.unshift(makeOption(placeholder[0], AREA_EMPTY_CODE, city2));\n  }\n  return options;\n}\nexport {\n  AREA_EMPTY_CODE,\n  INHERIT_PROPS,\n  INHERIT_SLOTS,\n  formatDataForCascade\n};\n"], "mappings": ";;;AAAA,MAAMA,eAAe,GAAG,QAAQ;AAChC,MAAMC,aAAa,GAAG,CACpB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,EACb,gBAAgB,CACjB;AACD,MAAMC,aAAa,GAAG,CACpB,OAAO,EACP,SAAS,EACT,UAAU,EACV,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,CACpB;AACD,MAAMC,UAAU,GAAGA,CAACC,IAAI,GAAG,EAAE,EAAEC,KAAK,GAAGL,eAAe,EAAEM,QAAQ,GAAG,KAAK,CAAC,MAAM;EAC7EF,IAAI;EACJC,KAAK;EACLC;AACF,CAAC,CAAC;AACF,SAASC,oBAAoBA,CAAC;EAC5BC,QAAQ;EACRC,UAAU;EACVC,kBAAkB,EAAEC;AACtB,CAAC,EAAE;EACD,MAAM;IACJC,SAAS,EAAEC,IAAI,GAAG,CAAC,CAAC;IACpBC,WAAW,EAAEC,MAAM,GAAG,CAAC,CAAC;IACxBC,aAAa,EAAEC,QAAQ,GAAG,CAAC;EAC7B,CAAC,GAAGT,QAAQ;EACZ,MAAMU,QAAQ,GAAG,CAACT,UAAU,GAAG,CAAC;EAChC,MAAMU,UAAU,GAAG,CAACV,UAAU,GAAG,CAAC;EAClC,MAAMW,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIF,QAAQ,EAAE;MACZ,OAAOP,WAAW,CAACU,MAAM,GAAG,CAAC,GAAG,CAC9BlB,UAAU,CACRQ,WAAW,CAAC,CAAC,CAAC,EACdX,eAAe,EACfmB,UAAU,GAAG,EAAE,GAAG,KAAK,CACzB,CAAC,CACF,GAAG,EAAE;IACR;EACF,CAAC;EACD,MAAMG,WAAW,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;EAC7CC,MAAM,CAACC,IAAI,CAACR,QAAQ,CAAC,CAACS,OAAO,CAAEC,IAAI,IAAK;IACtCL,WAAW,CAACM,GAAG,CACbD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAChB1B,UAAU,CAACc,QAAQ,CAACU,IAAI,CAAC,EAAEA,IAAI,EAAEP,mBAAmB,CAAC,CAAC,CACxD,CAAC;EACH,CAAC,CAAC;EACF,MAAMU,OAAO,GAAG,eAAgB,IAAIP,GAAG,CAAC,CAAC;EACzC,IAAIL,QAAQ,EAAE;IACZ,MAAMa,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIZ,UAAU,EAAE;QACd,OAAOR,WAAW,CAACU,MAAM,GAAG,CAAC,GAAG,CAAClB,UAAU,CAACQ,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;MACnE;IACF,CAAC;IACDa,MAAM,CAACC,IAAI,CAACZ,IAAI,CAAC,CAACa,OAAO,CAAEC,IAAI,IAAK;MAClC,MAAMK,MAAM,GAAG7B,UAAU,CAACU,IAAI,CAACc,IAAI,CAAC,EAAEA,IAAI,EAAEI,eAAe,CAAC,CAAC,CAAC;MAC9DD,OAAO,CAACF,GAAG,CAACD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEG,MAAM,CAAC;MACrC,MAAMC,SAAS,GAAGX,WAAW,CAACY,GAAG,CAACP,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnD,IAAII,SAAS,EAAE;QACbA,SAAS,CAAC3B,QAAQ,CAAC6B,IAAI,CAACH,MAAM,CAAC;MACjC;IACF,CAAC,CAAC;EACJ;EACA,IAAIb,UAAU,EAAE;IACdK,MAAM,CAACC,IAAI,CAACV,MAAM,CAAC,CAACW,OAAO,CAAEC,IAAI,IAAK;MACpC,MAAMS,KAAK,GAAGN,OAAO,CAACI,GAAG,CAACP,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3C,IAAIO,KAAK,EAAE;QACTA,KAAK,CAAC9B,QAAQ,CAAC6B,IAAI,CAAChC,UAAU,CAACY,MAAM,CAACY,IAAI,CAAC,EAAEA,IAAI,CAAC,CAAC;MACrD;IACF,CAAC,CAAC;EACJ;EACA,MAAMU,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACjB,WAAW,CAACkB,MAAM,CAAC,CAAC,CAAC;EAChD,IAAI7B,WAAW,CAACU,MAAM,EAAE;IACtB,MAAMoB,OAAO,GAAGtB,UAAU,GAAG,CAAChB,UAAU,CAACQ,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAClE,MAAMyB,KAAK,GAAGlB,QAAQ,GAAG,CAACf,UAAU,CAACQ,WAAW,CAAC,CAAC,CAAC,EAAEX,eAAe,EAAEyC,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC;IACxFJ,OAAO,CAACK,OAAO,CAACvC,UAAU,CAACQ,WAAW,CAAC,CAAC,CAAC,EAAEX,eAAe,EAAEoC,KAAK,CAAC,CAAC;EACrE;EACA,OAAOC,OAAO;AAChB;AACA,SACErC,eAAe,EACfE,aAAa,EACbD,aAAa,EACbM,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}