{"ast": null, "code": "import { ref, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, addUnit, truthProp, numericProp, unknownProp, makeRequiredProp } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst checkerProps = {\n  name: unknown<PERSON>rop,\n  disabled: Boolean,\n  iconSize: numericProp,\n  modelValue: unknownProp,\n  checkedColor: String,\n  labelPosition: String,\n  labelDisabled: Boolean\n};\nvar stdin_default = defineComponent({\n  props: extend({}, checkerProps, {\n    bem: makeRequiredProp(Function),\n    role: String,\n    shape: String,\n    parent: Object,\n    checked: Boolean,\n    bindGroup: truthProp,\n    indeterminate: {\n      type: Boolean,\n      default: null\n    }\n  }),\n  emits: [\"click\", \"toggle\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const iconRef = ref();\n    const getParentProp = name => {\n      if (props.parent && props.bindGroup) {\n        return props.parent.props[name];\n      }\n    };\n    const disabled = computed(() => {\n      if (props.parent && props.bindGroup) {\n        const disabled2 = getParentProp(\"disabled\") || props.disabled;\n        if (props.role === \"checkbox\") {\n          const checkedCount = getParentProp(\"modelValue\").length;\n          const max = getParentProp(\"max\");\n          const overlimit = max && checkedCount >= +max;\n          return disabled2 || overlimit && !props.checked;\n        }\n        return disabled2;\n      }\n      return props.disabled;\n    });\n    const direction = computed(() => getParentProp(\"direction\"));\n    const iconStyle = computed(() => {\n      const checkedColor = props.checkedColor || getParentProp(\"checkedColor\");\n      if (checkedColor && (props.checked || props.indeterminate) && !disabled.value) {\n        return {\n          borderColor: checkedColor,\n          backgroundColor: checkedColor\n        };\n      }\n    });\n    const shape = computed(() => {\n      return props.shape || getParentProp(\"shape\") || \"round\";\n    });\n    const onClick = event => {\n      const {\n        target\n      } = event;\n      const icon = iconRef.value;\n      const iconClicked = icon === target || (icon == null ? void 0 : icon.contains(target));\n      if (!disabled.value && (iconClicked || !props.labelDisabled)) {\n        emit(\"toggle\");\n      }\n      emit(\"click\", event);\n    };\n    const renderIcon = () => {\n      var _a, _b;\n      const {\n        bem,\n        checked,\n        indeterminate\n      } = props;\n      const iconSize = props.iconSize || getParentProp(\"iconSize\");\n      return _createVNode(\"div\", {\n        \"ref\": iconRef,\n        \"class\": bem(\"icon\", [shape.value, {\n          disabled: disabled.value,\n          checked,\n          indeterminate\n        }]),\n        \"style\": shape.value !== \"dot\" ? {\n          fontSize: addUnit(iconSize)\n        } : {\n          width: addUnit(iconSize),\n          height: addUnit(iconSize),\n          borderColor: (_a = iconStyle.value) == null ? void 0 : _a.borderColor\n        }\n      }, [slots.icon ? slots.icon({\n        checked,\n        disabled: disabled.value\n      }) : shape.value !== \"dot\" ? _createVNode(Icon, {\n        \"name\": indeterminate ? \"minus\" : \"success\",\n        \"style\": iconStyle.value\n      }, null) : _createVNode(\"div\", {\n        \"class\": bem(\"icon--dot__icon\"),\n        \"style\": {\n          backgroundColor: (_b = iconStyle.value) == null ? void 0 : _b.backgroundColor\n        }\n      }, null)]);\n    };\n    const renderLabel = () => {\n      const {\n        checked\n      } = props;\n      if (slots.default) {\n        return _createVNode(\"span\", {\n          \"class\": props.bem(\"label\", [props.labelPosition, {\n            disabled: disabled.value\n          }])\n        }, [slots.default({\n          checked,\n          disabled: disabled.value\n        })]);\n      }\n    };\n    return () => {\n      const nodes = props.labelPosition === \"left\" ? [renderLabel(), renderIcon()] : [renderIcon(), renderLabel()];\n      return _createVNode(\"div\", {\n        \"role\": props.role,\n        \"class\": props.bem([{\n          disabled: disabled.value,\n          \"label-disabled\": props.labelDisabled\n        }, direction.value]),\n        \"tabindex\": disabled.value ? void 0 : 0,\n        \"aria-checked\": props.checked,\n        \"onClick\": onClick\n      }, [nodes]);\n    };\n  }\n});\nexport { checkerProps, stdin_default as default };", "map": {"version": 3, "names": ["ref", "computed", "defineComponent", "createVNode", "_createVNode", "extend", "addUnit", "truthProp", "numericProp", "unknownProp", "makeRequiredProp", "Icon", "checkerProps", "name", "disabled", "Boolean", "iconSize", "modelValue", "checkedColor", "String", "labelPosition", "labelDisabled", "stdin_default", "props", "bem", "Function", "role", "shape", "parent", "Object", "checked", "bindGroup", "indeterminate", "type", "default", "emits", "setup", "emit", "slots", "iconRef", "getParentProp", "disabled2", "checkedCount", "length", "max", "overlimit", "direction", "iconStyle", "value", "borderColor", "backgroundColor", "onClick", "event", "target", "icon", "iconClicked", "contains", "renderIcon", "_a", "_b", "fontSize", "width", "height", "renderLabel", "nodes"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/checkbox/Checker.mjs"], "sourcesContent": ["import { ref, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, addUnit, truthProp, numericProp, unknownProp, makeRequiredProp } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst checkerProps = {\n  name: unknown<PERSON>rop,\n  disabled: Boolean,\n  iconSize: numericProp,\n  modelValue: unknownProp,\n  checkedColor: String,\n  labelPosition: String,\n  labelDisabled: Boolean\n};\nvar stdin_default = defineComponent({\n  props: extend({}, checkerProps, {\n    bem: makeRequiredProp(Function),\n    role: String,\n    shape: String,\n    parent: Object,\n    checked: Boolean,\n    bindGroup: truthProp,\n    indeterminate: {\n      type: Boolean,\n      default: null\n    }\n  }),\n  emits: [\"click\", \"toggle\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const iconRef = ref();\n    const getParentProp = (name) => {\n      if (props.parent && props.bindGroup) {\n        return props.parent.props[name];\n      }\n    };\n    const disabled = computed(() => {\n      if (props.parent && props.bindGroup) {\n        const disabled2 = getParentProp(\"disabled\") || props.disabled;\n        if (props.role === \"checkbox\") {\n          const checkedCount = getParentProp(\"modelValue\").length;\n          const max = getParentProp(\"max\");\n          const overlimit = max && checkedCount >= +max;\n          return disabled2 || overlimit && !props.checked;\n        }\n        return disabled2;\n      }\n      return props.disabled;\n    });\n    const direction = computed(() => getParentProp(\"direction\"));\n    const iconStyle = computed(() => {\n      const checkedColor = props.checkedColor || getParentProp(\"checkedColor\");\n      if (checkedColor && (props.checked || props.indeterminate) && !disabled.value) {\n        return {\n          borderColor: checkedColor,\n          backgroundColor: checkedColor\n        };\n      }\n    });\n    const shape = computed(() => {\n      return props.shape || getParentProp(\"shape\") || \"round\";\n    });\n    const onClick = (event) => {\n      const {\n        target\n      } = event;\n      const icon = iconRef.value;\n      const iconClicked = icon === target || (icon == null ? void 0 : icon.contains(target));\n      if (!disabled.value && (iconClicked || !props.labelDisabled)) {\n        emit(\"toggle\");\n      }\n      emit(\"click\", event);\n    };\n    const renderIcon = () => {\n      var _a, _b;\n      const {\n        bem,\n        checked,\n        indeterminate\n      } = props;\n      const iconSize = props.iconSize || getParentProp(\"iconSize\");\n      return _createVNode(\"div\", {\n        \"ref\": iconRef,\n        \"class\": bem(\"icon\", [shape.value, {\n          disabled: disabled.value,\n          checked,\n          indeterminate\n        }]),\n        \"style\": shape.value !== \"dot\" ? {\n          fontSize: addUnit(iconSize)\n        } : {\n          width: addUnit(iconSize),\n          height: addUnit(iconSize),\n          borderColor: (_a = iconStyle.value) == null ? void 0 : _a.borderColor\n        }\n      }, [slots.icon ? slots.icon({\n        checked,\n        disabled: disabled.value\n      }) : shape.value !== \"dot\" ? _createVNode(Icon, {\n        \"name\": indeterminate ? \"minus\" : \"success\",\n        \"style\": iconStyle.value\n      }, null) : _createVNode(\"div\", {\n        \"class\": bem(\"icon--dot__icon\"),\n        \"style\": {\n          backgroundColor: (_b = iconStyle.value) == null ? void 0 : _b.backgroundColor\n        }\n      }, null)]);\n    };\n    const renderLabel = () => {\n      const {\n        checked\n      } = props;\n      if (slots.default) {\n        return _createVNode(\"span\", {\n          \"class\": props.bem(\"label\", [props.labelPosition, {\n            disabled: disabled.value\n          }])\n        }, [slots.default({\n          checked,\n          disabled: disabled.value\n        })]);\n      }\n    };\n    return () => {\n      const nodes = props.labelPosition === \"left\" ? [renderLabel(), renderIcon()] : [renderIcon(), renderLabel()];\n      return _createVNode(\"div\", {\n        \"role\": props.role,\n        \"class\": props.bem([{\n          disabled: disabled.value,\n          \"label-disabled\": props.labelDisabled\n        }, direction.value]),\n        \"tabindex\": disabled.value ? void 0 : 0,\n        \"aria-checked\": props.checked,\n        \"onClick\": onClick\n      }, [nodes]);\n    };\n  }\n});\nexport {\n  checkerProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACjF,SAASC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC3G,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAEJ,WAAW;EACjBK,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAER,WAAW;EACrBS,UAAU,EAAER,WAAW;EACvBS,YAAY,EAAEC,MAAM;EACpBC,aAAa,EAAED,MAAM;EACrBE,aAAa,EAAEN;AACjB,CAAC;AACD,IAAIO,aAAa,GAAGpB,eAAe,CAAC;EAClCqB,KAAK,EAAElB,MAAM,CAAC,CAAC,CAAC,EAAEO,YAAY,EAAE;IAC9BY,GAAG,EAAEd,gBAAgB,CAACe,QAAQ,CAAC;IAC/BC,IAAI,EAAEP,MAAM;IACZQ,KAAK,EAAER,MAAM;IACbS,MAAM,EAAEC,MAAM;IACdC,OAAO,EAAEf,OAAO;IAChBgB,SAAS,EAAExB,SAAS;IACpByB,aAAa,EAAE;MACbC,IAAI,EAAElB,OAAO;MACbmB,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACFC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;EAC1BC,KAAKA,CAACb,KAAK,EAAE;IACXc,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,OAAO,GAAGvC,GAAG,CAAC,CAAC;IACrB,MAAMwC,aAAa,GAAI3B,IAAI,IAAK;MAC9B,IAAIU,KAAK,CAACK,MAAM,IAAIL,KAAK,CAACQ,SAAS,EAAE;QACnC,OAAOR,KAAK,CAACK,MAAM,CAACL,KAAK,CAACV,IAAI,CAAC;MACjC;IACF,CAAC;IACD,MAAMC,QAAQ,GAAGb,QAAQ,CAAC,MAAM;MAC9B,IAAIsB,KAAK,CAACK,MAAM,IAAIL,KAAK,CAACQ,SAAS,EAAE;QACnC,MAAMU,SAAS,GAAGD,aAAa,CAAC,UAAU,CAAC,IAAIjB,KAAK,CAACT,QAAQ;QAC7D,IAAIS,KAAK,CAACG,IAAI,KAAK,UAAU,EAAE;UAC7B,MAAMgB,YAAY,GAAGF,aAAa,CAAC,YAAY,CAAC,CAACG,MAAM;UACvD,MAAMC,GAAG,GAAGJ,aAAa,CAAC,KAAK,CAAC;UAChC,MAAMK,SAAS,GAAGD,GAAG,IAAIF,YAAY,IAAI,CAACE,GAAG;UAC7C,OAAOH,SAAS,IAAII,SAAS,IAAI,CAACtB,KAAK,CAACO,OAAO;QACjD;QACA,OAAOW,SAAS;MAClB;MACA,OAAOlB,KAAK,CAACT,QAAQ;IACvB,CAAC,CAAC;IACF,MAAMgC,SAAS,GAAG7C,QAAQ,CAAC,MAAMuC,aAAa,CAAC,WAAW,CAAC,CAAC;IAC5D,MAAMO,SAAS,GAAG9C,QAAQ,CAAC,MAAM;MAC/B,MAAMiB,YAAY,GAAGK,KAAK,CAACL,YAAY,IAAIsB,aAAa,CAAC,cAAc,CAAC;MACxE,IAAItB,YAAY,KAAKK,KAAK,CAACO,OAAO,IAAIP,KAAK,CAACS,aAAa,CAAC,IAAI,CAAClB,QAAQ,CAACkC,KAAK,EAAE;QAC7E,OAAO;UACLC,WAAW,EAAE/B,YAAY;UACzBgC,eAAe,EAAEhC;QACnB,CAAC;MACH;IACF,CAAC,CAAC;IACF,MAAMS,KAAK,GAAG1B,QAAQ,CAAC,MAAM;MAC3B,OAAOsB,KAAK,CAACI,KAAK,IAAIa,aAAa,CAAC,OAAO,CAAC,IAAI,OAAO;IACzD,CAAC,CAAC;IACF,MAAMW,OAAO,GAAIC,KAAK,IAAK;MACzB,MAAM;QACJC;MACF,CAAC,GAAGD,KAAK;MACT,MAAME,IAAI,GAAGf,OAAO,CAACS,KAAK;MAC1B,MAAMO,WAAW,GAAGD,IAAI,KAAKD,MAAM,KAAKC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACE,QAAQ,CAACH,MAAM,CAAC,CAAC;MACtF,IAAI,CAACvC,QAAQ,CAACkC,KAAK,KAAKO,WAAW,IAAI,CAAChC,KAAK,CAACF,aAAa,CAAC,EAAE;QAC5DgB,IAAI,CAAC,QAAQ,CAAC;MAChB;MACAA,IAAI,CAAC,OAAO,EAAEe,KAAK,CAAC;IACtB,CAAC;IACD,MAAMK,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIC,EAAE,EAAEC,EAAE;MACV,MAAM;QACJnC,GAAG;QACHM,OAAO;QACPE;MACF,CAAC,GAAGT,KAAK;MACT,MAAMP,QAAQ,GAAGO,KAAK,CAACP,QAAQ,IAAIwB,aAAa,CAAC,UAAU,CAAC;MAC5D,OAAOpC,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEmC,OAAO;QACd,OAAO,EAAEf,GAAG,CAAC,MAAM,EAAE,CAACG,KAAK,CAACqB,KAAK,EAAE;UACjClC,QAAQ,EAAEA,QAAQ,CAACkC,KAAK;UACxBlB,OAAO;UACPE;QACF,CAAC,CAAC,CAAC;QACH,OAAO,EAAEL,KAAK,CAACqB,KAAK,KAAK,KAAK,GAAG;UAC/BY,QAAQ,EAAEtD,OAAO,CAACU,QAAQ;QAC5B,CAAC,GAAG;UACF6C,KAAK,EAAEvD,OAAO,CAACU,QAAQ,CAAC;UACxB8C,MAAM,EAAExD,OAAO,CAACU,QAAQ,CAAC;UACzBiC,WAAW,EAAE,CAACS,EAAE,GAAGX,SAAS,CAACC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGU,EAAE,CAACT;QAC5D;MACF,CAAC,EAAE,CAACX,KAAK,CAACgB,IAAI,GAAGhB,KAAK,CAACgB,IAAI,CAAC;QAC1BxB,OAAO;QACPhB,QAAQ,EAAEA,QAAQ,CAACkC;MACrB,CAAC,CAAC,GAAGrB,KAAK,CAACqB,KAAK,KAAK,KAAK,GAAG5C,YAAY,CAACO,IAAI,EAAE;QAC9C,MAAM,EAAEqB,aAAa,GAAG,OAAO,GAAG,SAAS;QAC3C,OAAO,EAAEe,SAAS,CAACC;MACrB,CAAC,EAAE,IAAI,CAAC,GAAG5C,YAAY,CAAC,KAAK,EAAE;QAC7B,OAAO,EAAEoB,GAAG,CAAC,iBAAiB,CAAC;QAC/B,OAAO,EAAE;UACP0B,eAAe,EAAE,CAACS,EAAE,GAAGZ,SAAS,CAACC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,EAAE,CAACT;QAChE;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,MAAMa,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAM;QACJjC;MACF,CAAC,GAAGP,KAAK;MACT,IAAIe,KAAK,CAACJ,OAAO,EAAE;QACjB,OAAO9B,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEmB,KAAK,CAACC,GAAG,CAAC,OAAO,EAAE,CAACD,KAAK,CAACH,aAAa,EAAE;YAChDN,QAAQ,EAAEA,QAAQ,CAACkC;UACrB,CAAC,CAAC;QACJ,CAAC,EAAE,CAACV,KAAK,CAACJ,OAAO,CAAC;UAChBJ,OAAO;UACPhB,QAAQ,EAAEA,QAAQ,CAACkC;QACrB,CAAC,CAAC,CAAC,CAAC;MACN;IACF,CAAC;IACD,OAAO,MAAM;MACX,MAAMgB,KAAK,GAAGzC,KAAK,CAACH,aAAa,KAAK,MAAM,GAAG,CAAC2C,WAAW,CAAC,CAAC,EAAEN,UAAU,CAAC,CAAC,CAAC,GAAG,CAACA,UAAU,CAAC,CAAC,EAAEM,WAAW,CAAC,CAAC,CAAC;MAC5G,OAAO3D,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAEmB,KAAK,CAACG,IAAI;QAClB,OAAO,EAAEH,KAAK,CAACC,GAAG,CAAC,CAAC;UAClBV,QAAQ,EAAEA,QAAQ,CAACkC,KAAK;UACxB,gBAAgB,EAAEzB,KAAK,CAACF;QAC1B,CAAC,EAAEyB,SAAS,CAACE,KAAK,CAAC,CAAC;QACpB,UAAU,EAAElC,QAAQ,CAACkC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;QACvC,cAAc,EAAEzB,KAAK,CAACO,OAAO;QAC7B,SAAS,EAAEqB;MACb,CAAC,EAAE,CAACa,KAAK,CAAC,CAAC;IACb,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEpD,YAAY,EACZU,aAAa,IAAIY,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}