{"ast": null, "code": "import { ref, watch, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, closest, createNamespace, makeArrayProp, makeNumericProp, preventDefault, truthProp, windowHeight } from \"../utils/index.mjs\";\nimport { useEventListener } from \"@vant/use\";\nimport { useLockScroll } from \"../composables/use-lock-scroll.mjs\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useSyncPropRef } from \"../composables/use-sync-prop-ref.mjs\";\nconst floatingPanelProps = {\n  height: makeNumericProp(0),\n  anchors: makeArrayProp(),\n  duration: makeNumericProp(0.3),\n  contentDraggable: truthProp,\n  lockScroll: Boolean,\n  safeAreaInsetBottom: truthProp\n};\nconst [name, bem] = createNamespace(\"floating-panel\");\nvar stdin_default = defineComponent({\n  name,\n  props: floatingPanelProps,\n  emits: [\"heightChange\", \"update:height\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const DAMP = 0.2;\n    const rootRef = ref();\n    const contentRef = ref();\n    const height = useSyncPropRef(() => +props.height, value => emit(\"update:height\", value));\n    const boundary = computed(() => {\n      var _a, _b;\n      return {\n        min: (_a = props.anchors[0]) != null ? _a : 100,\n        max: (_b = props.anchors[props.anchors.length - 1]) != null ? _b : Math.round(windowHeight.value * 0.6)\n      };\n    });\n    const anchors = computed(() => props.anchors.length >= 2 ? props.anchors : [boundary.value.min, boundary.value.max]);\n    const dragging = ref(false);\n    const rootStyle = computed(() => ({\n      height: addUnit(boundary.value.max),\n      transform: `translateY(calc(100% + ${addUnit(-height.value)}))`,\n      transition: !dragging.value ? `transform ${props.duration}s cubic-bezier(0.18, 0.89, 0.32, 1.28)` : \"none\"\n    }));\n    const ease = moveY => {\n      const absDistance = Math.abs(moveY);\n      const {\n        min,\n        max\n      } = boundary.value;\n      if (absDistance > max) {\n        return -(max + (absDistance - max) * DAMP);\n      }\n      if (absDistance < min) {\n        return -(min - (min - absDistance) * DAMP);\n      }\n      return moveY;\n    };\n    let startY;\n    let maxScroll = -1;\n    const touch = useTouch();\n    const onTouchstart = e => {\n      touch.start(e);\n      dragging.value = true;\n      startY = -height.value;\n      maxScroll = -1;\n    };\n    const onTouchmove = e => {\n      var _a;\n      touch.move(e);\n      const target = e.target;\n      if (contentRef.value === target || ((_a = contentRef.value) == null ? void 0 : _a.contains(target))) {\n        const {\n          scrollTop\n        } = contentRef.value;\n        maxScroll = Math.max(maxScroll, scrollTop);\n        if (!props.contentDraggable) return;\n        if (-startY < boundary.value.max) {\n          preventDefault(e, true);\n        } else if (!(scrollTop <= 0 && touch.deltaY.value > 0) || maxScroll > 0) {\n          return;\n        }\n      }\n      const moveY = touch.deltaY.value + startY;\n      height.value = -ease(moveY);\n    };\n    const onTouchend = () => {\n      maxScroll = -1;\n      dragging.value = false;\n      height.value = closest(anchors.value, height.value);\n      if (height.value !== -startY) {\n        emit(\"heightChange\", {\n          height: height.value\n        });\n      }\n    };\n    watch(boundary, () => {\n      height.value = closest(anchors.value, height.value);\n    }, {\n      immediate: true\n    });\n    useLockScroll(rootRef, () => props.lockScroll || dragging.value);\n    useEventListener(\"touchmove\", onTouchmove, {\n      target: rootRef\n    });\n    const renderHeader = () => {\n      if (slots.header) {\n        return slots.header();\n      }\n      return _createVNode(\"div\", {\n        \"class\": bem(\"header\")\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"header-bar\")\n      }, null)]);\n    };\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": [bem(), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }],\n        \"ref\": rootRef,\n        \"style\": rootStyle.value,\n        \"onTouchstartPassive\": onTouchstart,\n        \"onTouchend\": onTouchend,\n        \"onTouchcancel\": onTouchend\n      }, [renderHeader(), _createVNode(\"div\", {\n        \"class\": bem(\"content\"),\n        \"ref\": contentRef\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport { stdin_default as default, floatingPanelProps };", "map": {"version": 3, "names": ["ref", "watch", "computed", "defineComponent", "createVNode", "_createVNode", "addUnit", "closest", "createNamespace", "makeArrayProp", "makeNumericProp", "preventDefault", "truthProp", "windowHeight", "useEventListener", "useLockScroll", "useTouch", "useSyncPropRef", "floatingPanelProps", "height", "anchors", "duration", "contentDraggable", "lockScroll", "Boolean", "safeAreaInsetBottom", "name", "bem", "stdin_default", "props", "emits", "setup", "emit", "slots", "DAMP", "rootRef", "contentRef", "value", "boundary", "_a", "_b", "min", "max", "length", "Math", "round", "dragging", "rootStyle", "transform", "transition", "ease", "moveY", "absDistance", "abs", "startY", "maxScroll", "touch", "onTouchstart", "e", "start", "onTouchmove", "move", "target", "contains", "scrollTop", "deltaY", "onTouchend", "immediate", "renderHeader", "header", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/floating-panel/FloatingPanel.mjs"], "sourcesContent": ["import { ref, watch, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, closest, createNamespace, makeArrayProp, makeNumericProp, preventDefault, truthProp, windowHeight } from \"../utils/index.mjs\";\nimport { useEventListener } from \"@vant/use\";\nimport { useLockScroll } from \"../composables/use-lock-scroll.mjs\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useSyncPropRef } from \"../composables/use-sync-prop-ref.mjs\";\nconst floatingPanelProps = {\n  height: makeNumericProp(0),\n  anchors: makeArrayProp(),\n  duration: makeNumericProp(0.3),\n  contentDraggable: truthProp,\n  lockScroll: Boolean,\n  safeAreaInsetBottom: truthProp\n};\nconst [name, bem] = createNamespace(\"floating-panel\");\nvar stdin_default = defineComponent({\n  name,\n  props: floatingPanelProps,\n  emits: [\"heightChange\", \"update:height\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const DAMP = 0.2;\n    const rootRef = ref();\n    const contentRef = ref();\n    const height = useSyncPropRef(() => +props.height, (value) => emit(\"update:height\", value));\n    const boundary = computed(() => {\n      var _a, _b;\n      return {\n        min: (_a = props.anchors[0]) != null ? _a : 100,\n        max: (_b = props.anchors[props.anchors.length - 1]) != null ? _b : Math.round(windowHeight.value * 0.6)\n      };\n    });\n    const anchors = computed(() => props.anchors.length >= 2 ? props.anchors : [boundary.value.min, boundary.value.max]);\n    const dragging = ref(false);\n    const rootStyle = computed(() => ({\n      height: addUnit(boundary.value.max),\n      transform: `translateY(calc(100% + ${addUnit(-height.value)}))`,\n      transition: !dragging.value ? `transform ${props.duration}s cubic-bezier(0.18, 0.89, 0.32, 1.28)` : \"none\"\n    }));\n    const ease = (moveY) => {\n      const absDistance = Math.abs(moveY);\n      const {\n        min,\n        max\n      } = boundary.value;\n      if (absDistance > max) {\n        return -(max + (absDistance - max) * DAMP);\n      }\n      if (absDistance < min) {\n        return -(min - (min - absDistance) * DAMP);\n      }\n      return moveY;\n    };\n    let startY;\n    let maxScroll = -1;\n    const touch = useTouch();\n    const onTouchstart = (e) => {\n      touch.start(e);\n      dragging.value = true;\n      startY = -height.value;\n      maxScroll = -1;\n    };\n    const onTouchmove = (e) => {\n      var _a;\n      touch.move(e);\n      const target = e.target;\n      if (contentRef.value === target || ((_a = contentRef.value) == null ? void 0 : _a.contains(target))) {\n        const {\n          scrollTop\n        } = contentRef.value;\n        maxScroll = Math.max(maxScroll, scrollTop);\n        if (!props.contentDraggable) return;\n        if (-startY < boundary.value.max) {\n          preventDefault(e, true);\n        } else if (!(scrollTop <= 0 && touch.deltaY.value > 0) || maxScroll > 0) {\n          return;\n        }\n      }\n      const moveY = touch.deltaY.value + startY;\n      height.value = -ease(moveY);\n    };\n    const onTouchend = () => {\n      maxScroll = -1;\n      dragging.value = false;\n      height.value = closest(anchors.value, height.value);\n      if (height.value !== -startY) {\n        emit(\"heightChange\", {\n          height: height.value\n        });\n      }\n    };\n    watch(boundary, () => {\n      height.value = closest(anchors.value, height.value);\n    }, {\n      immediate: true\n    });\n    useLockScroll(rootRef, () => props.lockScroll || dragging.value);\n    useEventListener(\"touchmove\", onTouchmove, {\n      target: rootRef\n    });\n    const renderHeader = () => {\n      if (slots.header) {\n        return slots.header();\n      }\n      return _createVNode(\"div\", {\n        \"class\": bem(\"header\")\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"header-bar\")\n      }, null)]);\n    };\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": [bem(), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }],\n        \"ref\": rootRef,\n        \"style\": rootStyle.value,\n        \"onTouchstartPassive\": onTouchstart,\n        \"onTouchend\": onTouchend,\n        \"onTouchcancel\": onTouchend\n      }, [renderHeader(), _createVNode(\"div\", {\n        \"class\": bem(\"content\"),\n        \"ref\": contentRef\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  floatingPanelProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACxF,SAASC,OAAO,EAAEC,OAAO,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe,EAAEC,cAAc,EAAEC,SAAS,EAAEC,YAAY,QAAQ,oBAAoB;AAC/I,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,cAAc,QAAQ,sCAAsC;AACrE,MAAMC,kBAAkB,GAAG;EACzBC,MAAM,EAAET,eAAe,CAAC,CAAC,CAAC;EAC1BU,OAAO,EAAEX,aAAa,CAAC,CAAC;EACxBY,QAAQ,EAAEX,eAAe,CAAC,GAAG,CAAC;EAC9BY,gBAAgB,EAAEV,SAAS;EAC3BW,UAAU,EAAEC,OAAO;EACnBC,mBAAmB,EAAEb;AACvB,CAAC;AACD,MAAM,CAACc,IAAI,EAAEC,GAAG,CAAC,GAAGnB,eAAe,CAAC,gBAAgB,CAAC;AACrD,IAAIoB,aAAa,GAAGzB,eAAe,CAAC;EAClCuB,IAAI;EACJG,KAAK,EAAEX,kBAAkB;EACzBY,KAAK,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC;EACxCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAG,GAAG;IAChB,MAAMC,OAAO,GAAGnC,GAAG,CAAC,CAAC;IACrB,MAAMoC,UAAU,GAAGpC,GAAG,CAAC,CAAC;IACxB,MAAMmB,MAAM,GAAGF,cAAc,CAAC,MAAM,CAACY,KAAK,CAACV,MAAM,EAAGkB,KAAK,IAAKL,IAAI,CAAC,eAAe,EAAEK,KAAK,CAAC,CAAC;IAC3F,MAAMC,QAAQ,GAAGpC,QAAQ,CAAC,MAAM;MAC9B,IAAIqC,EAAE,EAAEC,EAAE;MACV,OAAO;QACLC,GAAG,EAAE,CAACF,EAAE,GAAGV,KAAK,CAACT,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGmB,EAAE,GAAG,GAAG;QAC/CG,GAAG,EAAE,CAACF,EAAE,GAAGX,KAAK,CAACT,OAAO,CAACS,KAAK,CAACT,OAAO,CAACuB,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,GAAGH,EAAE,GAAGI,IAAI,CAACC,KAAK,CAAChC,YAAY,CAACwB,KAAK,GAAG,GAAG;MACxG,CAAC;IACH,CAAC,CAAC;IACF,MAAMjB,OAAO,GAAGlB,QAAQ,CAAC,MAAM2B,KAAK,CAACT,OAAO,CAACuB,MAAM,IAAI,CAAC,GAAGd,KAAK,CAACT,OAAO,GAAG,CAACkB,QAAQ,CAACD,KAAK,CAACI,GAAG,EAAEH,QAAQ,CAACD,KAAK,CAACK,GAAG,CAAC,CAAC;IACpH,MAAMI,QAAQ,GAAG9C,GAAG,CAAC,KAAK,CAAC;IAC3B,MAAM+C,SAAS,GAAG7C,QAAQ,CAAC,OAAO;MAChCiB,MAAM,EAAEb,OAAO,CAACgC,QAAQ,CAACD,KAAK,CAACK,GAAG,CAAC;MACnCM,SAAS,EAAE,0BAA0B1C,OAAO,CAAC,CAACa,MAAM,CAACkB,KAAK,CAAC,IAAI;MAC/DY,UAAU,EAAE,CAACH,QAAQ,CAACT,KAAK,GAAG,aAAaR,KAAK,CAACR,QAAQ,wCAAwC,GAAG;IACtG,CAAC,CAAC,CAAC;IACH,MAAM6B,IAAI,GAAIC,KAAK,IAAK;MACtB,MAAMC,WAAW,GAAGR,IAAI,CAACS,GAAG,CAACF,KAAK,CAAC;MACnC,MAAM;QACJV,GAAG;QACHC;MACF,CAAC,GAAGJ,QAAQ,CAACD,KAAK;MAClB,IAAIe,WAAW,GAAGV,GAAG,EAAE;QACrB,OAAO,EAAEA,GAAG,GAAG,CAACU,WAAW,GAAGV,GAAG,IAAIR,IAAI,CAAC;MAC5C;MACA,IAAIkB,WAAW,GAAGX,GAAG,EAAE;QACrB,OAAO,EAAEA,GAAG,GAAG,CAACA,GAAG,GAAGW,WAAW,IAAIlB,IAAI,CAAC;MAC5C;MACA,OAAOiB,KAAK;IACd,CAAC;IACD,IAAIG,MAAM;IACV,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAGxC,QAAQ,CAAC,CAAC;IACxB,MAAMyC,YAAY,GAAIC,CAAC,IAAK;MAC1BF,KAAK,CAACG,KAAK,CAACD,CAAC,CAAC;MACdZ,QAAQ,CAACT,KAAK,GAAG,IAAI;MACrBiB,MAAM,GAAG,CAACnC,MAAM,CAACkB,KAAK;MACtBkB,SAAS,GAAG,CAAC,CAAC;IAChB,CAAC;IACD,MAAMK,WAAW,GAAIF,CAAC,IAAK;MACzB,IAAInB,EAAE;MACNiB,KAAK,CAACK,IAAI,CAACH,CAAC,CAAC;MACb,MAAMI,MAAM,GAAGJ,CAAC,CAACI,MAAM;MACvB,IAAI1B,UAAU,CAACC,KAAK,KAAKyB,MAAM,KAAK,CAACvB,EAAE,GAAGH,UAAU,CAACC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,EAAE,CAACwB,QAAQ,CAACD,MAAM,CAAC,CAAC,EAAE;QACnG,MAAM;UACJE;QACF,CAAC,GAAG5B,UAAU,CAACC,KAAK;QACpBkB,SAAS,GAAGX,IAAI,CAACF,GAAG,CAACa,SAAS,EAAES,SAAS,CAAC;QAC1C,IAAI,CAACnC,KAAK,CAACP,gBAAgB,EAAE;QAC7B,IAAI,CAACgC,MAAM,GAAGhB,QAAQ,CAACD,KAAK,CAACK,GAAG,EAAE;UAChC/B,cAAc,CAAC+C,CAAC,EAAE,IAAI,CAAC;QACzB,CAAC,MAAM,IAAI,EAAEM,SAAS,IAAI,CAAC,IAAIR,KAAK,CAACS,MAAM,CAAC5B,KAAK,GAAG,CAAC,CAAC,IAAIkB,SAAS,GAAG,CAAC,EAAE;UACvE;QACF;MACF;MACA,MAAMJ,KAAK,GAAGK,KAAK,CAACS,MAAM,CAAC5B,KAAK,GAAGiB,MAAM;MACzCnC,MAAM,CAACkB,KAAK,GAAG,CAACa,IAAI,CAACC,KAAK,CAAC;IAC7B,CAAC;IACD,MAAMe,UAAU,GAAGA,CAAA,KAAM;MACvBX,SAAS,GAAG,CAAC,CAAC;MACdT,QAAQ,CAACT,KAAK,GAAG,KAAK;MACtBlB,MAAM,CAACkB,KAAK,GAAG9B,OAAO,CAACa,OAAO,CAACiB,KAAK,EAAElB,MAAM,CAACkB,KAAK,CAAC;MACnD,IAAIlB,MAAM,CAACkB,KAAK,KAAK,CAACiB,MAAM,EAAE;QAC5BtB,IAAI,CAAC,cAAc,EAAE;UACnBb,MAAM,EAAEA,MAAM,CAACkB;QACjB,CAAC,CAAC;MACJ;IACF,CAAC;IACDpC,KAAK,CAACqC,QAAQ,EAAE,MAAM;MACpBnB,MAAM,CAACkB,KAAK,GAAG9B,OAAO,CAACa,OAAO,CAACiB,KAAK,EAAElB,MAAM,CAACkB,KAAK,CAAC;IACrD,CAAC,EAAE;MACD8B,SAAS,EAAE;IACb,CAAC,CAAC;IACFpD,aAAa,CAACoB,OAAO,EAAE,MAAMN,KAAK,CAACN,UAAU,IAAIuB,QAAQ,CAACT,KAAK,CAAC;IAChEvB,gBAAgB,CAAC,WAAW,EAAE8C,WAAW,EAAE;MACzCE,MAAM,EAAE3B;IACV,CAAC,CAAC;IACF,MAAMiC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAInC,KAAK,CAACoC,MAAM,EAAE;QAChB,OAAOpC,KAAK,CAACoC,MAAM,CAAC,CAAC;MACvB;MACA,OAAOhE,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEsB,GAAG,CAAC,QAAQ;MACvB,CAAC,EAAE,CAACtB,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEsB,GAAG,CAAC,YAAY;MAC3B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,OAAO,MAAM;MACX,IAAIY,EAAE;MACN,OAAOlC,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAE,CAACsB,GAAG,CAAC,CAAC,EAAE;UACf,sBAAsB,EAAEE,KAAK,CAACJ;QAChC,CAAC,CAAC;QACF,KAAK,EAAEU,OAAO;QACd,OAAO,EAAEY,SAAS,CAACV,KAAK;QACxB,qBAAqB,EAAEoB,YAAY;QACnC,YAAY,EAAES,UAAU;QACxB,eAAe,EAAEA;MACnB,CAAC,EAAE,CAACE,YAAY,CAAC,CAAC,EAAE/D,YAAY,CAAC,KAAK,EAAE;QACtC,OAAO,EAAEsB,GAAG,CAAC,SAAS,CAAC;QACvB,KAAK,EAAES;MACT,CAAC,EAAE,CAAC,CAACG,EAAE,GAAGN,KAAK,CAACqC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG/B,EAAE,CAACgC,IAAI,CAACtC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEL,aAAa,IAAI0C,OAAO,EACxBpD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}