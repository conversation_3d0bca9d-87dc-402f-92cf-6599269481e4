{"ast": null, "code": "import { computed, ref, onMounted, defineComponent, watch, createVNode as _createVNode } from \"vue\";\nimport { inBrowser, makeNumberProp, makeStringProp, createNamespace, preventDefault, windowWidth } from \"../utils/index.mjs\";\nimport { useRect } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Button } from \"../button/index.mjs\";\nconst [name, bem, t] = createNamespace(\"signature\");\nconst signatureProps = {\n  tips: String,\n  type: makeStringProp(\"png\"),\n  penColor: makeStringProp(\"#000\"),\n  lineWidth: makeNumberProp(3),\n  clearButtonText: String,\n  backgroundColor: makeStringProp(\"\"),\n  confirmButtonText: String\n};\nconst hasCanvasSupport = () => {\n  var _a;\n  const canvas = document.createElement(\"canvas\");\n  return !!((_a = canvas.getContext) == null ? void 0 : _a.call(canvas, \"2d\"));\n};\nvar stdin_default = defineComponent({\n  name,\n  props: signatureProps,\n  emits: [\"submit\", \"clear\", \"start\", \"end\", \"signing\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const canvasRef = ref();\n    const wrapRef = ref();\n    const ctx = computed(() => {\n      if (!canvasRef.value) return null;\n      return canvasRef.value.getContext(\"2d\");\n    });\n    const isRenderCanvas = inBrowser ? hasCanvasSupport() : true;\n    let canvasWidth = 0;\n    let canvasHeight = 0;\n    let canvasRect;\n    const touchStart = () => {\n      if (!ctx.value) {\n        return false;\n      }\n      ctx.value.beginPath();\n      ctx.value.lineWidth = props.lineWidth;\n      ctx.value.strokeStyle = props.penColor;\n      canvasRect = useRect(canvasRef);\n      emit(\"start\");\n    };\n    const touchMove = event => {\n      if (!ctx.value) {\n        return false;\n      }\n      preventDefault(event);\n      const touch = event.touches[0];\n      const mouseX = touch.clientX - ((canvasRect == null ? void 0 : canvasRect.left) || 0);\n      const mouseY = touch.clientY - ((canvasRect == null ? void 0 : canvasRect.top) || 0);\n      ctx.value.lineCap = \"round\";\n      ctx.value.lineJoin = \"round\";\n      ctx.value.lineTo(mouseX, mouseY);\n      ctx.value.stroke();\n      emit(\"signing\", event);\n    };\n    const touchEnd = event => {\n      preventDefault(event);\n      emit(\"end\");\n    };\n    const isCanvasEmpty = canvas => {\n      const empty = document.createElement(\"canvas\");\n      empty.width = canvas.width;\n      empty.height = canvas.height;\n      if (props.backgroundColor) {\n        const emptyCtx = empty.getContext(\"2d\");\n        setCanvasBgColor(emptyCtx);\n      }\n      return canvas.toDataURL() === empty.toDataURL();\n    };\n    const setCanvasBgColor = ctx2 => {\n      if (ctx2 && props.backgroundColor) {\n        ctx2.fillStyle = props.backgroundColor;\n        ctx2.fillRect(0, 0, canvasWidth, canvasHeight);\n      }\n    };\n    const submit = () => {\n      var _a, _b;\n      const canvas = canvasRef.value;\n      if (!canvas) {\n        return;\n      }\n      const isEmpty = isCanvasEmpty(canvas);\n      const image = isEmpty ? \"\" : ((_b = (_a = {\n        jpg: () => canvas.toDataURL(\"image/jpeg\", 0.8),\n        jpeg: () => canvas.toDataURL(\"image/jpeg\", 0.8)\n      })[props.type]) == null ? void 0 : _b.call(_a)) || canvas.toDataURL(`image/${props.type}`);\n      emit(\"submit\", {\n        image,\n        canvas\n      });\n    };\n    const clear = () => {\n      if (ctx.value) {\n        ctx.value.clearRect(0, 0, canvasWidth, canvasHeight);\n        ctx.value.closePath();\n        setCanvasBgColor(ctx.value);\n      }\n      emit(\"clear\");\n    };\n    const initialize = () => {\n      var _a, _b, _c;\n      if (isRenderCanvas && canvasRef.value) {\n        const canvas = canvasRef.value;\n        const dpr = inBrowser ? window.devicePixelRatio : 1;\n        canvasWidth = canvas.width = (((_a = wrapRef.value) == null ? void 0 : _a.offsetWidth) || 0) * dpr;\n        canvasHeight = canvas.height = (((_b = wrapRef.value) == null ? void 0 : _b.offsetHeight) || 0) * dpr;\n        (_c = ctx.value) == null ? void 0 : _c.scale(dpr, dpr);\n        setCanvasBgColor(ctx.value);\n      }\n    };\n    const resize = () => {\n      if (ctx.value) {\n        const data = ctx.value.getImageData(0, 0, canvasWidth, canvasHeight);\n        initialize();\n        ctx.value.putImageData(data, 0, 0);\n      }\n    };\n    watch(windowWidth, resize);\n    onMounted(initialize);\n    useExpose({\n      resize,\n      clear,\n      submit\n    });\n    return () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [_createVNode(\"div\", {\n      \"class\": bem(\"content\"),\n      \"ref\": wrapRef\n    }, [isRenderCanvas ? _createVNode(\"canvas\", {\n      \"ref\": canvasRef,\n      \"onTouchstartPassive\": touchStart,\n      \"onTouchmove\": touchMove,\n      \"onTouchend\": touchEnd\n    }, null) : slots.tips ? slots.tips() : _createVNode(\"p\", null, [props.tips])]), _createVNode(\"div\", {\n      \"class\": bem(\"footer\")\n    }, [_createVNode(Button, {\n      \"size\": \"small\",\n      \"onClick\": clear\n    }, {\n      default: () => [props.clearButtonText || t(\"clear\")]\n    }), _createVNode(Button, {\n      \"type\": \"primary\",\n      \"size\": \"small\",\n      \"onClick\": submit\n    }, {\n      default: () => [props.confirmButtonText || t(\"confirm\")]\n    })])]);\n  }\n});\nexport { stdin_default as default, signatureProps };", "map": {"version": 3, "names": ["computed", "ref", "onMounted", "defineComponent", "watch", "createVNode", "_createVNode", "inBrowser", "makeNumberProp", "makeStringProp", "createNamespace", "preventDefault", "windowWidth", "useRect", "useExpose", "<PERSON><PERSON>", "name", "bem", "t", "signatureProps", "tips", "String", "type", "penColor", "lineWidth", "clearButtonText", "backgroundColor", "confirmButtonText", "hasCanvasSupport", "_a", "canvas", "document", "createElement", "getContext", "call", "stdin_default", "props", "emits", "setup", "emit", "slots", "canvasRef", "wrapRef", "ctx", "value", "isRenderCanvas", "canvasWidth", "canvasHeight", "canvasRect", "touchStart", "beginPath", "strokeStyle", "touchMove", "event", "touch", "touches", "mouseX", "clientX", "left", "mouseY", "clientY", "top", "lineCap", "lineJoin", "lineTo", "stroke", "touchEnd", "isCanvasEmpty", "empty", "width", "height", "emptyCtx", "setCanvasBgColor", "toDataURL", "ctx2", "fillStyle", "fillRect", "submit", "_b", "isEmpty", "image", "jpg", "jpeg", "clear", "clearRect", "closePath", "initialize", "_c", "dpr", "window", "devicePixelRatio", "offsetWidth", "offsetHeight", "scale", "resize", "data", "getImageData", "putImageData", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/signature/Signature.mjs"], "sourcesContent": ["import { computed, ref, onMounted, defineComponent, watch, createVNode as _createVNode } from \"vue\";\nimport { inBrowser, makeNumberProp, makeStringProp, createNamespace, preventDefault, windowWidth } from \"../utils/index.mjs\";\nimport { useRect } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Button } from \"../button/index.mjs\";\nconst [name, bem, t] = createNamespace(\"signature\");\nconst signatureProps = {\n  tips: String,\n  type: makeStringProp(\"png\"),\n  penColor: makeStringProp(\"#000\"),\n  lineWidth: makeNumberProp(3),\n  clearButtonText: String,\n  backgroundColor: makeStringProp(\"\"),\n  confirmButtonText: String\n};\nconst hasCanvasSupport = () => {\n  var _a;\n  const canvas = document.createElement(\"canvas\");\n  return !!((_a = canvas.getContext) == null ? void 0 : _a.call(canvas, \"2d\"));\n};\nvar stdin_default = defineComponent({\n  name,\n  props: signatureProps,\n  emits: [\"submit\", \"clear\", \"start\", \"end\", \"signing\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const canvasRef = ref();\n    const wrapRef = ref();\n    const ctx = computed(() => {\n      if (!canvasRef.value) return null;\n      return canvasRef.value.getContext(\"2d\");\n    });\n    const isRenderCanvas = inBrowser ? hasCanvasSupport() : true;\n    let canvasWidth = 0;\n    let canvasHeight = 0;\n    let canvasRect;\n    const touchStart = () => {\n      if (!ctx.value) {\n        return false;\n      }\n      ctx.value.beginPath();\n      ctx.value.lineWidth = props.lineWidth;\n      ctx.value.strokeStyle = props.penColor;\n      canvasRect = useRect(canvasRef);\n      emit(\"start\");\n    };\n    const touchMove = (event) => {\n      if (!ctx.value) {\n        return false;\n      }\n      preventDefault(event);\n      const touch = event.touches[0];\n      const mouseX = touch.clientX - ((canvasRect == null ? void 0 : canvasRect.left) || 0);\n      const mouseY = touch.clientY - ((canvasRect == null ? void 0 : canvasRect.top) || 0);\n      ctx.value.lineCap = \"round\";\n      ctx.value.lineJoin = \"round\";\n      ctx.value.lineTo(mouseX, mouseY);\n      ctx.value.stroke();\n      emit(\"signing\", event);\n    };\n    const touchEnd = (event) => {\n      preventDefault(event);\n      emit(\"end\");\n    };\n    const isCanvasEmpty = (canvas) => {\n      const empty = document.createElement(\"canvas\");\n      empty.width = canvas.width;\n      empty.height = canvas.height;\n      if (props.backgroundColor) {\n        const emptyCtx = empty.getContext(\"2d\");\n        setCanvasBgColor(emptyCtx);\n      }\n      return canvas.toDataURL() === empty.toDataURL();\n    };\n    const setCanvasBgColor = (ctx2) => {\n      if (ctx2 && props.backgroundColor) {\n        ctx2.fillStyle = props.backgroundColor;\n        ctx2.fillRect(0, 0, canvasWidth, canvasHeight);\n      }\n    };\n    const submit = () => {\n      var _a, _b;\n      const canvas = canvasRef.value;\n      if (!canvas) {\n        return;\n      }\n      const isEmpty = isCanvasEmpty(canvas);\n      const image = isEmpty ? \"\" : ((_b = (_a = {\n        jpg: () => canvas.toDataURL(\"image/jpeg\", 0.8),\n        jpeg: () => canvas.toDataURL(\"image/jpeg\", 0.8)\n      })[props.type]) == null ? void 0 : _b.call(_a)) || canvas.toDataURL(`image/${props.type}`);\n      emit(\"submit\", {\n        image,\n        canvas\n      });\n    };\n    const clear = () => {\n      if (ctx.value) {\n        ctx.value.clearRect(0, 0, canvasWidth, canvasHeight);\n        ctx.value.closePath();\n        setCanvasBgColor(ctx.value);\n      }\n      emit(\"clear\");\n    };\n    const initialize = () => {\n      var _a, _b, _c;\n      if (isRenderCanvas && canvasRef.value) {\n        const canvas = canvasRef.value;\n        const dpr = inBrowser ? window.devicePixelRatio : 1;\n        canvasWidth = canvas.width = (((_a = wrapRef.value) == null ? void 0 : _a.offsetWidth) || 0) * dpr;\n        canvasHeight = canvas.height = (((_b = wrapRef.value) == null ? void 0 : _b.offsetHeight) || 0) * dpr;\n        (_c = ctx.value) == null ? void 0 : _c.scale(dpr, dpr);\n        setCanvasBgColor(ctx.value);\n      }\n    };\n    const resize = () => {\n      if (ctx.value) {\n        const data = ctx.value.getImageData(0, 0, canvasWidth, canvasHeight);\n        initialize();\n        ctx.value.putImageData(data, 0, 0);\n      }\n    };\n    watch(windowWidth, resize);\n    onMounted(initialize);\n    useExpose({\n      resize,\n      clear,\n      submit\n    });\n    return () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [_createVNode(\"div\", {\n      \"class\": bem(\"content\"),\n      \"ref\": wrapRef\n    }, [isRenderCanvas ? _createVNode(\"canvas\", {\n      \"ref\": canvasRef,\n      \"onTouchstartPassive\": touchStart,\n      \"onTouchmove\": touchMove,\n      \"onTouchend\": touchEnd\n    }, null) : slots.tips ? slots.tips() : _createVNode(\"p\", null, [props.tips])]), _createVNode(\"div\", {\n      \"class\": bem(\"footer\")\n    }, [_createVNode(Button, {\n      \"size\": \"small\",\n      \"onClick\": clear\n    }, {\n      default: () => [props.clearButtonText || t(\"clear\")]\n    }), _createVNode(Button, {\n      \"type\": \"primary\",\n      \"size\": \"small\",\n      \"onClick\": submit\n    }, {\n      default: () => [props.confirmButtonText || t(\"confirm\")]\n    })])]);\n  }\n});\nexport {\n  stdin_default as default,\n  signatureProps\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,GAAG,EAAEC,SAAS,EAAEC,eAAe,EAAEC,KAAK,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACnG,SAASC,SAAS,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,cAAc,EAAEC,WAAW,QAAQ,oBAAoB;AAC5H,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGR,eAAe,CAAC,WAAW,CAAC;AACnD,MAAMS,cAAc,GAAG;EACrBC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAEb,cAAc,CAAC,KAAK,CAAC;EAC3Bc,QAAQ,EAAEd,cAAc,CAAC,MAAM,CAAC;EAChCe,SAAS,EAAEhB,cAAc,CAAC,CAAC,CAAC;EAC5BiB,eAAe,EAAEJ,MAAM;EACvBK,eAAe,EAAEjB,cAAc,CAAC,EAAE,CAAC;EACnCkB,iBAAiB,EAAEN;AACrB,CAAC;AACD,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,IAAIC,EAAE;EACN,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,OAAO,CAAC,EAAE,CAACH,EAAE,GAAGC,MAAM,CAACG,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACK,IAAI,CAACJ,MAAM,EAAE,IAAI,CAAC,CAAC;AAC9E,CAAC;AACD,IAAIK,aAAa,GAAGhC,eAAe,CAAC;EAClCa,IAAI;EACJoB,KAAK,EAAEjB,cAAc;EACrBkB,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC;EACrDC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,SAAS,GAAGxC,GAAG,CAAC,CAAC;IACvB,MAAMyC,OAAO,GAAGzC,GAAG,CAAC,CAAC;IACrB,MAAM0C,GAAG,GAAG3C,QAAQ,CAAC,MAAM;MACzB,IAAI,CAACyC,SAAS,CAACG,KAAK,EAAE,OAAO,IAAI;MACjC,OAAOH,SAAS,CAACG,KAAK,CAACX,UAAU,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC;IACF,MAAMY,cAAc,GAAGtC,SAAS,GAAGqB,gBAAgB,CAAC,CAAC,GAAG,IAAI;IAC5D,IAAIkB,WAAW,GAAG,CAAC;IACnB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,UAAU;IACd,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI,CAACN,GAAG,CAACC,KAAK,EAAE;QACd,OAAO,KAAK;MACd;MACAD,GAAG,CAACC,KAAK,CAACM,SAAS,CAAC,CAAC;MACrBP,GAAG,CAACC,KAAK,CAACpB,SAAS,GAAGY,KAAK,CAACZ,SAAS;MACrCmB,GAAG,CAACC,KAAK,CAACO,WAAW,GAAGf,KAAK,CAACb,QAAQ;MACtCyB,UAAU,GAAGnC,OAAO,CAAC4B,SAAS,CAAC;MAC/BF,IAAI,CAAC,OAAO,CAAC;IACf,CAAC;IACD,MAAMa,SAAS,GAAIC,KAAK,IAAK;MAC3B,IAAI,CAACV,GAAG,CAACC,KAAK,EAAE;QACd,OAAO,KAAK;MACd;MACAjC,cAAc,CAAC0C,KAAK,CAAC;MACrB,MAAMC,KAAK,GAAGD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;MAC9B,MAAMC,MAAM,GAAGF,KAAK,CAACG,OAAO,IAAI,CAACT,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACU,IAAI,KAAK,CAAC,CAAC;MACrF,MAAMC,MAAM,GAAGL,KAAK,CAACM,OAAO,IAAI,CAACZ,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACa,GAAG,KAAK,CAAC,CAAC;MACpFlB,GAAG,CAACC,KAAK,CAACkB,OAAO,GAAG,OAAO;MAC3BnB,GAAG,CAACC,KAAK,CAACmB,QAAQ,GAAG,OAAO;MAC5BpB,GAAG,CAACC,KAAK,CAACoB,MAAM,CAACR,MAAM,EAAEG,MAAM,CAAC;MAChChB,GAAG,CAACC,KAAK,CAACqB,MAAM,CAAC,CAAC;MAClB1B,IAAI,CAAC,SAAS,EAAEc,KAAK,CAAC;IACxB,CAAC;IACD,MAAMa,QAAQ,GAAIb,KAAK,IAAK;MAC1B1C,cAAc,CAAC0C,KAAK,CAAC;MACrBd,IAAI,CAAC,KAAK,CAAC;IACb,CAAC;IACD,MAAM4B,aAAa,GAAIrC,MAAM,IAAK;MAChC,MAAMsC,KAAK,GAAGrC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC9CoC,KAAK,CAACC,KAAK,GAAGvC,MAAM,CAACuC,KAAK;MAC1BD,KAAK,CAACE,MAAM,GAAGxC,MAAM,CAACwC,MAAM;MAC5B,IAAIlC,KAAK,CAACV,eAAe,EAAE;QACzB,MAAM6C,QAAQ,GAAGH,KAAK,CAACnC,UAAU,CAAC,IAAI,CAAC;QACvCuC,gBAAgB,CAACD,QAAQ,CAAC;MAC5B;MACA,OAAOzC,MAAM,CAAC2C,SAAS,CAAC,CAAC,KAAKL,KAAK,CAACK,SAAS,CAAC,CAAC;IACjD,CAAC;IACD,MAAMD,gBAAgB,GAAIE,IAAI,IAAK;MACjC,IAAIA,IAAI,IAAItC,KAAK,CAACV,eAAe,EAAE;QACjCgD,IAAI,CAACC,SAAS,GAAGvC,KAAK,CAACV,eAAe;QACtCgD,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE9B,WAAW,EAAEC,YAAY,CAAC;MAChD;IACF,CAAC;IACD,MAAM8B,MAAM,GAAGA,CAAA,KAAM;MACnB,IAAIhD,EAAE,EAAEiD,EAAE;MACV,MAAMhD,MAAM,GAAGW,SAAS,CAACG,KAAK;MAC9B,IAAI,CAACd,MAAM,EAAE;QACX;MACF;MACA,MAAMiD,OAAO,GAAGZ,aAAa,CAACrC,MAAM,CAAC;MACrC,MAAMkD,KAAK,GAAGD,OAAO,GAAG,EAAE,GAAG,CAAC,CAACD,EAAE,GAAG,CAACjD,EAAE,GAAG;QACxCoD,GAAG,EAAEA,CAAA,KAAMnD,MAAM,CAAC2C,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC;QAC9CS,IAAI,EAAEA,CAAA,KAAMpD,MAAM,CAAC2C,SAAS,CAAC,YAAY,EAAE,GAAG;MAChD,CAAC,EAAErC,KAAK,CAACd,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwD,EAAE,CAAC5C,IAAI,CAACL,EAAE,CAAC,KAAKC,MAAM,CAAC2C,SAAS,CAAC,SAASrC,KAAK,CAACd,IAAI,EAAE,CAAC;MAC1FiB,IAAI,CAAC,QAAQ,EAAE;QACbyC,KAAK;QACLlD;MACF,CAAC,CAAC;IACJ,CAAC;IACD,MAAMqD,KAAK,GAAGA,CAAA,KAAM;MAClB,IAAIxC,GAAG,CAACC,KAAK,EAAE;QACbD,GAAG,CAACC,KAAK,CAACwC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEtC,WAAW,EAAEC,YAAY,CAAC;QACpDJ,GAAG,CAACC,KAAK,CAACyC,SAAS,CAAC,CAAC;QACrBb,gBAAgB,CAAC7B,GAAG,CAACC,KAAK,CAAC;MAC7B;MACAL,IAAI,CAAC,OAAO,CAAC;IACf,CAAC;IACD,MAAM+C,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIzD,EAAE,EAAEiD,EAAE,EAAES,EAAE;MACd,IAAI1C,cAAc,IAAIJ,SAAS,CAACG,KAAK,EAAE;QACrC,MAAMd,MAAM,GAAGW,SAAS,CAACG,KAAK;QAC9B,MAAM4C,GAAG,GAAGjF,SAAS,GAAGkF,MAAM,CAACC,gBAAgB,GAAG,CAAC;QACnD5C,WAAW,GAAGhB,MAAM,CAACuC,KAAK,GAAG,CAAC,CAAC,CAACxC,EAAE,GAAGa,OAAO,CAACE,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGf,EAAE,CAAC8D,WAAW,KAAK,CAAC,IAAIH,GAAG;QAClGzC,YAAY,GAAGjB,MAAM,CAACwC,MAAM,GAAG,CAAC,CAAC,CAACQ,EAAE,GAAGpC,OAAO,CAACE,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkC,EAAE,CAACc,YAAY,KAAK,CAAC,IAAIJ,GAAG;QACrG,CAACD,EAAE,GAAG5C,GAAG,CAACC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2C,EAAE,CAACM,KAAK,CAACL,GAAG,EAAEA,GAAG,CAAC;QACtDhB,gBAAgB,CAAC7B,GAAG,CAACC,KAAK,CAAC;MAC7B;IACF,CAAC;IACD,MAAMkD,MAAM,GAAGA,CAAA,KAAM;MACnB,IAAInD,GAAG,CAACC,KAAK,EAAE;QACb,MAAMmD,IAAI,GAAGpD,GAAG,CAACC,KAAK,CAACoD,YAAY,CAAC,CAAC,EAAE,CAAC,EAAElD,WAAW,EAAEC,YAAY,CAAC;QACpEuC,UAAU,CAAC,CAAC;QACZ3C,GAAG,CAACC,KAAK,CAACqD,YAAY,CAACF,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MACpC;IACF,CAAC;IACD3F,KAAK,CAACQ,WAAW,EAAEkF,MAAM,CAAC;IAC1B5F,SAAS,CAACoF,UAAU,CAAC;IACrBxE,SAAS,CAAC;MACRgF,MAAM;MACNX,KAAK;MACLN;IACF,CAAC,CAAC;IACF,OAAO,MAAMvE,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEW,GAAG,CAAC;IACf,CAAC,EAAE,CAACX,YAAY,CAAC,KAAK,EAAE;MACtB,OAAO,EAAEW,GAAG,CAAC,SAAS,CAAC;MACvB,KAAK,EAAEyB;IACT,CAAC,EAAE,CAACG,cAAc,GAAGvC,YAAY,CAAC,QAAQ,EAAE;MAC1C,KAAK,EAAEmC,SAAS;MAChB,qBAAqB,EAAEQ,UAAU;MACjC,aAAa,EAAEG,SAAS;MACxB,YAAY,EAAEc;IAChB,CAAC,EAAE,IAAI,CAAC,GAAG1B,KAAK,CAACpB,IAAI,GAAGoB,KAAK,CAACpB,IAAI,CAAC,CAAC,GAAGd,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC8B,KAAK,CAAChB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEd,YAAY,CAAC,KAAK,EAAE;MAClG,OAAO,EAAEW,GAAG,CAAC,QAAQ;IACvB,CAAC,EAAE,CAACX,YAAY,CAACS,MAAM,EAAE;MACvB,MAAM,EAAE,OAAO;MACf,SAAS,EAAEoE;IACb,CAAC,EAAE;MACDe,OAAO,EAAEA,CAAA,KAAM,CAAC9D,KAAK,CAACX,eAAe,IAAIP,CAAC,CAAC,OAAO,CAAC;IACrD,CAAC,CAAC,EAAEZ,YAAY,CAACS,MAAM,EAAE;MACvB,MAAM,EAAE,SAAS;MACjB,MAAM,EAAE,OAAO;MACf,SAAS,EAAE8D;IACb,CAAC,EAAE;MACDqB,OAAO,EAAEA,CAAA,KAAM,CAAC9D,KAAK,CAACT,iBAAiB,IAAIT,CAAC,CAAC,SAAS,CAAC;IACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AACF,SACEiB,aAAa,IAAI+D,OAAO,EACxB/E,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}