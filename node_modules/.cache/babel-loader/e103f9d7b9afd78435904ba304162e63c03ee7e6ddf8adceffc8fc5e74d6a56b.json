{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Dialog from \"./Dialog.mjs\";\nconst Dialog = withInstall(_Dialog);\nvar stdin_default = Dialog;\nimport { dialogProps } from \"./Dialog.mjs\";\nimport { showDialog, closeDialog, showConfirmDialog, setDialogDefaultOptions, resetDialogDefaultOptions } from \"./function-call.mjs\";\nexport { Dialog, closeDialog, stdin_default as default, dialogProps, resetDialogDefaultOptions, setDialogDefaultOptions, showConfirmDialog, showDialog };", "map": {"version": 3, "names": ["withInstall", "_Dialog", "Dialog", "stdin_default", "dialogProps", "showDialog", "closeDialog", "showConfirmDialog", "setDialogDefaultOptions", "resetDialogDefaultOptions", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/dialog/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Dialog from \"./Dialog.mjs\";\nconst Dialog = withInstall(_Dialog);\nvar stdin_default = Dialog;\nimport { dialogProps } from \"./Dialog.mjs\";\nimport {\n  showDialog,\n  closeDialog,\n  showConfirmDialog,\n  setDialogDefaultOptions,\n  resetDialogDefaultOptions\n} from \"./function-call.mjs\";\nexport {\n  Dialog,\n  closeDialog,\n  stdin_default as default,\n  dialogProps,\n  resetDialogDefaultOptions,\n  setDialogDefaultOptions,\n  showConfirmDialog,\n  showDialog\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEC,UAAU,EACVC,WAAW,EACXC,iBAAiB,EACjBC,uBAAuB,EACvBC,yBAAyB,QACpB,qBAAqB;AAC5B,SACEP,MAAM,EACNI,WAAW,EACXH,aAAa,IAAIO,OAAO,EACxBN,WAAW,EACXK,yBAAyB,EACzBD,uBAAuB,EACvBD,iBAAiB,EACjBF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}