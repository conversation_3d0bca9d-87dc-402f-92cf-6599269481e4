{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Progress from \"./Progress.mjs\";\nconst Progress = withInstall(_Progress);\nvar stdin_default = Progress;\nimport { progressProps } from \"./Progress.mjs\";\nexport { Progress, stdin_default as default, progressProps };", "map": {"version": 3, "names": ["withInstall", "_Progress", "Progress", "stdin_default", "progressProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/progress/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Progress from \"./Progress.mjs\";\nconst Progress = withInstall(_Progress);\nvar stdin_default = Progress;\nimport { progressProps } from \"./Progress.mjs\";\nexport {\n  Progress,\n  stdin_default as default,\n  progressProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SAASE,aAAa,QAAQ,gBAAgB;AAC9C,SACEF,QAAQ,EACRC,aAAa,IAAIE,OAAO,EACxBD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}