{"ast": null, "code": "import { ref, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, numericProp, BORDER_BOTTOM, getZIndexStyle, createNamespace, HAPTICS_FEEDBACK } from \"../utils/index.mjs\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"nav-bar\");\nconst navBarProps = {\n  title: String,\n  fixed: Boolean,\n  zIndex: numericProp,\n  border: truthProp,\n  leftText: String,\n  rightText: String,\n  leftDisabled: Boolean,\n  rightDisabled: <PERSON>olean,\n  leftArrow: <PERSON>olean,\n  placeholder: Boolean,\n  safeAreaInsetTop: Boolean,\n  clickable: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: navBarProps,\n  emits: [\"clickLeft\", \"clickRight\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const navBarRef = ref();\n    const renderPlaceholder = usePlaceholder(navBarRef, bem);\n    const onClickLeft = event => {\n      if (!props.leftDisabled) {\n        emit(\"clickLeft\", event);\n      }\n    };\n    const onClickRight = event => {\n      if (!props.rightDisabled) {\n        emit(\"clickRight\", event);\n      }\n    };\n    const renderLeft = () => {\n      if (slots.left) {\n        return slots.left();\n      }\n      return [props.leftArrow && _createVNode(Icon, {\n        \"class\": bem(\"arrow\"),\n        \"name\": \"arrow-left\"\n      }, null), props.leftText && _createVNode(\"span\", {\n        \"class\": bem(\"text\")\n      }, [props.leftText])];\n    };\n    const renderRight = () => {\n      if (slots.right) {\n        return slots.right();\n      }\n      return _createVNode(\"span\", {\n        \"class\": bem(\"text\")\n      }, [props.rightText]);\n    };\n    const renderNavBar = () => {\n      const {\n        title,\n        fixed,\n        border,\n        zIndex\n      } = props;\n      const style = getZIndexStyle(zIndex);\n      const hasLeft = props.leftArrow || props.leftText || slots.left;\n      const hasRight = props.rightText || slots.right;\n      return _createVNode(\"div\", {\n        \"ref\": navBarRef,\n        \"style\": style,\n        \"class\": [bem({\n          fixed\n        }), {\n          [BORDER_BOTTOM]: border,\n          \"van-safe-area-top\": props.safeAreaInsetTop\n        }]\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [hasLeft && _createVNode(\"div\", {\n        \"class\": [bem(\"left\", {\n          disabled: props.leftDisabled\n        }), props.clickable && !props.leftDisabled ? HAPTICS_FEEDBACK : \"\"],\n        \"onClick\": onClickLeft\n      }, [renderLeft()]), _createVNode(\"div\", {\n        \"class\": [bem(\"title\"), \"van-ellipsis\"]\n      }, [slots.title ? slots.title() : title]), hasRight && _createVNode(\"div\", {\n        \"class\": [bem(\"right\", {\n          disabled: props.rightDisabled\n        }), props.clickable && !props.rightDisabled ? HAPTICS_FEEDBACK : \"\"],\n        \"onClick\": onClickRight\n      }, [renderRight()])])]);\n    };\n    return () => {\n      if (props.fixed && props.placeholder) {\n        return renderPlaceholder(renderNavBar);\n      }\n      return renderNavBar();\n    };\n  }\n});\nexport { stdin_default as default, navBarProps };", "map": {"version": 3, "names": ["ref", "defineComponent", "createVNode", "_createVNode", "truthProp", "numericProp", "BORDER_BOTTOM", "getZIndexStyle", "createNamespace", "HAPTICS_FEEDBACK", "usePlaceholder", "Icon", "name", "bem", "navBarProps", "title", "String", "fixed", "Boolean", "zIndex", "border", "leftText", "rightText", "leftDisabled", "rightDisabled", "leftArrow", "placeholder", "safeAreaInsetTop", "clickable", "stdin_default", "props", "emits", "setup", "emit", "slots", "navBarRef", "renderPlaceholder", "onClickLeft", "event", "onClickRight", "renderLeft", "left", "renderRight", "right", "renderNavBar", "style", "hasLeft", "hasRight", "disabled", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/nav-bar/NavBar.mjs"], "sourcesContent": ["import { ref, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, numericProp, BORDER_BOTTOM, getZIndexStyle, createNamespace, HAPTICS_FEEDBACK } from \"../utils/index.mjs\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"nav-bar\");\nconst navBarProps = {\n  title: String,\n  fixed: Boolean,\n  zIndex: numericProp,\n  border: truthProp,\n  leftText: String,\n  rightText: String,\n  leftDisabled: Boolean,\n  rightDisabled: <PERSON>olean,\n  leftArrow: <PERSON>olean,\n  placeholder: Boolean,\n  safeAreaInsetTop: Boolean,\n  clickable: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: navBarProps,\n  emits: [\"clickLeft\", \"clickRight\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const navBarRef = ref();\n    const renderPlaceholder = usePlaceholder(navBarRef, bem);\n    const onClickLeft = (event) => {\n      if (!props.leftDisabled) {\n        emit(\"clickLeft\", event);\n      }\n    };\n    const onClickRight = (event) => {\n      if (!props.rightDisabled) {\n        emit(\"clickRight\", event);\n      }\n    };\n    const renderLeft = () => {\n      if (slots.left) {\n        return slots.left();\n      }\n      return [props.leftArrow && _createVNode(Icon, {\n        \"class\": bem(\"arrow\"),\n        \"name\": \"arrow-left\"\n      }, null), props.leftText && _createVNode(\"span\", {\n        \"class\": bem(\"text\")\n      }, [props.leftText])];\n    };\n    const renderRight = () => {\n      if (slots.right) {\n        return slots.right();\n      }\n      return _createVNode(\"span\", {\n        \"class\": bem(\"text\")\n      }, [props.rightText]);\n    };\n    const renderNavBar = () => {\n      const {\n        title,\n        fixed,\n        border,\n        zIndex\n      } = props;\n      const style = getZIndexStyle(zIndex);\n      const hasLeft = props.leftArrow || props.leftText || slots.left;\n      const hasRight = props.rightText || slots.right;\n      return _createVNode(\"div\", {\n        \"ref\": navBarRef,\n        \"style\": style,\n        \"class\": [bem({\n          fixed\n        }), {\n          [BORDER_BOTTOM]: border,\n          \"van-safe-area-top\": props.safeAreaInsetTop\n        }]\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [hasLeft && _createVNode(\"div\", {\n        \"class\": [bem(\"left\", {\n          disabled: props.leftDisabled\n        }), props.clickable && !props.leftDisabled ? HAPTICS_FEEDBACK : \"\"],\n        \"onClick\": onClickLeft\n      }, [renderLeft()]), _createVNode(\"div\", {\n        \"class\": [bem(\"title\"), \"van-ellipsis\"]\n      }, [slots.title ? slots.title() : title]), hasRight && _createVNode(\"div\", {\n        \"class\": [bem(\"right\", {\n          disabled: props.rightDisabled\n        }), props.clickable && !props.rightDisabled ? HAPTICS_FEEDBACK : \"\"],\n        \"onClick\": onClickRight\n      }, [renderRight()])])]);\n    };\n    return () => {\n      if (props.fixed && props.placeholder) {\n        return renderPlaceholder(renderNavBar);\n      }\n      return renderNavBar();\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  navBarProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACvE,SAASC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC7H,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,SAAS,CAAC;AAC9C,MAAMM,WAAW,GAAG;EAClBC,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAEC,OAAO;EACdC,MAAM,EAAEd,WAAW;EACnBe,MAAM,EAAEhB,SAAS;EACjBiB,QAAQ,EAAEL,MAAM;EAChBM,SAAS,EAAEN,MAAM;EACjBO,YAAY,EAAEL,OAAO;EACrBM,aAAa,EAAEN,OAAO;EACtBO,SAAS,EAAEP,OAAO;EAClBQ,WAAW,EAAER,OAAO;EACpBS,gBAAgB,EAAET,OAAO;EACzBU,SAAS,EAAExB;AACb,CAAC;AACD,IAAIyB,aAAa,GAAG5B,eAAe,CAAC;EAClCW,IAAI;EACJkB,KAAK,EAAEhB,WAAW;EAClBiB,KAAK,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EAClCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,SAAS,GAAGnC,GAAG,CAAC,CAAC;IACvB,MAAMoC,iBAAiB,GAAG1B,cAAc,CAACyB,SAAS,EAAEtB,GAAG,CAAC;IACxD,MAAMwB,WAAW,GAAIC,KAAK,IAAK;MAC7B,IAAI,CAACR,KAAK,CAACP,YAAY,EAAE;QACvBU,IAAI,CAAC,WAAW,EAAEK,KAAK,CAAC;MAC1B;IACF,CAAC;IACD,MAAMC,YAAY,GAAID,KAAK,IAAK;MAC9B,IAAI,CAACR,KAAK,CAACN,aAAa,EAAE;QACxBS,IAAI,CAAC,YAAY,EAAEK,KAAK,CAAC;MAC3B;IACF,CAAC;IACD,MAAME,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIN,KAAK,CAACO,IAAI,EAAE;QACd,OAAOP,KAAK,CAACO,IAAI,CAAC,CAAC;MACrB;MACA,OAAO,CAACX,KAAK,CAACL,SAAS,IAAItB,YAAY,CAACQ,IAAI,EAAE;QAC5C,OAAO,EAAEE,GAAG,CAAC,OAAO,CAAC;QACrB,MAAM,EAAE;MACV,CAAC,EAAE,IAAI,CAAC,EAAEiB,KAAK,CAACT,QAAQ,IAAIlB,YAAY,CAAC,MAAM,EAAE;QAC/C,OAAO,EAAEU,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACiB,KAAK,CAACT,QAAQ,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,MAAMqB,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIR,KAAK,CAACS,KAAK,EAAE;QACf,OAAOT,KAAK,CAACS,KAAK,CAAC,CAAC;MACtB;MACA,OAAOxC,YAAY,CAAC,MAAM,EAAE;QAC1B,OAAO,EAAEU,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACiB,KAAK,CAACR,SAAS,CAAC,CAAC;IACvB,CAAC;IACD,MAAMsB,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAM;QACJ7B,KAAK;QACLE,KAAK;QACLG,MAAM;QACND;MACF,CAAC,GAAGW,KAAK;MACT,MAAMe,KAAK,GAAGtC,cAAc,CAACY,MAAM,CAAC;MACpC,MAAM2B,OAAO,GAAGhB,KAAK,CAACL,SAAS,IAAIK,KAAK,CAACT,QAAQ,IAAIa,KAAK,CAACO,IAAI;MAC/D,MAAMM,QAAQ,GAAGjB,KAAK,CAACR,SAAS,IAAIY,KAAK,CAACS,KAAK;MAC/C,OAAOxC,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEgC,SAAS;QAChB,OAAO,EAAEU,KAAK;QACd,OAAO,EAAE,CAAChC,GAAG,CAAC;UACZI;QACF,CAAC,CAAC,EAAE;UACF,CAACX,aAAa,GAAGc,MAAM;UACvB,mBAAmB,EAAEU,KAAK,CAACH;QAC7B,CAAC;MACH,CAAC,EAAE,CAACxB,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEU,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,CAACiC,OAAO,IAAI3C,YAAY,CAAC,KAAK,EAAE;QACjC,OAAO,EAAE,CAACU,GAAG,CAAC,MAAM,EAAE;UACpBmC,QAAQ,EAAElB,KAAK,CAACP;QAClB,CAAC,CAAC,EAAEO,KAAK,CAACF,SAAS,IAAI,CAACE,KAAK,CAACP,YAAY,GAAGd,gBAAgB,GAAG,EAAE,CAAC;QACnE,SAAS,EAAE4B;MACb,CAAC,EAAE,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAErC,YAAY,CAAC,KAAK,EAAE;QACtC,OAAO,EAAE,CAACU,GAAG,CAAC,OAAO,CAAC,EAAE,cAAc;MACxC,CAAC,EAAE,CAACqB,KAAK,CAACnB,KAAK,GAAGmB,KAAK,CAACnB,KAAK,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,EAAEgC,QAAQ,IAAI5C,YAAY,CAAC,KAAK,EAAE;QACzE,OAAO,EAAE,CAACU,GAAG,CAAC,OAAO,EAAE;UACrBmC,QAAQ,EAAElB,KAAK,CAACN;QAClB,CAAC,CAAC,EAAEM,KAAK,CAACF,SAAS,IAAI,CAACE,KAAK,CAACN,aAAa,GAAGf,gBAAgB,GAAG,EAAE,CAAC;QACpE,SAAS,EAAE8B;MACb,CAAC,EAAE,CAACG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACD,OAAO,MAAM;MACX,IAAIZ,KAAK,CAACb,KAAK,IAAIa,KAAK,CAACJ,WAAW,EAAE;QACpC,OAAOU,iBAAiB,CAACQ,YAAY,CAAC;MACxC;MACA,OAAOA,YAAY,CAAC,CAAC;IACvB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEf,aAAa,IAAIoB,OAAO,EACxBnC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}