{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, watch, reactive, computed, onMounted, onActivated, onDeactivated, onBeforeUnmount, defineComponent, nextTick, createVNode as _createVNode } from \"vue\";\nimport { clamp, isHidden, truthProp, numericProp, windowWidth, windowHeight, preventDefault, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { doubleRaf, useChildren, useEventListener, usePageVisibility } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nconst [name, bem] = createNamespace(\"swipe\");\nconst swipeProps = {\n  loop: truthProp,\n  width: numericProp,\n  height: numericProp,\n  vertical: Boolean,\n  autoplay: makeNumericProp(0),\n  duration: makeNumericProp(500),\n  touchable: truthProp,\n  lazyRender: Boolean,\n  initialSwipe: makeNumericProp(0),\n  indicatorColor: String,\n  showIndicators: truthProp,\n  stopPropagation: truthProp\n};\nconst SWIPE_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: swipeProps,\n  emits: [\"change\", \"dragStart\", \"dragEnd\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const track = ref();\n    const state = reactive({\n      rect: null,\n      width: 0,\n      height: 0,\n      offset: 0,\n      active: 0,\n      swiping: false\n    });\n    let dragging = false;\n    const touch = useTouch();\n    const {\n      children,\n      linkChildren\n    } = useChildren(SWIPE_KEY);\n    const count = computed(() => children.length);\n    const size = computed(() => state[props.vertical ? \"height\" : \"width\"]);\n    const delta = computed(() => props.vertical ? touch.deltaY.value : touch.deltaX.value);\n    const minOffset = computed(() => {\n      if (state.rect) {\n        const base = props.vertical ? state.rect.height : state.rect.width;\n        return base - size.value * count.value;\n      }\n      return 0;\n    });\n    const maxCount = computed(() => size.value ? Math.ceil(Math.abs(minOffset.value) / size.value) : count.value);\n    const trackSize = computed(() => count.value * size.value);\n    const activeIndicator = computed(() => (state.active + count.value) % count.value);\n    const isCorrectDirection = computed(() => {\n      const expect = props.vertical ? \"vertical\" : \"horizontal\";\n      return touch.direction.value === expect;\n    });\n    const trackStyle = computed(() => {\n      const style = {\n        transitionDuration: `${state.swiping ? 0 : props.duration}ms`,\n        transform: `translate${props.vertical ? \"Y\" : \"X\"}(${+state.offset.toFixed(2)}px)`\n      };\n      if (size.value) {\n        const mainAxis = props.vertical ? \"height\" : \"width\";\n        const crossAxis = props.vertical ? \"width\" : \"height\";\n        style[mainAxis] = `${trackSize.value}px`;\n        style[crossAxis] = props[crossAxis] ? `${props[crossAxis]}px` : \"\";\n      }\n      return style;\n    });\n    const getTargetActive = pace => {\n      const {\n        active\n      } = state;\n      if (pace) {\n        if (props.loop) {\n          return clamp(active + pace, -1, count.value);\n        }\n        return clamp(active + pace, 0, maxCount.value);\n      }\n      return active;\n    };\n    const getTargetOffset = (targetActive, offset = 0) => {\n      let currentPosition = targetActive * size.value;\n      if (!props.loop) {\n        currentPosition = Math.min(currentPosition, -minOffset.value);\n      }\n      let targetOffset = offset - currentPosition;\n      if (!props.loop) {\n        targetOffset = clamp(targetOffset, minOffset.value, 0);\n      }\n      return targetOffset;\n    };\n    const move = ({\n      pace = 0,\n      offset = 0,\n      emitChange\n    }) => {\n      if (count.value <= 1) {\n        return;\n      }\n      const {\n        active\n      } = state;\n      const targetActive = getTargetActive(pace);\n      const targetOffset = getTargetOffset(targetActive, offset);\n      if (props.loop) {\n        if (children[0] && targetOffset !== minOffset.value) {\n          const outRightBound = targetOffset < minOffset.value;\n          children[0].setOffset(outRightBound ? trackSize.value : 0);\n        }\n        if (children[count.value - 1] && targetOffset !== 0) {\n          const outLeftBound = targetOffset > 0;\n          children[count.value - 1].setOffset(outLeftBound ? -trackSize.value : 0);\n        }\n      }\n      state.active = targetActive;\n      state.offset = targetOffset;\n      if (emitChange && targetActive !== active) {\n        emit(\"change\", activeIndicator.value);\n      }\n    };\n    const correctPosition = () => {\n      state.swiping = true;\n      if (state.active <= -1) {\n        move({\n          pace: count.value\n        });\n      } else if (state.active >= count.value) {\n        move({\n          pace: -count.value\n        });\n      }\n    };\n    const prev = () => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        state.swiping = false;\n        move({\n          pace: -1,\n          emitChange: true\n        });\n      });\n    };\n    const next = () => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        state.swiping = false;\n        move({\n          pace: 1,\n          emitChange: true\n        });\n      });\n    };\n    let autoplayTimer;\n    const stopAutoplay = () => clearTimeout(autoplayTimer);\n    const autoplay = () => {\n      stopAutoplay();\n      if (+props.autoplay > 0 && count.value > 1) {\n        autoplayTimer = setTimeout(() => {\n          next();\n          autoplay();\n        }, +props.autoplay);\n      }\n    };\n    const initialize = (active = +props.initialSwipe) => {\n      if (!root.value) {\n        return;\n      }\n      const cb = () => {\n        var _a, _b;\n        if (!isHidden(root)) {\n          const rect = {\n            width: root.value.offsetWidth,\n            height: root.value.offsetHeight\n          };\n          state.rect = rect;\n          state.width = +((_a = props.width) != null ? _a : rect.width);\n          state.height = +((_b = props.height) != null ? _b : rect.height);\n        }\n        if (count.value) {\n          active = Math.min(count.value - 1, active);\n          if (active === -1) {\n            active = count.value - 1;\n          }\n        }\n        state.active = active;\n        state.swiping = true;\n        state.offset = getTargetOffset(active);\n        children.forEach(swipe => {\n          swipe.setOffset(0);\n        });\n        autoplay();\n      };\n      if (isHidden(root)) {\n        nextTick().then(cb);\n      } else {\n        cb();\n      }\n    };\n    const resize = () => initialize(state.active);\n    let touchStartTime;\n    const onTouchStart = event => {\n      if (!props.touchable ||\n      // avoid resetting position on multi-finger touch\n      event.touches.length > 1) return;\n      touch.start(event);\n      dragging = false;\n      touchStartTime = Date.now();\n      stopAutoplay();\n      correctPosition();\n    };\n    const onTouchMove = event => {\n      if (props.touchable && state.swiping) {\n        touch.move(event);\n        if (isCorrectDirection.value) {\n          const isEdgeTouch = !props.loop && (state.active === 0 && delta.value > 0 || state.active === count.value - 1 && delta.value < 0);\n          if (!isEdgeTouch) {\n            preventDefault(event, props.stopPropagation);\n            move({\n              offset: delta.value\n            });\n            if (!dragging) {\n              emit(\"dragStart\", {\n                index: activeIndicator.value\n              });\n              dragging = true;\n            }\n          }\n        }\n      }\n    };\n    const onTouchEnd = () => {\n      if (!props.touchable || !state.swiping) {\n        return;\n      }\n      const duration = Date.now() - touchStartTime;\n      const speed = delta.value / duration;\n      const shouldSwipe = Math.abs(speed) > 0.25 || Math.abs(delta.value) > size.value / 2;\n      if (shouldSwipe && isCorrectDirection.value) {\n        const offset = props.vertical ? touch.offsetY.value : touch.offsetX.value;\n        let pace = 0;\n        if (props.loop) {\n          pace = offset > 0 ? delta.value > 0 ? -1 : 1 : 0;\n        } else {\n          pace = -Math[delta.value > 0 ? \"ceil\" : \"floor\"](delta.value / size.value);\n        }\n        move({\n          pace,\n          emitChange: true\n        });\n      } else if (delta.value) {\n        move({\n          pace: 0\n        });\n      }\n      dragging = false;\n      state.swiping = false;\n      emit(\"dragEnd\", {\n        index: activeIndicator.value\n      });\n      autoplay();\n    };\n    const swipeTo = (index, options = {}) => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        let targetIndex;\n        if (props.loop && index === count.value) {\n          targetIndex = state.active === 0 ? 0 : index;\n        } else {\n          targetIndex = index % count.value;\n        }\n        if (options.immediate) {\n          doubleRaf(() => {\n            state.swiping = false;\n          });\n        } else {\n          state.swiping = false;\n        }\n        move({\n          pace: targetIndex - state.active,\n          emitChange: true\n        });\n      });\n    };\n    const renderDot = (_, index) => {\n      const active = index === activeIndicator.value;\n      const style = active ? {\n        backgroundColor: props.indicatorColor\n      } : void 0;\n      return _createVNode(\"i\", {\n        \"style\": style,\n        \"class\": bem(\"indicator\", {\n          active\n        })\n      }, null);\n    };\n    const renderIndicator = () => {\n      if (slots.indicator) {\n        return slots.indicator({\n          active: activeIndicator.value,\n          total: count.value\n        });\n      }\n      if (props.showIndicators && count.value > 1) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"indicators\", {\n            vertical: props.vertical\n          })\n        }, [Array(count.value).fill(\"\").map(renderDot)]);\n      }\n    };\n    useExpose({\n      prev,\n      next,\n      state,\n      resize,\n      swipeTo\n    });\n    linkChildren({\n      size,\n      props,\n      count,\n      activeIndicator\n    });\n    watch(() => props.initialSwipe, value => initialize(+value));\n    watch(count, () => initialize(state.active));\n    watch(() => props.autoplay, autoplay);\n    watch([windowWidth, windowHeight, () => props.width, () => props.height], resize);\n    watch(usePageVisibility(), visible => {\n      if (visible === \"visible\") {\n        autoplay();\n      } else {\n        stopAutoplay();\n      }\n    });\n    onMounted(initialize);\n    onActivated(() => initialize(state.active));\n    onPopupReopen(() => initialize(state.active));\n    onDeactivated(stopAutoplay);\n    onBeforeUnmount(stopAutoplay);\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: track\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"ref\": track,\n        \"style\": trackStyle.value,\n        \"class\": bem(\"track\", {\n          vertical: props.vertical\n        }),\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]), renderIndicator()]);\n    };\n  }\n});\nexport { SWIPE_KEY, stdin_default as default, swipeProps };", "map": {"version": 3, "names": ["ref", "watch", "reactive", "computed", "onMounted", "onActivated", "onDeactivated", "onBeforeUnmount", "defineComponent", "nextTick", "createVNode", "_createVNode", "clamp", "isHidden", "truthProp", "numericProp", "windowWidth", "windowHeight", "preventDefault", "createNamespace", "makeNumericProp", "doubleRaf", "useChildren", "useEventListener", "usePageVisibility", "useTouch", "useExpose", "onPopupReopen", "name", "bem", "swipeProps", "loop", "width", "height", "vertical", "Boolean", "autoplay", "duration", "touchable", "lazy<PERSON>ender", "initialSwipe", "indicatorColor", "String", "showIndicators", "stopPropagation", "SWIPE_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "emit", "slots", "root", "track", "state", "rect", "offset", "active", "swiping", "dragging", "touch", "children", "linkChildren", "count", "length", "size", "delta", "deltaY", "value", "deltaX", "minOffset", "base", "maxCount", "Math", "ceil", "abs", "trackSize", "activeIndicator", "isCorrectDirection", "expect", "direction", "trackStyle", "style", "transitionDuration", "transform", "toFixed", "mainAxis", "crossAxis", "getTargetActive", "pace", "getTargetOffset", "targetActive", "currentPosition", "min", "targetOffset", "move", "emitChange", "outRightBound", "setOffset", "outLeftBound", "correctPosition", "prev", "reset", "next", "autoplayTimer", "stopAutoplay", "clearTimeout", "setTimeout", "initialize", "cb", "_a", "_b", "offsetWidth", "offsetHeight", "for<PERSON>ach", "swipe", "then", "resize", "touchStartTime", "onTouchStart", "event", "touches", "start", "Date", "now", "onTouchMove", "isEdgeTouch", "index", "onTouchEnd", "speed", "shouldSwipe", "offsetY", "offsetX", "swipeTo", "options", "targetIndex", "immediate", "renderDot", "_", "backgroundColor", "renderIndicator", "indicator", "total", "Array", "fill", "map", "visible", "target", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/swipe/Swipe.mjs"], "sourcesContent": ["import { ref, watch, reactive, computed, onMounted, onActivated, onDeactivated, onBeforeUnmount, defineComponent, nextTick, createVNode as _createVNode } from \"vue\";\nimport { clamp, isHidden, truthProp, numericProp, windowWidth, windowHeight, preventDefault, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { doubleRaf, useChildren, useEventListener, usePageVisibility } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nconst [name, bem] = createNamespace(\"swipe\");\nconst swipeProps = {\n  loop: truthProp,\n  width: numericProp,\n  height: numericProp,\n  vertical: Boolean,\n  autoplay: makeNumericProp(0),\n  duration: makeNumericProp(500),\n  touchable: truthProp,\n  lazyRender: Boolean,\n  initialSwipe: makeNumericProp(0),\n  indicatorColor: String,\n  showIndicators: truthProp,\n  stopPropagation: truthProp\n};\nconst SWIPE_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: swipeProps,\n  emits: [\"change\", \"dragStart\", \"dragEnd\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const track = ref();\n    const state = reactive({\n      rect: null,\n      width: 0,\n      height: 0,\n      offset: 0,\n      active: 0,\n      swiping: false\n    });\n    let dragging = false;\n    const touch = useTouch();\n    const {\n      children,\n      linkChildren\n    } = useChildren(SWIPE_KEY);\n    const count = computed(() => children.length);\n    const size = computed(() => state[props.vertical ? \"height\" : \"width\"]);\n    const delta = computed(() => props.vertical ? touch.deltaY.value : touch.deltaX.value);\n    const minOffset = computed(() => {\n      if (state.rect) {\n        const base = props.vertical ? state.rect.height : state.rect.width;\n        return base - size.value * count.value;\n      }\n      return 0;\n    });\n    const maxCount = computed(() => size.value ? Math.ceil(Math.abs(minOffset.value) / size.value) : count.value);\n    const trackSize = computed(() => count.value * size.value);\n    const activeIndicator = computed(() => (state.active + count.value) % count.value);\n    const isCorrectDirection = computed(() => {\n      const expect = props.vertical ? \"vertical\" : \"horizontal\";\n      return touch.direction.value === expect;\n    });\n    const trackStyle = computed(() => {\n      const style = {\n        transitionDuration: `${state.swiping ? 0 : props.duration}ms`,\n        transform: `translate${props.vertical ? \"Y\" : \"X\"}(${+state.offset.toFixed(2)}px)`\n      };\n      if (size.value) {\n        const mainAxis = props.vertical ? \"height\" : \"width\";\n        const crossAxis = props.vertical ? \"width\" : \"height\";\n        style[mainAxis] = `${trackSize.value}px`;\n        style[crossAxis] = props[crossAxis] ? `${props[crossAxis]}px` : \"\";\n      }\n      return style;\n    });\n    const getTargetActive = (pace) => {\n      const {\n        active\n      } = state;\n      if (pace) {\n        if (props.loop) {\n          return clamp(active + pace, -1, count.value);\n        }\n        return clamp(active + pace, 0, maxCount.value);\n      }\n      return active;\n    };\n    const getTargetOffset = (targetActive, offset = 0) => {\n      let currentPosition = targetActive * size.value;\n      if (!props.loop) {\n        currentPosition = Math.min(currentPosition, -minOffset.value);\n      }\n      let targetOffset = offset - currentPosition;\n      if (!props.loop) {\n        targetOffset = clamp(targetOffset, minOffset.value, 0);\n      }\n      return targetOffset;\n    };\n    const move = ({\n      pace = 0,\n      offset = 0,\n      emitChange\n    }) => {\n      if (count.value <= 1) {\n        return;\n      }\n      const {\n        active\n      } = state;\n      const targetActive = getTargetActive(pace);\n      const targetOffset = getTargetOffset(targetActive, offset);\n      if (props.loop) {\n        if (children[0] && targetOffset !== minOffset.value) {\n          const outRightBound = targetOffset < minOffset.value;\n          children[0].setOffset(outRightBound ? trackSize.value : 0);\n        }\n        if (children[count.value - 1] && targetOffset !== 0) {\n          const outLeftBound = targetOffset > 0;\n          children[count.value - 1].setOffset(outLeftBound ? -trackSize.value : 0);\n        }\n      }\n      state.active = targetActive;\n      state.offset = targetOffset;\n      if (emitChange && targetActive !== active) {\n        emit(\"change\", activeIndicator.value);\n      }\n    };\n    const correctPosition = () => {\n      state.swiping = true;\n      if (state.active <= -1) {\n        move({\n          pace: count.value\n        });\n      } else if (state.active >= count.value) {\n        move({\n          pace: -count.value\n        });\n      }\n    };\n    const prev = () => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        state.swiping = false;\n        move({\n          pace: -1,\n          emitChange: true\n        });\n      });\n    };\n    const next = () => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        state.swiping = false;\n        move({\n          pace: 1,\n          emitChange: true\n        });\n      });\n    };\n    let autoplayTimer;\n    const stopAutoplay = () => clearTimeout(autoplayTimer);\n    const autoplay = () => {\n      stopAutoplay();\n      if (+props.autoplay > 0 && count.value > 1) {\n        autoplayTimer = setTimeout(() => {\n          next();\n          autoplay();\n        }, +props.autoplay);\n      }\n    };\n    const initialize = (active = +props.initialSwipe) => {\n      if (!root.value) {\n        return;\n      }\n      const cb = () => {\n        var _a, _b;\n        if (!isHidden(root)) {\n          const rect = {\n            width: root.value.offsetWidth,\n            height: root.value.offsetHeight\n          };\n          state.rect = rect;\n          state.width = +((_a = props.width) != null ? _a : rect.width);\n          state.height = +((_b = props.height) != null ? _b : rect.height);\n        }\n        if (count.value) {\n          active = Math.min(count.value - 1, active);\n          if (active === -1) {\n            active = count.value - 1;\n          }\n        }\n        state.active = active;\n        state.swiping = true;\n        state.offset = getTargetOffset(active);\n        children.forEach((swipe) => {\n          swipe.setOffset(0);\n        });\n        autoplay();\n      };\n      if (isHidden(root)) {\n        nextTick().then(cb);\n      } else {\n        cb();\n      }\n    };\n    const resize = () => initialize(state.active);\n    let touchStartTime;\n    const onTouchStart = (event) => {\n      if (!props.touchable || // avoid resetting position on multi-finger touch\n      event.touches.length > 1) return;\n      touch.start(event);\n      dragging = false;\n      touchStartTime = Date.now();\n      stopAutoplay();\n      correctPosition();\n    };\n    const onTouchMove = (event) => {\n      if (props.touchable && state.swiping) {\n        touch.move(event);\n        if (isCorrectDirection.value) {\n          const isEdgeTouch = !props.loop && (state.active === 0 && delta.value > 0 || state.active === count.value - 1 && delta.value < 0);\n          if (!isEdgeTouch) {\n            preventDefault(event, props.stopPropagation);\n            move({\n              offset: delta.value\n            });\n            if (!dragging) {\n              emit(\"dragStart\", {\n                index: activeIndicator.value\n              });\n              dragging = true;\n            }\n          }\n        }\n      }\n    };\n    const onTouchEnd = () => {\n      if (!props.touchable || !state.swiping) {\n        return;\n      }\n      const duration = Date.now() - touchStartTime;\n      const speed = delta.value / duration;\n      const shouldSwipe = Math.abs(speed) > 0.25 || Math.abs(delta.value) > size.value / 2;\n      if (shouldSwipe && isCorrectDirection.value) {\n        const offset = props.vertical ? touch.offsetY.value : touch.offsetX.value;\n        let pace = 0;\n        if (props.loop) {\n          pace = offset > 0 ? delta.value > 0 ? -1 : 1 : 0;\n        } else {\n          pace = -Math[delta.value > 0 ? \"ceil\" : \"floor\"](delta.value / size.value);\n        }\n        move({\n          pace,\n          emitChange: true\n        });\n      } else if (delta.value) {\n        move({\n          pace: 0\n        });\n      }\n      dragging = false;\n      state.swiping = false;\n      emit(\"dragEnd\", {\n        index: activeIndicator.value\n      });\n      autoplay();\n    };\n    const swipeTo = (index, options = {}) => {\n      correctPosition();\n      touch.reset();\n      doubleRaf(() => {\n        let targetIndex;\n        if (props.loop && index === count.value) {\n          targetIndex = state.active === 0 ? 0 : index;\n        } else {\n          targetIndex = index % count.value;\n        }\n        if (options.immediate) {\n          doubleRaf(() => {\n            state.swiping = false;\n          });\n        } else {\n          state.swiping = false;\n        }\n        move({\n          pace: targetIndex - state.active,\n          emitChange: true\n        });\n      });\n    };\n    const renderDot = (_, index) => {\n      const active = index === activeIndicator.value;\n      const style = active ? {\n        backgroundColor: props.indicatorColor\n      } : void 0;\n      return _createVNode(\"i\", {\n        \"style\": style,\n        \"class\": bem(\"indicator\", {\n          active\n        })\n      }, null);\n    };\n    const renderIndicator = () => {\n      if (slots.indicator) {\n        return slots.indicator({\n          active: activeIndicator.value,\n          total: count.value\n        });\n      }\n      if (props.showIndicators && count.value > 1) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"indicators\", {\n            vertical: props.vertical\n          })\n        }, [Array(count.value).fill(\"\").map(renderDot)]);\n      }\n    };\n    useExpose({\n      prev,\n      next,\n      state,\n      resize,\n      swipeTo\n    });\n    linkChildren({\n      size,\n      props,\n      count,\n      activeIndicator\n    });\n    watch(() => props.initialSwipe, (value) => initialize(+value));\n    watch(count, () => initialize(state.active));\n    watch(() => props.autoplay, autoplay);\n    watch([windowWidth, windowHeight, () => props.width, () => props.height], resize);\n    watch(usePageVisibility(), (visible) => {\n      if (visible === \"visible\") {\n        autoplay();\n      } else {\n        stopAutoplay();\n      }\n    });\n    onMounted(initialize);\n    onActivated(() => initialize(state.active));\n    onPopupReopen(() => initialize(state.active));\n    onDeactivated(stopAutoplay);\n    onBeforeUnmount(stopAutoplay);\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: track\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [_createVNode(\"div\", {\n        \"ref\": track,\n        \"style\": trackStyle.value,\n        \"class\": bem(\"track\", {\n          vertical: props.vertical\n        }),\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]), renderIndicator()]);\n    };\n  }\n});\nexport {\n  SWIPE_KEY,\n  stdin_default as default,\n  swipeProps\n};\n"], "mappings": ";;;AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACpK,SAASC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACzJ,SAASC,SAAS,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,WAAW;AACvF,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAa,QAAQ,oCAAoC;AAClE,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGV,eAAe,CAAC,OAAO,CAAC;AAC5C,MAAMW,UAAU,GAAG;EACjBC,IAAI,EAAEjB,SAAS;EACfkB,KAAK,EAAEjB,WAAW;EAClBkB,MAAM,EAAElB,WAAW;EACnBmB,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAEhB,eAAe,CAAC,CAAC,CAAC;EAC5BiB,QAAQ,EAAEjB,eAAe,CAAC,GAAG,CAAC;EAC9BkB,SAAS,EAAExB,SAAS;EACpByB,UAAU,EAAEJ,OAAO;EACnBK,YAAY,EAAEpB,eAAe,CAAC,CAAC,CAAC;EAChCqB,cAAc,EAAEC,MAAM;EACtBC,cAAc,EAAE7B,SAAS;EACzB8B,eAAe,EAAE9B;AACnB,CAAC;AACD,MAAM+B,SAAS,GAAGC,MAAM,CAAClB,IAAI,CAAC;AAC9B,IAAImB,aAAa,GAAGvC,eAAe,CAAC;EAClCoB,IAAI;EACJoB,KAAK,EAAElB,UAAU;EACjBmB,KAAK,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;EACzCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAGrD,GAAG,CAAC,CAAC;IAClB,MAAMsD,KAAK,GAAGtD,GAAG,CAAC,CAAC;IACnB,MAAMuD,KAAK,GAAGrD,QAAQ,CAAC;MACrBsD,IAAI,EAAE,IAAI;MACVxB,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTwB,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAIC,QAAQ,GAAG,KAAK;IACpB,MAAMC,KAAK,GAAGpC,QAAQ,CAAC,CAAC;IACxB,MAAM;MACJqC,QAAQ;MACRC;IACF,CAAC,GAAGzC,WAAW,CAACuB,SAAS,CAAC;IAC1B,MAAMmB,KAAK,GAAG7D,QAAQ,CAAC,MAAM2D,QAAQ,CAACG,MAAM,CAAC;IAC7C,MAAMC,IAAI,GAAG/D,QAAQ,CAAC,MAAMoD,KAAK,CAACP,KAAK,CAACd,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC;IACvE,MAAMiC,KAAK,GAAGhE,QAAQ,CAAC,MAAM6C,KAAK,CAACd,QAAQ,GAAG2B,KAAK,CAACO,MAAM,CAACC,KAAK,GAAGR,KAAK,CAACS,MAAM,CAACD,KAAK,CAAC;IACtF,MAAME,SAAS,GAAGpE,QAAQ,CAAC,MAAM;MAC/B,IAAIoD,KAAK,CAACC,IAAI,EAAE;QACd,MAAMgB,IAAI,GAAGxB,KAAK,CAACd,QAAQ,GAAGqB,KAAK,CAACC,IAAI,CAACvB,MAAM,GAAGsB,KAAK,CAACC,IAAI,CAACxB,KAAK;QAClE,OAAOwC,IAAI,GAAGN,IAAI,CAACG,KAAK,GAAGL,KAAK,CAACK,KAAK;MACxC;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IACF,MAAMI,QAAQ,GAAGtE,QAAQ,CAAC,MAAM+D,IAAI,CAACG,KAAK,GAAGK,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACL,SAAS,CAACF,KAAK,CAAC,GAAGH,IAAI,CAACG,KAAK,CAAC,GAAGL,KAAK,CAACK,KAAK,CAAC;IAC7G,MAAMQ,SAAS,GAAG1E,QAAQ,CAAC,MAAM6D,KAAK,CAACK,KAAK,GAAGH,IAAI,CAACG,KAAK,CAAC;IAC1D,MAAMS,eAAe,GAAG3E,QAAQ,CAAC,MAAM,CAACoD,KAAK,CAACG,MAAM,GAAGM,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAAC;IAClF,MAAMU,kBAAkB,GAAG5E,QAAQ,CAAC,MAAM;MACxC,MAAM6E,MAAM,GAAGhC,KAAK,CAACd,QAAQ,GAAG,UAAU,GAAG,YAAY;MACzD,OAAO2B,KAAK,CAACoB,SAAS,CAACZ,KAAK,KAAKW,MAAM;IACzC,CAAC,CAAC;IACF,MAAME,UAAU,GAAG/E,QAAQ,CAAC,MAAM;MAChC,MAAMgF,KAAK,GAAG;QACZC,kBAAkB,EAAE,GAAG7B,KAAK,CAACI,OAAO,GAAG,CAAC,GAAGX,KAAK,CAACX,QAAQ,IAAI;QAC7DgD,SAAS,EAAE,YAAYrC,KAAK,CAACd,QAAQ,GAAG,GAAG,GAAG,GAAG,IAAI,CAACqB,KAAK,CAACE,MAAM,CAAC6B,OAAO,CAAC,CAAC,CAAC;MAC/E,CAAC;MACD,IAAIpB,IAAI,CAACG,KAAK,EAAE;QACd,MAAMkB,QAAQ,GAAGvC,KAAK,CAACd,QAAQ,GAAG,QAAQ,GAAG,OAAO;QACpD,MAAMsD,SAAS,GAAGxC,KAAK,CAACd,QAAQ,GAAG,OAAO,GAAG,QAAQ;QACrDiD,KAAK,CAACI,QAAQ,CAAC,GAAG,GAAGV,SAAS,CAACR,KAAK,IAAI;QACxCc,KAAK,CAACK,SAAS,CAAC,GAAGxC,KAAK,CAACwC,SAAS,CAAC,GAAG,GAAGxC,KAAK,CAACwC,SAAS,CAAC,IAAI,GAAG,EAAE;MACpE;MACA,OAAOL,KAAK;IACd,CAAC,CAAC;IACF,MAAMM,eAAe,GAAIC,IAAI,IAAK;MAChC,MAAM;QACJhC;MACF,CAAC,GAAGH,KAAK;MACT,IAAImC,IAAI,EAAE;QACR,IAAI1C,KAAK,CAACjB,IAAI,EAAE;UACd,OAAOnB,KAAK,CAAC8C,MAAM,GAAGgC,IAAI,EAAE,CAAC,CAAC,EAAE1B,KAAK,CAACK,KAAK,CAAC;QAC9C;QACA,OAAOzD,KAAK,CAAC8C,MAAM,GAAGgC,IAAI,EAAE,CAAC,EAAEjB,QAAQ,CAACJ,KAAK,CAAC;MAChD;MACA,OAAOX,MAAM;IACf,CAAC;IACD,MAAMiC,eAAe,GAAGA,CAACC,YAAY,EAAEnC,MAAM,GAAG,CAAC,KAAK;MACpD,IAAIoC,eAAe,GAAGD,YAAY,GAAG1B,IAAI,CAACG,KAAK;MAC/C,IAAI,CAACrB,KAAK,CAACjB,IAAI,EAAE;QACf8D,eAAe,GAAGnB,IAAI,CAACoB,GAAG,CAACD,eAAe,EAAE,CAACtB,SAAS,CAACF,KAAK,CAAC;MAC/D;MACA,IAAI0B,YAAY,GAAGtC,MAAM,GAAGoC,eAAe;MAC3C,IAAI,CAAC7C,KAAK,CAACjB,IAAI,EAAE;QACfgE,YAAY,GAAGnF,KAAK,CAACmF,YAAY,EAAExB,SAAS,CAACF,KAAK,EAAE,CAAC,CAAC;MACxD;MACA,OAAO0B,YAAY;IACrB,CAAC;IACD,MAAMC,IAAI,GAAGA,CAAC;MACZN,IAAI,GAAG,CAAC;MACRjC,MAAM,GAAG,CAAC;MACVwC;IACF,CAAC,KAAK;MACJ,IAAIjC,KAAK,CAACK,KAAK,IAAI,CAAC,EAAE;QACpB;MACF;MACA,MAAM;QACJX;MACF,CAAC,GAAGH,KAAK;MACT,MAAMqC,YAAY,GAAGH,eAAe,CAACC,IAAI,CAAC;MAC1C,MAAMK,YAAY,GAAGJ,eAAe,CAACC,YAAY,EAAEnC,MAAM,CAAC;MAC1D,IAAIT,KAAK,CAACjB,IAAI,EAAE;QACd,IAAI+B,QAAQ,CAAC,CAAC,CAAC,IAAIiC,YAAY,KAAKxB,SAAS,CAACF,KAAK,EAAE;UACnD,MAAM6B,aAAa,GAAGH,YAAY,GAAGxB,SAAS,CAACF,KAAK;UACpDP,QAAQ,CAAC,CAAC,CAAC,CAACqC,SAAS,CAACD,aAAa,GAAGrB,SAAS,CAACR,KAAK,GAAG,CAAC,CAAC;QAC5D;QACA,IAAIP,QAAQ,CAACE,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC,IAAI0B,YAAY,KAAK,CAAC,EAAE;UACnD,MAAMK,YAAY,GAAGL,YAAY,GAAG,CAAC;UACrCjC,QAAQ,CAACE,KAAK,CAACK,KAAK,GAAG,CAAC,CAAC,CAAC8B,SAAS,CAACC,YAAY,GAAG,CAACvB,SAAS,CAACR,KAAK,GAAG,CAAC,CAAC;QAC1E;MACF;MACAd,KAAK,CAACG,MAAM,GAAGkC,YAAY;MAC3BrC,KAAK,CAACE,MAAM,GAAGsC,YAAY;MAC3B,IAAIE,UAAU,IAAIL,YAAY,KAAKlC,MAAM,EAAE;QACzCP,IAAI,CAAC,QAAQ,EAAE2B,eAAe,CAACT,KAAK,CAAC;MACvC;IACF,CAAC;IACD,MAAMgC,eAAe,GAAGA,CAAA,KAAM;MAC5B9C,KAAK,CAACI,OAAO,GAAG,IAAI;MACpB,IAAIJ,KAAK,CAACG,MAAM,IAAI,CAAC,CAAC,EAAE;QACtBsC,IAAI,CAAC;UACHN,IAAI,EAAE1B,KAAK,CAACK;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAId,KAAK,CAACG,MAAM,IAAIM,KAAK,CAACK,KAAK,EAAE;QACtC2B,IAAI,CAAC;UACHN,IAAI,EAAE,CAAC1B,KAAK,CAACK;QACf,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAMiC,IAAI,GAAGA,CAAA,KAAM;MACjBD,eAAe,CAAC,CAAC;MACjBxC,KAAK,CAAC0C,KAAK,CAAC,CAAC;MACblF,SAAS,CAAC,MAAM;QACdkC,KAAK,CAACI,OAAO,GAAG,KAAK;QACrBqC,IAAI,CAAC;UACHN,IAAI,EAAE,CAAC,CAAC;UACRO,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,MAAMO,IAAI,GAAGA,CAAA,KAAM;MACjBH,eAAe,CAAC,CAAC;MACjBxC,KAAK,CAAC0C,KAAK,CAAC,CAAC;MACblF,SAAS,CAAC,MAAM;QACdkC,KAAK,CAACI,OAAO,GAAG,KAAK;QACrBqC,IAAI,CAAC;UACHN,IAAI,EAAE,CAAC;UACPO,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAIQ,aAAa;IACjB,MAAMC,YAAY,GAAGA,CAAA,KAAMC,YAAY,CAACF,aAAa,CAAC;IACtD,MAAMrE,QAAQ,GAAGA,CAAA,KAAM;MACrBsE,YAAY,CAAC,CAAC;MACd,IAAI,CAAC1D,KAAK,CAACZ,QAAQ,GAAG,CAAC,IAAI4B,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE;QAC1CoC,aAAa,GAAGG,UAAU,CAAC,MAAM;UAC/BJ,IAAI,CAAC,CAAC;UACNpE,QAAQ,CAAC,CAAC;QACZ,CAAC,EAAE,CAACY,KAAK,CAACZ,QAAQ,CAAC;MACrB;IACF,CAAC;IACD,MAAMyE,UAAU,GAAGA,CAACnD,MAAM,GAAG,CAACV,KAAK,CAACR,YAAY,KAAK;MACnD,IAAI,CAACa,IAAI,CAACgB,KAAK,EAAE;QACf;MACF;MACA,MAAMyC,EAAE,GAAGA,CAAA,KAAM;QACf,IAAIC,EAAE,EAAEC,EAAE;QACV,IAAI,CAACnG,QAAQ,CAACwC,IAAI,CAAC,EAAE;UACnB,MAAMG,IAAI,GAAG;YACXxB,KAAK,EAAEqB,IAAI,CAACgB,KAAK,CAAC4C,WAAW;YAC7BhF,MAAM,EAAEoB,IAAI,CAACgB,KAAK,CAAC6C;UACrB,CAAC;UACD3D,KAAK,CAACC,IAAI,GAAGA,IAAI;UACjBD,KAAK,CAACvB,KAAK,GAAG,EAAE,CAAC+E,EAAE,GAAG/D,KAAK,CAAChB,KAAK,KAAK,IAAI,GAAG+E,EAAE,GAAGvD,IAAI,CAACxB,KAAK,CAAC;UAC7DuB,KAAK,CAACtB,MAAM,GAAG,EAAE,CAAC+E,EAAE,GAAGhE,KAAK,CAACf,MAAM,KAAK,IAAI,GAAG+E,EAAE,GAAGxD,IAAI,CAACvB,MAAM,CAAC;QAClE;QACA,IAAI+B,KAAK,CAACK,KAAK,EAAE;UACfX,MAAM,GAAGgB,IAAI,CAACoB,GAAG,CAAC9B,KAAK,CAACK,KAAK,GAAG,CAAC,EAAEX,MAAM,CAAC;UAC1C,IAAIA,MAAM,KAAK,CAAC,CAAC,EAAE;YACjBA,MAAM,GAAGM,KAAK,CAACK,KAAK,GAAG,CAAC;UAC1B;QACF;QACAd,KAAK,CAACG,MAAM,GAAGA,MAAM;QACrBH,KAAK,CAACI,OAAO,GAAG,IAAI;QACpBJ,KAAK,CAACE,MAAM,GAAGkC,eAAe,CAACjC,MAAM,CAAC;QACtCI,QAAQ,CAACqD,OAAO,CAAEC,KAAK,IAAK;UAC1BA,KAAK,CAACjB,SAAS,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC;QACF/D,QAAQ,CAAC,CAAC;MACZ,CAAC;MACD,IAAIvB,QAAQ,CAACwC,IAAI,CAAC,EAAE;QAClB5C,QAAQ,CAAC,CAAC,CAAC4G,IAAI,CAACP,EAAE,CAAC;MACrB,CAAC,MAAM;QACLA,EAAE,CAAC,CAAC;MACN;IACF,CAAC;IACD,MAAMQ,MAAM,GAAGA,CAAA,KAAMT,UAAU,CAACtD,KAAK,CAACG,MAAM,CAAC;IAC7C,IAAI6D,cAAc;IAClB,MAAMC,YAAY,GAAIC,KAAK,IAAK;MAC9B,IAAI,CAACzE,KAAK,CAACV,SAAS;MAAI;MACxBmF,KAAK,CAACC,OAAO,CAACzD,MAAM,GAAG,CAAC,EAAE;MAC1BJ,KAAK,CAAC8D,KAAK,CAACF,KAAK,CAAC;MAClB7D,QAAQ,GAAG,KAAK;MAChB2D,cAAc,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3BnB,YAAY,CAAC,CAAC;MACdL,eAAe,CAAC,CAAC;IACnB,CAAC;IACD,MAAMyB,WAAW,GAAIL,KAAK,IAAK;MAC7B,IAAIzE,KAAK,CAACV,SAAS,IAAIiB,KAAK,CAACI,OAAO,EAAE;QACpCE,KAAK,CAACmC,IAAI,CAACyB,KAAK,CAAC;QACjB,IAAI1C,kBAAkB,CAACV,KAAK,EAAE;UAC5B,MAAM0D,WAAW,GAAG,CAAC/E,KAAK,CAACjB,IAAI,KAAKwB,KAAK,CAACG,MAAM,KAAK,CAAC,IAAIS,KAAK,CAACE,KAAK,GAAG,CAAC,IAAId,KAAK,CAACG,MAAM,KAAKM,KAAK,CAACK,KAAK,GAAG,CAAC,IAAIF,KAAK,CAACE,KAAK,GAAG,CAAC,CAAC;UACjI,IAAI,CAAC0D,WAAW,EAAE;YAChB7G,cAAc,CAACuG,KAAK,EAAEzE,KAAK,CAACJ,eAAe,CAAC;YAC5CoD,IAAI,CAAC;cACHvC,MAAM,EAAEU,KAAK,CAACE;YAChB,CAAC,CAAC;YACF,IAAI,CAACT,QAAQ,EAAE;cACbT,IAAI,CAAC,WAAW,EAAE;gBAChB6E,KAAK,EAAElD,eAAe,CAACT;cACzB,CAAC,CAAC;cACFT,QAAQ,GAAG,IAAI;YACjB;UACF;QACF;MACF;IACF,CAAC;IACD,MAAMqE,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI,CAACjF,KAAK,CAACV,SAAS,IAAI,CAACiB,KAAK,CAACI,OAAO,EAAE;QACtC;MACF;MACA,MAAMtB,QAAQ,GAAGuF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGN,cAAc;MAC5C,MAAMW,KAAK,GAAG/D,KAAK,CAACE,KAAK,GAAGhC,QAAQ;MACpC,MAAM8F,WAAW,GAAGzD,IAAI,CAACE,GAAG,CAACsD,KAAK,CAAC,GAAG,IAAI,IAAIxD,IAAI,CAACE,GAAG,CAACT,KAAK,CAACE,KAAK,CAAC,GAAGH,IAAI,CAACG,KAAK,GAAG,CAAC;MACpF,IAAI8D,WAAW,IAAIpD,kBAAkB,CAACV,KAAK,EAAE;QAC3C,MAAMZ,MAAM,GAAGT,KAAK,CAACd,QAAQ,GAAG2B,KAAK,CAACuE,OAAO,CAAC/D,KAAK,GAAGR,KAAK,CAACwE,OAAO,CAAChE,KAAK;QACzE,IAAIqB,IAAI,GAAG,CAAC;QACZ,IAAI1C,KAAK,CAACjB,IAAI,EAAE;UACd2D,IAAI,GAAGjC,MAAM,GAAG,CAAC,GAAGU,KAAK,CAACE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAClD,CAAC,MAAM;UACLqB,IAAI,GAAG,CAAChB,IAAI,CAACP,KAAK,CAACE,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,CAACF,KAAK,CAACE,KAAK,GAAGH,IAAI,CAACG,KAAK,CAAC;QAC5E;QACA2B,IAAI,CAAC;UACHN,IAAI;UACJO,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI9B,KAAK,CAACE,KAAK,EAAE;QACtB2B,IAAI,CAAC;UACHN,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA9B,QAAQ,GAAG,KAAK;MAChBL,KAAK,CAACI,OAAO,GAAG,KAAK;MACrBR,IAAI,CAAC,SAAS,EAAE;QACd6E,KAAK,EAAElD,eAAe,CAACT;MACzB,CAAC,CAAC;MACFjC,QAAQ,CAAC,CAAC;IACZ,CAAC;IACD,MAAMkG,OAAO,GAAGA,CAACN,KAAK,EAAEO,OAAO,GAAG,CAAC,CAAC,KAAK;MACvClC,eAAe,CAAC,CAAC;MACjBxC,KAAK,CAAC0C,KAAK,CAAC,CAAC;MACblF,SAAS,CAAC,MAAM;QACd,IAAImH,WAAW;QACf,IAAIxF,KAAK,CAACjB,IAAI,IAAIiG,KAAK,KAAKhE,KAAK,CAACK,KAAK,EAAE;UACvCmE,WAAW,GAAGjF,KAAK,CAACG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGsE,KAAK;QAC9C,CAAC,MAAM;UACLQ,WAAW,GAAGR,KAAK,GAAGhE,KAAK,CAACK,KAAK;QACnC;QACA,IAAIkE,OAAO,CAACE,SAAS,EAAE;UACrBpH,SAAS,CAAC,MAAM;YACdkC,KAAK,CAACI,OAAO,GAAG,KAAK;UACvB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLJ,KAAK,CAACI,OAAO,GAAG,KAAK;QACvB;QACAqC,IAAI,CAAC;UACHN,IAAI,EAAE8C,WAAW,GAAGjF,KAAK,CAACG,MAAM;UAChCuC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,MAAMyC,SAAS,GAAGA,CAACC,CAAC,EAAEX,KAAK,KAAK;MAC9B,MAAMtE,MAAM,GAAGsE,KAAK,KAAKlD,eAAe,CAACT,KAAK;MAC9C,MAAMc,KAAK,GAAGzB,MAAM,GAAG;QACrBkF,eAAe,EAAE5F,KAAK,CAACP;MACzB,CAAC,GAAG,KAAK,CAAC;MACV,OAAO9B,YAAY,CAAC,GAAG,EAAE;QACvB,OAAO,EAAEwE,KAAK;QACd,OAAO,EAAEtD,GAAG,CAAC,WAAW,EAAE;UACxB6B;QACF,CAAC;MACH,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,MAAMmF,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIzF,KAAK,CAAC0F,SAAS,EAAE;QACnB,OAAO1F,KAAK,CAAC0F,SAAS,CAAC;UACrBpF,MAAM,EAAEoB,eAAe,CAACT,KAAK;UAC7B0E,KAAK,EAAE/E,KAAK,CAACK;QACf,CAAC,CAAC;MACJ;MACA,IAAIrB,KAAK,CAACL,cAAc,IAAIqB,KAAK,CAACK,KAAK,GAAG,CAAC,EAAE;QAC3C,OAAO1D,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEkB,GAAG,CAAC,YAAY,EAAE;YACzBK,QAAQ,EAAEc,KAAK,CAACd;UAClB,CAAC;QACH,CAAC,EAAE,CAAC8G,KAAK,CAAChF,KAAK,CAACK,KAAK,CAAC,CAAC4E,IAAI,CAAC,EAAE,CAAC,CAACC,GAAG,CAACR,SAAS,CAAC,CAAC,CAAC;MAClD;IACF,CAAC;IACDhH,SAAS,CAAC;MACR4E,IAAI;MACJE,IAAI;MACJjD,KAAK;MACL+D,MAAM;MACNgB;IACF,CAAC,CAAC;IACFvE,YAAY,CAAC;MACXG,IAAI;MACJlB,KAAK;MACLgB,KAAK;MACLc;IACF,CAAC,CAAC;IACF7E,KAAK,CAAC,MAAM+C,KAAK,CAACR,YAAY,EAAG6B,KAAK,IAAKwC,UAAU,CAAC,CAACxC,KAAK,CAAC,CAAC;IAC9DpE,KAAK,CAAC+D,KAAK,EAAE,MAAM6C,UAAU,CAACtD,KAAK,CAACG,MAAM,CAAC,CAAC;IAC5CzD,KAAK,CAAC,MAAM+C,KAAK,CAACZ,QAAQ,EAAEA,QAAQ,CAAC;IACrCnC,KAAK,CAAC,CAACe,WAAW,EAAEC,YAAY,EAAE,MAAM+B,KAAK,CAAChB,KAAK,EAAE,MAAMgB,KAAK,CAACf,MAAM,CAAC,EAAEqF,MAAM,CAAC;IACjFrH,KAAK,CAACuB,iBAAiB,CAAC,CAAC,EAAG2H,OAAO,IAAK;MACtC,IAAIA,OAAO,KAAK,SAAS,EAAE;QACzB/G,QAAQ,CAAC,CAAC;MACZ,CAAC,MAAM;QACLsE,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,CAAC;IACFtG,SAAS,CAACyG,UAAU,CAAC;IACrBxG,WAAW,CAAC,MAAMwG,UAAU,CAACtD,KAAK,CAACG,MAAM,CAAC,CAAC;IAC3C/B,aAAa,CAAC,MAAMkF,UAAU,CAACtD,KAAK,CAACG,MAAM,CAAC,CAAC;IAC7CpD,aAAa,CAACoG,YAAY,CAAC;IAC3BnG,eAAe,CAACmG,YAAY,CAAC;IAC7BnF,gBAAgB,CAAC,WAAW,EAAEuG,WAAW,EAAE;MACzCsB,MAAM,EAAE9F;IACV,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIyD,EAAE;MACN,OAAOpG,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAE0C,IAAI;QACX,OAAO,EAAExB,GAAG,CAAC;MACf,CAAC,EAAE,CAAClB,YAAY,CAAC,KAAK,EAAE;QACtB,KAAK,EAAE2C,KAAK;QACZ,OAAO,EAAE4B,UAAU,CAACb,KAAK;QACzB,OAAO,EAAExC,GAAG,CAAC,OAAO,EAAE;UACpBK,QAAQ,EAAEc,KAAK,CAACd;QAClB,CAAC,CAAC;QACF,qBAAqB,EAAEsF,YAAY;QACnC,YAAY,EAAES,UAAU;QACxB,eAAe,EAAEA;MACnB,CAAC,EAAE,CAAC,CAAClB,EAAE,GAAG3D,KAAK,CAACiG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGtC,EAAE,CAACuC,IAAI,CAAClG,KAAK,CAAC,CAAC,CAAC,EAAEyF,eAAe,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEhG,SAAS,EACTE,aAAa,IAAIsG,OAAO,EACxBvH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}