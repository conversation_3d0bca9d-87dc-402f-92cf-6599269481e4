{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"divider\");\nconst dividerProps = {\n  dashed: <PERSON><PERSON><PERSON>,\n  hairline: truthProp,\n  vertical: <PERSON><PERSON><PERSON>,\n  contentPosition: makeStringProp(\"center\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: dividerProps,\n  setup(props, {\n    slots\n  }) {\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"role\": \"separator\",\n        \"class\": bem({\n          dashed: props.dashed,\n          hairline: props.hairline,\n          vertical: props.vertical,\n          [`content-${props.contentPosition}`]: !!slots.default && !props.vertical\n        })\n      }, [!props.vertical && ((_a = slots.default) == null ? void 0 : _a.call(slots))]);\n    };\n  }\n});\nexport { stdin_default as default, dividerProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "truthProp", "makeStringProp", "createNamespace", "name", "bem", "dividerProps", "dashed", "Boolean", "hairline", "vertical", "contentPosition", "stdin_default", "props", "setup", "slots", "_a", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/divider/Divider.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"divider\");\nconst dividerProps = {\n  dashed: <PERSON><PERSON><PERSON>,\n  hairline: truthProp,\n  vertical: <PERSON><PERSON><PERSON>,\n  contentPosition: makeStringProp(\"center\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: dividerProps,\n  setup(props, {\n    slots\n  }) {\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"role\": \"separator\",\n        \"class\": bem({\n          dashed: props.dashed,\n          hairline: props.hairline,\n          vertical: props.vertical,\n          [`content-${props.contentPosition}`]: !!slots.default && !props.vertical\n        })\n      }, [!props.vertical && ((_a = slots.default) == null ? void 0 : _a.call(slots))]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  dividerProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC/E,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGF,eAAe,CAAC,SAAS,CAAC;AAC9C,MAAMG,YAAY,GAAG;EACnBC,MAAM,EAAEC,OAAO;EACfC,QAAQ,EAAER,SAAS;EACnBS,QAAQ,EAAEF,OAAO;EACjBG,eAAe,EAAET,cAAc,CAAC,QAAQ;AAC1C,CAAC;AACD,IAAIU,aAAa,GAAGd,eAAe,CAAC;EAClCM,IAAI;EACJS,KAAK,EAAEP,YAAY;EACnBQ,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,OAAO,MAAM;MACX,IAAIC,EAAE;MACN,OAAOhB,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,WAAW;QACnB,OAAO,EAAEK,GAAG,CAAC;UACXE,MAAM,EAAEM,KAAK,CAACN,MAAM;UACpBE,QAAQ,EAAEI,KAAK,CAACJ,QAAQ;UACxBC,QAAQ,EAAEG,KAAK,CAACH,QAAQ;UACxB,CAAC,WAAWG,KAAK,CAACF,eAAe,EAAE,GAAG,CAAC,CAACI,KAAK,CAACE,OAAO,IAAI,CAACJ,KAAK,CAACH;QAClE,CAAC;MACH,CAAC,EAAE,CAAC,CAACG,KAAK,CAACH,QAAQ,KAAK,CAACM,EAAE,GAAGD,KAAK,CAACE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEH,aAAa,IAAIK,OAAO,EACxBX,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}