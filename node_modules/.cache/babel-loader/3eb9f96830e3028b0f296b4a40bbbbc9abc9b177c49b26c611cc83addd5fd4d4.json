{"ast": null, "code": "import { ref, watch } from \"vue\";\nfunction useLazyRender(show) {\n  const inited = ref(false);\n  watch(show, value => {\n    if (value) {\n      inited.value = value;\n    }\n  }, {\n    immediate: true\n  });\n  return render => () => inited.value ? render() : null;\n}\nexport { useLazyRender };", "map": {"version": 3, "names": ["ref", "watch", "useLazyRender", "show", "inited", "value", "immediate", "render"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-lazy-render.mjs"], "sourcesContent": ["import { ref, watch } from \"vue\";\nfunction useLazyRender(show) {\n  const inited = ref(false);\n  watch(\n    show,\n    (value) => {\n      if (value) {\n        inited.value = value;\n      }\n    },\n    { immediate: true }\n  );\n  return (render) => () => inited.value ? render() : null;\n}\nexport {\n  useLazyRender\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAChC,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,MAAMC,MAAM,GAAGJ,GAAG,CAAC,KAAK,CAAC;EACzBC,KAAK,CACHE,IAAI,EACHE,KAAK,IAAK;IACT,IAAIA,KAAK,EAAE;MACTD,MAAM,CAACC,KAAK,GAAGA,KAAK;IACtB;EACF,CAAC,EACD;IAAEC,SAAS,EAAE;EAAK,CACpB,CAAC;EACD,OAAQC,MAAM,IAAK,MAAMH,MAAM,CAACC,KAAK,GAAGE,MAAM,CAAC,CAAC,GAAG,IAAI;AACzD;AACA,SACEL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}