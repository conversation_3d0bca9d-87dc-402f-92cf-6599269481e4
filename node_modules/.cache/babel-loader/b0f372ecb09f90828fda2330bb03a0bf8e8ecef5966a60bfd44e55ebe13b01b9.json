{"ast": null, "code": "import { h } from \"vue\";\nimport { inBrowser, useRect } from \"@vant/use\";\nvar stdin_default = lazy => ({\n  props: {\n    tag: {\n      type: String,\n      default: \"div\"\n    }\n  },\n  emits: [\"show\"],\n  render() {\n    return h(this.tag, this.show && this.$slots.default ? this.$slots.default() : null);\n  },\n  data() {\n    return {\n      el: null,\n      state: {\n        loaded: false\n      },\n      show: false\n    };\n  },\n  mounted() {\n    this.el = this.$el;\n    lazy.addLazyBox(this);\n    lazy.lazyLoadHandler();\n  },\n  beforeUnmount() {\n    lazy.removeComponent(this);\n  },\n  methods: {\n    checkInView() {\n      const rect = useRect(this.$el);\n      return inBrowser && rect.top < window.innerHeight * lazy.options.preLoad && rect.bottom > 0 && rect.left < window.innerWidth * lazy.options.preLoad && rect.right > 0;\n    },\n    load() {\n      this.show = true;\n      this.state.loaded = true;\n      this.$emit(\"show\", this);\n    },\n    destroy() {\n      return this.$destroy;\n    }\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["h", "inBrowser", "useRect", "stdin_default", "lazy", "props", "tag", "type", "String", "default", "emits", "render", "show", "$slots", "data", "el", "state", "loaded", "mounted", "$el", "addLazyBox", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beforeUnmount", "removeComponent", "methods", "checkInView", "rect", "top", "window", "innerHeight", "options", "preLoad", "bottom", "left", "innerWidth", "right", "load", "$emit", "destroy", "$destroy"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/lazyload/vue-lazyload/lazy-component.mjs"], "sourcesContent": ["import { h } from \"vue\";\nimport { inBrowser, useRect } from \"@vant/use\";\nvar stdin_default = (lazy) => ({\n  props: {\n    tag: {\n      type: String,\n      default: \"div\"\n    }\n  },\n  emits: [\"show\"],\n  render() {\n    return h(\n      this.tag,\n      this.show && this.$slots.default ? this.$slots.default() : null\n    );\n  },\n  data() {\n    return {\n      el: null,\n      state: {\n        loaded: false\n      },\n      show: false\n    };\n  },\n  mounted() {\n    this.el = this.$el;\n    lazy.addLazyBox(this);\n    lazy.lazyLoadHandler();\n  },\n  beforeUnmount() {\n    lazy.removeComponent(this);\n  },\n  methods: {\n    checkInView() {\n      const rect = useRect(this.$el);\n      return inBrowser && rect.top < window.innerHeight * lazy.options.preLoad && rect.bottom > 0 && rect.left < window.innerWidth * lazy.options.preLoad && rect.right > 0;\n    },\n    load() {\n      this.show = true;\n      this.state.loaded = true;\n      this.$emit(\"show\", this);\n    },\n    destroy() {\n      return this.$destroy;\n    }\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,CAAC,QAAQ,KAAK;AACvB,SAASC,SAAS,EAAEC,OAAO,QAAQ,WAAW;AAC9C,IAAIC,aAAa,GAAIC,IAAI,KAAM;EAC7BC,KAAK,EAAE;IACLC,GAAG,EAAE;MACHC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,MAAM,CAAC;EACfC,MAAMA,CAAA,EAAG;IACP,OAAOX,CAAC,CACN,IAAI,CAACM,GAAG,EACR,IAAI,CAACM,IAAI,IAAI,IAAI,CAACC,MAAM,CAACJ,OAAO,GAAG,IAAI,CAACI,MAAM,CAACJ,OAAO,CAAC,CAAC,GAAG,IAC7D,CAAC;EACH,CAAC;EACDK,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,EAAE,EAAE,IAAI;MACRC,KAAK,EAAE;QACLC,MAAM,EAAE;MACV,CAAC;MACDL,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDM,OAAOA,CAAA,EAAG;IACR,IAAI,CAACH,EAAE,GAAG,IAAI,CAACI,GAAG;IAClBf,IAAI,CAACgB,UAAU,CAAC,IAAI,CAAC;IACrBhB,IAAI,CAACiB,eAAe,CAAC,CAAC;EACxB,CAAC;EACDC,aAAaA,CAAA,EAAG;IACdlB,IAAI,CAACmB,eAAe,CAAC,IAAI,CAAC;EAC5B,CAAC;EACDC,OAAO,EAAE;IACPC,WAAWA,CAAA,EAAG;MACZ,MAAMC,IAAI,GAAGxB,OAAO,CAAC,IAAI,CAACiB,GAAG,CAAC;MAC9B,OAAOlB,SAAS,IAAIyB,IAAI,CAACC,GAAG,GAAGC,MAAM,CAACC,WAAW,GAAGzB,IAAI,CAAC0B,OAAO,CAACC,OAAO,IAAIL,IAAI,CAACM,MAAM,GAAG,CAAC,IAAIN,IAAI,CAACO,IAAI,GAAGL,MAAM,CAACM,UAAU,GAAG9B,IAAI,CAAC0B,OAAO,CAACC,OAAO,IAAIL,IAAI,CAACS,KAAK,GAAG,CAAC;IACvK,CAAC;IACDC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACxB,IAAI,GAAG,IAAI;MAChB,IAAI,CAACI,KAAK,CAACC,MAAM,GAAG,IAAI;MACxB,IAAI,CAACoB,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC;IAC1B,CAAC;IACDC,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,QAAQ;IACtB;EACF;AACF,CAAC,CAAC;AACF,SACEpC,aAAa,IAAIM,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}