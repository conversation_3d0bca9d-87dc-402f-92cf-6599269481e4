{"ast": null, "code": "import { ref } from \"vue\";\nimport { TAP_OFFSET } from \"../utils/index.mjs\";\nfunction getDirection(x, y) {\n  if (x > y) {\n    return \"horizontal\";\n  }\n  if (y > x) {\n    return \"vertical\";\n  }\n  return \"\";\n}\nfunction useTouch() {\n  const startX = ref(0);\n  const startY = ref(0);\n  const deltaX = ref(0);\n  const deltaY = ref(0);\n  const offsetX = ref(0);\n  const offsetY = ref(0);\n  const direction = ref(\"\");\n  const isTap = ref(true);\n  const isVertical = () => direction.value === \"vertical\";\n  const isHorizontal = () => direction.value === \"horizontal\";\n  const reset = () => {\n    deltaX.value = 0;\n    deltaY.value = 0;\n    offsetX.value = 0;\n    offsetY.value = 0;\n    direction.value = \"\";\n    isTap.value = true;\n  };\n  const start = event => {\n    reset();\n    startX.value = event.touches[0].clientX;\n    startY.value = event.touches[0].clientY;\n  };\n  const move = event => {\n    const touch = event.touches[0];\n    deltaX.value = (touch.clientX < 0 ? 0 : touch.clientX) - startX.value;\n    deltaY.value = touch.clientY - startY.value;\n    offsetX.value = Math.abs(deltaX.value);\n    offsetY.value = Math.abs(deltaY.value);\n    const LOCK_DIRECTION_DISTANCE = 10;\n    if (!direction.value || offsetX.value < LOCK_DIRECTION_DISTANCE && offsetY.value < LOCK_DIRECTION_DISTANCE) {\n      direction.value = getDirection(offsetX.value, offsetY.value);\n    }\n    if (isTap.value && (offsetX.value > TAP_OFFSET || offsetY.value > TAP_OFFSET)) {\n      isTap.value = false;\n    }\n  };\n  return {\n    move,\n    start,\n    reset,\n    startX,\n    startY,\n    deltaX,\n    deltaY,\n    offsetX,\n    offsetY,\n    direction,\n    isVertical,\n    isHorizontal,\n    isTap\n  };\n}\nexport { useTouch };", "map": {"version": 3, "names": ["ref", "TAP_OFFSET", "getDirection", "x", "y", "useTouch", "startX", "startY", "deltaX", "deltaY", "offsetX", "offsetY", "direction", "isTap", "isVertical", "value", "isHorizontal", "reset", "start", "event", "touches", "clientX", "clientY", "move", "touch", "Math", "abs", "LOCK_DIRECTION_DISTANCE"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-touch.mjs"], "sourcesContent": ["import { ref } from \"vue\";\nimport { TAP_OFFSET } from \"../utils/index.mjs\";\nfunction getDirection(x, y) {\n  if (x > y) {\n    return \"horizontal\";\n  }\n  if (y > x) {\n    return \"vertical\";\n  }\n  return \"\";\n}\nfunction useTouch() {\n  const startX = ref(0);\n  const startY = ref(0);\n  const deltaX = ref(0);\n  const deltaY = ref(0);\n  const offsetX = ref(0);\n  const offsetY = ref(0);\n  const direction = ref(\"\");\n  const isTap = ref(true);\n  const isVertical = () => direction.value === \"vertical\";\n  const isHorizontal = () => direction.value === \"horizontal\";\n  const reset = () => {\n    deltaX.value = 0;\n    deltaY.value = 0;\n    offsetX.value = 0;\n    offsetY.value = 0;\n    direction.value = \"\";\n    isTap.value = true;\n  };\n  const start = (event) => {\n    reset();\n    startX.value = event.touches[0].clientX;\n    startY.value = event.touches[0].clientY;\n  };\n  const move = (event) => {\n    const touch = event.touches[0];\n    deltaX.value = (touch.clientX < 0 ? 0 : touch.clientX) - startX.value;\n    deltaY.value = touch.clientY - startY.value;\n    offsetX.value = Math.abs(deltaX.value);\n    offsetY.value = Math.abs(deltaY.value);\n    const LOCK_DIRECTION_DISTANCE = 10;\n    if (!direction.value || offsetX.value < LOCK_DIRECTION_DISTANCE && offsetY.value < LOCK_DIRECTION_DISTANCE) {\n      direction.value = getDirection(offsetX.value, offsetY.value);\n    }\n    if (isTap.value && (offsetX.value > TAP_OFFSET || offsetY.value > TAP_OFFSET)) {\n      isTap.value = false;\n    }\n  };\n  return {\n    move,\n    start,\n    reset,\n    startX,\n    startY,\n    deltaX,\n    deltaY,\n    offsetX,\n    offsetY,\n    direction,\n    isVertical,\n    isHorizontal,\n    isTap\n  };\n}\nexport {\n  useTouch\n};\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,KAAK;AACzB,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC1B,IAAID,CAAC,GAAGC,CAAC,EAAE;IACT,OAAO,YAAY;EACrB;EACA,IAAIA,CAAC,GAAGD,CAAC,EAAE;IACT,OAAO,UAAU;EACnB;EACA,OAAO,EAAE;AACX;AACA,SAASE,QAAQA,CAAA,EAAG;EAClB,MAAMC,MAAM,GAAGN,GAAG,CAAC,CAAC,CAAC;EACrB,MAAMO,MAAM,GAAGP,GAAG,CAAC,CAAC,CAAC;EACrB,MAAMQ,MAAM,GAAGR,GAAG,CAAC,CAAC,CAAC;EACrB,MAAMS,MAAM,GAAGT,GAAG,CAAC,CAAC,CAAC;EACrB,MAAMU,OAAO,GAAGV,GAAG,CAAC,CAAC,CAAC;EACtB,MAAMW,OAAO,GAAGX,GAAG,CAAC,CAAC,CAAC;EACtB,MAAMY,SAAS,GAAGZ,GAAG,CAAC,EAAE,CAAC;EACzB,MAAMa,KAAK,GAAGb,GAAG,CAAC,IAAI,CAAC;EACvB,MAAMc,UAAU,GAAGA,CAAA,KAAMF,SAAS,CAACG,KAAK,KAAK,UAAU;EACvD,MAAMC,YAAY,GAAGA,CAAA,KAAMJ,SAAS,CAACG,KAAK,KAAK,YAAY;EAC3D,MAAME,KAAK,GAAGA,CAAA,KAAM;IAClBT,MAAM,CAACO,KAAK,GAAG,CAAC;IAChBN,MAAM,CAACM,KAAK,GAAG,CAAC;IAChBL,OAAO,CAACK,KAAK,GAAG,CAAC;IACjBJ,OAAO,CAACI,KAAK,GAAG,CAAC;IACjBH,SAAS,CAACG,KAAK,GAAG,EAAE;IACpBF,KAAK,CAACE,KAAK,GAAG,IAAI;EACpB,CAAC;EACD,MAAMG,KAAK,GAAIC,KAAK,IAAK;IACvBF,KAAK,CAAC,CAAC;IACPX,MAAM,CAACS,KAAK,GAAGI,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACvCd,MAAM,CAACQ,KAAK,GAAGI,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO;EACzC,CAAC;EACD,MAAMC,IAAI,GAAIJ,KAAK,IAAK;IACtB,MAAMK,KAAK,GAAGL,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;IAC9BZ,MAAM,CAACO,KAAK,GAAG,CAACS,KAAK,CAACH,OAAO,GAAG,CAAC,GAAG,CAAC,GAAGG,KAAK,CAACH,OAAO,IAAIf,MAAM,CAACS,KAAK;IACrEN,MAAM,CAACM,KAAK,GAAGS,KAAK,CAACF,OAAO,GAAGf,MAAM,CAACQ,KAAK;IAC3CL,OAAO,CAACK,KAAK,GAAGU,IAAI,CAACC,GAAG,CAAClB,MAAM,CAACO,KAAK,CAAC;IACtCJ,OAAO,CAACI,KAAK,GAAGU,IAAI,CAACC,GAAG,CAACjB,MAAM,CAACM,KAAK,CAAC;IACtC,MAAMY,uBAAuB,GAAG,EAAE;IAClC,IAAI,CAACf,SAAS,CAACG,KAAK,IAAIL,OAAO,CAACK,KAAK,GAAGY,uBAAuB,IAAIhB,OAAO,CAACI,KAAK,GAAGY,uBAAuB,EAAE;MAC1Gf,SAAS,CAACG,KAAK,GAAGb,YAAY,CAACQ,OAAO,CAACK,KAAK,EAAEJ,OAAO,CAACI,KAAK,CAAC;IAC9D;IACA,IAAIF,KAAK,CAACE,KAAK,KAAKL,OAAO,CAACK,KAAK,GAAGd,UAAU,IAAIU,OAAO,CAACI,KAAK,GAAGd,UAAU,CAAC,EAAE;MAC7EY,KAAK,CAACE,KAAK,GAAG,KAAK;IACrB;EACF,CAAC;EACD,OAAO;IACLQ,IAAI;IACJL,KAAK;IACLD,KAAK;IACLX,MAAM;IACNC,MAAM;IACNC,MAAM;IACNC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,SAAS;IACTE,UAAU;IACVE,YAAY;IACZH;EACF,CAAC;AACH;AACA,SACER,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}