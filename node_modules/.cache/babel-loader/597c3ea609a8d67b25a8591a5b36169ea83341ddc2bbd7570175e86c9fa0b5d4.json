{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Search from \"./Search.mjs\";\nconst Search = withInstall(_Search);\nvar stdin_default = Search;\nimport { searchProps } from \"./Search.mjs\";\nexport { Search, stdin_default as default, searchProps };", "map": {"version": 3, "names": ["withInstall", "_Search", "Search", "stdin_default", "searchProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/search/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Search from \"./Search.mjs\";\nconst Search = withInstall(_Search);\nvar stdin_default = Search;\nimport { searchProps } from \"./Search.mjs\";\nexport {\n  Search,\n  stdin_default as default,\n  searchProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEF,MAAM,EACNC,aAAa,IAAIE,OAAO,EACxBD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}