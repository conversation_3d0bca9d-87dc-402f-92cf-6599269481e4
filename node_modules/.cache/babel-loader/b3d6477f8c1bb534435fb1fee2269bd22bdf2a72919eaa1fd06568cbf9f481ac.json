{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Steps from \"./Steps.mjs\";\nconst Steps = withInstall(_Steps);\nvar stdin_default = Steps;\nimport { stepsProps } from \"./Steps.mjs\";\nexport { Steps, stdin_default as default, stepsProps };", "map": {"version": 3, "names": ["withInstall", "_Steps", "Steps", "stdin_default", "stepsProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/steps/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Steps from \"./Steps.mjs\";\nconst Steps = withInstall(_Steps);\nvar stdin_default = Steps;\nimport { stepsProps } from \"./Steps.mjs\";\nexport {\n  Steps,\n  stdin_default as default,\n  stepsProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SAASE,UAAU,QAAQ,aAAa;AACxC,SACEF,KAAK,EACLC,aAAa,IAAIE,OAAO,EACxBD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}