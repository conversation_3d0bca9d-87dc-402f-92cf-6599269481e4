{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"empty\");\nconst emptyProps = {\n  image: makeStringProp(\"default\"),\n  imageSize: [Number, String, Array],\n  description: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: emptyProps,\n  setup(props, {\n    slots\n  }) {\n    const renderDescription = () => {\n      const description = slots.description ? slots.description() : props.description;\n      if (description) {\n        return _createVNode(\"p\", {\n          \"class\": bem(\"description\")\n        }, [description]);\n      }\n    };\n    const renderBottom = () => {\n      if (slots.default) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"bottom\")\n        }, [slots.default()]);\n      }\n    };\n    const baseId = useId();\n    const getId = num => `${baseId}-${num}`;\n    const getUrlById = num => `url(#${getId(num)})`;\n    const renderStop = (color, offset, opacity) => _createVNode(\"stop\", {\n      \"stop-color\": color,\n      \"offset\": `${offset}%`,\n      \"stop-opacity\": opacity\n    }, null);\n    const renderStops = (fromColor, toColor) => [renderStop(fromColor, 0), renderStop(toColor, 100)];\n    const renderShadow = id => [_createVNode(\"defs\", null, [_createVNode(\"radialGradient\", {\n      \"id\": getId(id),\n      \"cx\": \"50%\",\n      \"cy\": \"54%\",\n      \"fx\": \"50%\",\n      \"fy\": \"54%\",\n      \"r\": \"297%\",\n      \"gradientTransform\": \"matrix(-.16 0 0 -.33 .58 .72)\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#F2F3F5\", 100, 0.3)])]), _createVNode(\"ellipse\", {\n      \"fill\": getUrlById(id),\n      \"opacity\": \".8\",\n      \"cx\": \"80\",\n      \"cy\": \"140\",\n      \"rx\": \"46\",\n      \"ry\": \"8\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, null)];\n    const renderBuilding = () => [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n      \"id\": getId(\"a\"),\n      \"x1\": \"64%\",\n      \"y1\": \"100%\",\n      \"x2\": \"64%\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderStop(\"#FFF\", 0, 0.5), renderStop(\"#F2F3F5\", 100)])]), _createVNode(\"g\", {\n      \"opacity\": \".8\",\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"path\", {\n      \"d\": \"M36 131V53H16v20H2v58h34z\",\n      \"fill\": getUrlById(\"a\")\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M123 15h22v14h9v77h-31V15z\",\n      \"fill\": getUrlById(\"a\")\n    }, null)])];\n    const renderCloud = () => [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n      \"id\": getId(\"b\"),\n      \"x1\": \"64%\",\n      \"y1\": \"97%\",\n      \"x2\": \"64%\",\n      \"y2\": \"0%\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderStop(\"#F2F3F5\", 0, 0.3), renderStop(\"#F2F3F5\", 100)])]), _createVNode(\"g\", {\n      \"opacity\": \".8\",\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"path\", {\n      \"d\": \"M87 6c3 0 7 3 8 6a8 8 0 1 1-1 16H80a7 7 0 0 1-8-6c0-4 3-7 6-7 0-5 4-9 9-9Z\",\n      \"fill\": getUrlById(\"b\")\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M19 23c2 0 3 1 4 3 2 0 4 2 4 4a4 4 0 0 1-4 3v1h-7v-1l-1 1c-2 0-3-2-3-4 0-1 1-3 3-3 0-2 2-4 4-4Z\",\n      \"fill\": getUrlById(\"b\")\n    }, null)])];\n    const renderNetwork = () => _createVNode(\"svg\", {\n      \"viewBox\": \"0 0 160 160\"\n    }, [_createVNode(\"defs\", {\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"linearGradient\", {\n      \"id\": getId(1),\n      \"x1\": \"64%\",\n      \"y1\": \"100%\",\n      \"x2\": \"64%\"\n    }, [renderStop(\"#FFF\", 0, 0.5), renderStop(\"#F2F3F5\", 100)]), _createVNode(\"linearGradient\", {\n      \"id\": getId(2),\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"84%\"\n    }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#DCDEE0\", 100, 0)]), _createVNode(\"linearGradient\", {\n      \"id\": getId(3),\n      \"x1\": \"100%\",\n      \"x2\": \"100%\",\n      \"y2\": \"100%\"\n    }, [renderStops(\"#EAEDF0\", \"#DCDEE0\")]), _createVNode(\"radialGradient\", {\n      \"id\": getId(4),\n      \"cx\": \"50%\",\n      \"cy\": \"0%\",\n      \"fx\": \"50%\",\n      \"fy\": \"0%\",\n      \"r\": \"100%\",\n      \"gradientTransform\": \"matrix(0 1 -.54 0 .5 -.5)\"\n    }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#FFF\", 100, 0)])]), _createVNode(\"g\", {\n      \"fill\": \"none\"\n    }, [renderBuilding(), _createVNode(\"path\", {\n      \"fill\": getUrlById(4),\n      \"d\": \"M0 139h160v21H0z\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M80 54a7 7 0 0 1 3 13v27l-2 2h-2a2 2 0 0 1-2-2V67a7 7 0 0 1 3-13z\",\n      \"fill\": getUrlById(2),\n      \"data-allow-mismatch\": \"attribute\"\n    }, null), _createVNode(\"g\", {\n      \"opacity\": \".6\",\n      \"stroke-linecap\": \"round\",\n      \"stroke-width\": \"7\",\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"path\", {\n      \"d\": \"M64 47a19 19 0 0 0-5 13c0 5 2 10 5 13\",\n      \"stroke\": getUrlById(3)\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M53 36a34 34 0 0 0 0 48\",\n      \"stroke\": getUrlById(3)\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M95 73a19 19 0 0 0 6-13c0-5-2-9-6-13\",\n      \"stroke\": getUrlById(3)\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M106 84a34 34 0 0 0 0-48\",\n      \"stroke\": getUrlById(3)\n    }, null)]), _createVNode(\"g\", {\n      \"transform\": \"translate(31 105)\"\n    }, [_createVNode(\"rect\", {\n      \"fill\": \"#EBEDF0\",\n      \"width\": \"98\",\n      \"height\": \"34\",\n      \"rx\": \"2\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": \"#FFF\",\n      \"x\": \"9\",\n      \"y\": \"8\",\n      \"width\": \"80\",\n      \"height\": \"18\",\n      \"rx\": \"1.1\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": \"#EBEDF0\",\n      \"x\": \"15\",\n      \"y\": \"12\",\n      \"width\": \"18\",\n      \"height\": \"6\",\n      \"rx\": \"1.1\"\n    }, null)])])]);\n    const renderMaterial = () => _createVNode(\"svg\", {\n      \"viewBox\": \"0 0 160 160\"\n    }, [_createVNode(\"defs\", {\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"100%\",\n      \"id\": getId(5)\n    }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n      \"x1\": \"95%\",\n      \"y1\": \"48%\",\n      \"x2\": \"5.5%\",\n      \"y2\": \"51%\",\n      \"id\": getId(6)\n    }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n      \"y1\": \"45%\",\n      \"x2\": \"100%\",\n      \"y2\": \"54%\",\n      \"id\": getId(7)\n    }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")])]), renderBuilding(), renderCloud(), _createVNode(\"g\", {\n      \"transform\": \"translate(36 50)\",\n      \"fill\": \"none\"\n    }, [_createVNode(\"g\", {\n      \"transform\": \"translate(8)\"\n    }, [_createVNode(\"rect\", {\n      \"fill\": \"#EBEDF0\",\n      \"opacity\": \".6\",\n      \"x\": \"38\",\n      \"y\": \"13\",\n      \"width\": \"36\",\n      \"height\": \"53\",\n      \"rx\": \"2\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": getUrlById(5),\n      \"width\": \"64\",\n      \"height\": \"66\",\n      \"rx\": \"2\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": \"#FFF\",\n      \"x\": \"6\",\n      \"y\": \"6\",\n      \"width\": \"52\",\n      \"height\": \"55\",\n      \"rx\": \"1\"\n    }, null), _createVNode(\"g\", {\n      \"transform\": \"translate(15 17)\",\n      \"fill\": getUrlById(6),\n      \"data-allow-mismatch\": \"attribute\"\n    }, [_createVNode(\"rect\", {\n      \"width\": \"34\",\n      \"height\": \"6\",\n      \"rx\": \"1\"\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M0 14h34v6H0z\"\n    }, null), _createVNode(\"rect\", {\n      \"y\": \"28\",\n      \"width\": \"34\",\n      \"height\": \"6\",\n      \"rx\": \"1\"\n    }, null)])]), _createVNode(\"rect\", {\n      \"fill\": getUrlById(7),\n      \"y\": \"61\",\n      \"width\": \"88\",\n      \"height\": \"28\",\n      \"rx\": \"1\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": \"#F7F8FA\",\n      \"x\": \"29\",\n      \"y\": \"72\",\n      \"width\": \"30\",\n      \"height\": \"6\",\n      \"rx\": \"1\"\n    }, null)])]);\n    const renderError = () => _createVNode(\"svg\", {\n      \"viewBox\": \"0 0 160 160\"\n    }, [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"100%\",\n      \"id\": getId(8),\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")])]), renderBuilding(), renderCloud(), renderShadow(\"c\"), _createVNode(\"path\", {\n      \"d\": \"m59 60 21 21 21-21h3l9 9v3L92 93l21 21v3l-9 9h-3l-21-21-21 21h-3l-9-9v-3l21-21-21-21v-3l9-9h3Z\",\n      \"fill\": getUrlById(8),\n      \"data-allow-mismatch\": \"attribute\"\n    }, null)]);\n    const renderSearch = () => _createVNode(\"svg\", {\n      \"viewBox\": \"0 0 160 160\"\n    }, [_createVNode(\"defs\", {\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"y1\": \"100%\",\n      \"x2\": \"50%\",\n      \"id\": getId(9)\n    }, [renderStops(\"#EEE\", \"#D8D8D8\")]), _createVNode(\"linearGradient\", {\n      \"x1\": \"100%\",\n      \"y1\": \"50%\",\n      \"y2\": \"50%\",\n      \"id\": getId(10)\n    }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"100%\",\n      \"id\": getId(11)\n    }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"100%\",\n      \"id\": getId(12)\n    }, [renderStops(\"#FFF\", \"#F7F8FA\")])]), renderBuilding(), renderCloud(), renderShadow(\"d\"), _createVNode(\"g\", {\n      \"transform\": \"rotate(-45 113 -4)\",\n      \"fill\": \"none\",\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"rect\", {\n      \"fill\": getUrlById(9),\n      \"x\": \"24\",\n      \"y\": \"52.8\",\n      \"width\": \"5.8\",\n      \"height\": \"19\",\n      \"rx\": \"1\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": getUrlById(10),\n      \"x\": \"22.1\",\n      \"y\": \"67.3\",\n      \"width\": \"9.9\",\n      \"height\": \"28\",\n      \"rx\": \"1\"\n    }, null), _createVNode(\"circle\", {\n      \"stroke\": getUrlById(11),\n      \"stroke-width\": \"8\",\n      \"cx\": \"27\",\n      \"cy\": \"27\",\n      \"r\": \"27\"\n    }, null), _createVNode(\"circle\", {\n      \"fill\": getUrlById(12),\n      \"cx\": \"27\",\n      \"cy\": \"27\",\n      \"r\": \"16\"\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M37 7c-8 0-15 5-16 12\",\n      \"stroke\": getUrlById(11),\n      \"stroke-width\": \"3\",\n      \"opacity\": \".5\",\n      \"stroke-linecap\": \"round\",\n      \"transform\": \"rotate(45 29 13)\"\n    }, null)])]);\n    const renderImage = () => {\n      var _a;\n      if (slots.image) {\n        return slots.image();\n      }\n      const PRESET_IMAGES = {\n        error: renderError,\n        search: renderSearch,\n        network: renderNetwork,\n        default: renderMaterial\n      };\n      return ((_a = PRESET_IMAGES[props.image]) == null ? void 0 : _a.call(PRESET_IMAGES)) || _createVNode(\"img\", {\n        \"src\": props.image\n      }, null);\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [_createVNode(\"div\", {\n      \"class\": bem(\"image\"),\n      \"style\": getSizeStyle(props.imageSize)\n    }, [renderImage()]), renderDescription(), renderBottom()]);\n  }\n});\nexport { stdin_default as default, emptyProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "useId", "getSizeStyle", "makeStringProp", "createNamespace", "name", "bem", "emptyProps", "image", "imageSize", "Number", "String", "Array", "description", "stdin_default", "props", "setup", "slots", "renderDescription", "renderBottom", "default", "baseId", "getId", "num", "getUrlById", "renderStop", "color", "offset", "opacity", "renderStops", "fromColor", "toColor", "renderShadow", "id", "renderBuilding", "renderCloud", "renderNetwork", "renderMaterial", "renderError", "renderSearch", "renderImage", "_a", "PRESET_IMAGES", "error", "search", "network", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/empty/Empty.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"empty\");\nconst emptyProps = {\n  image: makeStringProp(\"default\"),\n  imageSize: [Number, String, Array],\n  description: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: emptyProps,\n  setup(props, {\n    slots\n  }) {\n    const renderDescription = () => {\n      const description = slots.description ? slots.description() : props.description;\n      if (description) {\n        return _createVNode(\"p\", {\n          \"class\": bem(\"description\")\n        }, [description]);\n      }\n    };\n    const renderBottom = () => {\n      if (slots.default) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"bottom\")\n        }, [slots.default()]);\n      }\n    };\n    const baseId = useId();\n    const getId = (num) => `${baseId}-${num}`;\n    const getUrlById = (num) => `url(#${getId(num)})`;\n    const renderStop = (color, offset, opacity) => _createVNode(\"stop\", {\n      \"stop-color\": color,\n      \"offset\": `${offset}%`,\n      \"stop-opacity\": opacity\n    }, null);\n    const renderStops = (fromColor, toColor) => [renderStop(fromColor, 0), renderStop(toColor, 100)];\n    const renderShadow = (id) => [_createVNode(\"defs\", null, [_createVNode(\"radialGradient\", {\n      \"id\": getId(id),\n      \"cx\": \"50%\",\n      \"cy\": \"54%\",\n      \"fx\": \"50%\",\n      \"fy\": \"54%\",\n      \"r\": \"297%\",\n      \"gradientTransform\": \"matrix(-.16 0 0 -.33 .58 .72)\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#F2F3F5\", 100, 0.3)])]), _createVNode(\"ellipse\", {\n      \"fill\": getUrlById(id),\n      \"opacity\": \".8\",\n      \"cx\": \"80\",\n      \"cy\": \"140\",\n      \"rx\": \"46\",\n      \"ry\": \"8\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, null)];\n    const renderBuilding = () => [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n      \"id\": getId(\"a\"),\n      \"x1\": \"64%\",\n      \"y1\": \"100%\",\n      \"x2\": \"64%\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderStop(\"#FFF\", 0, 0.5), renderStop(\"#F2F3F5\", 100)])]), _createVNode(\"g\", {\n      \"opacity\": \".8\",\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"path\", {\n      \"d\": \"M36 131V53H16v20H2v58h34z\",\n      \"fill\": getUrlById(\"a\")\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M123 15h22v14h9v77h-31V15z\",\n      \"fill\": getUrlById(\"a\")\n    }, null)])];\n    const renderCloud = () => [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n      \"id\": getId(\"b\"),\n      \"x1\": \"64%\",\n      \"y1\": \"97%\",\n      \"x2\": \"64%\",\n      \"y2\": \"0%\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderStop(\"#F2F3F5\", 0, 0.3), renderStop(\"#F2F3F5\", 100)])]), _createVNode(\"g\", {\n      \"opacity\": \".8\",\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"path\", {\n      \"d\": \"M87 6c3 0 7 3 8 6a8 8 0 1 1-1 16H80a7 7 0 0 1-8-6c0-4 3-7 6-7 0-5 4-9 9-9Z\",\n      \"fill\": getUrlById(\"b\")\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M19 23c2 0 3 1 4 3 2 0 4 2 4 4a4 4 0 0 1-4 3v1h-7v-1l-1 1c-2 0-3-2-3-4 0-1 1-3 3-3 0-2 2-4 4-4Z\",\n      \"fill\": getUrlById(\"b\")\n    }, null)])];\n    const renderNetwork = () => _createVNode(\"svg\", {\n      \"viewBox\": \"0 0 160 160\"\n    }, [_createVNode(\"defs\", {\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"linearGradient\", {\n      \"id\": getId(1),\n      \"x1\": \"64%\",\n      \"y1\": \"100%\",\n      \"x2\": \"64%\"\n    }, [renderStop(\"#FFF\", 0, 0.5), renderStop(\"#F2F3F5\", 100)]), _createVNode(\"linearGradient\", {\n      \"id\": getId(2),\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"84%\"\n    }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#DCDEE0\", 100, 0)]), _createVNode(\"linearGradient\", {\n      \"id\": getId(3),\n      \"x1\": \"100%\",\n      \"x2\": \"100%\",\n      \"y2\": \"100%\"\n    }, [renderStops(\"#EAEDF0\", \"#DCDEE0\")]), _createVNode(\"radialGradient\", {\n      \"id\": getId(4),\n      \"cx\": \"50%\",\n      \"cy\": \"0%\",\n      \"fx\": \"50%\",\n      \"fy\": \"0%\",\n      \"r\": \"100%\",\n      \"gradientTransform\": \"matrix(0 1 -.54 0 .5 -.5)\"\n    }, [renderStop(\"#EBEDF0\", 0), renderStop(\"#FFF\", 100, 0)])]), _createVNode(\"g\", {\n      \"fill\": \"none\"\n    }, [renderBuilding(), _createVNode(\"path\", {\n      \"fill\": getUrlById(4),\n      \"d\": \"M0 139h160v21H0z\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M80 54a7 7 0 0 1 3 13v27l-2 2h-2a2 2 0 0 1-2-2V67a7 7 0 0 1 3-13z\",\n      \"fill\": getUrlById(2),\n      \"data-allow-mismatch\": \"attribute\"\n    }, null), _createVNode(\"g\", {\n      \"opacity\": \".6\",\n      \"stroke-linecap\": \"round\",\n      \"stroke-width\": \"7\",\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"path\", {\n      \"d\": \"M64 47a19 19 0 0 0-5 13c0 5 2 10 5 13\",\n      \"stroke\": getUrlById(3)\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M53 36a34 34 0 0 0 0 48\",\n      \"stroke\": getUrlById(3)\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M95 73a19 19 0 0 0 6-13c0-5-2-9-6-13\",\n      \"stroke\": getUrlById(3)\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M106 84a34 34 0 0 0 0-48\",\n      \"stroke\": getUrlById(3)\n    }, null)]), _createVNode(\"g\", {\n      \"transform\": \"translate(31 105)\"\n    }, [_createVNode(\"rect\", {\n      \"fill\": \"#EBEDF0\",\n      \"width\": \"98\",\n      \"height\": \"34\",\n      \"rx\": \"2\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": \"#FFF\",\n      \"x\": \"9\",\n      \"y\": \"8\",\n      \"width\": \"80\",\n      \"height\": \"18\",\n      \"rx\": \"1.1\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": \"#EBEDF0\",\n      \"x\": \"15\",\n      \"y\": \"12\",\n      \"width\": \"18\",\n      \"height\": \"6\",\n      \"rx\": \"1.1\"\n    }, null)])])]);\n    const renderMaterial = () => _createVNode(\"svg\", {\n      \"viewBox\": \"0 0 160 160\"\n    }, [_createVNode(\"defs\", {\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"100%\",\n      \"id\": getId(5)\n    }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n      \"x1\": \"95%\",\n      \"y1\": \"48%\",\n      \"x2\": \"5.5%\",\n      \"y2\": \"51%\",\n      \"id\": getId(6)\n    }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n      \"y1\": \"45%\",\n      \"x2\": \"100%\",\n      \"y2\": \"54%\",\n      \"id\": getId(7)\n    }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")])]), renderBuilding(), renderCloud(), _createVNode(\"g\", {\n      \"transform\": \"translate(36 50)\",\n      \"fill\": \"none\"\n    }, [_createVNode(\"g\", {\n      \"transform\": \"translate(8)\"\n    }, [_createVNode(\"rect\", {\n      \"fill\": \"#EBEDF0\",\n      \"opacity\": \".6\",\n      \"x\": \"38\",\n      \"y\": \"13\",\n      \"width\": \"36\",\n      \"height\": \"53\",\n      \"rx\": \"2\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": getUrlById(5),\n      \"width\": \"64\",\n      \"height\": \"66\",\n      \"rx\": \"2\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": \"#FFF\",\n      \"x\": \"6\",\n      \"y\": \"6\",\n      \"width\": \"52\",\n      \"height\": \"55\",\n      \"rx\": \"1\"\n    }, null), _createVNode(\"g\", {\n      \"transform\": \"translate(15 17)\",\n      \"fill\": getUrlById(6),\n      \"data-allow-mismatch\": \"attribute\"\n    }, [_createVNode(\"rect\", {\n      \"width\": \"34\",\n      \"height\": \"6\",\n      \"rx\": \"1\"\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M0 14h34v6H0z\"\n    }, null), _createVNode(\"rect\", {\n      \"y\": \"28\",\n      \"width\": \"34\",\n      \"height\": \"6\",\n      \"rx\": \"1\"\n    }, null)])]), _createVNode(\"rect\", {\n      \"fill\": getUrlById(7),\n      \"y\": \"61\",\n      \"width\": \"88\",\n      \"height\": \"28\",\n      \"rx\": \"1\",\n      \"data-allow-mismatch\": \"attribute\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": \"#F7F8FA\",\n      \"x\": \"29\",\n      \"y\": \"72\",\n      \"width\": \"30\",\n      \"height\": \"6\",\n      \"rx\": \"1\"\n    }, null)])]);\n    const renderError = () => _createVNode(\"svg\", {\n      \"viewBox\": \"0 0 160 160\"\n    }, [_createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"100%\",\n      \"id\": getId(8),\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderStops(\"#EAEDF1\", \"#DCDEE0\")])]), renderBuilding(), renderCloud(), renderShadow(\"c\"), _createVNode(\"path\", {\n      \"d\": \"m59 60 21 21 21-21h3l9 9v3L92 93l21 21v3l-9 9h-3l-21-21-21 21h-3l-9-9v-3l21-21-21-21v-3l9-9h3Z\",\n      \"fill\": getUrlById(8),\n      \"data-allow-mismatch\": \"attribute\"\n    }, null)]);\n    const renderSearch = () => _createVNode(\"svg\", {\n      \"viewBox\": \"0 0 160 160\"\n    }, [_createVNode(\"defs\", {\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"y1\": \"100%\",\n      \"x2\": \"50%\",\n      \"id\": getId(9)\n    }, [renderStops(\"#EEE\", \"#D8D8D8\")]), _createVNode(\"linearGradient\", {\n      \"x1\": \"100%\",\n      \"y1\": \"50%\",\n      \"y2\": \"50%\",\n      \"id\": getId(10)\n    }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"100%\",\n      \"id\": getId(11)\n    }, [renderStops(\"#F2F3F5\", \"#DCDEE0\")]), _createVNode(\"linearGradient\", {\n      \"x1\": \"50%\",\n      \"x2\": \"50%\",\n      \"y2\": \"100%\",\n      \"id\": getId(12)\n    }, [renderStops(\"#FFF\", \"#F7F8FA\")])]), renderBuilding(), renderCloud(), renderShadow(\"d\"), _createVNode(\"g\", {\n      \"transform\": \"rotate(-45 113 -4)\",\n      \"fill\": \"none\",\n      \"data-allow-mismatch\": \"children\"\n    }, [_createVNode(\"rect\", {\n      \"fill\": getUrlById(9),\n      \"x\": \"24\",\n      \"y\": \"52.8\",\n      \"width\": \"5.8\",\n      \"height\": \"19\",\n      \"rx\": \"1\"\n    }, null), _createVNode(\"rect\", {\n      \"fill\": getUrlById(10),\n      \"x\": \"22.1\",\n      \"y\": \"67.3\",\n      \"width\": \"9.9\",\n      \"height\": \"28\",\n      \"rx\": \"1\"\n    }, null), _createVNode(\"circle\", {\n      \"stroke\": getUrlById(11),\n      \"stroke-width\": \"8\",\n      \"cx\": \"27\",\n      \"cy\": \"27\",\n      \"r\": \"27\"\n    }, null), _createVNode(\"circle\", {\n      \"fill\": getUrlById(12),\n      \"cx\": \"27\",\n      \"cy\": \"27\",\n      \"r\": \"16\"\n    }, null), _createVNode(\"path\", {\n      \"d\": \"M37 7c-8 0-15 5-16 12\",\n      \"stroke\": getUrlById(11),\n      \"stroke-width\": \"3\",\n      \"opacity\": \".5\",\n      \"stroke-linecap\": \"round\",\n      \"transform\": \"rotate(45 29 13)\"\n    }, null)])]);\n    const renderImage = () => {\n      var _a;\n      if (slots.image) {\n        return slots.image();\n      }\n      const PRESET_IMAGES = {\n        error: renderError,\n        search: renderSearch,\n        network: renderNetwork,\n        default: renderMaterial\n      };\n      return ((_a = PRESET_IMAGES[props.image]) == null ? void 0 : _a.call(PRESET_IMAGES)) || _createVNode(\"img\", {\n        \"src\": props.image\n      }, null);\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [_createVNode(\"div\", {\n      \"class\": bem(\"image\"),\n      \"style\": getSizeStyle(props.imageSize)\n    }, [renderImage()]), renderDescription(), renderBottom()]);\n  }\n});\nexport {\n  stdin_default as default,\n  emptyProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,YAAY,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAClF,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGF,eAAe,CAAC,OAAO,CAAC;AAC5C,MAAMG,UAAU,GAAG;EACjBC,KAAK,EAAEL,cAAc,CAAC,SAAS,CAAC;EAChCM,SAAS,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC;EAClCC,WAAW,EAAEF;AACf,CAAC;AACD,IAAIG,aAAa,GAAGhB,eAAe,CAAC;EAClCO,IAAI;EACJU,KAAK,EAAER,UAAU;EACjBS,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,MAAML,WAAW,GAAGI,KAAK,CAACJ,WAAW,GAAGI,KAAK,CAACJ,WAAW,CAAC,CAAC,GAAGE,KAAK,CAACF,WAAW;MAC/E,IAAIA,WAAW,EAAE;QACf,OAAOb,YAAY,CAAC,GAAG,EAAE;UACvB,OAAO,EAAEM,GAAG,CAAC,aAAa;QAC5B,CAAC,EAAE,CAACO,WAAW,CAAC,CAAC;MACnB;IACF,CAAC;IACD,MAAMM,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIF,KAAK,CAACG,OAAO,EAAE;QACjB,OAAOpB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEM,GAAG,CAAC,QAAQ;QACvB,CAAC,EAAE,CAACW,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IACD,MAAMC,MAAM,GAAGpB,KAAK,CAAC,CAAC;IACtB,MAAMqB,KAAK,GAAIC,GAAG,IAAK,GAAGF,MAAM,IAAIE,GAAG,EAAE;IACzC,MAAMC,UAAU,GAAID,GAAG,IAAK,QAAQD,KAAK,CAACC,GAAG,CAAC,GAAG;IACjD,MAAME,UAAU,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAEC,OAAO,KAAK5B,YAAY,CAAC,MAAM,EAAE;MAClE,YAAY,EAAE0B,KAAK;MACnB,QAAQ,EAAE,GAAGC,MAAM,GAAG;MACtB,cAAc,EAAEC;IAClB,CAAC,EAAE,IAAI,CAAC;IACR,MAAMC,WAAW,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK,CAACN,UAAU,CAACK,SAAS,EAAE,CAAC,CAAC,EAAEL,UAAU,CAACM,OAAO,EAAE,GAAG,CAAC,CAAC;IAChG,MAAMC,YAAY,GAAIC,EAAE,IAAK,CAACjC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACA,YAAY,CAAC,gBAAgB,EAAE;MACvF,IAAI,EAAEsB,KAAK,CAACW,EAAE,CAAC;MACf,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,GAAG,EAAE,MAAM;MACX,mBAAmB,EAAE,+BAA+B;MACpD,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACR,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,EAAEA,UAAU,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzB,YAAY,CAAC,SAAS,EAAE;MACzF,MAAM,EAAEwB,UAAU,CAACS,EAAE,CAAC;MACtB,SAAS,EAAE,IAAI;MACf,IAAI,EAAE,IAAI;MACV,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,IAAI;MACV,IAAI,EAAE,GAAG;MACT,qBAAqB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC,CAAC;IACT,MAAMC,cAAc,GAAGA,CAAA,KAAM,CAAClC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACA,YAAY,CAAC,gBAAgB,EAAE;MACvF,IAAI,EAAEsB,KAAK,CAAC,GAAG,CAAC;MAChB,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,KAAK;MACX,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,EAAEA,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzB,YAAY,CAAC,GAAG,EAAE;MAChF,SAAS,EAAE,IAAI;MACf,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,GAAG,EAAE,2BAA2B;MAChC,MAAM,EAAEwB,UAAU,CAAC,GAAG;IACxB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE,4BAA4B;MACjC,MAAM,EAAEwB,UAAU,CAAC,GAAG;IACxB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACX,MAAMW,WAAW,GAAGA,CAAA,KAAM,CAACnC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACA,YAAY,CAAC,gBAAgB,EAAE;MACpF,IAAI,EAAEsB,KAAK,CAAC,GAAG,CAAC;MAChB,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,IAAI;MACV,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACG,UAAU,CAAC,SAAS,EAAE,CAAC,EAAE,GAAG,CAAC,EAAEA,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzB,YAAY,CAAC,GAAG,EAAE;MACnF,SAAS,EAAE,IAAI;MACf,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,GAAG,EAAE,4EAA4E;MACjF,MAAM,EAAEwB,UAAU,CAAC,GAAG;IACxB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE,iGAAiG;MACtG,MAAM,EAAEwB,UAAU,CAAC,GAAG;IACxB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACX,MAAMY,aAAa,GAAGA,CAAA,KAAMpC,YAAY,CAAC,KAAK,EAAE;MAC9C,SAAS,EAAE;IACb,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACA,YAAY,CAAC,gBAAgB,EAAE;MACjC,IAAI,EAAEsB,KAAK,CAAC,CAAC,CAAC;MACd,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE;IACR,CAAC,EAAE,CAACG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,EAAEA,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,EAAEzB,YAAY,CAAC,gBAAgB,EAAE;MAC3F,IAAI,EAAEsB,KAAK,CAAC,CAAC,CAAC;MACd,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE;IACR,CAAC,EAAE,CAACG,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,EAAEA,UAAU,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEzB,YAAY,CAAC,gBAAgB,EAAE;MAC5F,IAAI,EAAEsB,KAAK,CAAC,CAAC,CAAC;MACd,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE;IACR,CAAC,EAAE,CAACO,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE7B,YAAY,CAAC,gBAAgB,EAAE;MACtE,IAAI,EAAEsB,KAAK,CAAC,CAAC,CAAC;MACd,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,IAAI;MACV,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,IAAI;MACV,GAAG,EAAE,MAAM;MACX,mBAAmB,EAAE;IACvB,CAAC,EAAE,CAACG,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,EAAEA,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzB,YAAY,CAAC,GAAG,EAAE;MAC9E,MAAM,EAAE;IACV,CAAC,EAAE,CAACkC,cAAc,CAAC,CAAC,EAAElC,YAAY,CAAC,MAAM,EAAE;MACzC,MAAM,EAAEwB,UAAU,CAAC,CAAC,CAAC;MACrB,GAAG,EAAE,kBAAkB;MACvB,qBAAqB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE,mEAAmE;MACxE,MAAM,EAAEwB,UAAU,CAAC,CAAC,CAAC;MACrB,qBAAqB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,GAAG,EAAE;MAC1B,SAAS,EAAE,IAAI;MACf,gBAAgB,EAAE,OAAO;MACzB,cAAc,EAAE,GAAG;MACnB,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,GAAG,EAAE,uCAAuC;MAC5C,QAAQ,EAAEwB,UAAU,CAAC,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE,yBAAyB;MAC9B,QAAQ,EAAEwB,UAAU,CAAC,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE,sCAAsC;MAC3C,QAAQ,EAAEwB,UAAU,CAAC,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE,0BAA0B;MAC/B,QAAQ,EAAEwB,UAAU,CAAC,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAExB,YAAY,CAAC,GAAG,EAAE;MAC5B,WAAW,EAAE;IACf,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,MAAM,EAAE,SAAS;MACjB,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,IAAI;MACd,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,EAAEA,YAAY,CAAC,MAAM,EAAE;MAC7B,MAAM,EAAE,MAAM;MACd,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,IAAI;MACd,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,EAAEA,YAAY,CAAC,MAAM,EAAE;MAC7B,MAAM,EAAE,SAAS;MACjB,GAAG,EAAE,IAAI;MACT,GAAG,EAAE,IAAI;MACT,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,GAAG;MACb,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,MAAMqC,cAAc,GAAGA,CAAA,KAAMrC,YAAY,CAAC,KAAK,EAAE;MAC/C,SAAS,EAAE;IACb,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACA,YAAY,CAAC,gBAAgB,EAAE;MACjC,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAEsB,KAAK,CAAC,CAAC;IACf,CAAC,EAAE,CAACO,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE7B,YAAY,CAAC,gBAAgB,EAAE;MACtE,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,KAAK;MACX,IAAI,EAAEsB,KAAK,CAAC,CAAC;IACf,CAAC,EAAE,CAACO,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE7B,YAAY,CAAC,gBAAgB,EAAE;MACtE,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,KAAK;MACX,IAAI,EAAEsB,KAAK,CAAC,CAAC;IACf,CAAC,EAAE,CAACO,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEK,cAAc,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC,EAAEnC,YAAY,CAAC,GAAG,EAAE;MAC5F,WAAW,EAAE,kBAAkB;MAC/B,MAAM,EAAE;IACV,CAAC,EAAE,CAACA,YAAY,CAAC,GAAG,EAAE;MACpB,WAAW,EAAE;IACf,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE,IAAI;MACf,GAAG,EAAE,IAAI;MACT,GAAG,EAAE,IAAI;MACT,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,IAAI;MACd,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,EAAEA,YAAY,CAAC,MAAM,EAAE;MAC7B,MAAM,EAAEwB,UAAU,CAAC,CAAC,CAAC;MACrB,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,IAAI;MACd,IAAI,EAAE,GAAG;MACT,qBAAqB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,MAAM,EAAE,MAAM;MACd,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,IAAI;MACd,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,EAAEA,YAAY,CAAC,GAAG,EAAE;MAC1B,WAAW,EAAE,kBAAkB;MAC/B,MAAM,EAAEwB,UAAU,CAAC,CAAC,CAAC;MACrB,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACxB,YAAY,CAAC,MAAM,EAAE;MACvB,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,GAAG;MACb,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,EAAEA,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE;IACP,CAAC,EAAE,IAAI,CAAC,EAAEA,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE,IAAI;MACT,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,GAAG;MACb,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,MAAM,EAAE;MACjC,MAAM,EAAEwB,UAAU,CAAC,CAAC,CAAC;MACrB,GAAG,EAAE,IAAI;MACT,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,IAAI;MACd,IAAI,EAAE,GAAG;MACT,qBAAqB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,MAAM,EAAE,SAAS;MACjB,GAAG,EAAE,IAAI;MACT,GAAG,EAAE,IAAI;MACT,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,GAAG;MACb,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,MAAMsC,WAAW,GAAGA,CAAA,KAAMtC,YAAY,CAAC,KAAK,EAAE;MAC5C,SAAS,EAAE;IACb,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACA,YAAY,CAAC,gBAAgB,EAAE;MAC7D,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAEsB,KAAK,CAAC,CAAC,CAAC;MACd,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACO,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEK,cAAc,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC,EAAEH,YAAY,CAAC,GAAG,CAAC,EAAEhC,YAAY,CAAC,MAAM,EAAE;MAClH,GAAG,EAAE,gGAAgG;MACrG,MAAM,EAAEwB,UAAU,CAAC,CAAC,CAAC;MACrB,qBAAqB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACV,MAAMe,YAAY,GAAGA,CAAA,KAAMvC,YAAY,CAAC,KAAK,EAAE;MAC7C,SAAS,EAAE;IACb,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACA,YAAY,CAAC,gBAAgB,EAAE;MACjC,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,KAAK;MACX,IAAI,EAAEsB,KAAK,CAAC,CAAC;IACf,CAAC,EAAE,CAACO,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE7B,YAAY,CAAC,gBAAgB,EAAE;MACnE,IAAI,EAAE,MAAM;MACZ,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAEsB,KAAK,CAAC,EAAE;IAChB,CAAC,EAAE,CAACO,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE7B,YAAY,CAAC,gBAAgB,EAAE;MACtE,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAEsB,KAAK,CAAC,EAAE;IAChB,CAAC,EAAE,CAACO,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE7B,YAAY,CAAC,gBAAgB,EAAE;MACtE,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,KAAK;MACX,IAAI,EAAE,MAAM;MACZ,IAAI,EAAEsB,KAAK,CAAC,EAAE;IAChB,CAAC,EAAE,CAACO,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEK,cAAc,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC,EAAEH,YAAY,CAAC,GAAG,CAAC,EAAEhC,YAAY,CAAC,GAAG,EAAE;MAC5G,WAAW,EAAE,oBAAoB;MACjC,MAAM,EAAE,MAAM;MACd,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACA,YAAY,CAAC,MAAM,EAAE;MACvB,MAAM,EAAEwB,UAAU,CAAC,CAAC,CAAC;MACrB,GAAG,EAAE,IAAI;MACT,GAAG,EAAE,MAAM;MACX,OAAO,EAAE,KAAK;MACd,QAAQ,EAAE,IAAI;MACd,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,MAAM,EAAEwB,UAAU,CAAC,EAAE,CAAC;MACtB,GAAG,EAAE,MAAM;MACX,GAAG,EAAE,MAAM;MACX,OAAO,EAAE,KAAK;MACd,QAAQ,EAAE,IAAI;MACd,IAAI,EAAE;IACR,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,QAAQ,EAAE;MAC/B,QAAQ,EAAEwB,UAAU,CAAC,EAAE,CAAC;MACxB,cAAc,EAAE,GAAG;MACnB,IAAI,EAAE,IAAI;MACV,IAAI,EAAE,IAAI;MACV,GAAG,EAAE;IACP,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,QAAQ,EAAE;MAC/B,MAAM,EAAEwB,UAAU,CAAC,EAAE,CAAC;MACtB,IAAI,EAAE,IAAI;MACV,IAAI,EAAE,IAAI;MACV,GAAG,EAAE;IACP,CAAC,EAAE,IAAI,CAAC,EAAExB,YAAY,CAAC,MAAM,EAAE;MAC7B,GAAG,EAAE,uBAAuB;MAC5B,QAAQ,EAAEwB,UAAU,CAAC,EAAE,CAAC;MACxB,cAAc,EAAE,GAAG;MACnB,SAAS,EAAE,IAAI;MACf,gBAAgB,EAAE,OAAO;MACzB,WAAW,EAAE;IACf,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,MAAMgB,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIC,EAAE;MACN,IAAIxB,KAAK,CAACT,KAAK,EAAE;QACf,OAAOS,KAAK,CAACT,KAAK,CAAC,CAAC;MACtB;MACA,MAAMkC,aAAa,GAAG;QACpBC,KAAK,EAAEL,WAAW;QAClBM,MAAM,EAAEL,YAAY;QACpBM,OAAO,EAAET,aAAa;QACtBhB,OAAO,EAAEiB;MACX,CAAC;MACD,OAAO,CAAC,CAACI,EAAE,GAAGC,aAAa,CAAC3B,KAAK,CAACP,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiC,EAAE,CAACK,IAAI,CAACJ,aAAa,CAAC,KAAK1C,YAAY,CAAC,KAAK,EAAE;QAC1G,KAAK,EAAEe,KAAK,CAACP;MACf,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,OAAO,MAAMR,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEM,GAAG,CAAC;IACf,CAAC,EAAE,CAACN,YAAY,CAAC,KAAK,EAAE;MACtB,OAAO,EAAEM,GAAG,CAAC,OAAO,CAAC;MACrB,OAAO,EAAEJ,YAAY,CAACa,KAAK,CAACN,SAAS;IACvC,CAAC,EAAE,CAAC+B,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEtB,iBAAiB,CAAC,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC,CAAC;EAC5D;AACF,CAAC,CAAC;AACF,SACEL,aAAa,IAAIM,OAAO,EACxBb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}