{"ast": null, "code": "import \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { defineComponent, nextTick, onMounted, onUnmounted, ref, watch, watchEffect, createVNode as _createVNode } from \"vue\";\nimport { extend, truthProp, numericProp, createNamespace, getZIndexStyle, makeNumberProp, makeNumericProp, makeStringProp } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"watermark\");\nconst watermarkProps = {\n  gapX: makeNumberProp(0),\n  gapY: makeNumberProp(0),\n  image: String,\n  width: makeNumberProp(100),\n  height: makeNumberProp(100),\n  rotate: makeNumericProp(-22),\n  zIndex: numericProp,\n  content: String,\n  opacity: numericProp,\n  fullPage: truthProp,\n  textColor: makeStringProp(\"#dcdee0\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: watermarkProps,\n  setup(props, {\n    slots\n  }) {\n    const svgElRef = ref();\n    const watermarkUrl = ref(\"\");\n    const imageBase64 = ref(\"\");\n    const renderWatermark = () => {\n      const rotateStyle = {\n        transformOrigin: \"center\",\n        transform: `rotate(${props.rotate}deg)`\n      };\n      const svgInner = () => {\n        if (props.image && !slots.content) {\n          return _createVNode(\"image\", {\n            \"href\": imageBase64.value,\n            \"xlink:href\": imageBase64.value,\n            \"x\": \"0\",\n            \"y\": \"0\",\n            \"width\": props.width,\n            \"height\": props.height,\n            \"style\": rotateStyle\n          }, null);\n        }\n        return _createVNode(\"foreignObject\", {\n          \"x\": \"0\",\n          \"y\": \"0\",\n          \"width\": props.width,\n          \"height\": props.height\n        }, [_createVNode(\"div\", {\n          \"xmlns\": \"http://www.w3.org/1999/xhtml\",\n          \"style\": rotateStyle\n        }, [slots.content ? slots.content() : _createVNode(\"span\", {\n          \"style\": {\n            color: props.textColor\n          }\n        }, [props.content])])]);\n      };\n      const svgWidth = props.width + props.gapX;\n      const svgHeight = props.height + props.gapY;\n      return _createVNode(\"svg\", {\n        \"viewBox\": `0 0 ${svgWidth} ${svgHeight}`,\n        \"width\": svgWidth,\n        \"height\": svgHeight,\n        \"xmlns\": \"http://www.w3.org/2000/svg\",\n        \"xmlns:xlink\": \"http://www.w3.org/1999/xlink\",\n        \"style\": {\n          padding: `0 ${props.gapX}px ${props.gapY}px 0`,\n          opacity: props.opacity\n        }\n      }, [svgInner()]);\n    };\n    const makeImageToBase64 = url => {\n      const canvas = document.createElement(\"canvas\");\n      const image = new Image();\n      image.crossOrigin = \"anonymous\";\n      image.referrerPolicy = \"no-referrer\";\n      image.onload = () => {\n        canvas.width = image.naturalWidth;\n        canvas.height = image.naturalHeight;\n        const ctx = canvas.getContext(\"2d\");\n        ctx == null ? void 0 : ctx.drawImage(image, 0, 0);\n        imageBase64.value = canvas.toDataURL();\n      };\n      image.src = url;\n    };\n    const makeSvgToBlobUrl = svgStr => {\n      const svgBlob = new Blob([svgStr], {\n        type: \"image/svg+xml\"\n      });\n      return URL.createObjectURL(svgBlob);\n    };\n    const revokeWatermarkUrl = () => {\n      if (watermarkUrl.value) {\n        URL.revokeObjectURL(watermarkUrl.value);\n      }\n    };\n    const generateWatermarkUrl = () => {\n      if (svgElRef.value) {\n        revokeWatermarkUrl();\n        watermarkUrl.value = makeSvgToBlobUrl(svgElRef.value.innerHTML);\n      }\n    };\n    watchEffect(() => {\n      if (props.image) {\n        makeImageToBase64(props.image);\n      }\n    });\n    watch(() => [props.content, props.textColor, props.height, props.width, props.rotate, props.gapX, props.gapY], generateWatermarkUrl);\n    watch(imageBase64, () => {\n      nextTick(generateWatermarkUrl);\n    });\n    onMounted(generateWatermarkUrl);\n    onUnmounted(revokeWatermarkUrl);\n    return () => {\n      const style = extend({\n        backgroundImage: `url(${watermarkUrl.value})`\n      }, getZIndexStyle(props.zIndex));\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          full: props.fullPage\n        }),\n        \"style\": style\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"wrapper\"),\n        \"ref\": svgElRef\n      }, [renderWatermark()])]);\n    };\n  }\n});\nexport { stdin_default as default, watermarkProps };", "map": {"version": 3, "names": ["defineComponent", "nextTick", "onMounted", "onUnmounted", "ref", "watch", "watchEffect", "createVNode", "_createVNode", "extend", "truthProp", "numericProp", "createNamespace", "getZIndexStyle", "makeNumberProp", "makeNumericProp", "makeStringProp", "name", "bem", "watermarkProps", "gapX", "gapY", "image", "String", "width", "height", "rotate", "zIndex", "content", "opacity", "fullPage", "textColor", "stdin_default", "props", "setup", "slots", "svgElRef", "watermarkUrl", "imageBase64", "renderWatermark", "rotateStyle", "transform<PERSON><PERSON>in", "transform", "svgInner", "value", "color", "svgWidth", "svgHeight", "padding", "makeImageToBase64", "url", "canvas", "document", "createElement", "Image", "crossOrigin", "referrerPolicy", "onload", "naturalWidth", "naturalHeight", "ctx", "getContext", "drawImage", "toDataURL", "src", "makeSvgToBlobUrl", "svgStr", "svgBlob", "Blob", "type", "URL", "createObjectURL", "revokeWatermarkUrl", "revokeObjectURL", "generateWatermarkUrl", "innerHTML", "style", "backgroundImage", "full", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/watermark/Watermark.mjs"], "sourcesContent": ["import { defineComponent, nextTick, onMounted, onUnmounted, ref, watch, watchEffect, createVNode as _createVNode } from \"vue\";\nimport { extend, truthProp, numericProp, createNamespace, getZIndexStyle, makeNumberProp, makeNumericProp, makeStringProp } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"watermark\");\nconst watermarkProps = {\n  gapX: makeNumberProp(0),\n  gapY: makeNumberProp(0),\n  image: String,\n  width: makeNumberProp(100),\n  height: makeNumberProp(100),\n  rotate: makeNumericProp(-22),\n  zIndex: numericProp,\n  content: String,\n  opacity: numericProp,\n  fullPage: truthProp,\n  textColor: makeStringProp(\"#dcdee0\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: watermarkProps,\n  setup(props, {\n    slots\n  }) {\n    const svgElRef = ref();\n    const watermarkUrl = ref(\"\");\n    const imageBase64 = ref(\"\");\n    const renderWatermark = () => {\n      const rotateStyle = {\n        transformOrigin: \"center\",\n        transform: `rotate(${props.rotate}deg)`\n      };\n      const svgInner = () => {\n        if (props.image && !slots.content) {\n          return _createVNode(\"image\", {\n            \"href\": imageBase64.value,\n            \"xlink:href\": imageBase64.value,\n            \"x\": \"0\",\n            \"y\": \"0\",\n            \"width\": props.width,\n            \"height\": props.height,\n            \"style\": rotateStyle\n          }, null);\n        }\n        return _createVNode(\"foreignObject\", {\n          \"x\": \"0\",\n          \"y\": \"0\",\n          \"width\": props.width,\n          \"height\": props.height\n        }, [_createVNode(\"div\", {\n          \"xmlns\": \"http://www.w3.org/1999/xhtml\",\n          \"style\": rotateStyle\n        }, [slots.content ? slots.content() : _createVNode(\"span\", {\n          \"style\": {\n            color: props.textColor\n          }\n        }, [props.content])])]);\n      };\n      const svgWidth = props.width + props.gapX;\n      const svgHeight = props.height + props.gapY;\n      return _createVNode(\"svg\", {\n        \"viewBox\": `0 0 ${svgWidth} ${svgHeight}`,\n        \"width\": svgWidth,\n        \"height\": svgHeight,\n        \"xmlns\": \"http://www.w3.org/2000/svg\",\n        \"xmlns:xlink\": \"http://www.w3.org/1999/xlink\",\n        \"style\": {\n          padding: `0 ${props.gapX}px ${props.gapY}px 0`,\n          opacity: props.opacity\n        }\n      }, [svgInner()]);\n    };\n    const makeImageToBase64 = (url) => {\n      const canvas = document.createElement(\"canvas\");\n      const image = new Image();\n      image.crossOrigin = \"anonymous\";\n      image.referrerPolicy = \"no-referrer\";\n      image.onload = () => {\n        canvas.width = image.naturalWidth;\n        canvas.height = image.naturalHeight;\n        const ctx = canvas.getContext(\"2d\");\n        ctx == null ? void 0 : ctx.drawImage(image, 0, 0);\n        imageBase64.value = canvas.toDataURL();\n      };\n      image.src = url;\n    };\n    const makeSvgToBlobUrl = (svgStr) => {\n      const svgBlob = new Blob([svgStr], {\n        type: \"image/svg+xml\"\n      });\n      return URL.createObjectURL(svgBlob);\n    };\n    const revokeWatermarkUrl = () => {\n      if (watermarkUrl.value) {\n        URL.revokeObjectURL(watermarkUrl.value);\n      }\n    };\n    const generateWatermarkUrl = () => {\n      if (svgElRef.value) {\n        revokeWatermarkUrl();\n        watermarkUrl.value = makeSvgToBlobUrl(svgElRef.value.innerHTML);\n      }\n    };\n    watchEffect(() => {\n      if (props.image) {\n        makeImageToBase64(props.image);\n      }\n    });\n    watch(() => [props.content, props.textColor, props.height, props.width, props.rotate, props.gapX, props.gapY], generateWatermarkUrl);\n    watch(imageBase64, () => {\n      nextTick(generateWatermarkUrl);\n    });\n    onMounted(generateWatermarkUrl);\n    onUnmounted(revokeWatermarkUrl);\n    return () => {\n      const style = extend({\n        backgroundImage: `url(${watermarkUrl.value})`\n      }, getZIndexStyle(props.zIndex));\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          full: props.fullPage\n        }),\n        \"style\": style\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"wrapper\"),\n        \"ref\": svgElRef\n      }, [renderWatermark()])]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  watermarkProps\n};\n"], "mappings": ";;;AAAA,SAASA,eAAe,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,GAAG,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC7H,SAASC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrJ,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGN,eAAe,CAAC,WAAW,CAAC;AAChD,MAAMO,cAAc,GAAG;EACrBC,IAAI,EAAEN,cAAc,CAAC,CAAC,CAAC;EACvBO,IAAI,EAAEP,cAAc,CAAC,CAAC,CAAC;EACvBQ,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAEV,cAAc,CAAC,GAAG,CAAC;EAC1BW,MAAM,EAAEX,cAAc,CAAC,GAAG,CAAC;EAC3BY,MAAM,EAAEX,eAAe,CAAC,CAAC,EAAE,CAAC;EAC5BY,MAAM,EAAEhB,WAAW;EACnBiB,OAAO,EAAEL,MAAM;EACfM,OAAO,EAAElB,WAAW;EACpBmB,QAAQ,EAAEpB,SAAS;EACnBqB,SAAS,EAAEf,cAAc,CAAC,SAAS;AACrC,CAAC;AACD,IAAIgB,aAAa,GAAGhC,eAAe,CAAC;EAClCiB,IAAI;EACJgB,KAAK,EAAEd,cAAc;EACrBe,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,QAAQ,GAAGhC,GAAG,CAAC,CAAC;IACtB,MAAMiC,YAAY,GAAGjC,GAAG,CAAC,EAAE,CAAC;IAC5B,MAAMkC,WAAW,GAAGlC,GAAG,CAAC,EAAE,CAAC;IAC3B,MAAMmC,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,WAAW,GAAG;QAClBC,eAAe,EAAE,QAAQ;QACzBC,SAAS,EAAE,UAAUT,KAAK,CAACP,MAAM;MACnC,CAAC;MACD,MAAMiB,QAAQ,GAAGA,CAAA,KAAM;QACrB,IAAIV,KAAK,CAACX,KAAK,IAAI,CAACa,KAAK,CAACP,OAAO,EAAE;UACjC,OAAOpB,YAAY,CAAC,OAAO,EAAE;YAC3B,MAAM,EAAE8B,WAAW,CAACM,KAAK;YACzB,YAAY,EAAEN,WAAW,CAACM,KAAK;YAC/B,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,GAAG;YACR,OAAO,EAAEX,KAAK,CAACT,KAAK;YACpB,QAAQ,EAAES,KAAK,CAACR,MAAM;YACtB,OAAO,EAAEe;UACX,CAAC,EAAE,IAAI,CAAC;QACV;QACA,OAAOhC,YAAY,CAAC,eAAe,EAAE;UACnC,GAAG,EAAE,GAAG;UACR,GAAG,EAAE,GAAG;UACR,OAAO,EAAEyB,KAAK,CAACT,KAAK;UACpB,QAAQ,EAAES,KAAK,CAACR;QAClB,CAAC,EAAE,CAACjB,YAAY,CAAC,KAAK,EAAE;UACtB,OAAO,EAAE,8BAA8B;UACvC,OAAO,EAAEgC;QACX,CAAC,EAAE,CAACL,KAAK,CAACP,OAAO,GAAGO,KAAK,CAACP,OAAO,CAAC,CAAC,GAAGpB,YAAY,CAAC,MAAM,EAAE;UACzD,OAAO,EAAE;YACPqC,KAAK,EAAEZ,KAAK,CAACF;UACf;QACF,CAAC,EAAE,CAACE,KAAK,CAACL,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC;MACD,MAAMkB,QAAQ,GAAGb,KAAK,CAACT,KAAK,GAAGS,KAAK,CAACb,IAAI;MACzC,MAAM2B,SAAS,GAAGd,KAAK,CAACR,MAAM,GAAGQ,KAAK,CAACZ,IAAI;MAC3C,OAAOb,YAAY,CAAC,KAAK,EAAE;QACzB,SAAS,EAAE,OAAOsC,QAAQ,IAAIC,SAAS,EAAE;QACzC,OAAO,EAAED,QAAQ;QACjB,QAAQ,EAAEC,SAAS;QACnB,OAAO,EAAE,4BAA4B;QACrC,aAAa,EAAE,8BAA8B;QAC7C,OAAO,EAAE;UACPC,OAAO,EAAE,KAAKf,KAAK,CAACb,IAAI,MAAMa,KAAK,CAACZ,IAAI,MAAM;UAC9CQ,OAAO,EAAEI,KAAK,CAACJ;QACjB;MACF,CAAC,EAAE,CAACc,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,MAAMM,iBAAiB,GAAIC,GAAG,IAAK;MACjC,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAM/B,KAAK,GAAG,IAAIgC,KAAK,CAAC,CAAC;MACzBhC,KAAK,CAACiC,WAAW,GAAG,WAAW;MAC/BjC,KAAK,CAACkC,cAAc,GAAG,aAAa;MACpClC,KAAK,CAACmC,MAAM,GAAG,MAAM;QACnBN,MAAM,CAAC3B,KAAK,GAAGF,KAAK,CAACoC,YAAY;QACjCP,MAAM,CAAC1B,MAAM,GAAGH,KAAK,CAACqC,aAAa;QACnC,MAAMC,GAAG,GAAGT,MAAM,CAACU,UAAU,CAAC,IAAI,CAAC;QACnCD,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACE,SAAS,CAACxC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QACjDgB,WAAW,CAACM,KAAK,GAAGO,MAAM,CAACY,SAAS,CAAC,CAAC;MACxC,CAAC;MACDzC,KAAK,CAAC0C,GAAG,GAAGd,GAAG;IACjB,CAAC;IACD,MAAMe,gBAAgB,GAAIC,MAAM,IAAK;MACnC,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACF,MAAM,CAAC,EAAE;QACjCG,IAAI,EAAE;MACR,CAAC,CAAC;MACF,OAAOC,GAAG,CAACC,eAAe,CAACJ,OAAO,CAAC;IACrC,CAAC;IACD,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAInC,YAAY,CAACO,KAAK,EAAE;QACtB0B,GAAG,CAACG,eAAe,CAACpC,YAAY,CAACO,KAAK,CAAC;MACzC;IACF,CAAC;IACD,MAAM8B,oBAAoB,GAAGA,CAAA,KAAM;MACjC,IAAItC,QAAQ,CAACQ,KAAK,EAAE;QAClB4B,kBAAkB,CAAC,CAAC;QACpBnC,YAAY,CAACO,KAAK,GAAGqB,gBAAgB,CAAC7B,QAAQ,CAACQ,KAAK,CAAC+B,SAAS,CAAC;MACjE;IACF,CAAC;IACDrE,WAAW,CAAC,MAAM;MAChB,IAAI2B,KAAK,CAACX,KAAK,EAAE;QACf2B,iBAAiB,CAAChB,KAAK,CAACX,KAAK,CAAC;MAChC;IACF,CAAC,CAAC;IACFjB,KAAK,CAAC,MAAM,CAAC4B,KAAK,CAACL,OAAO,EAAEK,KAAK,CAACF,SAAS,EAAEE,KAAK,CAACR,MAAM,EAAEQ,KAAK,CAACT,KAAK,EAAES,KAAK,CAACP,MAAM,EAAEO,KAAK,CAACb,IAAI,EAAEa,KAAK,CAACZ,IAAI,CAAC,EAAEqD,oBAAoB,CAAC;IACpIrE,KAAK,CAACiC,WAAW,EAAE,MAAM;MACvBrC,QAAQ,CAACyE,oBAAoB,CAAC;IAChC,CAAC,CAAC;IACFxE,SAAS,CAACwE,oBAAoB,CAAC;IAC/BvE,WAAW,CAACqE,kBAAkB,CAAC;IAC/B,OAAO,MAAM;MACX,MAAMI,KAAK,GAAGnE,MAAM,CAAC;QACnBoE,eAAe,EAAE,OAAOxC,YAAY,CAACO,KAAK;MAC5C,CAAC,EAAE/B,cAAc,CAACoB,KAAK,CAACN,MAAM,CAAC,CAAC;MAChC,OAAOnB,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEU,GAAG,CAAC;UACX4D,IAAI,EAAE7C,KAAK,CAACH;QACd,CAAC,CAAC;QACF,OAAO,EAAE8C;MACX,CAAC,EAAE,CAACpE,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEU,GAAG,CAAC,SAAS,CAAC;QACvB,KAAK,EAAEkB;MACT,CAAC,EAAE,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEP,aAAa,IAAI+C,OAAO,EACxB5D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}