{"ast": null, "code": "import { createApp, reactive } from \"vue\";\nimport { extend } from \"./basic.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nfunction usePopupState() {\n  const state = reactive({\n    show: false\n  });\n  const toggle = show => {\n    state.show = show;\n  };\n  const open = props => {\n    extend(state, props, {\n      transitionAppear: true\n    });\n    toggle(true);\n  };\n  const close = () => toggle(false);\n  useExpose({\n    open,\n    close,\n    toggle\n  });\n  return {\n    open,\n    close,\n    state,\n    toggle\n  };\n}\nfunction mountComponent(RootComponent) {\n  const app = createApp(RootComponent);\n  const root = document.createElement(\"div\");\n  document.body.appendChild(root);\n  return {\n    instance: app.mount(root),\n    unmount() {\n      app.unmount();\n      document.body.removeChild(root);\n    }\n  };\n}\nexport { mountComponent, usePopupState };", "map": {"version": 3, "names": ["createApp", "reactive", "extend", "useExpose", "usePopupState", "state", "show", "toggle", "open", "props", "transitionAppear", "close", "mountComponent", "RootComponent", "app", "root", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "instance", "mount", "unmount", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/utils/mount-component.mjs"], "sourcesContent": ["import { createApp, reactive } from \"vue\";\nimport { extend } from \"./basic.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nfunction usePopupState() {\n  const state = reactive({\n    show: false\n  });\n  const toggle = (show) => {\n    state.show = show;\n  };\n  const open = (props) => {\n    extend(state, props, { transitionAppear: true });\n    toggle(true);\n  };\n  const close = () => toggle(false);\n  useExpose({ open, close, toggle });\n  return {\n    open,\n    close,\n    state,\n    toggle\n  };\n}\nfunction mountComponent(RootComponent) {\n  const app = createApp(RootComponent);\n  const root = document.createElement(\"div\");\n  document.body.appendChild(root);\n  return {\n    instance: app.mount(root),\n    unmount() {\n      app.unmount();\n      document.body.removeChild(root);\n    }\n  };\n}\nexport {\n  mountComponent,\n  usePopupState\n};\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AACzC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAaA,CAAA,EAAG;EACvB,MAAMC,KAAK,GAAGJ,QAAQ,CAAC;IACrBK,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMC,MAAM,GAAID,IAAI,IAAK;IACvBD,KAAK,CAACC,IAAI,GAAGA,IAAI;EACnB,CAAC;EACD,MAAME,IAAI,GAAIC,KAAK,IAAK;IACtBP,MAAM,CAACG,KAAK,EAAEI,KAAK,EAAE;MAAEC,gBAAgB,EAAE;IAAK,CAAC,CAAC;IAChDH,MAAM,CAAC,IAAI,CAAC;EACd,CAAC;EACD,MAAMI,KAAK,GAAGA,CAAA,KAAMJ,MAAM,CAAC,KAAK,CAAC;EACjCJ,SAAS,CAAC;IAAEK,IAAI;IAAEG,KAAK;IAAEJ;EAAO,CAAC,CAAC;EAClC,OAAO;IACLC,IAAI;IACJG,KAAK;IACLN,KAAK;IACLE;EACF,CAAC;AACH;AACA,SAASK,cAAcA,CAACC,aAAa,EAAE;EACrC,MAAMC,GAAG,GAAGd,SAAS,CAACa,aAAa,CAAC;EACpC,MAAME,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC1CD,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;EAC/B,OAAO;IACLK,QAAQ,EAAEN,GAAG,CAACO,KAAK,CAACN,IAAI,CAAC;IACzBO,OAAOA,CAAA,EAAG;MACRR,GAAG,CAACQ,OAAO,CAAC,CAAC;MACbN,QAAQ,CAACE,IAAI,CAACK,WAAW,CAACR,IAAI,CAAC;IACjC;EACF,CAAC;AACH;AACA,SACEH,cAAc,EACdR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}