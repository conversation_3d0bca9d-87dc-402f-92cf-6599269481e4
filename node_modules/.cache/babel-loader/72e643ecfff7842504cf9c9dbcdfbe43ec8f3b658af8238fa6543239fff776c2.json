{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Collapse from \"./Collapse.mjs\";\nconst Collapse = withInstall(_Collapse);\nvar stdin_default = Collapse;\nimport { collapseProps } from \"./Collapse.mjs\";\nexport { Collapse, collapseProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Collapse", "Collapse", "stdin_default", "collapseProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/collapse/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Collapse from \"./Collapse.mjs\";\nconst Collapse = withInstall(_Collapse);\nvar stdin_default = Collapse;\nimport { collapseProps } from \"./Collapse.mjs\";\nexport {\n  Collapse,\n  collapseProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SAASE,aAAa,QAAQ,gBAAgB;AAC9C,SACEF,QAAQ,EACRE,aAAa,EACbD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}