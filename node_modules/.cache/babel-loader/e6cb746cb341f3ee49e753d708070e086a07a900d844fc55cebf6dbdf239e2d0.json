{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ActionBarIcon from \"./ActionBarIcon.mjs\";\nconst ActionBarIcon = withInstall(_ActionBarIcon);\nvar stdin_default = ActionBarIcon;\nimport { actionBarIconProps } from \"./ActionBarIcon.mjs\";\nexport { ActionBarIcon, actionBarIconProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ActionBarIcon", "ActionBarIcon", "stdin_default", "actionBarIconProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/action-bar-icon/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ActionBarIcon from \"./ActionBarIcon.mjs\";\nconst ActionBarIcon = withInstall(_ActionBarIcon);\nvar stdin_default = ActionBarIcon;\nimport { actionBarIconProps } from \"./ActionBarIcon.mjs\";\nexport {\n  ActionBarIcon,\n  actionBarIconProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,MAAMC,aAAa,GAAGF,WAAW,CAACC,cAAc,CAAC;AACjD,IAAIE,aAAa,GAAGD,aAAa;AACjC,SAASE,kBAAkB,QAAQ,qBAAqB;AACxD,SACEF,aAAa,EACbE,kBAAkB,EAClBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}