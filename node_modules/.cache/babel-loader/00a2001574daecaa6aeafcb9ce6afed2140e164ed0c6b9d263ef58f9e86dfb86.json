{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Grid from \"./Grid.mjs\";\nconst Grid = withInstall(_Grid);\nvar stdin_default = Grid;\nimport { gridProps } from \"./Grid.mjs\";\nexport { Grid, stdin_default as default, gridProps };", "map": {"version": 3, "names": ["withInstall", "_Grid", "Grid", "stdin_default", "gridProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/grid/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Grid from \"./Grid.mjs\";\nconst Grid = withInstall(_Grid);\nvar stdin_default = Grid;\nimport { gridProps } from \"./Grid.mjs\";\nexport {\n  Grid,\n  stdin_default as default,\n  gridProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SAASE,SAAS,QAAQ,YAAY;AACtC,SACEF,IAAI,EACJC,aAAa,IAAIE,OAAO,EACxBD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}