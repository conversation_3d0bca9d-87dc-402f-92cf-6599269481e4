{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Button from \"./Button.mjs\";\nconst Button = withInstall(_Button);\nvar stdin_default = Button;\nimport { buttonProps } from \"./Button.mjs\";\nexport { Button, buttonProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_<PERSON><PERSON>", "<PERSON><PERSON>", "stdin_default", "buttonProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/button/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Button from \"./Button.mjs\";\nconst Button = withInstall(_Button);\nvar stdin_default = Button;\nimport { buttonProps } from \"./Button.mjs\";\nexport {\n  Button,\n  buttonProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEF,MAAM,EACNE,WAAW,EACXD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}