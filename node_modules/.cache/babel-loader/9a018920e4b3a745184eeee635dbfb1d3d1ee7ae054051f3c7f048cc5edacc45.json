{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Rate from \"./Rate.mjs\";\nconst Rate = withInstall(_Rate);\nvar stdin_default = Rate;\nimport { rateProps } from \"./Rate.mjs\";\nexport { Rate, stdin_default as default, rateProps };", "map": {"version": 3, "names": ["withInstall", "_Rate", "Rate", "stdin_default", "rateProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/rate/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Rate from \"./Rate.mjs\";\nconst Rate = withInstall(_Rate);\nvar stdin_default = Rate;\nimport { rateProps } from \"./Rate.mjs\";\nexport {\n  Rate,\n  stdin_default as default,\n  rateProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SAASE,SAAS,QAAQ,YAAY;AACtC,SACEF,IAAI,EACJC,aAAa,IAAIE,OAAO,EACxBD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}