{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Row from \"./Row.mjs\";\nconst Row = withInstall(_Row);\nvar stdin_default = Row;\nimport { rowProps } from \"./Row.mjs\";\nexport { Row, stdin_default as default, rowProps };", "map": {"version": 3, "names": ["withInstall", "_Row", "Row", "stdin_default", "rowProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/row/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Row from \"./Row.mjs\";\nconst Row = withInstall(_Row);\nvar stdin_default = Row;\nimport { rowProps } from \"./Row.mjs\";\nexport {\n  Row,\n  stdin_default as default,\n  rowProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,IAAI,MAAM,WAAW;AAC5B,MAAMC,GAAG,GAAGF,WAAW,CAACC,IAAI,CAAC;AAC7B,IAAIE,aAAa,GAAGD,GAAG;AACvB,SAASE,QAAQ,QAAQ,WAAW;AACpC,SACEF,GAAG,EACHC,aAAa,IAAIE,OAAO,EACxBD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}