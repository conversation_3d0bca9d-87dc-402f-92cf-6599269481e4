{"ast": null, "code": "import { defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { SIDEBAR_KEY } from \"../sidebar/Sidebar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"sidebar-item\");\nconst sidebarItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  title: String,\n  badge: numericProp,\n  disabled: Boolean,\n  badgeProps: Object\n});\nvar stdin_default = defineComponent({\n  name,\n  props: sidebarItemProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const {\n      parent,\n      index\n    } = useParent(SIDEBAR_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <SidebarItem> must be a child component of <Sidebar>.\");\n      }\n      return;\n    }\n    const onClick = () => {\n      if (props.disabled) {\n        return;\n      }\n      emit(\"click\", index.value);\n      parent.setActive(index.value);\n      route();\n    };\n    return () => {\n      const {\n        dot,\n        badge,\n        title,\n        disabled\n      } = props;\n      const selected = index.value === parent.getActive();\n      return _createVNode(\"div\", {\n        \"role\": \"tab\",\n        \"class\": bem({\n          select: selected,\n          disabled\n        }),\n        \"tabindex\": disabled ? void 0 : 0,\n        \"aria-selected\": selected,\n        \"onClick\": onClick\n      }, [_createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"class\": bem(\"text\"),\n        \"content\": badge\n      }, props.badgeProps), {\n        default: () => [slots.title ? slots.title() : title]\n      })]);\n    };\n  }\n});\nexport { stdin_default as default, sidebarItemProps };", "map": {"version": 3, "names": ["defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "extend", "numericProp", "createNamespace", "SIDEBAR_KEY", "useParent", "useRoute", "routeProps", "Badge", "name", "bem", "sidebarItemProps", "dot", "Boolean", "title", "String", "badge", "disabled", "badgeProps", "Object", "stdin_default", "props", "emits", "setup", "emit", "slots", "route", "parent", "index", "process", "env", "NODE_ENV", "console", "error", "onClick", "value", "setActive", "selected", "getActive", "select", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/sidebar-item/SidebarItem.mjs"], "sourcesContent": ["import { defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { SIDEBAR_KEY } from \"../sidebar/Sidebar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"sidebar-item\");\nconst sidebarItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  title: String,\n  badge: numericProp,\n  disabled: Boolean,\n  badgeProps: Object\n});\nvar stdin_default = defineComponent({\n  name,\n  props: sidebarItemProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const {\n      parent,\n      index\n    } = useParent(SIDEBAR_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <SidebarItem> must be a child component of <Sidebar>.\");\n      }\n      return;\n    }\n    const onClick = () => {\n      if (props.disabled) {\n        return;\n      }\n      emit(\"click\", index.value);\n      parent.setActive(index.value);\n      route();\n    };\n    return () => {\n      const {\n        dot,\n        badge,\n        title,\n        disabled\n      } = props;\n      const selected = index.value === parent.getActive();\n      return _createVNode(\"div\", {\n        \"role\": \"tab\",\n        \"class\": bem({\n          select: selected,\n          disabled\n        }),\n        \"tabindex\": disabled ? void 0 : 0,\n        \"aria-selected\": selected,\n        \"onClick\": onClick\n      }, [_createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"class\": bem(\"text\"),\n        \"content\": badge\n      }, props.badgeProps), {\n        default: () => [slots.title ? slots.title() : title]\n      })]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  sidebarItemProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC7F,SAASC,MAAM,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AACzE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,8BAA8B;AACnE,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGP,eAAe,CAAC,cAAc,CAAC;AACnD,MAAMQ,gBAAgB,GAAGV,MAAM,CAAC,CAAC,CAAC,EAAEM,UAAU,EAAE;EAC9CK,GAAG,EAAEC,OAAO;EACZC,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAEd,WAAW;EAClBe,QAAQ,EAAEJ,OAAO;EACjBK,UAAU,EAAEC;AACd,CAAC,CAAC;AACF,IAAIC,aAAa,GAAGxB,eAAe,CAAC;EAClCa,IAAI;EACJY,KAAK,EAAEV,gBAAgB;EACvBW,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGpB,QAAQ,CAAC,CAAC;IACxB,MAAM;MACJqB,MAAM;MACNC;IACF,CAAC,GAAGvB,SAAS,CAACD,WAAW,CAAC;IAC1B,IAAI,CAACuB,MAAM,EAAE;MACX,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,8DAA8D,CAAC;MAC/E;MACA;IACF;IACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAIb,KAAK,CAACJ,QAAQ,EAAE;QAClB;MACF;MACAO,IAAI,CAAC,OAAO,EAAEI,KAAK,CAACO,KAAK,CAAC;MAC1BR,MAAM,CAACS,SAAS,CAACR,KAAK,CAACO,KAAK,CAAC;MAC7BT,KAAK,CAAC,CAAC;IACT,CAAC;IACD,OAAO,MAAM;MACX,MAAM;QACJd,GAAG;QACHI,KAAK;QACLF,KAAK;QACLG;MACF,CAAC,GAAGI,KAAK;MACT,MAAMgB,QAAQ,GAAGT,KAAK,CAACO,KAAK,KAAKR,MAAM,CAACW,SAAS,CAAC,CAAC;MACnD,OAAOtC,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,KAAK;QACb,OAAO,EAAEU,GAAG,CAAC;UACX6B,MAAM,EAAEF,QAAQ;UAChBpB;QACF,CAAC,CAAC;QACF,UAAU,EAAEA,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;QACjC,eAAe,EAAEoB,QAAQ;QACzB,SAAS,EAAEH;MACb,CAAC,EAAE,CAAClC,YAAY,CAACQ,KAAK,EAAEV,WAAW,CAAC;QAClC,KAAK,EAAEc,GAAG;QACV,OAAO,EAAEF,GAAG,CAAC,MAAM,CAAC;QACpB,SAAS,EAAEM;MACb,CAAC,EAAEK,KAAK,CAACH,UAAU,CAAC,EAAE;QACpBsB,OAAO,EAAEA,CAAA,KAAM,CAACf,KAAK,CAACX,KAAK,GAAGW,KAAK,CAACX,KAAK,CAAC,CAAC,GAAGA,KAAK;MACrD,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEM,aAAa,IAAIoB,OAAO,EACxB7B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}