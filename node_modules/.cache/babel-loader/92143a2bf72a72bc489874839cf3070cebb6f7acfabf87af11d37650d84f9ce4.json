{"ast": null, "code": "import _SkeletonTitle from \"./SkeletonTitle.mjs\";\nimport { withInstall } from \"../utils/index.mjs\";\nconst SkeletonTitle = withInstall(_SkeletonTitle);\nvar stdin_default = SkeletonTitle;\nimport { skeletonTitleProps } from \"./SkeletonTitle.mjs\";\nexport { SkeletonTitle, stdin_default as default, skeletonTitleProps };", "map": {"version": 3, "names": ["_SkeletonTitle", "withInstall", "SkeletonTitle", "stdin_default", "skeletonTitleProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/skeleton-title/index.mjs"], "sourcesContent": ["import _SkeletonTitle from \"./SkeletonTitle.mjs\";\nimport { withInstall } from \"../utils/index.mjs\";\nconst SkeletonTitle = withInstall(_SkeletonTitle);\nvar stdin_default = SkeletonTitle;\nimport { skeletonTitleProps } from \"./SkeletonTitle.mjs\";\nexport {\n  SkeletonTitle,\n  stdin_default as default,\n  skeletonTitleProps\n};\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,MAAMC,aAAa,GAAGD,WAAW,CAACD,cAAc,CAAC;AACjD,IAAIG,aAAa,GAAGD,aAAa;AACjC,SAASE,kBAAkB,QAAQ,qBAAqB;AACxD,SACEF,aAAa,EACbC,aAAa,IAAIE,OAAO,EACxBD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}