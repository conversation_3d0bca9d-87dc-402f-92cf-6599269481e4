{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, computed, createVNode as _createVNode } from \"vue\";\nimport { truthProp, numericProp, makeArrayProp, createNamespace, makeStringProp } from \"../utils/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { RadioGroup } from \"../radio-group/index.mjs\";\nimport { CheckboxGroup } from \"../checkbox-group/index.mjs\";\nimport AddressListItem from \"./AddressListItem.mjs\";\nconst [name, bem, t] = createNamespace(\"address-list\");\nconst addressListProps = {\n  list: makeArrayProp(),\n  modelValue: [...numericProp, Array],\n  switchable: truthProp,\n  disabledText: String,\n  disabledList: makeArrayProp(),\n  showAddButton: truthProp,\n  addButtonText: String,\n  defaultTagText: String,\n  rightIcon: makeStringProp(\"edit\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: addressListProps,\n  emits: [\"add\", \"edit\", \"select\", \"clickItem\", \"editDisabled\", \"selectDisabled\", \"update:modelValue\"],\n  setup(props, {\n    slots,\n    emit\n  }) {\n    const singleChoice = computed(() => !Array.isArray(props.modelValue));\n    const renderItem = (item, index, disabled) => {\n      const onEdit = () => emit(disabled ? \"editDisabled\" : \"edit\", item, index);\n      const onClick = event => emit(\"clickItem\", item, index, {\n        event\n      });\n      const onSelect = () => {\n        emit(disabled ? \"selectDisabled\" : \"select\", item, index);\n        if (!disabled) {\n          if (singleChoice.value) {\n            emit(\"update:modelValue\", item.id);\n          } else {\n            const value = props.modelValue;\n            if (value.includes(item.id)) {\n              emit(\"update:modelValue\", value.filter(id => id !== item.id));\n            } else {\n              emit(\"update:modelValue\", [...value, item.id]);\n            }\n          }\n        }\n      };\n      return _createVNode(AddressListItem, {\n        \"key\": item.id,\n        \"address\": item,\n        \"disabled\": disabled,\n        \"switchable\": props.switchable,\n        \"singleChoice\": singleChoice.value,\n        \"defaultTagText\": props.defaultTagText,\n        \"rightIcon\": props.rightIcon,\n        \"onEdit\": onEdit,\n        \"onClick\": onClick,\n        \"onSelect\": onSelect\n      }, {\n        bottom: slots[\"item-bottom\"],\n        tag: slots.tag\n      });\n    };\n    const renderList = (list, disabled) => {\n      if (list) {\n        return list.map((item, index) => renderItem(item, index, disabled));\n      }\n    };\n    const renderBottom = () => props.showAddButton ? _createVNode(\"div\", {\n      \"class\": [bem(\"bottom\"), \"van-safe-area-bottom\"]\n    }, [_createVNode(Button, {\n      \"round\": true,\n      \"block\": true,\n      \"type\": \"primary\",\n      \"text\": props.addButtonText || t(\"add\"),\n      \"class\": bem(\"add\"),\n      \"onClick\": () => emit(\"add\")\n    }, null)]) : void 0;\n    return () => {\n      var _a, _b;\n      const List = renderList(props.list);\n      const DisabledList = renderList(props.disabledList, true);\n      const DisabledText = props.disabledText && _createVNode(\"div\", {\n        \"class\": bem(\"disabled-text\")\n      }, [props.disabledText]);\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [(_a = slots.top) == null ? void 0 : _a.call(slots), !singleChoice.value && Array.isArray(props.modelValue) ? _createVNode(CheckboxGroup, {\n        \"modelValue\": props.modelValue\n      }, {\n        default: () => [List]\n      }) : _createVNode(RadioGroup, {\n        \"modelValue\": props.modelValue\n      }, {\n        default: () => [List]\n      }), DisabledText, DisabledList, (_b = slots.default) == null ? void 0 : _b.call(slots), renderBottom()]);\n    };\n  }\n});\nexport { addressListProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "computed", "createVNode", "_createVNode", "truthProp", "numericProp", "makeArrayProp", "createNamespace", "makeStringProp", "<PERSON><PERSON>", "RadioGroup", "CheckboxGroup", "AddressListItem", "name", "bem", "t", "addressListProps", "list", "modelValue", "Array", "switchable", "disabledText", "String", "disabledList", "showAddButton", "addButtonText", "defaultTagText", "rightIcon", "stdin_default", "props", "emits", "setup", "slots", "emit", "singleChoice", "isArray", "renderItem", "item", "index", "disabled", "onEdit", "onClick", "event", "onSelect", "value", "id", "includes", "filter", "bottom", "tag", "renderList", "map", "renderBottom", "_a", "_b", "List", "DisabledList", "DisabledText", "top", "call", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/address-list/AddressList.mjs"], "sourcesContent": ["import { defineComponent, computed, createVNode as _createVNode } from \"vue\";\nimport { truthProp, numericProp, makeArrayProp, createNamespace, makeStringProp } from \"../utils/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { RadioGroup } from \"../radio-group/index.mjs\";\nimport { CheckboxGroup } from \"../checkbox-group/index.mjs\";\nimport AddressListItem from \"./AddressListItem.mjs\";\nconst [name, bem, t] = createNamespace(\"address-list\");\nconst addressListProps = {\n  list: makeArrayProp(),\n  modelValue: [...numericProp, Array],\n  switchable: truthProp,\n  disabledText: String,\n  disabledList: makeArrayProp(),\n  showAddButton: truthProp,\n  addButtonText: String,\n  defaultTagText: String,\n  rightIcon: makeStringProp(\"edit\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: addressListProps,\n  emits: [\"add\", \"edit\", \"select\", \"clickItem\", \"editDisabled\", \"selectDisabled\", \"update:modelValue\"],\n  setup(props, {\n    slots,\n    emit\n  }) {\n    const singleChoice = computed(() => !Array.isArray(props.modelValue));\n    const renderItem = (item, index, disabled) => {\n      const onEdit = () => emit(disabled ? \"editDisabled\" : \"edit\", item, index);\n      const onClick = (event) => emit(\"clickItem\", item, index, {\n        event\n      });\n      const onSelect = () => {\n        emit(disabled ? \"selectDisabled\" : \"select\", item, index);\n        if (!disabled) {\n          if (singleChoice.value) {\n            emit(\"update:modelValue\", item.id);\n          } else {\n            const value = props.modelValue;\n            if (value.includes(item.id)) {\n              emit(\"update:modelValue\", value.filter((id) => id !== item.id));\n            } else {\n              emit(\"update:modelValue\", [...value, item.id]);\n            }\n          }\n        }\n      };\n      return _createVNode(AddressListItem, {\n        \"key\": item.id,\n        \"address\": item,\n        \"disabled\": disabled,\n        \"switchable\": props.switchable,\n        \"singleChoice\": singleChoice.value,\n        \"defaultTagText\": props.defaultTagText,\n        \"rightIcon\": props.rightIcon,\n        \"onEdit\": onEdit,\n        \"onClick\": onClick,\n        \"onSelect\": onSelect\n      }, {\n        bottom: slots[\"item-bottom\"],\n        tag: slots.tag\n      });\n    };\n    const renderList = (list, disabled) => {\n      if (list) {\n        return list.map((item, index) => renderItem(item, index, disabled));\n      }\n    };\n    const renderBottom = () => props.showAddButton ? _createVNode(\"div\", {\n      \"class\": [bem(\"bottom\"), \"van-safe-area-bottom\"]\n    }, [_createVNode(Button, {\n      \"round\": true,\n      \"block\": true,\n      \"type\": \"primary\",\n      \"text\": props.addButtonText || t(\"add\"),\n      \"class\": bem(\"add\"),\n      \"onClick\": () => emit(\"add\")\n    }, null)]) : void 0;\n    return () => {\n      var _a, _b;\n      const List = renderList(props.list);\n      const DisabledList = renderList(props.disabledList, true);\n      const DisabledText = props.disabledText && _createVNode(\"div\", {\n        \"class\": bem(\"disabled-text\")\n      }, [props.disabledText]);\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [(_a = slots.top) == null ? void 0 : _a.call(slots), !singleChoice.value && Array.isArray(props.modelValue) ? _createVNode(CheckboxGroup, {\n        \"modelValue\": props.modelValue\n      }, {\n        default: () => [List]\n      }) : _createVNode(RadioGroup, {\n        \"modelValue\": props.modelValue\n      }, {\n        default: () => [List]\n      }), DisabledText, DisabledList, (_b = slots.default) == null ? void 0 : _b.call(slots), renderBottom()]);\n    };\n  }\n});\nexport {\n  addressListProps,\n  stdin_default as default\n};\n"], "mappings": ";;;AAAA,SAASA,eAAe,EAAEC,QAAQ,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AAC3G,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,OAAOC,eAAe,MAAM,uBAAuB;AACnD,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGR,eAAe,CAAC,cAAc,CAAC;AACtD,MAAMS,gBAAgB,GAAG;EACvBC,IAAI,EAAEX,aAAa,CAAC,CAAC;EACrBY,UAAU,EAAE,CAAC,GAAGb,WAAW,EAAEc,KAAK,CAAC;EACnCC,UAAU,EAAEhB,SAAS;EACrBiB,YAAY,EAAEC,MAAM;EACpBC,YAAY,EAAEjB,aAAa,CAAC,CAAC;EAC7BkB,aAAa,EAAEpB,SAAS;EACxBqB,aAAa,EAAEH,MAAM;EACrBI,cAAc,EAAEJ,MAAM;EACtBK,SAAS,EAAEnB,cAAc,CAAC,MAAM;AAClC,CAAC;AACD,IAAIoB,aAAa,GAAG5B,eAAe,CAAC;EAClCa,IAAI;EACJgB,KAAK,EAAEb,gBAAgB;EACvBc,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;EACpGC,KAAKA,CAACF,KAAK,EAAE;IACXG,KAAK;IACLC;EACF,CAAC,EAAE;IACD,MAAMC,YAAY,GAAGjC,QAAQ,CAAC,MAAM,CAACkB,KAAK,CAACgB,OAAO,CAACN,KAAK,CAACX,UAAU,CAAC,CAAC;IACrE,MAAMkB,UAAU,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;MAC5C,MAAMC,MAAM,GAAGA,CAAA,KAAMP,IAAI,CAACM,QAAQ,GAAG,cAAc,GAAG,MAAM,EAAEF,IAAI,EAAEC,KAAK,CAAC;MAC1E,MAAMG,OAAO,GAAIC,KAAK,IAAKT,IAAI,CAAC,WAAW,EAAEI,IAAI,EAAEC,KAAK,EAAE;QACxDI;MACF,CAAC,CAAC;MACF,MAAMC,QAAQ,GAAGA,CAAA,KAAM;QACrBV,IAAI,CAACM,QAAQ,GAAG,gBAAgB,GAAG,QAAQ,EAAEF,IAAI,EAAEC,KAAK,CAAC;QACzD,IAAI,CAACC,QAAQ,EAAE;UACb,IAAIL,YAAY,CAACU,KAAK,EAAE;YACtBX,IAAI,CAAC,mBAAmB,EAAEI,IAAI,CAACQ,EAAE,CAAC;UACpC,CAAC,MAAM;YACL,MAAMD,KAAK,GAAGf,KAAK,CAACX,UAAU;YAC9B,IAAI0B,KAAK,CAACE,QAAQ,CAACT,IAAI,CAACQ,EAAE,CAAC,EAAE;cAC3BZ,IAAI,CAAC,mBAAmB,EAAEW,KAAK,CAACG,MAAM,CAAEF,EAAE,IAAKA,EAAE,KAAKR,IAAI,CAACQ,EAAE,CAAC,CAAC;YACjE,CAAC,MAAM;cACLZ,IAAI,CAAC,mBAAmB,EAAE,CAAC,GAAGW,KAAK,EAAEP,IAAI,CAACQ,EAAE,CAAC,CAAC;YAChD;UACF;QACF;MACF,CAAC;MACD,OAAO1C,YAAY,CAACS,eAAe,EAAE;QACnC,KAAK,EAAEyB,IAAI,CAACQ,EAAE;QACd,SAAS,EAAER,IAAI;QACf,UAAU,EAAEE,QAAQ;QACpB,YAAY,EAAEV,KAAK,CAACT,UAAU;QAC9B,cAAc,EAAEc,YAAY,CAACU,KAAK;QAClC,gBAAgB,EAAEf,KAAK,CAACH,cAAc;QACtC,WAAW,EAAEG,KAAK,CAACF,SAAS;QAC5B,QAAQ,EAAEa,MAAM;QAChB,SAAS,EAAEC,OAAO;QAClB,UAAU,EAAEE;MACd,CAAC,EAAE;QACDK,MAAM,EAAEhB,KAAK,CAAC,aAAa,CAAC;QAC5BiB,GAAG,EAAEjB,KAAK,CAACiB;MACb,CAAC,CAAC;IACJ,CAAC;IACD,MAAMC,UAAU,GAAGA,CAACjC,IAAI,EAAEsB,QAAQ,KAAK;MACrC,IAAItB,IAAI,EAAE;QACR,OAAOA,IAAI,CAACkC,GAAG,CAAC,CAACd,IAAI,EAAEC,KAAK,KAAKF,UAAU,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,CAAC,CAAC;MACrE;IACF,CAAC;IACD,MAAMa,YAAY,GAAGA,CAAA,KAAMvB,KAAK,CAACL,aAAa,GAAGrB,YAAY,CAAC,KAAK,EAAE;MACnE,OAAO,EAAE,CAACW,GAAG,CAAC,QAAQ,CAAC,EAAE,sBAAsB;IACjD,CAAC,EAAE,CAACX,YAAY,CAACM,MAAM,EAAE;MACvB,OAAO,EAAE,IAAI;MACb,OAAO,EAAE,IAAI;MACb,MAAM,EAAE,SAAS;MACjB,MAAM,EAAEoB,KAAK,CAACJ,aAAa,IAAIV,CAAC,CAAC,KAAK,CAAC;MACvC,OAAO,EAAED,GAAG,CAAC,KAAK,CAAC;MACnB,SAAS,EAAE2B,CAAA,KAAMR,IAAI,CAAC,KAAK;IAC7B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IACnB,OAAO,MAAM;MACX,IAAIoB,EAAE,EAAEC,EAAE;MACV,MAAMC,IAAI,GAAGL,UAAU,CAACrB,KAAK,CAACZ,IAAI,CAAC;MACnC,MAAMuC,YAAY,GAAGN,UAAU,CAACrB,KAAK,CAACN,YAAY,EAAE,IAAI,CAAC;MACzD,MAAMkC,YAAY,GAAG5B,KAAK,CAACR,YAAY,IAAIlB,YAAY,CAAC,KAAK,EAAE;QAC7D,OAAO,EAAEW,GAAG,CAAC,eAAe;MAC9B,CAAC,EAAE,CAACe,KAAK,CAACR,YAAY,CAAC,CAAC;MACxB,OAAOlB,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEW,GAAG,CAAC;MACf,CAAC,EAAE,CAAC,CAACuC,EAAE,GAAGrB,KAAK,CAAC0B,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,EAAE,CAACM,IAAI,CAAC3B,KAAK,CAAC,EAAE,CAACE,YAAY,CAACU,KAAK,IAAIzB,KAAK,CAACgB,OAAO,CAACN,KAAK,CAACX,UAAU,CAAC,GAAGf,YAAY,CAACQ,aAAa,EAAE;QAC3I,YAAY,EAAEkB,KAAK,CAACX;MACtB,CAAC,EAAE;QACD0C,OAAO,EAAEA,CAAA,KAAM,CAACL,IAAI;MACtB,CAAC,CAAC,GAAGpD,YAAY,CAACO,UAAU,EAAE;QAC5B,YAAY,EAAEmB,KAAK,CAACX;MACtB,CAAC,EAAE;QACD0C,OAAO,EAAEA,CAAA,KAAM,CAACL,IAAI;MACtB,CAAC,CAAC,EAAEE,YAAY,EAAED,YAAY,EAAE,CAACF,EAAE,GAAGtB,KAAK,CAAC4B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,EAAE,CAACK,IAAI,CAAC3B,KAAK,CAAC,EAAEoB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC1G,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEpC,gBAAgB,EAChBY,aAAa,IAAIgC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}