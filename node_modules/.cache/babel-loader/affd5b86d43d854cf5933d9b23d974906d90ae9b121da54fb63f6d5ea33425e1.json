{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport Vant from 'vant';\nimport 'vant/lib/index.css';\nconst app = createApp(App);\napp.use(router);\napp.use(Vant);\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "<PERSON><PERSON>", "app", "use", "mount"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport Vant from 'vant'\nimport 'vant/lib/index.css'\n\nconst app = createApp(App)\napp.use(router)\napp.use(Vant)\napp.mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAO,oBAAoB;AAE3B,MAAMC,GAAG,GAAGJ,SAAS,CAACC,GAAG,CAAC;AAC1BG,GAAG,CAACC,GAAG,CAACH,MAAM,CAAC;AACfE,GAAG,CAACC,GAAG,CAACF,IAAI,CAAC;AACbC,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}