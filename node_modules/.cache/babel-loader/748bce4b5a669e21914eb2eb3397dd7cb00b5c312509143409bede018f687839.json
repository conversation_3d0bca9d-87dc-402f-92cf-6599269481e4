{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ActionBar from \"./ActionBar.mjs\";\nconst ActionBar = withInstall(_ActionBar);\nvar stdin_default = ActionBar;\nimport { actionBarProps } from \"./ActionBar.mjs\";\nexport { ActionBar, actionBarProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ActionBar", "ActionBar", "stdin_default", "actionBarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/action-bar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ActionBar from \"./ActionBar.mjs\";\nconst ActionBar = withInstall(_ActionBar);\nvar stdin_default = ActionBar;\nimport { actionBarProps } from \"./ActionBar.mjs\";\nexport {\n  ActionBar,\n  actionBarProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SAASE,cAAc,QAAQ,iBAAiB;AAChD,SACEF,SAAS,EACTE,cAAc,EACdD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}