{"ast": null, "code": "import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, createNamespace, makeNumericProp, makeStringProp, extend } from \"../utils/index.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { ROW_KEY } from \"../row/Row.mjs\";\nconst [name, bem] = createNamespace(\"col\");\nconst colProps = {\n  tag: makeStringProp(\"div\"),\n  span: makeNumericProp(0),\n  offset: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: colProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      parent,\n      index\n    } = useParent(ROW_KEY);\n    const style = computed(() => {\n      if (!parent) {\n        return;\n      }\n      const {\n        spaces,\n        verticalSpaces\n      } = parent;\n      let styles = {};\n      if (spaces && spaces.value && spaces.value[index.value]) {\n        const {\n          left,\n          right\n        } = spaces.value[index.value];\n        styles = {\n          paddingLeft: left ? `${left}px` : null,\n          paddingRight: right ? `${right}px` : null\n        };\n      }\n      const {\n        bottom\n      } = verticalSpaces.value[index.value] || {};\n      return extend(styles, {\n        marginBottom: bottom ? `${bottom}px` : null\n      });\n    });\n    return () => {\n      const {\n        tag,\n        span,\n        offset\n      } = props;\n      return _createVNode(tag, {\n        \"style\": style.value,\n        \"class\": bem({\n          [span]: span,\n          [`offset-${offset}`]: offset\n        })\n      }, {\n        default: () => {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      });\n    };\n  }\n});\nexport { colProps, stdin_default as default };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "numericProp", "createNamespace", "makeNumericProp", "makeStringProp", "extend", "useParent", "ROW_KEY", "name", "bem", "colProps", "tag", "span", "offset", "stdin_default", "props", "setup", "slots", "parent", "index", "style", "spaces", "verticalSpaces", "styles", "value", "left", "right", "paddingLeft", "paddingRight", "bottom", "marginBottom", "default", "_a", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/col/Col.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, createNamespace, makeNumericProp, makeStringProp, extend } from \"../utils/index.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { ROW_KEY } from \"../row/Row.mjs\";\nconst [name, bem] = createNamespace(\"col\");\nconst colProps = {\n  tag: makeStringProp(\"div\"),\n  span: makeNumericProp(0),\n  offset: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: colProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      parent,\n      index\n    } = useParent(ROW_KEY);\n    const style = computed(() => {\n      if (!parent) {\n        return;\n      }\n      const {\n        spaces,\n        verticalSpaces\n      } = parent;\n      let styles = {};\n      if (spaces && spaces.value && spaces.value[index.value]) {\n        const {\n          left,\n          right\n        } = spaces.value[index.value];\n        styles = {\n          paddingLeft: left ? `${left}px` : null,\n          paddingRight: right ? `${right}px` : null\n        };\n      }\n      const {\n        bottom\n      } = verticalSpaces.value[index.value] || {};\n      return extend(styles, {\n        marginBottom: bottom ? `${bottom}px` : null\n      });\n    });\n    return () => {\n      const {\n        tag,\n        span,\n        offset\n      } = props;\n      return _createVNode(tag, {\n        \"style\": style.value,\n        \"class\": bem({\n          [span]: span,\n          [`offset-${offset}`]: offset\n        })\n      }, {\n        default: () => {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      });\n    };\n  }\n});\nexport {\n  colProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,WAAW,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,MAAM,QAAQ,oBAAoB;AAC1G,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,OAAO,QAAQ,gBAAgB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGP,eAAe,CAAC,KAAK,CAAC;AAC1C,MAAMQ,QAAQ,GAAG;EACfC,GAAG,EAAEP,cAAc,CAAC,KAAK,CAAC;EAC1BQ,IAAI,EAAET,eAAe,CAAC,CAAC,CAAC;EACxBU,MAAM,EAAEZ;AACV,CAAC;AACD,IAAIa,aAAa,GAAGhB,eAAe,CAAC;EAClCU,IAAI;EACJO,KAAK,EAAEL,QAAQ;EACfM,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAM;MACJC,MAAM;MACNC;IACF,CAAC,GAAGb,SAAS,CAACC,OAAO,CAAC;IACtB,MAAMa,KAAK,GAAGvB,QAAQ,CAAC,MAAM;MAC3B,IAAI,CAACqB,MAAM,EAAE;QACX;MACF;MACA,MAAM;QACJG,MAAM;QACNC;MACF,CAAC,GAAGJ,MAAM;MACV,IAAIK,MAAM,GAAG,CAAC,CAAC;MACf,IAAIF,MAAM,IAAIA,MAAM,CAACG,KAAK,IAAIH,MAAM,CAACG,KAAK,CAACL,KAAK,CAACK,KAAK,CAAC,EAAE;QACvD,MAAM;UACJC,IAAI;UACJC;QACF,CAAC,GAAGL,MAAM,CAACG,KAAK,CAACL,KAAK,CAACK,KAAK,CAAC;QAC7BD,MAAM,GAAG;UACPI,WAAW,EAAEF,IAAI,GAAG,GAAGA,IAAI,IAAI,GAAG,IAAI;UACtCG,YAAY,EAAEF,KAAK,GAAG,GAAGA,KAAK,IAAI,GAAG;QACvC,CAAC;MACH;MACA,MAAM;QACJG;MACF,CAAC,GAAGP,cAAc,CAACE,KAAK,CAACL,KAAK,CAACK,KAAK,CAAC,IAAI,CAAC,CAAC;MAC3C,OAAOnB,MAAM,CAACkB,MAAM,EAAE;QACpBO,YAAY,EAAED,MAAM,GAAG,GAAGA,MAAM,IAAI,GAAG;MACzC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO,MAAM;MACX,MAAM;QACJlB,GAAG;QACHC,IAAI;QACJC;MACF,CAAC,GAAGE,KAAK;MACT,OAAOf,YAAY,CAACW,GAAG,EAAE;QACvB,OAAO,EAAES,KAAK,CAACI,KAAK;QACpB,OAAO,EAAEf,GAAG,CAAC;UACX,CAACG,IAAI,GAAGA,IAAI;UACZ,CAAC,UAAUC,MAAM,EAAE,GAAGA;QACxB,CAAC;MACH,CAAC,EAAE;QACDkB,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIC,EAAE;UACN,OAAO,CAAC,CAACA,EAAE,GAAGf,KAAK,CAACc,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,EAAE,CAACC,IAAI,CAAChB,KAAK,CAAC,CAAC;QACjE;MACF,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEP,QAAQ,EACRI,aAAa,IAAIiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}