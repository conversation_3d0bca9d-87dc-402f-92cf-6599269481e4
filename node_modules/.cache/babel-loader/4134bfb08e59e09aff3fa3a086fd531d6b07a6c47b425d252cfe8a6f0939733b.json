{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _TextEllipsis from \"./TextEllipsis.mjs\";\nconst TextEllipsis = withInstall(_TextEllipsis);\nvar stdin_default = TextEllipsis;\nimport { textEllipsisProps } from \"./TextEllipsis.mjs\";\nexport { TextEllipsis, stdin_default as default, textEllipsisProps };", "map": {"version": 3, "names": ["withInstall", "_TextEllipsis", "TextEllipsis", "stdin_default", "textEllipsisProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/text-ellipsis/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _TextEllipsis from \"./TextEllipsis.mjs\";\nconst TextEllipsis = withInstall(_TextEllipsis);\nvar stdin_default = TextEllipsis;\nimport { textEllipsisProps } from \"./TextEllipsis.mjs\";\nexport {\n  TextEllipsis,\n  stdin_default as default,\n  textEllipsisProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,MAAMC,YAAY,GAAGF,WAAW,CAACC,aAAa,CAAC;AAC/C,IAAIE,aAAa,GAAGD,YAAY;AAChC,SAASE,iBAAiB,QAAQ,oBAAoB;AACtD,SACEF,YAAY,EACZC,aAAa,IAAIE,OAAO,EACxBD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}