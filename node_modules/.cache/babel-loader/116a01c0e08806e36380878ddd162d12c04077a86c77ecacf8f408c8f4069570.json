{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _AddressList from \"./AddressList.mjs\";\nconst AddressList = withInstall(_AddressList);\nvar stdin_default = AddressList;\nimport { addressListProps } from \"./AddressList.mjs\";\nexport { AddressList, addressListProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_AddressList", "AddressList", "stdin_default", "addressListProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/address-list/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _AddressList from \"./AddressList.mjs\";\nconst AddressList = withInstall(_AddressList);\nvar stdin_default = AddressList;\nimport { addressListProps } from \"./AddressList.mjs\";\nexport {\n  AddressList,\n  addressListProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXE,gBAAgB,EAChBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}