{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Loading from \"./Loading.mjs\";\nconst Loading = withInstall(_Loading);\nvar stdin_default = Loading;\nimport { loadingProps } from \"./Loading.mjs\";\nexport { Loading, stdin_default as default, loadingProps };", "map": {"version": 3, "names": ["withInstall", "_Loading", "Loading", "stdin_default", "loadingProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/loading/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Loading from \"./Loading.mjs\";\nconst Loading = withInstall(_Loading);\nvar stdin_default = Loading;\nimport { loadingProps } from \"./Loading.mjs\";\nexport {\n  Loading,\n  stdin_default as default,\n  loadingProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,MAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SAASE,YAAY,QAAQ,eAAe;AAC5C,SACEF,OAAO,EACPC,aAAa,IAAIE,OAAO,EACxBD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}