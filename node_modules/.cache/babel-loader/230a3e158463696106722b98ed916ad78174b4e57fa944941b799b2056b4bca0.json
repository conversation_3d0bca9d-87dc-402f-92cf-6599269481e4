{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _IndexAnchor from \"./IndexAnchor.mjs\";\nconst IndexAnchor = withInstall(_IndexAnchor);\nvar stdin_default = IndexAnchor;\nimport { indexAnchorProps } from \"./IndexAnchor.mjs\";\nexport { IndexAnchor, stdin_default as default, indexAnchorProps };", "map": {"version": 3, "names": ["withInstall", "_IndexAnchor", "IndexAnchor", "stdin_default", "indexAnchorProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/index-anchor/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _IndexAnchor from \"./IndexAnchor.mjs\";\nconst IndexAnchor = withInstall(_IndexAnchor);\nvar stdin_default = IndexAnchor;\nimport { indexAnchorProps } from \"./IndexAnchor.mjs\";\nexport {\n  IndexAnchor,\n  stdin_default as default,\n  indexAnchorProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXC,aAAa,IAAIE,OAAO,EACxBD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}