{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { defineComponent, computed, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, makeRequiredProp, makeStringProp, truthProp } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"highlight\");\nconst highlightProps = {\n  autoEscape: truthProp,\n  caseSensitive: Boolean,\n  highlightClass: String,\n  highlightTag: makeStringProp(\"span\"),\n  keywords: makeRequiredProp([String, Array]),\n  sourceString: makeStringProp(\"\"),\n  tag: makeStringProp(\"div\"),\n  unhighlightClass: String,\n  unhighlightTag: makeStringProp(\"span\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: highlightProps,\n  setup(props) {\n    const highlightChunks = computed(() => {\n      const {\n        autoEscape,\n        caseSensitive,\n        keywords,\n        sourceString\n      } = props;\n      const flags = caseSensitive ? \"g\" : \"gi\";\n      const _keywords = Array.isArray(keywords) ? keywords : [keywords];\n      let chunks = _keywords.filter(keyword => keyword).reduce((chunks2, keyword) => {\n        if (autoEscape) {\n          keyword = keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n        }\n        const regex = new RegExp(keyword, flags);\n        let match;\n        while (match = regex.exec(sourceString)) {\n          const start = match.index;\n          const end = regex.lastIndex;\n          if (start >= end) {\n            regex.lastIndex++;\n            continue;\n          }\n          chunks2.push({\n            start,\n            end,\n            highlight: true\n          });\n        }\n        return chunks2;\n      }, []);\n      chunks = chunks.sort((a, b) => a.start - b.start).reduce((chunks2, currentChunk) => {\n        const prevChunk = chunks2[chunks2.length - 1];\n        if (!prevChunk || currentChunk.start > prevChunk.end) {\n          const unhighlightStart = prevChunk ? prevChunk.end : 0;\n          const unhighlightEnd = currentChunk.start;\n          if (unhighlightStart !== unhighlightEnd) {\n            chunks2.push({\n              start: unhighlightStart,\n              end: unhighlightEnd,\n              highlight: false\n            });\n          }\n          chunks2.push(currentChunk);\n        } else {\n          prevChunk.end = Math.max(prevChunk.end, currentChunk.end);\n        }\n        return chunks2;\n      }, []);\n      const lastChunk = chunks[chunks.length - 1];\n      if (!lastChunk) {\n        chunks.push({\n          start: 0,\n          end: sourceString.length,\n          highlight: false\n        });\n      }\n      if (lastChunk && lastChunk.end < sourceString.length) {\n        chunks.push({\n          start: lastChunk.end,\n          end: sourceString.length,\n          highlight: false\n        });\n      }\n      return chunks;\n    });\n    const renderContent = () => {\n      const {\n        sourceString,\n        highlightClass,\n        unhighlightClass,\n        highlightTag,\n        unhighlightTag\n      } = props;\n      return highlightChunks.value.map(chunk => {\n        const {\n          start,\n          end,\n          highlight\n        } = chunk;\n        const text = sourceString.slice(start, end);\n        if (highlight) {\n          return _createVNode(highlightTag, {\n            \"class\": [bem(\"tag\"), highlightClass]\n          }, {\n            default: () => [text]\n          });\n        }\n        return _createVNode(unhighlightTag, {\n          \"class\": unhighlightClass\n        }, {\n          default: () => [text]\n        });\n      });\n    };\n    return () => {\n      const {\n        tag\n      } = props;\n      return _createVNode(tag, {\n        \"class\": bem()\n      }, {\n        default: () => [renderContent()]\n      });\n    };\n  }\n});\nexport { stdin_default as default, highlightProps };", "map": {"version": 3, "names": ["defineComponent", "computed", "createVNode", "_createVNode", "createNamespace", "makeRequiredProp", "makeStringProp", "truthProp", "name", "bem", "highlightProps", "autoEscape", "caseSensitive", "Boolean", "highlightClass", "String", "highlightTag", "keywords", "Array", "sourceString", "tag", "unhighlightClass", "unhighlightTag", "stdin_default", "props", "setup", "highlightChunks", "flags", "_keywords", "isArray", "chunks", "filter", "keyword", "reduce", "chunks2", "replace", "regex", "RegExp", "match", "exec", "start", "index", "end", "lastIndex", "push", "highlight", "sort", "a", "b", "currentChunk", "prevChunk", "length", "unhighlightStart", "unhighlightEnd", "Math", "max", "lastChunk", "renderContent", "value", "map", "chunk", "text", "slice", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/highlight/Highlight.mjs"], "sourcesContent": ["import { defineComponent, computed, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, makeRequiredProp, makeStringProp, truthProp } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"highlight\");\nconst highlightProps = {\n  autoEscape: truthProp,\n  caseSensitive: <PERSON><PERSON><PERSON>,\n  highlightClass: String,\n  highlightTag: makeStringProp(\"span\"),\n  keywords: makeRequiredProp([String, Array]),\n  sourceString: makeStringProp(\"\"),\n  tag: makeStringProp(\"div\"),\n  unhighlightClass: String,\n  unhighlightTag: makeStringProp(\"span\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: highlightProps,\n  setup(props) {\n    const highlightChunks = computed(() => {\n      const {\n        autoEscape,\n        caseSensitive,\n        keywords,\n        sourceString\n      } = props;\n      const flags = caseSensitive ? \"g\" : \"gi\";\n      const _keywords = Array.isArray(keywords) ? keywords : [keywords];\n      let chunks = _keywords.filter((keyword) => keyword).reduce((chunks2, keyword) => {\n        if (autoEscape) {\n          keyword = keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n        }\n        const regex = new RegExp(keyword, flags);\n        let match;\n        while (match = regex.exec(sourceString)) {\n          const start = match.index;\n          const end = regex.lastIndex;\n          if (start >= end) {\n            regex.lastIndex++;\n            continue;\n          }\n          chunks2.push({\n            start,\n            end,\n            highlight: true\n          });\n        }\n        return chunks2;\n      }, []);\n      chunks = chunks.sort((a, b) => a.start - b.start).reduce((chunks2, currentChunk) => {\n        const prevChunk = chunks2[chunks2.length - 1];\n        if (!prevChunk || currentChunk.start > prevChunk.end) {\n          const unhighlightStart = prevChunk ? prevChunk.end : 0;\n          const unhighlightEnd = currentChunk.start;\n          if (unhighlightStart !== unhighlightEnd) {\n            chunks2.push({\n              start: unhighlightStart,\n              end: unhighlightEnd,\n              highlight: false\n            });\n          }\n          chunks2.push(currentChunk);\n        } else {\n          prevChunk.end = Math.max(prevChunk.end, currentChunk.end);\n        }\n        return chunks2;\n      }, []);\n      const lastChunk = chunks[chunks.length - 1];\n      if (!lastChunk) {\n        chunks.push({\n          start: 0,\n          end: sourceString.length,\n          highlight: false\n        });\n      }\n      if (lastChunk && lastChunk.end < sourceString.length) {\n        chunks.push({\n          start: lastChunk.end,\n          end: sourceString.length,\n          highlight: false\n        });\n      }\n      return chunks;\n    });\n    const renderContent = () => {\n      const {\n        sourceString,\n        highlightClass,\n        unhighlightClass,\n        highlightTag,\n        unhighlightTag\n      } = props;\n      return highlightChunks.value.map((chunk) => {\n        const {\n          start,\n          end,\n          highlight\n        } = chunk;\n        const text = sourceString.slice(start, end);\n        if (highlight) {\n          return _createVNode(highlightTag, {\n            \"class\": [bem(\"tag\"), highlightClass]\n          }, {\n            default: () => [text]\n          });\n        }\n        return _createVNode(unhighlightTag, {\n          \"class\": unhighlightClass\n        }, {\n          default: () => [text]\n        });\n      });\n    };\n    return () => {\n      const {\n        tag\n      } = props;\n      return _createVNode(tag, {\n        \"class\": bem()\n      }, {\n        default: () => [renderContent()]\n      });\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  highlightProps\n};\n"], "mappings": ";;;;;AAAA,SAASA,eAAe,EAAEC,QAAQ,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,eAAe,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,SAAS,QAAQ,oBAAoB;AACjG,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,WAAW,CAAC;AAChD,MAAMM,cAAc,GAAG;EACrBC,UAAU,EAAEJ,SAAS;EACrBK,aAAa,EAAEC,OAAO;EACtBC,cAAc,EAAEC,MAAM;EACtBC,YAAY,EAAEV,cAAc,CAAC,MAAM,CAAC;EACpCW,QAAQ,EAAEZ,gBAAgB,CAAC,CAACU,MAAM,EAAEG,KAAK,CAAC,CAAC;EAC3CC,YAAY,EAAEb,cAAc,CAAC,EAAE,CAAC;EAChCc,GAAG,EAAEd,cAAc,CAAC,KAAK,CAAC;EAC1Be,gBAAgB,EAAEN,MAAM;EACxBO,cAAc,EAAEhB,cAAc,CAAC,MAAM;AACvC,CAAC;AACD,IAAIiB,aAAa,GAAGvB,eAAe,CAAC;EAClCQ,IAAI;EACJgB,KAAK,EAAEd,cAAc;EACrBe,KAAKA,CAACD,KAAK,EAAE;IACX,MAAME,eAAe,GAAGzB,QAAQ,CAAC,MAAM;MACrC,MAAM;QACJU,UAAU;QACVC,aAAa;QACbK,QAAQ;QACRE;MACF,CAAC,GAAGK,KAAK;MACT,MAAMG,KAAK,GAAGf,aAAa,GAAG,GAAG,GAAG,IAAI;MACxC,MAAMgB,SAAS,GAAGV,KAAK,CAACW,OAAO,CAACZ,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;MACjE,IAAIa,MAAM,GAAGF,SAAS,CAACG,MAAM,CAAEC,OAAO,IAAKA,OAAO,CAAC,CAACC,MAAM,CAAC,CAACC,OAAO,EAAEF,OAAO,KAAK;QAC/E,IAAIrB,UAAU,EAAE;UACdqB,OAAO,GAAGA,OAAO,CAACG,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;QAC1D;QACA,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACL,OAAO,EAAEL,KAAK,CAAC;QACxC,IAAIW,KAAK;QACT,OAAOA,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACpB,YAAY,CAAC,EAAE;UACvC,MAAMqB,KAAK,GAAGF,KAAK,CAACG,KAAK;UACzB,MAAMC,GAAG,GAAGN,KAAK,CAACO,SAAS;UAC3B,IAAIH,KAAK,IAAIE,GAAG,EAAE;YAChBN,KAAK,CAACO,SAAS,EAAE;YACjB;UACF;UACAT,OAAO,CAACU,IAAI,CAAC;YACXJ,KAAK;YACLE,GAAG;YACHG,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;QACA,OAAOX,OAAO;MAChB,CAAC,EAAE,EAAE,CAAC;MACNJ,MAAM,GAAGA,MAAM,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACP,KAAK,GAAGQ,CAAC,CAACR,KAAK,CAAC,CAACP,MAAM,CAAC,CAACC,OAAO,EAAEe,YAAY,KAAK;QAClF,MAAMC,SAAS,GAAGhB,OAAO,CAACA,OAAO,CAACiB,MAAM,GAAG,CAAC,CAAC;QAC7C,IAAI,CAACD,SAAS,IAAID,YAAY,CAACT,KAAK,GAAGU,SAAS,CAACR,GAAG,EAAE;UACpD,MAAMU,gBAAgB,GAAGF,SAAS,GAAGA,SAAS,CAACR,GAAG,GAAG,CAAC;UACtD,MAAMW,cAAc,GAAGJ,YAAY,CAACT,KAAK;UACzC,IAAIY,gBAAgB,KAAKC,cAAc,EAAE;YACvCnB,OAAO,CAACU,IAAI,CAAC;cACXJ,KAAK,EAAEY,gBAAgB;cACvBV,GAAG,EAAEW,cAAc;cACnBR,SAAS,EAAE;YACb,CAAC,CAAC;UACJ;UACAX,OAAO,CAACU,IAAI,CAACK,YAAY,CAAC;QAC5B,CAAC,MAAM;UACLC,SAAS,CAACR,GAAG,GAAGY,IAAI,CAACC,GAAG,CAACL,SAAS,CAACR,GAAG,EAAEO,YAAY,CAACP,GAAG,CAAC;QAC3D;QACA,OAAOR,OAAO;MAChB,CAAC,EAAE,EAAE,CAAC;MACN,MAAMsB,SAAS,GAAG1B,MAAM,CAACA,MAAM,CAACqB,MAAM,GAAG,CAAC,CAAC;MAC3C,IAAI,CAACK,SAAS,EAAE;QACd1B,MAAM,CAACc,IAAI,CAAC;UACVJ,KAAK,EAAE,CAAC;UACRE,GAAG,EAAEvB,YAAY,CAACgC,MAAM;UACxBN,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MACA,IAAIW,SAAS,IAAIA,SAAS,CAACd,GAAG,GAAGvB,YAAY,CAACgC,MAAM,EAAE;QACpDrB,MAAM,CAACc,IAAI,CAAC;UACVJ,KAAK,EAAEgB,SAAS,CAACd,GAAG;UACpBA,GAAG,EAAEvB,YAAY,CAACgC,MAAM;UACxBN,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;MACA,OAAOf,MAAM;IACf,CAAC,CAAC;IACF,MAAM2B,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAM;QACJtC,YAAY;QACZL,cAAc;QACdO,gBAAgB;QAChBL,YAAY;QACZM;MACF,CAAC,GAAGE,KAAK;MACT,OAAOE,eAAe,CAACgC,KAAK,CAACC,GAAG,CAAEC,KAAK,IAAK;QAC1C,MAAM;UACJpB,KAAK;UACLE,GAAG;UACHG;QACF,CAAC,GAAGe,KAAK;QACT,MAAMC,IAAI,GAAG1C,YAAY,CAAC2C,KAAK,CAACtB,KAAK,EAAEE,GAAG,CAAC;QAC3C,IAAIG,SAAS,EAAE;UACb,OAAO1C,YAAY,CAACa,YAAY,EAAE;YAChC,OAAO,EAAE,CAACP,GAAG,CAAC,KAAK,CAAC,EAAEK,cAAc;UACtC,CAAC,EAAE;YACDiD,OAAO,EAAEA,CAAA,KAAM,CAACF,IAAI;UACtB,CAAC,CAAC;QACJ;QACA,OAAO1D,YAAY,CAACmB,cAAc,EAAE;UAClC,OAAO,EAAED;QACX,CAAC,EAAE;UACD0C,OAAO,EAAEA,CAAA,KAAM,CAACF,IAAI;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,MAAM;MACX,MAAM;QACJzC;MACF,CAAC,GAAGI,KAAK;MACT,OAAOrB,YAAY,CAACiB,GAAG,EAAE;QACvB,OAAO,EAAEX,GAAG,CAAC;MACf,CAAC,EAAE;QACDsD,OAAO,EAAEA,CAAA,KAAM,CAACN,aAAa,CAAC,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACElC,aAAa,IAAIwC,OAAO,EACxBrD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}