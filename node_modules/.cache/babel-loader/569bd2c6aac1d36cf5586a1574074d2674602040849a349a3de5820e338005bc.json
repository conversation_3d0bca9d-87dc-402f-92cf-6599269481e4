{"ast": null, "code": "import { ref, reactive, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, numericProp, BORDER_BOTTOM, getZIndexStyle, createNamespace } from \"../utils/index.mjs\";\nimport { INDEX_BAR_KEY } from \"../index-bar/IndexBar.mjs\";\nimport { getScrollTop, getRootScrollTop } from \"../utils/dom.mjs\";\nimport { useRect, useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"index-anchor\");\nconst indexAnchorProps = {\n  index: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: indexAnchorProps,\n  setup(props, {\n    slots\n  }) {\n    const state = reactive({\n      top: 0,\n      left: null,\n      rect: {\n        top: 0,\n        height: 0\n      },\n      width: null,\n      active: false\n    });\n    const root = ref();\n    const {\n      parent\n    } = useParent(INDEX_BAR_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <IndexAnchor> must be a child component of <IndexBar>.\");\n      }\n      return;\n    }\n    const isSticky = () => state.active && parent.props.sticky;\n    const anchorStyle = computed(() => {\n      const {\n        zIndex,\n        highlightColor\n      } = parent.props;\n      if (isSticky()) {\n        return extend(getZIndexStyle(zIndex), {\n          left: state.left ? `${state.left}px` : void 0,\n          width: state.width ? `${state.width}px` : void 0,\n          transform: state.top ? `translate3d(0, ${state.top}px, 0)` : void 0,\n          color: highlightColor\n        });\n      }\n    });\n    const getRect = (scrollParent, scrollParentRect) => {\n      const rootRect = useRect(root);\n      state.rect.height = rootRect.height;\n      if (scrollParent === window || scrollParent === document.body) {\n        state.rect.top = rootRect.top + getRootScrollTop();\n      } else {\n        state.rect.top = rootRect.top + getScrollTop(scrollParent) - scrollParentRect.top;\n      }\n      return state.rect;\n    };\n    useExpose({\n      state,\n      getRect\n    });\n    return () => {\n      const sticky = isSticky();\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"style\": {\n          height: sticky ? `${state.rect.height}px` : void 0\n        }\n      }, [_createVNode(\"div\", {\n        \"style\": anchorStyle.value,\n        \"class\": [bem({\n          sticky\n        }), {\n          [BORDER_BOTTOM]: sticky\n        }]\n      }, [slots.default ? slots.default() : props.index])]);\n    };\n  }\n});\nexport { stdin_default as default, indexAnchorProps };", "map": {"version": 3, "names": ["ref", "reactive", "computed", "defineComponent", "createVNode", "_createVNode", "extend", "numericProp", "BORDER_BOTTOM", "getZIndexStyle", "createNamespace", "INDEX_BAR_KEY", "getScrollTop", "getRootScrollTop", "useRect", "useParent", "useExpose", "name", "bem", "indexAnchorProps", "index", "stdin_default", "props", "setup", "slots", "state", "top", "left", "rect", "height", "width", "active", "root", "parent", "process", "env", "NODE_ENV", "console", "error", "isSticky", "sticky", "anchorStyle", "zIndex", "highlightColor", "transform", "color", "getRect", "scrollParent", "scrollParentRect", "rootRect", "window", "document", "body", "value", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/index-anchor/IndexAnchor.mjs"], "sourcesContent": ["import { ref, reactive, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, numericProp, BORDER_BOTTOM, getZIndexStyle, createNamespace } from \"../utils/index.mjs\";\nimport { INDEX_BAR_KEY } from \"../index-bar/IndexBar.mjs\";\nimport { getScrollTop, getRootScrollTop } from \"../utils/dom.mjs\";\nimport { useRect, useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"index-anchor\");\nconst indexAnchorProps = {\n  index: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: indexAnchorProps,\n  setup(props, {\n    slots\n  }) {\n    const state = reactive({\n      top: 0,\n      left: null,\n      rect: {\n        top: 0,\n        height: 0\n      },\n      width: null,\n      active: false\n    });\n    const root = ref();\n    const {\n      parent\n    } = useParent(INDEX_BAR_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <IndexAnchor> must be a child component of <IndexBar>.\");\n      }\n      return;\n    }\n    const isSticky = () => state.active && parent.props.sticky;\n    const anchorStyle = computed(() => {\n      const {\n        zIndex,\n        highlightColor\n      } = parent.props;\n      if (isSticky()) {\n        return extend(getZIndexStyle(zIndex), {\n          left: state.left ? `${state.left}px` : void 0,\n          width: state.width ? `${state.width}px` : void 0,\n          transform: state.top ? `translate3d(0, ${state.top}px, 0)` : void 0,\n          color: highlightColor\n        });\n      }\n    });\n    const getRect = (scrollParent, scrollParentRect) => {\n      const rootRect = useRect(root);\n      state.rect.height = rootRect.height;\n      if (scrollParent === window || scrollParent === document.body) {\n        state.rect.top = rootRect.top + getRootScrollTop();\n      } else {\n        state.rect.top = rootRect.top + getScrollTop(scrollParent) - scrollParentRect.top;\n      }\n      return state.rect;\n    };\n    useExpose({\n      state,\n      getRect\n    });\n    return () => {\n      const sticky = isSticky();\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"style\": {\n          height: sticky ? `${state.rect.height}px` : void 0\n        }\n      }, [_createVNode(\"div\", {\n        \"style\": anchorStyle.value,\n        \"class\": [bem({\n          sticky\n        }), {\n          [BORDER_BOTTOM]: sticky\n        }]\n      }, [slots.default ? slots.default() : props.index])]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  indexAnchorProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC3F,SAASC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACxG,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,kBAAkB;AACjE,SAASC,OAAO,EAAEC,SAAS,QAAQ,WAAW;AAC9C,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGR,eAAe,CAAC,cAAc,CAAC;AACnD,MAAMS,gBAAgB,GAAG;EACvBC,KAAK,EAAEb;AACT,CAAC;AACD,IAAIc,aAAa,GAAGlB,eAAe,CAAC;EAClCc,IAAI;EACJK,KAAK,EAAEH,gBAAgB;EACvBI,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGxB,QAAQ,CAAC;MACrByB,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;QACJF,GAAG,EAAE,CAAC;QACNG,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE;IACV,CAAC,CAAC;IACF,MAAMC,IAAI,GAAGhC,GAAG,CAAC,CAAC;IAClB,MAAM;MACJiC;IACF,CAAC,GAAGlB,SAAS,CAACJ,aAAa,CAAC;IAC5B,IAAI,CAACsB,MAAM,EAAE;MACX,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,+DAA+D,CAAC;MAChF;MACA;IACF;IACA,MAAMC,QAAQ,GAAGA,CAAA,KAAMd,KAAK,CAACM,MAAM,IAAIE,MAAM,CAACX,KAAK,CAACkB,MAAM;IAC1D,MAAMC,WAAW,GAAGvC,QAAQ,CAAC,MAAM;MACjC,MAAM;QACJwC,MAAM;QACNC;MACF,CAAC,GAAGV,MAAM,CAACX,KAAK;MAChB,IAAIiB,QAAQ,CAAC,CAAC,EAAE;QACd,OAAOjC,MAAM,CAACG,cAAc,CAACiC,MAAM,CAAC,EAAE;UACpCf,IAAI,EAAEF,KAAK,CAACE,IAAI,GAAG,GAAGF,KAAK,CAACE,IAAI,IAAI,GAAG,KAAK,CAAC;UAC7CG,KAAK,EAAEL,KAAK,CAACK,KAAK,GAAG,GAAGL,KAAK,CAACK,KAAK,IAAI,GAAG,KAAK,CAAC;UAChDc,SAAS,EAAEnB,KAAK,CAACC,GAAG,GAAG,kBAAkBD,KAAK,CAACC,GAAG,QAAQ,GAAG,KAAK,CAAC;UACnEmB,KAAK,EAAEF;QACT,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,MAAMG,OAAO,GAAGA,CAACC,YAAY,EAAEC,gBAAgB,KAAK;MAClD,MAAMC,QAAQ,GAAGnC,OAAO,CAACkB,IAAI,CAAC;MAC9BP,KAAK,CAACG,IAAI,CAACC,MAAM,GAAGoB,QAAQ,CAACpB,MAAM;MACnC,IAAIkB,YAAY,KAAKG,MAAM,IAAIH,YAAY,KAAKI,QAAQ,CAACC,IAAI,EAAE;QAC7D3B,KAAK,CAACG,IAAI,CAACF,GAAG,GAAGuB,QAAQ,CAACvB,GAAG,GAAGb,gBAAgB,CAAC,CAAC;MACpD,CAAC,MAAM;QACLY,KAAK,CAACG,IAAI,CAACF,GAAG,GAAGuB,QAAQ,CAACvB,GAAG,GAAGd,YAAY,CAACmC,YAAY,CAAC,GAAGC,gBAAgB,CAACtB,GAAG;MACnF;MACA,OAAOD,KAAK,CAACG,IAAI;IACnB,CAAC;IACDZ,SAAS,CAAC;MACRS,KAAK;MACLqB;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,MAAMN,MAAM,GAAGD,QAAQ,CAAC,CAAC;MACzB,OAAOlC,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAE2B,IAAI;QACX,OAAO,EAAE;UACPH,MAAM,EAAEW,MAAM,GAAG,GAAGf,KAAK,CAACG,IAAI,CAACC,MAAM,IAAI,GAAG,KAAK;QACnD;MACF,CAAC,EAAE,CAACxB,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEoC,WAAW,CAACY,KAAK;QAC1B,OAAO,EAAE,CAACnC,GAAG,CAAC;UACZsB;QACF,CAAC,CAAC,EAAE;UACF,CAAChC,aAAa,GAAGgC;QACnB,CAAC;MACH,CAAC,EAAE,CAAChB,KAAK,CAAC8B,OAAO,GAAG9B,KAAK,CAAC8B,OAAO,CAAC,CAAC,GAAGhC,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEC,aAAa,IAAIiC,OAAO,EACxBnC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}