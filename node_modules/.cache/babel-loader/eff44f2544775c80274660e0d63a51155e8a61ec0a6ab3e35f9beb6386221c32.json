{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Tab from \"./Tab.mjs\";\nconst Tab = withInstall(_Tab);\nvar stdin_default = Tab;\nimport { tabProps } from \"./Tab.mjs\";\nimport { useTabStatus, useAllTabStatus } from \"../composables/use-tab-status.mjs\";\nexport { Tab, stdin_default as default, tabProps, useAllTabStatus, useTabStatus };", "map": {"version": 3, "names": ["withInstall", "_Tab", "Tab", "stdin_default", "tabProps", "useTabStatus", "useAllTabStatus", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tab/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Tab from \"./Tab.mjs\";\nconst Tab = withInstall(_Tab);\nvar stdin_default = Tab;\nimport { tabProps } from \"./Tab.mjs\";\nimport { useTabStatus, useAllTabStatus } from \"../composables/use-tab-status.mjs\";\nexport {\n  Tab,\n  stdin_default as default,\n  tabProps,\n  useAllTabStatus,\n  useTabStatus\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,IAAI,MAAM,WAAW;AAC5B,MAAMC,GAAG,GAAGF,WAAW,CAACC,IAAI,CAAC;AAC7B,IAAIE,aAAa,GAAGD,GAAG;AACvB,SAASE,QAAQ,QAAQ,WAAW;AACpC,SAASC,YAAY,EAAEC,eAAe,QAAQ,mCAAmC;AACjF,SACEJ,GAAG,EACHC,aAAa,IAAII,OAAO,EACxBH,QAAQ,EACRE,eAAe,EACfD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}