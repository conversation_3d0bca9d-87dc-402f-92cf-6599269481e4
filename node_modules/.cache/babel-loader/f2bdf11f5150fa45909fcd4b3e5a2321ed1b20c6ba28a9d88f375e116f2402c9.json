{"ast": null, "code": "import { ref, watch, onMounted, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, makeRequiredProp, createNamespace } from \"../utils/index.mjs\";\nimport { Swipe } from \"../swipe/index.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"tabs\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    count: makeRequiredProp(Number),\n    inited: <PERSON><PERSON><PERSON>,\n    animated: <PERSON><PERSON><PERSON>,\n    duration: makeRequiredProp(numericProp),\n    swipeable: <PERSON><PERSON><PERSON>,\n    lazyRender: Boolean,\n    currentIndex: makeRequiredProp(Number)\n  },\n  emits: [\"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const swipeRef = ref();\n    const onChange = index => emit(\"change\", index);\n    const renderChildren = () => {\n      var _a;\n      const Content = (_a = slots.default) == null ? void 0 : _a.call(slots);\n      if (props.animated || props.swipeable) {\n        return _createVNode(Swipe, {\n          \"ref\": swipeRef,\n          \"loop\": false,\n          \"class\": bem(\"track\"),\n          \"duration\": +props.duration * 1e3,\n          \"touchable\": props.swipeable,\n          \"lazyRender\": props.lazyRender,\n          \"showIndicators\": false,\n          \"onChange\": onChange\n        }, {\n          default: () => [Content]\n        });\n      }\n      return Content;\n    };\n    const swipeToCurrentTab = index => {\n      const swipe = swipeRef.value;\n      if (swipe && swipe.state.active !== index) {\n        swipe.swipeTo(index, {\n          immediate: !props.inited\n        });\n      }\n    };\n    watch(() => props.currentIndex, swipeToCurrentTab);\n    onMounted(() => {\n      swipeToCurrentTab(props.currentIndex);\n    });\n    useExpose({\n      swipeRef\n    });\n    return () => _createVNode(\"div\", {\n      \"class\": bem(\"content\", {\n        animated: props.animated || props.swipeable\n      })\n    }, [renderChildren()]);\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["ref", "watch", "onMounted", "defineComponent", "createVNode", "_createVNode", "numericProp", "makeRequiredProp", "createNamespace", "Swipe", "useExpose", "name", "bem", "stdin_default", "props", "count", "Number", "inited", "Boolean", "animated", "duration", "swipeable", "lazy<PERSON>ender", "currentIndex", "emits", "setup", "emit", "slots", "swipeRef", "onChange", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_a", "Content", "default", "call", "swipeToCurrentTab", "swipe", "value", "state", "active", "swipeTo", "immediate"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tabs/TabsContent.mjs"], "sourcesContent": ["import { ref, watch, onMounted, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, makeRequiredProp, createNamespace } from \"../utils/index.mjs\";\nimport { Swipe } from \"../swipe/index.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"tabs\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    count: makeRequiredProp(Number),\n    inited: <PERSON><PERSON><PERSON>,\n    animated: <PERSON><PERSON><PERSON>,\n    duration: makeRequiredProp(numericProp),\n    swipeable: <PERSON><PERSON><PERSON>,\n    lazyRender: Boolean,\n    currentIndex: makeRequiredProp(Number)\n  },\n  emits: [\"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const swipeRef = ref();\n    const onChange = (index) => emit(\"change\", index);\n    const renderChildren = () => {\n      var _a;\n      const Content = (_a = slots.default) == null ? void 0 : _a.call(slots);\n      if (props.animated || props.swipeable) {\n        return _createVNode(Swipe, {\n          \"ref\": swipeRef,\n          \"loop\": false,\n          \"class\": bem(\"track\"),\n          \"duration\": +props.duration * 1e3,\n          \"touchable\": props.swipeable,\n          \"lazyRender\": props.lazyRender,\n          \"showIndicators\": false,\n          \"onChange\": onChange\n        }, {\n          default: () => [Content]\n        });\n      }\n      return Content;\n    };\n    const swipeToCurrentTab = (index) => {\n      const swipe = swipeRef.value;\n      if (swipe && swipe.state.active !== index) {\n        swipe.swipeTo(index, {\n          immediate: !props.inited\n        });\n      }\n    };\n    watch(() => props.currentIndex, swipeToCurrentTab);\n    onMounted(() => {\n      swipeToCurrentTab(props.currentIndex);\n    });\n    useExpose({\n      swipeRef\n    });\n    return () => _createVNode(\"div\", {\n      \"class\": bem(\"content\", {\n        animated: props.animated || props.swipeable\n      })\n    }, [renderChildren()]);\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACzF,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,oBAAoB;AACnF,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,MAAM,CAAC;AAC3C,IAAIK,aAAa,GAAGV,eAAe,CAAC;EAClCQ,IAAI;EACJG,KAAK,EAAE;IACLC,KAAK,EAAER,gBAAgB,CAACS,MAAM,CAAC;IAC/BC,MAAM,EAAEC,OAAO;IACfC,QAAQ,EAAED,OAAO;IACjBE,QAAQ,EAAEb,gBAAgB,CAACD,WAAW,CAAC;IACvCe,SAAS,EAAEH,OAAO;IAClBI,UAAU,EAAEJ,OAAO;IACnBK,YAAY,EAAEhB,gBAAgB,CAACS,MAAM;EACvC,CAAC;EACDQ,KAAK,EAAE,CAAC,QAAQ,CAAC;EACjBC,KAAKA,CAACX,KAAK,EAAE;IACXY,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,QAAQ,GAAG5B,GAAG,CAAC,CAAC;IACtB,MAAM6B,QAAQ,GAAIC,KAAK,IAAKJ,IAAI,CAAC,QAAQ,EAAEI,KAAK,CAAC;IACjD,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIC,EAAE;MACN,MAAMC,OAAO,GAAG,CAACD,EAAE,GAAGL,KAAK,CAACO,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,IAAI,CAACR,KAAK,CAAC;MACtE,IAAIb,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACO,SAAS,EAAE;QACrC,OAAOhB,YAAY,CAACI,KAAK,EAAE;UACzB,KAAK,EAAEmB,QAAQ;UACf,MAAM,EAAE,KAAK;UACb,OAAO,EAAEhB,GAAG,CAAC,OAAO,CAAC;UACrB,UAAU,EAAE,CAACE,KAAK,CAACM,QAAQ,GAAG,GAAG;UACjC,WAAW,EAAEN,KAAK,CAACO,SAAS;UAC5B,YAAY,EAAEP,KAAK,CAACQ,UAAU;UAC9B,gBAAgB,EAAE,KAAK;UACvB,UAAU,EAAEO;QACd,CAAC,EAAE;UACDK,OAAO,EAAEA,CAAA,KAAM,CAACD,OAAO;QACzB,CAAC,CAAC;MACJ;MACA,OAAOA,OAAO;IAChB,CAAC;IACD,MAAMG,iBAAiB,GAAIN,KAAK,IAAK;MACnC,MAAMO,KAAK,GAAGT,QAAQ,CAACU,KAAK;MAC5B,IAAID,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACC,MAAM,KAAKV,KAAK,EAAE;QACzCO,KAAK,CAACI,OAAO,CAACX,KAAK,EAAE;UACnBY,SAAS,EAAE,CAAC5B,KAAK,CAACG;QACpB,CAAC,CAAC;MACJ;IACF,CAAC;IACDhB,KAAK,CAAC,MAAMa,KAAK,CAACS,YAAY,EAAEa,iBAAiB,CAAC;IAClDlC,SAAS,CAAC,MAAM;MACdkC,iBAAiB,CAACtB,KAAK,CAACS,YAAY,CAAC;IACvC,CAAC,CAAC;IACFb,SAAS,CAAC;MACRkB;IACF,CAAC,CAAC;IACF,OAAO,MAAMvB,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEO,GAAG,CAAC,SAAS,EAAE;QACtBO,QAAQ,EAAEL,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACO;MACpC,CAAC;IACH,CAAC,EAAE,CAACU,cAAc,CAAC,CAAC,CAAC,CAAC;EACxB;AACF,CAAC,CAAC;AACF,SACElB,aAAa,IAAIqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}