{"ast": null, "code": "import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, addUnit, isNumeric, truthProp, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"badge\");\nconst badgeProps = {\n  dot: Boolean,\n  max: numericProp,\n  tag: makeStringProp(\"div\"),\n  color: String,\n  offset: Array,\n  content: numericProp,\n  showZero: truthProp,\n  position: makeStringProp(\"top-right\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: badgeProps,\n  setup(props, {\n    slots\n  }) {\n    const hasContent = () => {\n      if (slots.content) {\n        return true;\n      }\n      const {\n        content,\n        showZero\n      } = props;\n      return isDef(content) && content !== \"\" && (showZero || content !== 0 && content !== \"0\");\n    };\n    const renderContent = () => {\n      const {\n        dot,\n        max,\n        content\n      } = props;\n      if (!dot && hasContent()) {\n        if (slots.content) {\n          return slots.content();\n        }\n        if (isDef(max) && isNumeric(content) && +content > +max) {\n          return `${max}+`;\n        }\n        return content;\n      }\n    };\n    const getOffsetWithMinusString = val => val.startsWith(\"-\") ? val.replace(\"-\", \"\") : `-${val}`;\n    const style = computed(() => {\n      const style2 = {\n        background: props.color\n      };\n      if (props.offset) {\n        const [x, y] = props.offset;\n        const {\n          position\n        } = props;\n        const [offsetY, offsetX] = position.split(\"-\");\n        if (slots.default) {\n          if (typeof y === \"number\") {\n            style2[offsetY] = addUnit(offsetY === \"top\" ? y : -y);\n          } else {\n            style2[offsetY] = offsetY === \"top\" ? addUnit(y) : getOffsetWithMinusString(y);\n          }\n          if (typeof x === \"number\") {\n            style2[offsetX] = addUnit(offsetX === \"left\" ? x : -x);\n          } else {\n            style2[offsetX] = offsetX === \"left\" ? addUnit(x) : getOffsetWithMinusString(x);\n          }\n        } else {\n          style2.marginTop = addUnit(y);\n          style2.marginLeft = addUnit(x);\n        }\n      }\n      return style2;\n    });\n    const renderBadge = () => {\n      if (hasContent() || props.dot) {\n        return _createVNode(\"div\", {\n          \"class\": bem([props.position, {\n            dot: props.dot,\n            fixed: !!slots.default\n          }]),\n          \"style\": style.value\n        }, [renderContent()]);\n      }\n    };\n    return () => {\n      if (slots.default) {\n        const {\n          tag\n        } = props;\n        return _createVNode(tag, {\n          \"class\": bem(\"wrapper\")\n        }, {\n          default: () => [slots.default(), renderBadge()]\n        });\n      }\n      return renderBadge();\n    };\n  }\n});\nexport { badgeProps, stdin_default as default };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "isDef", "addUnit", "isNumeric", "truthProp", "numericProp", "makeStringProp", "createNamespace", "name", "bem", "badgeProps", "dot", "Boolean", "max", "tag", "color", "String", "offset", "Array", "content", "showZero", "position", "stdin_default", "props", "setup", "slots", "<PERSON><PERSON><PERSON><PERSON>", "renderContent", "getOffsetWithMinusString", "val", "startsWith", "replace", "style", "style2", "background", "x", "y", "offsetY", "offsetX", "split", "default", "marginTop", "marginLeft", "renderBadge", "fixed", "value"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/badge/Badge.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, addUnit, isNumeric, truthProp, numericProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"badge\");\nconst badgeProps = {\n  dot: Boolean,\n  max: numericProp,\n  tag: makeStringProp(\"div\"),\n  color: String,\n  offset: Array,\n  content: numericProp,\n  showZero: truthProp,\n  position: makeStringProp(\"top-right\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: badgeProps,\n  setup(props, {\n    slots\n  }) {\n    const hasContent = () => {\n      if (slots.content) {\n        return true;\n      }\n      const {\n        content,\n        showZero\n      } = props;\n      return isDef(content) && content !== \"\" && (showZero || content !== 0 && content !== \"0\");\n    };\n    const renderContent = () => {\n      const {\n        dot,\n        max,\n        content\n      } = props;\n      if (!dot && hasContent()) {\n        if (slots.content) {\n          return slots.content();\n        }\n        if (isDef(max) && isNumeric(content) && +content > +max) {\n          return `${max}+`;\n        }\n        return content;\n      }\n    };\n    const getOffsetWithMinusString = (val) => val.startsWith(\"-\") ? val.replace(\"-\", \"\") : `-${val}`;\n    const style = computed(() => {\n      const style2 = {\n        background: props.color\n      };\n      if (props.offset) {\n        const [x, y] = props.offset;\n        const {\n          position\n        } = props;\n        const [offsetY, offsetX] = position.split(\"-\");\n        if (slots.default) {\n          if (typeof y === \"number\") {\n            style2[offsetY] = addUnit(offsetY === \"top\" ? y : -y);\n          } else {\n            style2[offsetY] = offsetY === \"top\" ? addUnit(y) : getOffsetWithMinusString(y);\n          }\n          if (typeof x === \"number\") {\n            style2[offsetX] = addUnit(offsetX === \"left\" ? x : -x);\n          } else {\n            style2[offsetX] = offsetX === \"left\" ? addUnit(x) : getOffsetWithMinusString(x);\n          }\n        } else {\n          style2.marginTop = addUnit(y);\n          style2.marginLeft = addUnit(x);\n        }\n      }\n      return style2;\n    });\n    const renderBadge = () => {\n      if (hasContent() || props.dot) {\n        return _createVNode(\"div\", {\n          \"class\": bem([props.position, {\n            dot: props.dot,\n            fixed: !!slots.default\n          }]),\n          \"style\": style.value\n        }, [renderContent()]);\n      }\n    };\n    return () => {\n      if (slots.default) {\n        const {\n          tag\n        } = props;\n        return _createVNode(tag, {\n          \"class\": bem(\"wrapper\")\n        }, {\n          default: () => [slots.default(), renderBadge()]\n        });\n      }\n      return renderBadge();\n    };\n  }\n});\nexport {\n  badgeProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACvH,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGF,eAAe,CAAC,OAAO,CAAC;AAC5C,MAAMG,UAAU,GAAG;EACjBC,GAAG,EAAEC,OAAO;EACZC,GAAG,EAAER,WAAW;EAChBS,GAAG,EAAER,cAAc,CAAC,KAAK,CAAC;EAC1BS,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAEC,KAAK;EACbC,OAAO,EAAEd,WAAW;EACpBe,QAAQ,EAAEhB,SAAS;EACnBiB,QAAQ,EAAEf,cAAc,CAAC,WAAW;AACtC,CAAC;AACD,IAAIgB,aAAa,GAAGxB,eAAe,CAAC;EAClCU,IAAI;EACJe,KAAK,EAAEb,UAAU;EACjBc,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAID,KAAK,CAACN,OAAO,EAAE;QACjB,OAAO,IAAI;MACb;MACA,MAAM;QACJA,OAAO;QACPC;MACF,CAAC,GAAGG,KAAK;MACT,OAAOtB,KAAK,CAACkB,OAAO,CAAC,IAAIA,OAAO,KAAK,EAAE,KAAKC,QAAQ,IAAID,OAAO,KAAK,CAAC,IAAIA,OAAO,KAAK,GAAG,CAAC;IAC3F,CAAC;IACD,MAAMQ,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAM;QACJhB,GAAG;QACHE,GAAG;QACHM;MACF,CAAC,GAAGI,KAAK;MACT,IAAI,CAACZ,GAAG,IAAIe,UAAU,CAAC,CAAC,EAAE;QACxB,IAAID,KAAK,CAACN,OAAO,EAAE;UACjB,OAAOM,KAAK,CAACN,OAAO,CAAC,CAAC;QACxB;QACA,IAAIlB,KAAK,CAACY,GAAG,CAAC,IAAIV,SAAS,CAACgB,OAAO,CAAC,IAAI,CAACA,OAAO,GAAG,CAACN,GAAG,EAAE;UACvD,OAAO,GAAGA,GAAG,GAAG;QAClB;QACA,OAAOM,OAAO;MAChB;IACF,CAAC;IACD,MAAMS,wBAAwB,GAAIC,GAAG,IAAKA,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,GAAGD,GAAG,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,IAAIF,GAAG,EAAE;IAChG,MAAMG,KAAK,GAAGnC,QAAQ,CAAC,MAAM;MAC3B,MAAMoC,MAAM,GAAG;QACbC,UAAU,EAAEX,KAAK,CAACR;MACpB,CAAC;MACD,IAAIQ,KAAK,CAACN,MAAM,EAAE;QAChB,MAAM,CAACkB,CAAC,EAAEC,CAAC,CAAC,GAAGb,KAAK,CAACN,MAAM;QAC3B,MAAM;UACJI;QACF,CAAC,GAAGE,KAAK;QACT,MAAM,CAACc,OAAO,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAACkB,KAAK,CAAC,GAAG,CAAC;QAC9C,IAAId,KAAK,CAACe,OAAO,EAAE;UACjB,IAAI,OAAOJ,CAAC,KAAK,QAAQ,EAAE;YACzBH,MAAM,CAACI,OAAO,CAAC,GAAGnC,OAAO,CAACmC,OAAO,KAAK,KAAK,GAAGD,CAAC,GAAG,CAACA,CAAC,CAAC;UACvD,CAAC,MAAM;YACLH,MAAM,CAACI,OAAO,CAAC,GAAGA,OAAO,KAAK,KAAK,GAAGnC,OAAO,CAACkC,CAAC,CAAC,GAAGR,wBAAwB,CAACQ,CAAC,CAAC;UAChF;UACA,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;YACzBF,MAAM,CAACK,OAAO,CAAC,GAAGpC,OAAO,CAACoC,OAAO,KAAK,MAAM,GAAGH,CAAC,GAAG,CAACA,CAAC,CAAC;UACxD,CAAC,MAAM;YACLF,MAAM,CAACK,OAAO,CAAC,GAAGA,OAAO,KAAK,MAAM,GAAGpC,OAAO,CAACiC,CAAC,CAAC,GAAGP,wBAAwB,CAACO,CAAC,CAAC;UACjF;QACF,CAAC,MAAM;UACLF,MAAM,CAACQ,SAAS,GAAGvC,OAAO,CAACkC,CAAC,CAAC;UAC7BH,MAAM,CAACS,UAAU,GAAGxC,OAAO,CAACiC,CAAC,CAAC;QAChC;MACF;MACA,OAAOF,MAAM;IACf,CAAC,CAAC;IACF,MAAMU,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIjB,UAAU,CAAC,CAAC,IAAIH,KAAK,CAACZ,GAAG,EAAE;QAC7B,OAAOX,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAES,GAAG,CAAC,CAACc,KAAK,CAACF,QAAQ,EAAE;YAC5BV,GAAG,EAAEY,KAAK,CAACZ,GAAG;YACdiC,KAAK,EAAE,CAAC,CAACnB,KAAK,CAACe;UACjB,CAAC,CAAC,CAAC;UACH,OAAO,EAAER,KAAK,CAACa;QACjB,CAAC,EAAE,CAAClB,aAAa,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IACD,OAAO,MAAM;MACX,IAAIF,KAAK,CAACe,OAAO,EAAE;QACjB,MAAM;UACJ1B;QACF,CAAC,GAAGS,KAAK;QACT,OAAOvB,YAAY,CAACc,GAAG,EAAE;UACvB,OAAO,EAAEL,GAAG,CAAC,SAAS;QACxB,CAAC,EAAE;UACD+B,OAAO,EAAEA,CAAA,KAAM,CAACf,KAAK,CAACe,OAAO,CAAC,CAAC,EAAEG,WAAW,CAAC,CAAC;QAChD,CAAC,CAAC;MACJ;MACA,OAAOA,WAAW,CAAC,CAAC;IACtB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEjC,UAAU,EACVY,aAAa,IAAIkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}