{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, numericProp, unknownProp, createNamespace } from \"../utils/index.mjs\";\nimport { useCustomFieldValue } from \"@vant/use\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"switch\");\nconst switchProps = {\n  size: numericProp,\n  loading: Boolean,\n  disabled: <PERSON>olean,\n  modelValue: unknownProp,\n  activeColor: String,\n  inactiveColor: String,\n  activeValue: {\n    type: unknownProp,\n    default: true\n  },\n  inactiveValue: {\n    type: unknownProp,\n    default: false\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: switchProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const isChecked = () => props.modelValue === props.activeValue;\n    const onClick = () => {\n      if (!props.disabled && !props.loading) {\n        const newValue = isChecked() ? props.inactiveValue : props.activeValue;\n        emit(\"update:modelValue\", newValue);\n        emit(\"change\", newValue);\n      }\n    };\n    const renderLoading = () => {\n      if (props.loading) {\n        const color = isChecked() ? props.activeColor : props.inactiveColor;\n        return _createVNode(Loading, {\n          \"class\": bem(\"loading\"),\n          \"color\": color\n        }, null);\n      }\n      if (slots.node) {\n        return slots.node();\n      }\n    };\n    useCustomFieldValue(() => props.modelValue);\n    return () => {\n      var _a;\n      const {\n        size,\n        loading,\n        disabled,\n        activeColor,\n        inactiveColor\n      } = props;\n      const checked = isChecked();\n      const style = {\n        fontSize: addUnit(size),\n        backgroundColor: checked ? activeColor : inactiveColor\n      };\n      return _createVNode(\"div\", {\n        \"role\": \"switch\",\n        \"class\": bem({\n          on: checked,\n          loading,\n          disabled\n        }),\n        \"style\": style,\n        \"tabindex\": disabled ? void 0 : 0,\n        \"aria-checked\": checked,\n        \"onClick\": onClick\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"node\")\n      }, [renderLoading()]), (_a = slots.background) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { stdin_default as default, switchProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "addUnit", "numericProp", "unknownProp", "createNamespace", "useCustomFieldValue", "Loading", "name", "bem", "switchProps", "size", "loading", "Boolean", "disabled", "modelValue", "activeColor", "String", "inactiveColor", "activeValue", "type", "default", "inactiveValue", "stdin_default", "props", "emits", "setup", "emit", "slots", "isChecked", "onClick", "newValue", "renderLoading", "color", "node", "_a", "checked", "style", "fontSize", "backgroundColor", "on", "background", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/switch/Switch.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { addUnit, numericProp, unknownProp, createNamespace } from \"../utils/index.mjs\";\nimport { useCustomFieldValue } from \"@vant/use\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"switch\");\nconst switchProps = {\n  size: numericProp,\n  loading: Boolean,\n  disabled: <PERSON>olean,\n  modelValue: unknownProp,\n  activeColor: String,\n  inactiveColor: String,\n  activeValue: {\n    type: unknownProp,\n    default: true\n  },\n  inactiveValue: {\n    type: unknownProp,\n    default: false\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: switchProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const isChecked = () => props.modelValue === props.activeValue;\n    const onClick = () => {\n      if (!props.disabled && !props.loading) {\n        const newValue = isChecked() ? props.inactiveValue : props.activeValue;\n        emit(\"update:modelValue\", newValue);\n        emit(\"change\", newValue);\n      }\n    };\n    const renderLoading = () => {\n      if (props.loading) {\n        const color = isChecked() ? props.activeColor : props.inactiveColor;\n        return _createVNode(Loading, {\n          \"class\": bem(\"loading\"),\n          \"color\": color\n        }, null);\n      }\n      if (slots.node) {\n        return slots.node();\n      }\n    };\n    useCustomFieldValue(() => props.modelValue);\n    return () => {\n      var _a;\n      const {\n        size,\n        loading,\n        disabled,\n        activeColor,\n        inactiveColor\n      } = props;\n      const checked = isChecked();\n      const style = {\n        fontSize: addUnit(size),\n        backgroundColor: checked ? activeColor : inactiveColor\n      };\n      return _createVNode(\"div\", {\n        \"role\": \"switch\",\n        \"class\": bem({\n          on: checked,\n          loading,\n          disabled\n        }),\n        \"style\": style,\n        \"tabindex\": disabled ? void 0 : 0,\n        \"aria-checked\": checked,\n        \"onClick\": onClick\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"node\")\n      }, [renderLoading()]), (_a = slots.background) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  switchProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AACvF,SAASC,mBAAmB,QAAQ,WAAW;AAC/C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,QAAQ,CAAC;AAC7C,MAAMK,WAAW,GAAG;EAClBC,IAAI,EAAER,WAAW;EACjBS,OAAO,EAAEC,OAAO;EAChBC,QAAQ,EAAED,OAAO;EACjBE,UAAU,EAAEX,WAAW;EACvBY,WAAW,EAAEC,MAAM;EACnBC,aAAa,EAAED,MAAM;EACrBE,WAAW,EAAE;IACXC,IAAI,EAAEhB,WAAW;IACjBiB,OAAO,EAAE;EACX,CAAC;EACDC,aAAa,EAAE;IACbF,IAAI,EAAEhB,WAAW;IACjBiB,OAAO,EAAE;EACX;AACF,CAAC;AACD,IAAIE,aAAa,GAAGxB,eAAe,CAAC;EAClCS,IAAI;EACJgB,KAAK,EAAEd,WAAW;EAClBe,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,SAAS,GAAGA,CAAA,KAAML,KAAK,CAACT,UAAU,KAAKS,KAAK,CAACL,WAAW;IAC9D,MAAMW,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAACN,KAAK,CAACV,QAAQ,IAAI,CAACU,KAAK,CAACZ,OAAO,EAAE;QACrC,MAAMmB,QAAQ,GAAGF,SAAS,CAAC,CAAC,GAAGL,KAAK,CAACF,aAAa,GAAGE,KAAK,CAACL,WAAW;QACtEQ,IAAI,CAAC,mBAAmB,EAAEI,QAAQ,CAAC;QACnCJ,IAAI,CAAC,QAAQ,EAAEI,QAAQ,CAAC;MAC1B;IACF,CAAC;IACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIR,KAAK,CAACZ,OAAO,EAAE;QACjB,MAAMqB,KAAK,GAAGJ,SAAS,CAAC,CAAC,GAAGL,KAAK,CAACR,WAAW,GAAGQ,KAAK,CAACN,aAAa;QACnE,OAAOjB,YAAY,CAACM,OAAO,EAAE;UAC3B,OAAO,EAAEE,GAAG,CAAC,SAAS,CAAC;UACvB,OAAO,EAAEwB;QACX,CAAC,EAAE,IAAI,CAAC;MACV;MACA,IAAIL,KAAK,CAACM,IAAI,EAAE;QACd,OAAON,KAAK,CAACM,IAAI,CAAC,CAAC;MACrB;IACF,CAAC;IACD5B,mBAAmB,CAAC,MAAMkB,KAAK,CAACT,UAAU,CAAC;IAC3C,OAAO,MAAM;MACX,IAAIoB,EAAE;MACN,MAAM;QACJxB,IAAI;QACJC,OAAO;QACPE,QAAQ;QACRE,WAAW;QACXE;MACF,CAAC,GAAGM,KAAK;MACT,MAAMY,OAAO,GAAGP,SAAS,CAAC,CAAC;MAC3B,MAAMQ,KAAK,GAAG;QACZC,QAAQ,EAAEpC,OAAO,CAACS,IAAI,CAAC;QACvB4B,eAAe,EAAEH,OAAO,GAAGpB,WAAW,GAAGE;MAC3C,CAAC;MACD,OAAOjB,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,QAAQ;QAChB,OAAO,EAAEQ,GAAG,CAAC;UACX+B,EAAE,EAAEJ,OAAO;UACXxB,OAAO;UACPE;QACF,CAAC,CAAC;QACF,OAAO,EAAEuB,KAAK;QACd,UAAU,EAAEvB,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;QACjC,cAAc,EAAEsB,OAAO;QACvB,SAAS,EAAEN;MACb,CAAC,EAAE,CAAC7B,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEQ,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACuB,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,CAACG,EAAE,GAAGP,KAAK,CAACa,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,EAAE,CAACO,IAAI,CAACd,KAAK,CAAC,CAAC,CAAC;IACpF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEL,aAAa,IAAIF,OAAO,EACxBX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}