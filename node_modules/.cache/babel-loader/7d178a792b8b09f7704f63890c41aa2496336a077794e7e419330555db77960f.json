{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n// src/utils.ts\nvar inBrowser = typeof window !== \"undefined\";\nvar supportsPassive = true;\nfunction raf(fn) {\n  return inBrowser ? requestAnimationFrame(fn) : -1;\n}\nfunction cancelRaf(id) {\n  if (inBrowser) {\n    cancelAnimationFrame(id);\n  }\n}\nfunction doubleRaf(fn) {\n  raf(() => raf(fn));\n}\n\n// src/useRect/index.ts\nimport { unref } from \"vue\";\nvar isWindow = val => val === window;\nvar makeDOMRect = (width2, height2) => ({\n  top: 0,\n  left: 0,\n  right: width2,\n  bottom: height2,\n  width: width2,\n  height: height2\n});\nvar useRect = elementOrRef => {\n  const element = unref(elementOrRef);\n  if (isWindow(element)) {\n    const width2 = element.innerWidth;\n    const height2 = element.innerHeight;\n    return makeDOMRect(width2, height2);\n  }\n  if (element == null ? void 0 : element.getBoundingClientRect) {\n    return element.getBoundingClientRect();\n  }\n  return makeDOMRect(0, 0);\n};\n\n// src/useToggle/index.ts\nimport { ref } from \"vue\";\nfunction useToggle(defaultValue = false) {\n  const state = ref(defaultValue);\n  const toggle = (value = !state.value) => {\n    state.value = value;\n  };\n  return [state, toggle];\n}\n\n// src/useRelation/useParent.ts\nimport { ref as ref2, inject, computed, onUnmounted, getCurrentInstance } from \"vue\";\nfunction useParent(key) {\n  const parent = inject(key, null);\n  if (parent) {\n    const instance = getCurrentInstance();\n    const {\n      link,\n      unlink,\n      internalChildren\n    } = parent;\n    link(instance);\n    onUnmounted(() => unlink(instance));\n    const index = computed(() => internalChildren.indexOf(instance));\n    return {\n      parent,\n      index\n    };\n  }\n  return {\n    parent: null,\n    index: ref2(-1)\n  };\n}\n\n// src/useRelation/useChildren.ts\nimport { isVNode, provide, reactive, getCurrentInstance as getCurrentInstance2 } from \"vue\";\nfunction flattenVNodes(children) {\n  const result = [];\n  const traverse = children2 => {\n    if (Array.isArray(children2)) {\n      children2.forEach(child => {\n        var _a;\n        if (isVNode(child)) {\n          result.push(child);\n          if ((_a = child.component) == null ? void 0 : _a.subTree) {\n            result.push(child.component.subTree);\n            traverse(child.component.subTree.children);\n          }\n          if (child.children) {\n            traverse(child.children);\n          }\n        }\n      });\n    }\n  };\n  traverse(children);\n  return result;\n}\nvar findVNodeIndex = (vnodes, vnode) => {\n  const index = vnodes.indexOf(vnode);\n  if (index === -1) {\n    return vnodes.findIndex(item => vnode.key !== void 0 && vnode.key !== null && item.type === vnode.type && item.key === vnode.key);\n  }\n  return index;\n};\nfunction sortChildren(parent, publicChildren, internalChildren) {\n  const vnodes = flattenVNodes(parent.subTree.children);\n  internalChildren.sort((a, b) => findVNodeIndex(vnodes, a.vnode) - findVNodeIndex(vnodes, b.vnode));\n  const orderedPublicChildren = internalChildren.map(item => item.proxy);\n  publicChildren.sort((a, b) => {\n    const indexA = orderedPublicChildren.indexOf(a);\n    const indexB = orderedPublicChildren.indexOf(b);\n    return indexA - indexB;\n  });\n}\nfunction useChildren(key) {\n  const publicChildren = reactive([]);\n  const internalChildren = reactive([]);\n  const parent = getCurrentInstance2();\n  const linkChildren = value => {\n    const link = child => {\n      if (child.proxy) {\n        internalChildren.push(child);\n        publicChildren.push(child.proxy);\n        sortChildren(parent, publicChildren, internalChildren);\n      }\n    };\n    const unlink = child => {\n      const index = internalChildren.indexOf(child);\n      publicChildren.splice(index, 1);\n      internalChildren.splice(index, 1);\n    };\n    provide(key, Object.assign({\n      link,\n      unlink,\n      children: publicChildren,\n      internalChildren\n    }, value));\n  };\n  return {\n    children: publicChildren,\n    linkChildren\n  };\n}\n\n// src/useCountDown/index.ts\nimport { ref as ref3, computed as computed2, onActivated, onDeactivated, onBeforeUnmount } from \"vue\";\nvar SECOND = 1e3;\nvar MINUTE = 60 * SECOND;\nvar HOUR = 60 * MINUTE;\nvar DAY = 24 * HOUR;\nfunction parseTime(time) {\n  const days = Math.floor(time / DAY);\n  const hours = Math.floor(time % DAY / HOUR);\n  const minutes = Math.floor(time % HOUR / MINUTE);\n  const seconds = Math.floor(time % MINUTE / SECOND);\n  const milliseconds = Math.floor(time % SECOND);\n  return {\n    total: time,\n    days,\n    hours,\n    minutes,\n    seconds,\n    milliseconds\n  };\n}\nfunction isSameSecond(time1, time2) {\n  return Math.floor(time1 / 1e3) === Math.floor(time2 / 1e3);\n}\nfunction useCountDown(options) {\n  let rafId;\n  let endTime;\n  let counting;\n  let deactivated;\n  const remain = ref3(options.time);\n  const current = computed2(() => parseTime(remain.value));\n  const pause = () => {\n    counting = false;\n    cancelRaf(rafId);\n  };\n  const getCurrentRemain = () => Math.max(endTime - Date.now(), 0);\n  const setRemain = value => {\n    var _a, _b;\n    remain.value = value;\n    (_a = options.onChange) == null ? void 0 : _a.call(options, current.value);\n    if (value === 0) {\n      pause();\n      (_b = options.onFinish) == null ? void 0 : _b.call(options);\n    }\n  };\n  const microTick = () => {\n    rafId = raf(() => {\n      if (counting) {\n        setRemain(getCurrentRemain());\n        if (remain.value > 0) {\n          microTick();\n        }\n      }\n    });\n  };\n  const macroTick = () => {\n    rafId = raf(() => {\n      if (counting) {\n        const remainRemain = getCurrentRemain();\n        if (!isSameSecond(remainRemain, remain.value) || remainRemain === 0) {\n          setRemain(remainRemain);\n        }\n        if (remain.value > 0) {\n          macroTick();\n        }\n      }\n    });\n  };\n  const tick = () => {\n    if (!inBrowser) {\n      return;\n    }\n    if (options.millisecond) {\n      microTick();\n    } else {\n      macroTick();\n    }\n  };\n  const start = () => {\n    if (!counting) {\n      endTime = Date.now() + remain.value;\n      counting = true;\n      tick();\n    }\n  };\n  const reset = (totalTime = options.time) => {\n    pause();\n    remain.value = totalTime;\n  };\n  onBeforeUnmount(pause);\n  onActivated(() => {\n    if (deactivated) {\n      counting = true;\n      deactivated = false;\n      tick();\n    }\n  });\n  onDeactivated(() => {\n    if (counting) {\n      pause();\n      deactivated = true;\n    }\n  });\n  return {\n    start,\n    pause,\n    reset,\n    current\n  };\n}\n\n// src/useClickAway/index.ts\nimport { unref as unref3 } from \"vue\";\n\n// src/useEventListener/index.ts\nimport { watch, isRef, unref as unref2, onUnmounted as onUnmounted2, onDeactivated as onDeactivated2 } from \"vue\";\n\n// src/onMountedOrActivated/index.ts\nimport { nextTick, onMounted, onActivated as onActivated2 } from \"vue\";\nfunction onMountedOrActivated(hook) {\n  let mounted;\n  onMounted(() => {\n    hook();\n    nextTick(() => {\n      mounted = true;\n    });\n  });\n  onActivated2(() => {\n    if (mounted) {\n      hook();\n    }\n  });\n}\n\n// src/useEventListener/index.ts\nfunction useEventListener(type, listener, options = {}) {\n  if (!inBrowser) {\n    return;\n  }\n  const {\n    target = window,\n    passive = false,\n    capture = false\n  } = options;\n  let cleaned = false;\n  let attached;\n  const add = target2 => {\n    if (cleaned) {\n      return;\n    }\n    const element = unref2(target2);\n    if (element && !attached) {\n      element.addEventListener(type, listener, {\n        capture,\n        passive\n      });\n      attached = true;\n    }\n  };\n  const remove = target2 => {\n    if (cleaned) {\n      return;\n    }\n    const element = unref2(target2);\n    if (element && attached) {\n      element.removeEventListener(type, listener, capture);\n      attached = false;\n    }\n  };\n  onUnmounted2(() => remove(target));\n  onDeactivated2(() => remove(target));\n  onMountedOrActivated(() => add(target));\n  let stopWatch;\n  if (isRef(target)) {\n    stopWatch = watch(target, (val, oldVal) => {\n      remove(oldVal);\n      add(val);\n    });\n  }\n  return () => {\n    stopWatch == null ? void 0 : stopWatch();\n    remove(target);\n    cleaned = true;\n  };\n}\n\n// src/useClickAway/index.ts\nfunction useClickAway(target, listener, options = {}) {\n  if (!inBrowser) {\n    return;\n  }\n  const {\n    eventName = \"click\"\n  } = options;\n  const onClick = event => {\n    const targets = Array.isArray(target) ? target : [target];\n    const isClickAway = targets.every(item => {\n      const element = unref3(item);\n      return element && !element.contains(event.target);\n    });\n    if (isClickAway) {\n      listener(event);\n    }\n  };\n  useEventListener(eventName, onClick, {\n    target: document\n  });\n}\n\n// src/useWindowSize/index.ts\nimport { ref as ref4 } from \"vue\";\nvar width;\nvar height;\nfunction useWindowSize() {\n  if (!width) {\n    width = ref4(0);\n    height = ref4(0);\n    if (inBrowser) {\n      const update = () => {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      };\n      update();\n      window.addEventListener(\"resize\", update, {\n        passive: true\n      });\n      window.addEventListener(\"orientationchange\", update, {\n        passive: true\n      });\n    }\n  }\n  return {\n    width,\n    height\n  };\n}\n\n// src/useScrollParent/index.ts\nimport { ref as ref5, onMounted as onMounted2 } from \"vue\";\nvar overflowScrollReg = /scroll|auto|overlay/i;\nvar defaultRoot = inBrowser ? window : void 0;\nfunction isElement(node) {\n  const ELEMENT_NODE_TYPE = 1;\n  return node.tagName !== \"HTML\" && node.tagName !== \"BODY\" && node.nodeType === ELEMENT_NODE_TYPE;\n}\nfunction getScrollParent(el, root = defaultRoot) {\n  let node = el;\n  while (node && node !== root && isElement(node)) {\n    const {\n      overflowY\n    } = window.getComputedStyle(node);\n    if (overflowScrollReg.test(overflowY)) {\n      return node;\n    }\n    node = node.parentNode;\n  }\n  return root;\n}\nfunction useScrollParent(el, root = defaultRoot) {\n  const scrollParent = ref5();\n  onMounted2(() => {\n    if (el.value) {\n      scrollParent.value = getScrollParent(el.value, root);\n    }\n  });\n  return scrollParent;\n}\n\n// src/usePageVisibility/index.ts\nimport { ref as ref6 } from \"vue\";\nvar visibility;\nfunction usePageVisibility() {\n  if (!visibility) {\n    visibility = ref6(\"visible\");\n    if (inBrowser) {\n      const update = () => {\n        visibility.value = document.hidden ? \"hidden\" : \"visible\";\n      };\n      update();\n      window.addEventListener(\"visibilitychange\", update);\n    }\n  }\n  return visibility;\n}\n\n// src/useCustomFieldValue/index.ts\nimport { watch as watch2, inject as inject2 } from \"vue\";\nvar CUSTOM_FIELD_INJECTION_KEY = Symbol(\"van-field\");\nfunction useCustomFieldValue(customValue) {\n  const field = inject2(CUSTOM_FIELD_INJECTION_KEY, null);\n  if (field && !field.customValue.value) {\n    field.customValue.value = customValue;\n    watch2(customValue, () => {\n      field.resetValidation();\n      field.validateWithTrigger(\"onChange\");\n    });\n  }\n}\n\n// src/useRaf/index.ts\nfunction useRaf(fn, options) {\n  if (inBrowser) {\n    const {\n      interval = 0,\n      isLoop = false\n    } = options || {};\n    let start;\n    let isStopped = false;\n    let rafId;\n    const stop = () => {\n      isStopped = true;\n      cancelAnimationFrame(rafId);\n    };\n    const frameWrapper = timestamp => {\n      if (isStopped) return;\n      if (start === void 0) {\n        start = timestamp;\n      } else if (timestamp - start > interval) {\n        fn(timestamp);\n        start = timestamp;\n        if (!isLoop) {\n          stop();\n          return;\n        }\n      }\n      rafId = requestAnimationFrame(frameWrapper);\n    };\n    rafId = requestAnimationFrame(frameWrapper);\n    return stop;\n  }\n  return () => {};\n}\nexport { CUSTOM_FIELD_INJECTION_KEY, cancelRaf, doubleRaf, flattenVNodes, getScrollParent, inBrowser, onMountedOrActivated, raf, sortChildren, supportsPassive, useChildren, useClickAway, useCountDown, useCustomFieldValue, useEventListener, usePageVisibility, useParent, useRaf, useRect, useScrollParent, useToggle, useWindowSize };", "map": {"version": 3, "names": ["inBrowser", "window", "supportsPassive", "raf", "fn", "requestAnimationFrame", "cancelRaf", "id", "cancelAnimationFrame", "doubleRaf", "unref", "isWindow", "val", "makeDOMRect", "width2", "height2", "top", "left", "right", "bottom", "width", "height", "useRect", "elementOrRef", "element", "innerWidth", "innerHeight", "getBoundingClientRect", "ref", "useToggle", "defaultValue", "state", "toggle", "value", "ref2", "inject", "computed", "onUnmounted", "getCurrentInstance", "useParent", "key", "parent", "instance", "link", "unlink", "internalChildren", "index", "indexOf", "isVNode", "provide", "reactive", "getCurrentInstance2", "flattenVNodes", "children", "result", "traverse", "children2", "Array", "isArray", "for<PERSON>ach", "child", "_a", "push", "component", "subTree", "findVNodeIndex", "vnodes", "vnode", "findIndex", "item", "type", "sort<PERSON><PERSON><PERSON><PERSON>", "publicC<PERSON><PERSON>n", "sort", "a", "b", "orderedPublicChildren", "map", "proxy", "indexA", "indexB", "useChildren", "linkChildren", "splice", "Object", "assign", "ref3", "computed2", "onActivated", "onDeactivated", "onBeforeUnmount", "SECOND", "MINUTE", "HOUR", "DAY", "parseTime", "time", "days", "Math", "floor", "hours", "minutes", "seconds", "milliseconds", "total", "isSameSecond", "time1", "time2", "useCountDown", "options", "rafId", "endTime", "counting", "deactivated", "remain", "current", "pause", "getCurrentRemain", "max", "Date", "now", "setRemain", "_b", "onChange", "call", "onFinish", "microTick", "macroTick", "remainRemain", "tick", "millisecond", "start", "reset", "totalTime", "unref3", "watch", "isRef", "unref2", "onUnmounted2", "onDeactivated2", "nextTick", "onMounted", "onActivated2", "onMountedOrActivated", "hook", "mounted", "useEventListener", "listener", "target", "passive", "capture", "cleaned", "attached", "add", "target2", "addEventListener", "remove", "removeEventListener", "stopWatch", "oldVal", "useClickAway", "eventName", "onClick", "event", "targets", "isClickAway", "every", "contains", "document", "ref4", "useWindowSize", "update", "ref5", "onMounted2", "overflowScrollReg", "defaultRoot", "isElement", "node", "ELEMENT_NODE_TYPE", "tagName", "nodeType", "getScrollParent", "el", "root", "overflowY", "getComputedStyle", "test", "parentNode", "useScrollParent", "scrollParent", "ref6", "visibility", "usePageVisibility", "hidden", "watch2", "inject2", "CUSTOM_FIELD_INJECTION_KEY", "Symbol", "useCustomFieldValue", "customValue", "field", "resetValidation", "validateWithTrigger", "useRaf", "interval", "isLoop", "isStopped", "stop", "frameWrapper", "timestamp"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/@vant/use/dist/index.esm.mjs"], "sourcesContent": ["// src/utils.ts\nvar inBrowser = typeof window !== \"undefined\";\nvar supportsPassive = true;\nfunction raf(fn) {\n  return inBrowser ? requestAnimationFrame(fn) : -1;\n}\nfunction cancelRaf(id) {\n  if (inBrowser) {\n    cancelAnimationFrame(id);\n  }\n}\nfunction doubleRaf(fn) {\n  raf(() => raf(fn));\n}\n\n// src/useRect/index.ts\nimport { unref } from \"vue\";\nvar isWindow = (val) => val === window;\nvar makeDOMRect = (width2, height2) => ({\n  top: 0,\n  left: 0,\n  right: width2,\n  bottom: height2,\n  width: width2,\n  height: height2\n});\nvar useRect = (elementOrRef) => {\n  const element = unref(elementOrRef);\n  if (isWindow(element)) {\n    const width2 = element.innerWidth;\n    const height2 = element.innerHeight;\n    return makeDOMRect(width2, height2);\n  }\n  if (element == null ? void 0 : element.getBoundingClientRect) {\n    return element.getBoundingClientRect();\n  }\n  return makeDOMRect(0, 0);\n};\n\n// src/useToggle/index.ts\nimport { ref } from \"vue\";\nfunction useToggle(defaultValue = false) {\n  const state = ref(defaultValue);\n  const toggle = (value = !state.value) => {\n    state.value = value;\n  };\n  return [state, toggle];\n}\n\n// src/useRelation/useParent.ts\nimport {\n  ref as ref2,\n  inject,\n  computed,\n  onUnmounted,\n  getCurrentInstance\n} from \"vue\";\nfunction useParent(key) {\n  const parent = inject(key, null);\n  if (parent) {\n    const instance = getCurrentInstance();\n    const { link, unlink, internalChildren } = parent;\n    link(instance);\n    onUnmounted(() => unlink(instance));\n    const index = computed(() => internalChildren.indexOf(instance));\n    return {\n      parent,\n      index\n    };\n  }\n  return {\n    parent: null,\n    index: ref2(-1)\n  };\n}\n\n// src/useRelation/useChildren.ts\nimport {\n  isVNode,\n  provide,\n  reactive,\n  getCurrentInstance as getCurrentInstance2\n} from \"vue\";\nfunction flattenVNodes(children) {\n  const result = [];\n  const traverse = (children2) => {\n    if (Array.isArray(children2)) {\n      children2.forEach((child) => {\n        var _a;\n        if (isVNode(child)) {\n          result.push(child);\n          if ((_a = child.component) == null ? void 0 : _a.subTree) {\n            result.push(child.component.subTree);\n            traverse(child.component.subTree.children);\n          }\n          if (child.children) {\n            traverse(child.children);\n          }\n        }\n      });\n    }\n  };\n  traverse(children);\n  return result;\n}\nvar findVNodeIndex = (vnodes, vnode) => {\n  const index = vnodes.indexOf(vnode);\n  if (index === -1) {\n    return vnodes.findIndex(\n      (item) => vnode.key !== void 0 && vnode.key !== null && item.type === vnode.type && item.key === vnode.key\n    );\n  }\n  return index;\n};\nfunction sortChildren(parent, publicChildren, internalChildren) {\n  const vnodes = flattenVNodes(parent.subTree.children);\n  internalChildren.sort(\n    (a, b) => findVNodeIndex(vnodes, a.vnode) - findVNodeIndex(vnodes, b.vnode)\n  );\n  const orderedPublicChildren = internalChildren.map((item) => item.proxy);\n  publicChildren.sort((a, b) => {\n    const indexA = orderedPublicChildren.indexOf(a);\n    const indexB = orderedPublicChildren.indexOf(b);\n    return indexA - indexB;\n  });\n}\nfunction useChildren(key) {\n  const publicChildren = reactive([]);\n  const internalChildren = reactive([]);\n  const parent = getCurrentInstance2();\n  const linkChildren = (value) => {\n    const link = (child) => {\n      if (child.proxy) {\n        internalChildren.push(child);\n        publicChildren.push(child.proxy);\n        sortChildren(parent, publicChildren, internalChildren);\n      }\n    };\n    const unlink = (child) => {\n      const index = internalChildren.indexOf(child);\n      publicChildren.splice(index, 1);\n      internalChildren.splice(index, 1);\n    };\n    provide(\n      key,\n      Object.assign(\n        {\n          link,\n          unlink,\n          children: publicChildren,\n          internalChildren\n        },\n        value\n      )\n    );\n  };\n  return {\n    children: publicChildren,\n    linkChildren\n  };\n}\n\n// src/useCountDown/index.ts\nimport {\n  ref as ref3,\n  computed as computed2,\n  onActivated,\n  onDeactivated,\n  onBeforeUnmount\n} from \"vue\";\nvar SECOND = 1e3;\nvar MINUTE = 60 * SECOND;\nvar HOUR = 60 * MINUTE;\nvar DAY = 24 * HOUR;\nfunction parseTime(time) {\n  const days = Math.floor(time / DAY);\n  const hours = Math.floor(time % DAY / HOUR);\n  const minutes = Math.floor(time % HOUR / MINUTE);\n  const seconds = Math.floor(time % MINUTE / SECOND);\n  const milliseconds = Math.floor(time % SECOND);\n  return {\n    total: time,\n    days,\n    hours,\n    minutes,\n    seconds,\n    milliseconds\n  };\n}\nfunction isSameSecond(time1, time2) {\n  return Math.floor(time1 / 1e3) === Math.floor(time2 / 1e3);\n}\nfunction useCountDown(options) {\n  let rafId;\n  let endTime;\n  let counting;\n  let deactivated;\n  const remain = ref3(options.time);\n  const current = computed2(() => parseTime(remain.value));\n  const pause = () => {\n    counting = false;\n    cancelRaf(rafId);\n  };\n  const getCurrentRemain = () => Math.max(endTime - Date.now(), 0);\n  const setRemain = (value) => {\n    var _a, _b;\n    remain.value = value;\n    (_a = options.onChange) == null ? void 0 : _a.call(options, current.value);\n    if (value === 0) {\n      pause();\n      (_b = options.onFinish) == null ? void 0 : _b.call(options);\n    }\n  };\n  const microTick = () => {\n    rafId = raf(() => {\n      if (counting) {\n        setRemain(getCurrentRemain());\n        if (remain.value > 0) {\n          microTick();\n        }\n      }\n    });\n  };\n  const macroTick = () => {\n    rafId = raf(() => {\n      if (counting) {\n        const remainRemain = getCurrentRemain();\n        if (!isSameSecond(remainRemain, remain.value) || remainRemain === 0) {\n          setRemain(remainRemain);\n        }\n        if (remain.value > 0) {\n          macroTick();\n        }\n      }\n    });\n  };\n  const tick = () => {\n    if (!inBrowser) {\n      return;\n    }\n    if (options.millisecond) {\n      microTick();\n    } else {\n      macroTick();\n    }\n  };\n  const start = () => {\n    if (!counting) {\n      endTime = Date.now() + remain.value;\n      counting = true;\n      tick();\n    }\n  };\n  const reset = (totalTime = options.time) => {\n    pause();\n    remain.value = totalTime;\n  };\n  onBeforeUnmount(pause);\n  onActivated(() => {\n    if (deactivated) {\n      counting = true;\n      deactivated = false;\n      tick();\n    }\n  });\n  onDeactivated(() => {\n    if (counting) {\n      pause();\n      deactivated = true;\n    }\n  });\n  return {\n    start,\n    pause,\n    reset,\n    current\n  };\n}\n\n// src/useClickAway/index.ts\nimport { unref as unref3 } from \"vue\";\n\n// src/useEventListener/index.ts\nimport {\n  watch,\n  isRef,\n  unref as unref2,\n  onUnmounted as onUnmounted2,\n  onDeactivated as onDeactivated2\n} from \"vue\";\n\n// src/onMountedOrActivated/index.ts\nimport { nextTick, onMounted, onActivated as onActivated2 } from \"vue\";\nfunction onMountedOrActivated(hook) {\n  let mounted;\n  onMounted(() => {\n    hook();\n    nextTick(() => {\n      mounted = true;\n    });\n  });\n  onActivated2(() => {\n    if (mounted) {\n      hook();\n    }\n  });\n}\n\n// src/useEventListener/index.ts\nfunction useEventListener(type, listener, options = {}) {\n  if (!inBrowser) {\n    return;\n  }\n  const { target = window, passive = false, capture = false } = options;\n  let cleaned = false;\n  let attached;\n  const add = (target2) => {\n    if (cleaned) {\n      return;\n    }\n    const element = unref2(target2);\n    if (element && !attached) {\n      element.addEventListener(type, listener, {\n        capture,\n        passive\n      });\n      attached = true;\n    }\n  };\n  const remove = (target2) => {\n    if (cleaned) {\n      return;\n    }\n    const element = unref2(target2);\n    if (element && attached) {\n      element.removeEventListener(type, listener, capture);\n      attached = false;\n    }\n  };\n  onUnmounted2(() => remove(target));\n  onDeactivated2(() => remove(target));\n  onMountedOrActivated(() => add(target));\n  let stopWatch;\n  if (isRef(target)) {\n    stopWatch = watch(target, (val, oldVal) => {\n      remove(oldVal);\n      add(val);\n    });\n  }\n  return () => {\n    stopWatch == null ? void 0 : stopWatch();\n    remove(target);\n    cleaned = true;\n  };\n}\n\n// src/useClickAway/index.ts\nfunction useClickAway(target, listener, options = {}) {\n  if (!inBrowser) {\n    return;\n  }\n  const { eventName = \"click\" } = options;\n  const onClick = (event) => {\n    const targets = Array.isArray(target) ? target : [target];\n    const isClickAway = targets.every((item) => {\n      const element = unref3(item);\n      return element && !element.contains(event.target);\n    });\n    if (isClickAway) {\n      listener(event);\n    }\n  };\n  useEventListener(eventName, onClick, { target: document });\n}\n\n// src/useWindowSize/index.ts\nimport { ref as ref4 } from \"vue\";\nvar width;\nvar height;\nfunction useWindowSize() {\n  if (!width) {\n    width = ref4(0);\n    height = ref4(0);\n    if (inBrowser) {\n      const update = () => {\n        width.value = window.innerWidth;\n        height.value = window.innerHeight;\n      };\n      update();\n      window.addEventListener(\"resize\", update, { passive: true });\n      window.addEventListener(\"orientationchange\", update, { passive: true });\n    }\n  }\n  return { width, height };\n}\n\n// src/useScrollParent/index.ts\nimport { ref as ref5, onMounted as onMounted2 } from \"vue\";\nvar overflowScrollReg = /scroll|auto|overlay/i;\nvar defaultRoot = inBrowser ? window : void 0;\nfunction isElement(node) {\n  const ELEMENT_NODE_TYPE = 1;\n  return node.tagName !== \"HTML\" && node.tagName !== \"BODY\" && node.nodeType === ELEMENT_NODE_TYPE;\n}\nfunction getScrollParent(el, root = defaultRoot) {\n  let node = el;\n  while (node && node !== root && isElement(node)) {\n    const { overflowY } = window.getComputedStyle(node);\n    if (overflowScrollReg.test(overflowY)) {\n      return node;\n    }\n    node = node.parentNode;\n  }\n  return root;\n}\nfunction useScrollParent(el, root = defaultRoot) {\n  const scrollParent = ref5();\n  onMounted2(() => {\n    if (el.value) {\n      scrollParent.value = getScrollParent(el.value, root);\n    }\n  });\n  return scrollParent;\n}\n\n// src/usePageVisibility/index.ts\nimport { ref as ref6 } from \"vue\";\nvar visibility;\nfunction usePageVisibility() {\n  if (!visibility) {\n    visibility = ref6(\"visible\");\n    if (inBrowser) {\n      const update = () => {\n        visibility.value = document.hidden ? \"hidden\" : \"visible\";\n      };\n      update();\n      window.addEventListener(\"visibilitychange\", update);\n    }\n  }\n  return visibility;\n}\n\n// src/useCustomFieldValue/index.ts\nimport { watch as watch2, inject as inject2 } from \"vue\";\nvar CUSTOM_FIELD_INJECTION_KEY = Symbol(\"van-field\");\nfunction useCustomFieldValue(customValue) {\n  const field = inject2(CUSTOM_FIELD_INJECTION_KEY, null);\n  if (field && !field.customValue.value) {\n    field.customValue.value = customValue;\n    watch2(customValue, () => {\n      field.resetValidation();\n      field.validateWithTrigger(\"onChange\");\n    });\n  }\n}\n\n// src/useRaf/index.ts\nfunction useRaf(fn, options) {\n  if (inBrowser) {\n    const { interval = 0, isLoop = false } = options || {};\n    let start;\n    let isStopped = false;\n    let rafId;\n    const stop = () => {\n      isStopped = true;\n      cancelAnimationFrame(rafId);\n    };\n    const frameWrapper = (timestamp) => {\n      if (isStopped)\n        return;\n      if (start === void 0) {\n        start = timestamp;\n      } else if (timestamp - start > interval) {\n        fn(timestamp);\n        start = timestamp;\n        if (!isLoop) {\n          stop();\n          return;\n        }\n      }\n      rafId = requestAnimationFrame(frameWrapper);\n    };\n    rafId = requestAnimationFrame(frameWrapper);\n    return stop;\n  }\n  return () => {\n  };\n}\nexport {\n  CUSTOM_FIELD_INJECTION_KEY,\n  cancelRaf,\n  doubleRaf,\n  flattenVNodes,\n  getScrollParent,\n  inBrowser,\n  onMountedOrActivated,\n  raf,\n  sortChildren,\n  supportsPassive,\n  useChildren,\n  useClickAway,\n  useCountDown,\n  useCustomFieldValue,\n  useEventListener,\n  usePageVisibility,\n  useParent,\n  useRaf,\n  useRect,\n  useScrollParent,\n  useToggle,\n  useWindowSize\n};\n"], "mappings": ";;;;;AAAA;AACA,IAAIA,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;AAC7C,IAAIC,eAAe,GAAG,IAAI;AAC1B,SAASC,GAAGA,CAACC,EAAE,EAAE;EACf,OAAOJ,SAAS,GAAGK,qBAAqB,CAACD,EAAE,CAAC,GAAG,CAAC,CAAC;AACnD;AACA,SAASE,SAASA,CAACC,EAAE,EAAE;EACrB,IAAIP,SAAS,EAAE;IACbQ,oBAAoB,CAACD,EAAE,CAAC;EAC1B;AACF;AACA,SAASE,SAASA,CAACL,EAAE,EAAE;EACrBD,GAAG,CAAC,MAAMA,GAAG,CAACC,EAAE,CAAC,CAAC;AACpB;;AAEA;AACA,SAASM,KAAK,QAAQ,KAAK;AAC3B,IAAIC,QAAQ,GAAIC,GAAG,IAAKA,GAAG,KAAKX,MAAM;AACtC,IAAIY,WAAW,GAAGA,CAACC,MAAM,EAAEC,OAAO,MAAM;EACtCC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAEJ,MAAM;EACbK,MAAM,EAAEJ,OAAO;EACfK,KAAK,EAAEN,MAAM;EACbO,MAAM,EAAEN;AACV,CAAC,CAAC;AACF,IAAIO,OAAO,GAAIC,YAAY,IAAK;EAC9B,MAAMC,OAAO,GAAGd,KAAK,CAACa,YAAY,CAAC;EACnC,IAAIZ,QAAQ,CAACa,OAAO,CAAC,EAAE;IACrB,MAAMV,MAAM,GAAGU,OAAO,CAACC,UAAU;IACjC,MAAMV,OAAO,GAAGS,OAAO,CAACE,WAAW;IACnC,OAAOb,WAAW,CAACC,MAAM,EAAEC,OAAO,CAAC;EACrC;EACA,IAAIS,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,qBAAqB,EAAE;IAC5D,OAAOH,OAAO,CAACG,qBAAqB,CAAC,CAAC;EACxC;EACA,OAAOd,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,CAAC;;AAED;AACA,SAASe,GAAG,QAAQ,KAAK;AACzB,SAASC,SAASA,CAACC,YAAY,GAAG,KAAK,EAAE;EACvC,MAAMC,KAAK,GAAGH,GAAG,CAACE,YAAY,CAAC;EAC/B,MAAME,MAAM,GAAGA,CAACC,KAAK,GAAG,CAACF,KAAK,CAACE,KAAK,KAAK;IACvCF,KAAK,CAACE,KAAK,GAAGA,KAAK;EACrB,CAAC;EACD,OAAO,CAACF,KAAK,EAAEC,MAAM,CAAC;AACxB;;AAEA;AACA,SACEJ,GAAG,IAAIM,IAAI,EACXC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,kBAAkB,QACb,KAAK;AACZ,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,MAAMC,MAAM,GAAGN,MAAM,CAACK,GAAG,EAAE,IAAI,CAAC;EAChC,IAAIC,MAAM,EAAE;IACV,MAAMC,QAAQ,GAAGJ,kBAAkB,CAAC,CAAC;IACrC,MAAM;MAAEK,IAAI;MAAEC,MAAM;MAAEC;IAAiB,CAAC,GAAGJ,MAAM;IACjDE,IAAI,CAACD,QAAQ,CAAC;IACdL,WAAW,CAAC,MAAMO,MAAM,CAACF,QAAQ,CAAC,CAAC;IACnC,MAAMI,KAAK,GAAGV,QAAQ,CAAC,MAAMS,gBAAgB,CAACE,OAAO,CAACL,QAAQ,CAAC,CAAC;IAChE,OAAO;MACLD,MAAM;MACNK;IACF,CAAC;EACH;EACA,OAAO;IACLL,MAAM,EAAE,IAAI;IACZK,KAAK,EAAEZ,IAAI,CAAC,CAAC,CAAC;EAChB,CAAC;AACH;;AAEA;AACA,SACEc,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRZ,kBAAkB,IAAIa,mBAAmB,QACpC,KAAK;AACZ,SAASC,aAAaA,CAACC,QAAQ,EAAE;EAC/B,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,QAAQ,GAAIC,SAAS,IAAK;IAC9B,IAAIC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;MAC5BA,SAAS,CAACG,OAAO,CAAEC,KAAK,IAAK;QAC3B,IAAIC,EAAE;QACN,IAAIb,OAAO,CAACY,KAAK,CAAC,EAAE;UAClBN,MAAM,CAACQ,IAAI,CAACF,KAAK,CAAC;UAClB,IAAI,CAACC,EAAE,GAAGD,KAAK,CAACG,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,OAAO,EAAE;YACxDV,MAAM,CAACQ,IAAI,CAACF,KAAK,CAACG,SAAS,CAACC,OAAO,CAAC;YACpCT,QAAQ,CAACK,KAAK,CAACG,SAAS,CAACC,OAAO,CAACX,QAAQ,CAAC;UAC5C;UACA,IAAIO,KAAK,CAACP,QAAQ,EAAE;YAClBE,QAAQ,CAACK,KAAK,CAACP,QAAQ,CAAC;UAC1B;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACDE,QAAQ,CAACF,QAAQ,CAAC;EAClB,OAAOC,MAAM;AACf;AACA,IAAIW,cAAc,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACtC,MAAMrB,KAAK,GAAGoB,MAAM,CAACnB,OAAO,CAACoB,KAAK,CAAC;EACnC,IAAIrB,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,OAAOoB,MAAM,CAACE,SAAS,CACpBC,IAAI,IAAKF,KAAK,CAAC3B,GAAG,KAAK,KAAK,CAAC,IAAI2B,KAAK,CAAC3B,GAAG,KAAK,IAAI,IAAI6B,IAAI,CAACC,IAAI,KAAKH,KAAK,CAACG,IAAI,IAAID,IAAI,CAAC7B,GAAG,KAAK2B,KAAK,CAAC3B,GACzG,CAAC;EACH;EACA,OAAOM,KAAK;AACd,CAAC;AACD,SAASyB,YAAYA,CAAC9B,MAAM,EAAE+B,cAAc,EAAE3B,gBAAgB,EAAE;EAC9D,MAAMqB,MAAM,GAAGd,aAAa,CAACX,MAAM,CAACuB,OAAO,CAACX,QAAQ,CAAC;EACrDR,gBAAgB,CAAC4B,IAAI,CACnB,CAACC,CAAC,EAAEC,CAAC,KAAKV,cAAc,CAACC,MAAM,EAAEQ,CAAC,CAACP,KAAK,CAAC,GAAGF,cAAc,CAACC,MAAM,EAAES,CAAC,CAACR,KAAK,CAC5E,CAAC;EACD,MAAMS,qBAAqB,GAAG/B,gBAAgB,CAACgC,GAAG,CAAER,IAAI,IAAKA,IAAI,CAACS,KAAK,CAAC;EACxEN,cAAc,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC5B,MAAMI,MAAM,GAAGH,qBAAqB,CAAC7B,OAAO,CAAC2B,CAAC,CAAC;IAC/C,MAAMM,MAAM,GAAGJ,qBAAqB,CAAC7B,OAAO,CAAC4B,CAAC,CAAC;IAC/C,OAAOI,MAAM,GAAGC,MAAM;EACxB,CAAC,CAAC;AACJ;AACA,SAASC,WAAWA,CAACzC,GAAG,EAAE;EACxB,MAAMgC,cAAc,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAML,gBAAgB,GAAGK,QAAQ,CAAC,EAAE,CAAC;EACrC,MAAMT,MAAM,GAAGU,mBAAmB,CAAC,CAAC;EACpC,MAAM+B,YAAY,GAAIjD,KAAK,IAAK;IAC9B,MAAMU,IAAI,GAAIiB,KAAK,IAAK;MACtB,IAAIA,KAAK,CAACkB,KAAK,EAAE;QACfjC,gBAAgB,CAACiB,IAAI,CAACF,KAAK,CAAC;QAC5BY,cAAc,CAACV,IAAI,CAACF,KAAK,CAACkB,KAAK,CAAC;QAChCP,YAAY,CAAC9B,MAAM,EAAE+B,cAAc,EAAE3B,gBAAgB,CAAC;MACxD;IACF,CAAC;IACD,MAAMD,MAAM,GAAIgB,KAAK,IAAK;MACxB,MAAMd,KAAK,GAAGD,gBAAgB,CAACE,OAAO,CAACa,KAAK,CAAC;MAC7CY,cAAc,CAACW,MAAM,CAACrC,KAAK,EAAE,CAAC,CAAC;MAC/BD,gBAAgB,CAACsC,MAAM,CAACrC,KAAK,EAAE,CAAC,CAAC;IACnC,CAAC;IACDG,OAAO,CACLT,GAAG,EACH4C,MAAM,CAACC,MAAM,CACX;MACE1C,IAAI;MACJC,MAAM;MACNS,QAAQ,EAAEmB,cAAc;MACxB3B;IACF,CAAC,EACDZ,KACF,CACF,CAAC;EACH,CAAC;EACD,OAAO;IACLoB,QAAQ,EAAEmB,cAAc;IACxBU;EACF,CAAC;AACH;;AAEA;AACA,SACEtD,GAAG,IAAI0D,IAAI,EACXlD,QAAQ,IAAImD,SAAS,EACrBC,WAAW,EACXC,aAAa,EACbC,eAAe,QACV,KAAK;AACZ,IAAIC,MAAM,GAAG,GAAG;AAChB,IAAIC,MAAM,GAAG,EAAE,GAAGD,MAAM;AACxB,IAAIE,IAAI,GAAG,EAAE,GAAGD,MAAM;AACtB,IAAIE,GAAG,GAAG,EAAE,GAAGD,IAAI;AACnB,SAASE,SAASA,CAACC,IAAI,EAAE;EACvB,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGF,GAAG,CAAC;EACnC,MAAMM,KAAK,GAAGF,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGF,GAAG,GAAGD,IAAI,CAAC;EAC3C,MAAMQ,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGH,IAAI,GAAGD,MAAM,CAAC;EAChD,MAAMU,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGJ,MAAM,GAAGD,MAAM,CAAC;EAClD,MAAMY,YAAY,GAAGL,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGL,MAAM,CAAC;EAC9C,OAAO;IACLa,KAAK,EAAER,IAAI;IACXC,IAAI;IACJG,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC;EACF,CAAC;AACH;AACA,SAASE,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAClC,OAAOT,IAAI,CAACC,KAAK,CAACO,KAAK,GAAG,GAAG,CAAC,KAAKR,IAAI,CAACC,KAAK,CAACQ,KAAK,GAAG,GAAG,CAAC;AAC5D;AACA,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAIC,KAAK;EACT,IAAIC,OAAO;EACX,IAAIC,QAAQ;EACZ,IAAIC,WAAW;EACf,MAAMC,MAAM,GAAG5B,IAAI,CAACuB,OAAO,CAACb,IAAI,CAAC;EACjC,MAAMmB,OAAO,GAAG5B,SAAS,CAAC,MAAMQ,SAAS,CAACmB,MAAM,CAACjF,KAAK,CAAC,CAAC;EACxD,MAAMmF,KAAK,GAAGA,CAAA,KAAM;IAClBJ,QAAQ,GAAG,KAAK;IAChB1G,SAAS,CAACwG,KAAK,CAAC;EAClB,CAAC;EACD,MAAMO,gBAAgB,GAAGA,CAAA,KAAMnB,IAAI,CAACoB,GAAG,CAACP,OAAO,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAChE,MAAMC,SAAS,GAAIxF,KAAK,IAAK;IAC3B,IAAI4B,EAAE,EAAE6D,EAAE;IACVR,MAAM,CAACjF,KAAK,GAAGA,KAAK;IACpB,CAAC4B,EAAE,GAAGgD,OAAO,CAACc,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9D,EAAE,CAAC+D,IAAI,CAACf,OAAO,EAAEM,OAAO,CAAClF,KAAK,CAAC;IAC1E,IAAIA,KAAK,KAAK,CAAC,EAAE;MACfmF,KAAK,CAAC,CAAC;MACP,CAACM,EAAE,GAAGb,OAAO,CAACgB,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACE,IAAI,CAACf,OAAO,CAAC;IAC7D;EACF,CAAC;EACD,MAAMiB,SAAS,GAAGA,CAAA,KAAM;IACtBhB,KAAK,GAAG3G,GAAG,CAAC,MAAM;MAChB,IAAI6G,QAAQ,EAAE;QACZS,SAAS,CAACJ,gBAAgB,CAAC,CAAC,CAAC;QAC7B,IAAIH,MAAM,CAACjF,KAAK,GAAG,CAAC,EAAE;UACpB6F,SAAS,CAAC,CAAC;QACb;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtBjB,KAAK,GAAG3G,GAAG,CAAC,MAAM;MAChB,IAAI6G,QAAQ,EAAE;QACZ,MAAMgB,YAAY,GAAGX,gBAAgB,CAAC,CAAC;QACvC,IAAI,CAACZ,YAAY,CAACuB,YAAY,EAAEd,MAAM,CAACjF,KAAK,CAAC,IAAI+F,YAAY,KAAK,CAAC,EAAE;UACnEP,SAAS,CAACO,YAAY,CAAC;QACzB;QACA,IAAId,MAAM,CAACjF,KAAK,GAAG,CAAC,EAAE;UACpB8F,SAAS,CAAC,CAAC;QACb;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAME,IAAI,GAAGA,CAAA,KAAM;IACjB,IAAI,CAACjI,SAAS,EAAE;MACd;IACF;IACA,IAAI6G,OAAO,CAACqB,WAAW,EAAE;MACvBJ,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACLC,SAAS,CAAC,CAAC;IACb;EACF,CAAC;EACD,MAAMI,KAAK,GAAGA,CAAA,KAAM;IAClB,IAAI,CAACnB,QAAQ,EAAE;MACbD,OAAO,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGN,MAAM,CAACjF,KAAK;MACnC+E,QAAQ,GAAG,IAAI;MACfiB,IAAI,CAAC,CAAC;IACR;EACF,CAAC;EACD,MAAMG,KAAK,GAAGA,CAACC,SAAS,GAAGxB,OAAO,CAACb,IAAI,KAAK;IAC1CoB,KAAK,CAAC,CAAC;IACPF,MAAM,CAACjF,KAAK,GAAGoG,SAAS;EAC1B,CAAC;EACD3C,eAAe,CAAC0B,KAAK,CAAC;EACtB5B,WAAW,CAAC,MAAM;IAChB,IAAIyB,WAAW,EAAE;MACfD,QAAQ,GAAG,IAAI;MACfC,WAAW,GAAG,KAAK;MACnBgB,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EACFxC,aAAa,CAAC,MAAM;IAClB,IAAIuB,QAAQ,EAAE;MACZI,KAAK,CAAC,CAAC;MACPH,WAAW,GAAG,IAAI;IACpB;EACF,CAAC,CAAC;EACF,OAAO;IACLkB,KAAK;IACLf,KAAK;IACLgB,KAAK;IACLjB;EACF,CAAC;AACH;;AAEA;AACA,SAASzG,KAAK,IAAI4H,MAAM,QAAQ,KAAK;;AAErC;AACA,SACEC,KAAK,EACLC,KAAK,EACL9H,KAAK,IAAI+H,MAAM,EACfpG,WAAW,IAAIqG,YAAY,EAC3BjD,aAAa,IAAIkD,cAAc,QAC1B,KAAK;;AAEZ;AACA,SAASC,QAAQ,EAAEC,SAAS,EAAErD,WAAW,IAAIsD,YAAY,QAAQ,KAAK;AACtE,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EAClC,IAAIC,OAAO;EACXJ,SAAS,CAAC,MAAM;IACdG,IAAI,CAAC,CAAC;IACNJ,QAAQ,CAAC,MAAM;MACbK,OAAO,GAAG,IAAI;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EACFH,YAAY,CAAC,MAAM;IACjB,IAAIG,OAAO,EAAE;MACXD,IAAI,CAAC,CAAC;IACR;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,SAASE,gBAAgBA,CAAC5E,IAAI,EAAE6E,QAAQ,EAAEtC,OAAO,GAAG,CAAC,CAAC,EAAE;EACtD,IAAI,CAAC7G,SAAS,EAAE;IACd;EACF;EACA,MAAM;IAAEoJ,MAAM,GAAGnJ,MAAM;IAAEoJ,OAAO,GAAG,KAAK;IAAEC,OAAO,GAAG;EAAM,CAAC,GAAGzC,OAAO;EACrE,IAAI0C,OAAO,GAAG,KAAK;EACnB,IAAIC,QAAQ;EACZ,MAAMC,GAAG,GAAIC,OAAO,IAAK;IACvB,IAAIH,OAAO,EAAE;MACX;IACF;IACA,MAAM/H,OAAO,GAAGiH,MAAM,CAACiB,OAAO,CAAC;IAC/B,IAAIlI,OAAO,IAAI,CAACgI,QAAQ,EAAE;MACxBhI,OAAO,CAACmI,gBAAgB,CAACrF,IAAI,EAAE6E,QAAQ,EAAE;QACvCG,OAAO;QACPD;MACF,CAAC,CAAC;MACFG,QAAQ,GAAG,IAAI;IACjB;EACF,CAAC;EACD,MAAMI,MAAM,GAAIF,OAAO,IAAK;IAC1B,IAAIH,OAAO,EAAE;MACX;IACF;IACA,MAAM/H,OAAO,GAAGiH,MAAM,CAACiB,OAAO,CAAC;IAC/B,IAAIlI,OAAO,IAAIgI,QAAQ,EAAE;MACvBhI,OAAO,CAACqI,mBAAmB,CAACvF,IAAI,EAAE6E,QAAQ,EAAEG,OAAO,CAAC;MACpDE,QAAQ,GAAG,KAAK;IAClB;EACF,CAAC;EACDd,YAAY,CAAC,MAAMkB,MAAM,CAACR,MAAM,CAAC,CAAC;EAClCT,cAAc,CAAC,MAAMiB,MAAM,CAACR,MAAM,CAAC,CAAC;EACpCL,oBAAoB,CAAC,MAAMU,GAAG,CAACL,MAAM,CAAC,CAAC;EACvC,IAAIU,SAAS;EACb,IAAItB,KAAK,CAACY,MAAM,CAAC,EAAE;IACjBU,SAAS,GAAGvB,KAAK,CAACa,MAAM,EAAE,CAACxI,GAAG,EAAEmJ,MAAM,KAAK;MACzCH,MAAM,CAACG,MAAM,CAAC;MACdN,GAAG,CAAC7I,GAAG,CAAC;IACV,CAAC,CAAC;EACJ;EACA,OAAO,MAAM;IACXkJ,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC,CAAC;IACxCF,MAAM,CAACR,MAAM,CAAC;IACdG,OAAO,GAAG,IAAI;EAChB,CAAC;AACH;;AAEA;AACA,SAASS,YAAYA,CAACZ,MAAM,EAAED,QAAQ,EAAEtC,OAAO,GAAG,CAAC,CAAC,EAAE;EACpD,IAAI,CAAC7G,SAAS,EAAE;IACd;EACF;EACA,MAAM;IAAEiK,SAAS,GAAG;EAAQ,CAAC,GAAGpD,OAAO;EACvC,MAAMqD,OAAO,GAAIC,KAAK,IAAK;IACzB,MAAMC,OAAO,GAAG3G,KAAK,CAACC,OAAO,CAAC0F,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;IACzD,MAAMiB,WAAW,GAAGD,OAAO,CAACE,KAAK,CAAEjG,IAAI,IAAK;MAC1C,MAAM7C,OAAO,GAAG8G,MAAM,CAACjE,IAAI,CAAC;MAC5B,OAAO7C,OAAO,IAAI,CAACA,OAAO,CAAC+I,QAAQ,CAACJ,KAAK,CAACf,MAAM,CAAC;IACnD,CAAC,CAAC;IACF,IAAIiB,WAAW,EAAE;MACflB,QAAQ,CAACgB,KAAK,CAAC;IACjB;EACF,CAAC;EACDjB,gBAAgB,CAACe,SAAS,EAAEC,OAAO,EAAE;IAAEd,MAAM,EAAEoB;EAAS,CAAC,CAAC;AAC5D;;AAEA;AACA,SAAS5I,GAAG,IAAI6I,IAAI,QAAQ,KAAK;AACjC,IAAIrJ,KAAK;AACT,IAAIC,MAAM;AACV,SAASqJ,aAAaA,CAAA,EAAG;EACvB,IAAI,CAACtJ,KAAK,EAAE;IACVA,KAAK,GAAGqJ,IAAI,CAAC,CAAC,CAAC;IACfpJ,MAAM,GAAGoJ,IAAI,CAAC,CAAC,CAAC;IAChB,IAAIzK,SAAS,EAAE;MACb,MAAM2K,MAAM,GAAGA,CAAA,KAAM;QACnBvJ,KAAK,CAACa,KAAK,GAAGhC,MAAM,CAACwB,UAAU;QAC/BJ,MAAM,CAACY,KAAK,GAAGhC,MAAM,CAACyB,WAAW;MACnC,CAAC;MACDiJ,MAAM,CAAC,CAAC;MACR1K,MAAM,CAAC0J,gBAAgB,CAAC,QAAQ,EAAEgB,MAAM,EAAE;QAAEtB,OAAO,EAAE;MAAK,CAAC,CAAC;MAC5DpJ,MAAM,CAAC0J,gBAAgB,CAAC,mBAAmB,EAAEgB,MAAM,EAAE;QAAEtB,OAAO,EAAE;MAAK,CAAC,CAAC;IACzE;EACF;EACA,OAAO;IAAEjI,KAAK;IAAEC;EAAO,CAAC;AAC1B;;AAEA;AACA,SAASO,GAAG,IAAIgJ,IAAI,EAAE/B,SAAS,IAAIgC,UAAU,QAAQ,KAAK;AAC1D,IAAIC,iBAAiB,GAAG,sBAAsB;AAC9C,IAAIC,WAAW,GAAG/K,SAAS,GAAGC,MAAM,GAAG,KAAK,CAAC;AAC7C,SAAS+K,SAASA,CAACC,IAAI,EAAE;EACvB,MAAMC,iBAAiB,GAAG,CAAC;EAC3B,OAAOD,IAAI,CAACE,OAAO,KAAK,MAAM,IAAIF,IAAI,CAACE,OAAO,KAAK,MAAM,IAAIF,IAAI,CAACG,QAAQ,KAAKF,iBAAiB;AAClG;AACA,SAASG,eAAeA,CAACC,EAAE,EAAEC,IAAI,GAAGR,WAAW,EAAE;EAC/C,IAAIE,IAAI,GAAGK,EAAE;EACb,OAAOL,IAAI,IAAIA,IAAI,KAAKM,IAAI,IAAIP,SAAS,CAACC,IAAI,CAAC,EAAE;IAC/C,MAAM;MAAEO;IAAU,CAAC,GAAGvL,MAAM,CAACwL,gBAAgB,CAACR,IAAI,CAAC;IACnD,IAAIH,iBAAiB,CAACY,IAAI,CAACF,SAAS,CAAC,EAAE;MACrC,OAAOP,IAAI;IACb;IACAA,IAAI,GAAGA,IAAI,CAACU,UAAU;EACxB;EACA,OAAOJ,IAAI;AACb;AACA,SAASK,eAAeA,CAACN,EAAE,EAAEC,IAAI,GAAGR,WAAW,EAAE;EAC/C,MAAMc,YAAY,GAAGjB,IAAI,CAAC,CAAC;EAC3BC,UAAU,CAAC,MAAM;IACf,IAAIS,EAAE,CAACrJ,KAAK,EAAE;MACZ4J,YAAY,CAAC5J,KAAK,GAAGoJ,eAAe,CAACC,EAAE,CAACrJ,KAAK,EAAEsJ,IAAI,CAAC;IACtD;EACF,CAAC,CAAC;EACF,OAAOM,YAAY;AACrB;;AAEA;AACA,SAASjK,GAAG,IAAIkK,IAAI,QAAQ,KAAK;AACjC,IAAIC,UAAU;AACd,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,IAAI,CAACD,UAAU,EAAE;IACfA,UAAU,GAAGD,IAAI,CAAC,SAAS,CAAC;IAC5B,IAAI9L,SAAS,EAAE;MACb,MAAM2K,MAAM,GAAGA,CAAA,KAAM;QACnBoB,UAAU,CAAC9J,KAAK,GAAGuI,QAAQ,CAACyB,MAAM,GAAG,QAAQ,GAAG,SAAS;MAC3D,CAAC;MACDtB,MAAM,CAAC,CAAC;MACR1K,MAAM,CAAC0J,gBAAgB,CAAC,kBAAkB,EAAEgB,MAAM,CAAC;IACrD;EACF;EACA,OAAOoB,UAAU;AACnB;;AAEA;AACA,SAASxD,KAAK,IAAI2D,MAAM,EAAE/J,MAAM,IAAIgK,OAAO,QAAQ,KAAK;AACxD,IAAIC,0BAA0B,GAAGC,MAAM,CAAC,WAAW,CAAC;AACpD,SAASC,mBAAmBA,CAACC,WAAW,EAAE;EACxC,MAAMC,KAAK,GAAGL,OAAO,CAACC,0BAA0B,EAAE,IAAI,CAAC;EACvD,IAAII,KAAK,IAAI,CAACA,KAAK,CAACD,WAAW,CAACtK,KAAK,EAAE;IACrCuK,KAAK,CAACD,WAAW,CAACtK,KAAK,GAAGsK,WAAW;IACrCL,MAAM,CAACK,WAAW,EAAE,MAAM;MACxBC,KAAK,CAACC,eAAe,CAAC,CAAC;MACvBD,KAAK,CAACE,mBAAmB,CAAC,UAAU,CAAC;IACvC,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,SAASC,MAAMA,CAACvM,EAAE,EAAEyG,OAAO,EAAE;EAC3B,IAAI7G,SAAS,EAAE;IACb,MAAM;MAAE4M,QAAQ,GAAG,CAAC;MAAEC,MAAM,GAAG;IAAM,CAAC,GAAGhG,OAAO,IAAI,CAAC,CAAC;IACtD,IAAIsB,KAAK;IACT,IAAI2E,SAAS,GAAG,KAAK;IACrB,IAAIhG,KAAK;IACT,MAAMiG,IAAI,GAAGA,CAAA,KAAM;MACjBD,SAAS,GAAG,IAAI;MAChBtM,oBAAoB,CAACsG,KAAK,CAAC;IAC7B,CAAC;IACD,MAAMkG,YAAY,GAAIC,SAAS,IAAK;MAClC,IAAIH,SAAS,EACX;MACF,IAAI3E,KAAK,KAAK,KAAK,CAAC,EAAE;QACpBA,KAAK,GAAG8E,SAAS;MACnB,CAAC,MAAM,IAAIA,SAAS,GAAG9E,KAAK,GAAGyE,QAAQ,EAAE;QACvCxM,EAAE,CAAC6M,SAAS,CAAC;QACb9E,KAAK,GAAG8E,SAAS;QACjB,IAAI,CAACJ,MAAM,EAAE;UACXE,IAAI,CAAC,CAAC;UACN;QACF;MACF;MACAjG,KAAK,GAAGzG,qBAAqB,CAAC2M,YAAY,CAAC;IAC7C,CAAC;IACDlG,KAAK,GAAGzG,qBAAqB,CAAC2M,YAAY,CAAC;IAC3C,OAAOD,IAAI;EACb;EACA,OAAO,MAAM,CACb,CAAC;AACH;AACA,SACEX,0BAA0B,EAC1B9L,SAAS,EACTG,SAAS,EACT2C,aAAa,EACbiI,eAAe,EACfrL,SAAS,EACT+I,oBAAoB,EACpB5I,GAAG,EACHoE,YAAY,EACZrE,eAAe,EACf+E,WAAW,EACX+E,YAAY,EACZpD,YAAY,EACZ0F,mBAAmB,EACnBpD,gBAAgB,EAChB8C,iBAAiB,EACjBzJ,SAAS,EACToK,MAAM,EACNrL,OAAO,EACPsK,eAAe,EACf/J,SAAS,EACT6I,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}