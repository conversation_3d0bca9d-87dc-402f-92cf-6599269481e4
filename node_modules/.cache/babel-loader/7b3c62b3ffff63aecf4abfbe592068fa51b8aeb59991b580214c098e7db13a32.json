{"ast": null, "code": "import { computed, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { BORDER, extend, addUnit, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { GRID_KEY } from \"../grid/Grid.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"grid-item\");\nconst gridItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  text: String,\n  icon: String,\n  badge: numericProp,\n  iconColor: String,\n  iconPrefix: String,\n  badgeProps: Object\n});\nvar stdin_default = defineComponent({\n  name,\n  props: gridItemProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      parent,\n      index\n    } = useParent(GRID_KEY);\n    const route = useRoute();\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <GridItem> must be a child component of <Grid>.\");\n      }\n      return;\n    }\n    const rootStyle = computed(() => {\n      const {\n        square,\n        gutter,\n        columnNum\n      } = parent.props;\n      const percent = `${100 / +columnNum}%`;\n      const style = {\n        flexBasis: percent\n      };\n      if (square) {\n        style.paddingTop = percent;\n      } else if (gutter) {\n        const gutterValue = addUnit(gutter);\n        style.paddingRight = gutterValue;\n        if (index.value >= +columnNum) {\n          style.marginTop = gutterValue;\n        }\n      }\n      return style;\n    });\n    const contentStyle = computed(() => {\n      const {\n        square,\n        gutter\n      } = parent.props;\n      if (square && gutter) {\n        const gutterValue = addUnit(gutter);\n        return {\n          right: gutterValue,\n          bottom: gutterValue,\n          height: \"auto\"\n        };\n      }\n    });\n    const renderIcon = () => {\n      if (slots.icon) {\n        return _createVNode(Badge, _mergeProps({\n          \"dot\": props.dot,\n          \"content\": props.badge\n        }, props.badgeProps), {\n          default: slots.icon\n        });\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"dot\": props.dot,\n          \"name\": props.icon,\n          \"size\": parent.props.iconSize,\n          \"badge\": props.badge,\n          \"class\": bem(\"icon\"),\n          \"color\": props.iconColor,\n          \"badgeProps\": props.badgeProps,\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    const renderText = () => {\n      if (slots.text) {\n        return slots.text();\n      }\n      if (props.text) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\")\n        }, [props.text]);\n      }\n    };\n    const renderContent = () => {\n      if (slots.default) {\n        return slots.default();\n      }\n      return [renderIcon(), renderText()];\n    };\n    return () => {\n      const {\n        center,\n        border,\n        square,\n        gutter,\n        reverse,\n        direction,\n        clickable\n      } = parent.props;\n      const classes = [bem(\"content\", [direction, {\n        center,\n        square,\n        reverse,\n        clickable,\n        surround: border && gutter\n      }]), {\n        [BORDER]: border\n      }];\n      return _createVNode(\"div\", {\n        \"class\": [bem({\n          square\n        })],\n        \"style\": rootStyle.value\n      }, [_createVNode(\"div\", {\n        \"role\": clickable ? \"button\" : void 0,\n        \"class\": classes,\n        \"style\": contentStyle.value,\n        \"tabindex\": clickable ? 0 : void 0,\n        \"onClick\": route\n      }, [renderContent()])]);\n    };\n  }\n});\nexport { stdin_default as default, gridItemProps };", "map": {"version": 3, "names": ["computed", "defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "BORDER", "extend", "addUnit", "numericProp", "createNamespace", "GRID_KEY", "useParent", "useRoute", "routeProps", "Icon", "Badge", "name", "bem", "gridItemProps", "dot", "Boolean", "text", "String", "icon", "badge", "iconColor", "iconPrefix", "badgeProps", "Object", "stdin_default", "props", "setup", "slots", "parent", "index", "route", "process", "env", "NODE_ENV", "console", "error", "rootStyle", "square", "gutter", "columnNum", "percent", "style", "flexBasis", "paddingTop", "gutterValue", "paddingRight", "value", "marginTop", "contentStyle", "right", "bottom", "height", "renderIcon", "default", "iconSize", "renderText", "renderContent", "center", "border", "reverse", "direction", "clickable", "classes", "surround"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/grid-item/GridItem.mjs"], "sourcesContent": ["import { computed, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { BORDER, extend, addUnit, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { GRID_KEY } from \"../grid/Grid.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"grid-item\");\nconst gridItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  text: String,\n  icon: String,\n  badge: numericProp,\n  iconColor: String,\n  iconPrefix: String,\n  badgeProps: Object\n});\nvar stdin_default = defineComponent({\n  name,\n  props: gridItemProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      parent,\n      index\n    } = useParent(GRID_KEY);\n    const route = useRoute();\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <GridItem> must be a child component of <Grid>.\");\n      }\n      return;\n    }\n    const rootStyle = computed(() => {\n      const {\n        square,\n        gutter,\n        columnNum\n      } = parent.props;\n      const percent = `${100 / +columnNum}%`;\n      const style = {\n        flexBasis: percent\n      };\n      if (square) {\n        style.paddingTop = percent;\n      } else if (gutter) {\n        const gutterValue = addUnit(gutter);\n        style.paddingRight = gutterValue;\n        if (index.value >= +columnNum) {\n          style.marginTop = gutterValue;\n        }\n      }\n      return style;\n    });\n    const contentStyle = computed(() => {\n      const {\n        square,\n        gutter\n      } = parent.props;\n      if (square && gutter) {\n        const gutterValue = addUnit(gutter);\n        return {\n          right: gutterValue,\n          bottom: gutterValue,\n          height: \"auto\"\n        };\n      }\n    });\n    const renderIcon = () => {\n      if (slots.icon) {\n        return _createVNode(Badge, _mergeProps({\n          \"dot\": props.dot,\n          \"content\": props.badge\n        }, props.badgeProps), {\n          default: slots.icon\n        });\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"dot\": props.dot,\n          \"name\": props.icon,\n          \"size\": parent.props.iconSize,\n          \"badge\": props.badge,\n          \"class\": bem(\"icon\"),\n          \"color\": props.iconColor,\n          \"badgeProps\": props.badgeProps,\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    const renderText = () => {\n      if (slots.text) {\n        return slots.text();\n      }\n      if (props.text) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\")\n        }, [props.text]);\n      }\n    };\n    const renderContent = () => {\n      if (slots.default) {\n        return slots.default();\n      }\n      return [renderIcon(), renderText()];\n    };\n    return () => {\n      const {\n        center,\n        border,\n        square,\n        gutter,\n        reverse,\n        direction,\n        clickable\n      } = parent.props;\n      const classes = [bem(\"content\", [direction, {\n        center,\n        square,\n        reverse,\n        clickable,\n        surround: border && gutter\n      }]), {\n        [BORDER]: border\n      }];\n      return _createVNode(\"div\", {\n        \"class\": [bem({\n          square\n        })],\n        \"style\": rootStyle.value\n      }, [_createVNode(\"div\", {\n        \"role\": clickable ? \"button\" : void 0,\n        \"class\": classes,\n        \"style\": contentStyle.value,\n        \"tabindex\": clickable ? 0 : void 0,\n        \"onClick\": route\n      }, [renderContent()])]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  gridItemProps\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACvG,SAASC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AAC1F,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGR,eAAe,CAAC,WAAW,CAAC;AAChD,MAAMS,aAAa,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAEO,UAAU,EAAE;EAC3CM,GAAG,EAAEC,OAAO;EACZC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAED,MAAM;EACZE,KAAK,EAAEhB,WAAW;EAClBiB,SAAS,EAAEH,MAAM;EACjBI,UAAU,EAAEJ,MAAM;EAClBK,UAAU,EAAEC;AACd,CAAC,CAAC;AACF,IAAIC,aAAa,GAAG7B,eAAe,CAAC;EAClCgB,IAAI;EACJc,KAAK,EAAEZ,aAAa;EACpBa,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAM;MACJC,MAAM;MACNC;IACF,CAAC,GAAGvB,SAAS,CAACD,QAAQ,CAAC;IACvB,MAAMyB,KAAK,GAAGvB,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACqB,MAAM,EAAE;MACX,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,wDAAwD,CAAC;MACzE;MACA;IACF;IACA,MAAMC,SAAS,GAAG1C,QAAQ,CAAC,MAAM;MAC/B,MAAM;QACJ2C,MAAM;QACNC,MAAM;QACNC;MACF,CAAC,GAAGX,MAAM,CAACH,KAAK;MAChB,MAAMe,OAAO,GAAG,GAAG,GAAG,GAAG,CAACD,SAAS,GAAG;MACtC,MAAME,KAAK,GAAG;QACZC,SAAS,EAAEF;MACb,CAAC;MACD,IAAIH,MAAM,EAAE;QACVI,KAAK,CAACE,UAAU,GAAGH,OAAO;MAC5B,CAAC,MAAM,IAAIF,MAAM,EAAE;QACjB,MAAMM,WAAW,GAAG1C,OAAO,CAACoC,MAAM,CAAC;QACnCG,KAAK,CAACI,YAAY,GAAGD,WAAW;QAChC,IAAIf,KAAK,CAACiB,KAAK,IAAI,CAACP,SAAS,EAAE;UAC7BE,KAAK,CAACM,SAAS,GAAGH,WAAW;QAC/B;MACF;MACA,OAAOH,KAAK;IACd,CAAC,CAAC;IACF,MAAMO,YAAY,GAAGtD,QAAQ,CAAC,MAAM;MAClC,MAAM;QACJ2C,MAAM;QACNC;MACF,CAAC,GAAGV,MAAM,CAACH,KAAK;MAChB,IAAIY,MAAM,IAAIC,MAAM,EAAE;QACpB,MAAMM,WAAW,GAAG1C,OAAO,CAACoC,MAAM,CAAC;QACnC,OAAO;UACLW,KAAK,EAAEL,WAAW;UAClBM,MAAM,EAAEN,WAAW;UACnBO,MAAM,EAAE;QACV,CAAC;MACH;IACF,CAAC,CAAC;IACF,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIzB,KAAK,CAACT,IAAI,EAAE;QACd,OAAOnB,YAAY,CAACW,KAAK,EAAEb,WAAW,CAAC;UACrC,KAAK,EAAE4B,KAAK,CAACX,GAAG;UAChB,SAAS,EAAEW,KAAK,CAACN;QACnB,CAAC,EAAEM,KAAK,CAACH,UAAU,CAAC,EAAE;UACpB+B,OAAO,EAAE1B,KAAK,CAACT;QACjB,CAAC,CAAC;MACJ;MACA,IAAIO,KAAK,CAACP,IAAI,EAAE;QACd,OAAOnB,YAAY,CAACU,IAAI,EAAE;UACxB,KAAK,EAAEgB,KAAK,CAACX,GAAG;UAChB,MAAM,EAAEW,KAAK,CAACP,IAAI;UAClB,MAAM,EAAEU,MAAM,CAACH,KAAK,CAAC6B,QAAQ;UAC7B,OAAO,EAAE7B,KAAK,CAACN,KAAK;UACpB,OAAO,EAAEP,GAAG,CAAC,MAAM,CAAC;UACpB,OAAO,EAAEa,KAAK,CAACL,SAAS;UACxB,YAAY,EAAEK,KAAK,CAACH,UAAU;UAC9B,aAAa,EAAEG,KAAK,CAACJ;QACvB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,MAAMkC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI5B,KAAK,CAACX,IAAI,EAAE;QACd,OAAOW,KAAK,CAACX,IAAI,CAAC,CAAC;MACrB;MACA,IAAIS,KAAK,CAACT,IAAI,EAAE;QACd,OAAOjB,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEa,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAACa,KAAK,CAACT,IAAI,CAAC,CAAC;MAClB;IACF,CAAC;IACD,MAAMwC,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI7B,KAAK,CAAC0B,OAAO,EAAE;QACjB,OAAO1B,KAAK,CAAC0B,OAAO,CAAC,CAAC;MACxB;MACA,OAAO,CAACD,UAAU,CAAC,CAAC,EAAEG,UAAU,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,OAAO,MAAM;MACX,MAAM;QACJE,MAAM;QACNC,MAAM;QACNrB,MAAM;QACNC,MAAM;QACNqB,OAAO;QACPC,SAAS;QACTC;MACF,CAAC,GAAGjC,MAAM,CAACH,KAAK;MAChB,MAAMqC,OAAO,GAAG,CAAClD,GAAG,CAAC,SAAS,EAAE,CAACgD,SAAS,EAAE;QAC1CH,MAAM;QACNpB,MAAM;QACNsB,OAAO;QACPE,SAAS;QACTE,QAAQ,EAAEL,MAAM,IAAIpB;MACtB,CAAC,CAAC,CAAC,EAAE;QACH,CAACtC,MAAM,GAAG0D;MACZ,CAAC,CAAC;MACF,OAAO3D,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAE,CAACa,GAAG,CAAC;UACZyB;QACF,CAAC,CAAC,CAAC;QACH,OAAO,EAAED,SAAS,CAACU;MACrB,CAAC,EAAE,CAAC/C,YAAY,CAAC,KAAK,EAAE;QACtB,MAAM,EAAE8D,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;QACrC,OAAO,EAAEC,OAAO;QAChB,OAAO,EAAEd,YAAY,CAACF,KAAK;QAC3B,UAAU,EAAEe,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC;QAClC,SAAS,EAAE/B;MACb,CAAC,EAAE,CAAC0B,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEhC,aAAa,IAAI6B,OAAO,EACxBxC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}