{"ast": null, "code": "import { ref, watch, computed, nextTick, reactive, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, isHidden, unitToPx, numericProp, windowWidth, windowHeight, getScrollTop, getZIndexStyle, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useRect, useEventListener, useScrollParent } from \"@vant/use\";\nimport { useVisibilityChange } from \"../composables/use-visibility-change.mjs\";\nconst [name, bem] = createNamespace(\"sticky\");\nconst stickyProps = {\n  zIndex: numericProp,\n  position: makeStringProp(\"top\"),\n  container: Object,\n  offsetTop: makeNumericProp(0),\n  offsetBottom: makeNumericProp(0)\n};\nvar stdin_default = defineComponent({\n  name,\n  props: stickyProps,\n  emits: [\"scroll\", \"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const scrollParent = useScrollParent(root);\n    const state = reactive({\n      fixed: false,\n      width: 0,\n      // root width\n      height: 0,\n      // root height\n      transform: 0\n    });\n    const isReset = ref(false);\n    const offset = computed(() => unitToPx(props.position === \"top\" ? props.offsetTop : props.offsetBottom));\n    const rootStyle = computed(() => {\n      if (isReset.value) {\n        return;\n      }\n      const {\n        fixed,\n        height,\n        width\n      } = state;\n      if (fixed) {\n        return {\n          width: `${width}px`,\n          height: `${height}px`\n        };\n      }\n    });\n    const stickyStyle = computed(() => {\n      if (!state.fixed || isReset.value) {\n        return;\n      }\n      const style = extend(getZIndexStyle(props.zIndex), {\n        width: `${state.width}px`,\n        height: `${state.height}px`,\n        [props.position]: `${offset.value}px`\n      });\n      if (state.transform) {\n        style.transform = `translate3d(0, ${state.transform}px, 0)`;\n      }\n      return style;\n    });\n    const emitScroll = scrollTop => emit(\"scroll\", {\n      scrollTop,\n      isFixed: state.fixed\n    });\n    const onScroll = () => {\n      if (!root.value || isHidden(root)) {\n        return;\n      }\n      const {\n        container,\n        position\n      } = props;\n      const rootRect = useRect(root);\n      const scrollTop = getScrollTop(window);\n      state.width = rootRect.width;\n      state.height = rootRect.height;\n      if (position === \"top\") {\n        if (container) {\n          const containerRect = useRect(container);\n          const difference = containerRect.bottom - offset.value - state.height;\n          state.fixed = offset.value > rootRect.top && containerRect.bottom > 0;\n          state.transform = difference < 0 ? difference : 0;\n        } else {\n          state.fixed = offset.value > rootRect.top;\n        }\n      } else {\n        const {\n          clientHeight\n        } = document.documentElement;\n        if (container) {\n          const containerRect = useRect(container);\n          const difference = clientHeight - containerRect.top - offset.value - state.height;\n          state.fixed = clientHeight - offset.value < rootRect.bottom && clientHeight > containerRect.top;\n          state.transform = difference < 0 ? -difference : 0;\n        } else {\n          state.fixed = clientHeight - offset.value < rootRect.bottom;\n        }\n      }\n      emitScroll(scrollTop);\n    };\n    watch(() => state.fixed, value => emit(\"change\", value));\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    useVisibilityChange(root, onScroll);\n    watch([windowWidth, windowHeight], () => {\n      if (!root.value || isHidden(root) || !state.fixed) {\n        return;\n      }\n      isReset.value = true;\n      nextTick(() => {\n        const rootRect = useRect(root);\n        state.width = rootRect.width;\n        state.height = rootRect.height;\n        isReset.value = false;\n      });\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"style\": rootStyle.value\n      }, [_createVNode(\"div\", {\n        \"class\": bem({\n          fixed: state.fixed && !isReset.value\n        }),\n        \"style\": stickyStyle.value\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport { stdin_default as default, stickyProps };", "map": {"version": 3, "names": ["ref", "watch", "computed", "nextTick", "reactive", "defineComponent", "createVNode", "_createVNode", "extend", "isHidden", "unitToPx", "numericProp", "windowWidth", "windowHeight", "getScrollTop", "getZIndexStyle", "makeStringProp", "makeNumericProp", "createNamespace", "useRect", "useEventListener", "useScrollParent", "useVisibilityChange", "name", "bem", "stickyProps", "zIndex", "position", "container", "Object", "offsetTop", "offsetBottom", "stdin_default", "props", "emits", "setup", "emit", "slots", "root", "scrollParent", "state", "fixed", "width", "height", "transform", "isReset", "offset", "rootStyle", "value", "stickyStyle", "style", "emitScroll", "scrollTop", "isFixed", "onScroll", "rootRect", "window", "containerRect", "difference", "bottom", "top", "clientHeight", "document", "documentElement", "target", "passive", "_a", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/sticky/Sticky.mjs"], "sourcesContent": ["import { ref, watch, computed, nextTick, reactive, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, isHidden, unitToPx, numericProp, windowWidth, windowHeight, getScrollTop, getZIndexStyle, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useRect, useEventListener, useScrollParent } from \"@vant/use\";\nimport { useVisibilityChange } from \"../composables/use-visibility-change.mjs\";\nconst [name, bem] = createNamespace(\"sticky\");\nconst stickyProps = {\n  zIndex: numericProp,\n  position: makeStringProp(\"top\"),\n  container: Object,\n  offsetTop: makeNumericProp(0),\n  offsetBottom: makeNumericProp(0)\n};\nvar stdin_default = defineComponent({\n  name,\n  props: stickyProps,\n  emits: [\"scroll\", \"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const scrollParent = useScrollParent(root);\n    const state = reactive({\n      fixed: false,\n      width: 0,\n      // root width\n      height: 0,\n      // root height\n      transform: 0\n    });\n    const isReset = ref(false);\n    const offset = computed(() => unitToPx(props.position === \"top\" ? props.offsetTop : props.offsetBottom));\n    const rootStyle = computed(() => {\n      if (isReset.value) {\n        return;\n      }\n      const {\n        fixed,\n        height,\n        width\n      } = state;\n      if (fixed) {\n        return {\n          width: `${width}px`,\n          height: `${height}px`\n        };\n      }\n    });\n    const stickyStyle = computed(() => {\n      if (!state.fixed || isReset.value) {\n        return;\n      }\n      const style = extend(getZIndexStyle(props.zIndex), {\n        width: `${state.width}px`,\n        height: `${state.height}px`,\n        [props.position]: `${offset.value}px`\n      });\n      if (state.transform) {\n        style.transform = `translate3d(0, ${state.transform}px, 0)`;\n      }\n      return style;\n    });\n    const emitScroll = (scrollTop) => emit(\"scroll\", {\n      scrollTop,\n      isFixed: state.fixed\n    });\n    const onScroll = () => {\n      if (!root.value || isHidden(root)) {\n        return;\n      }\n      const {\n        container,\n        position\n      } = props;\n      const rootRect = useRect(root);\n      const scrollTop = getScrollTop(window);\n      state.width = rootRect.width;\n      state.height = rootRect.height;\n      if (position === \"top\") {\n        if (container) {\n          const containerRect = useRect(container);\n          const difference = containerRect.bottom - offset.value - state.height;\n          state.fixed = offset.value > rootRect.top && containerRect.bottom > 0;\n          state.transform = difference < 0 ? difference : 0;\n        } else {\n          state.fixed = offset.value > rootRect.top;\n        }\n      } else {\n        const {\n          clientHeight\n        } = document.documentElement;\n        if (container) {\n          const containerRect = useRect(container);\n          const difference = clientHeight - containerRect.top - offset.value - state.height;\n          state.fixed = clientHeight - offset.value < rootRect.bottom && clientHeight > containerRect.top;\n          state.transform = difference < 0 ? -difference : 0;\n        } else {\n          state.fixed = clientHeight - offset.value < rootRect.bottom;\n        }\n      }\n      emitScroll(scrollTop);\n    };\n    watch(() => state.fixed, (value) => emit(\"change\", value));\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    useVisibilityChange(root, onScroll);\n    watch([windowWidth, windowHeight], () => {\n      if (!root.value || isHidden(root) || !state.fixed) {\n        return;\n      }\n      isReset.value = true;\n      nextTick(() => {\n        const rootRect = useRect(root);\n        state.width = rootRect.width;\n        state.height = rootRect.height;\n        isReset.value = false;\n      });\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"style\": rootStyle.value\n      }, [_createVNode(\"div\", {\n        \"class\": bem({\n          fixed: state.fixed && !isReset.value\n        }),\n        \"style\": stickyStyle.value\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)])]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  stickyProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5G,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACvL,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,WAAW;AACtE,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGN,eAAe,CAAC,QAAQ,CAAC;AAC7C,MAAMO,WAAW,GAAG;EAClBC,MAAM,EAAEf,WAAW;EACnBgB,QAAQ,EAAEX,cAAc,CAAC,KAAK,CAAC;EAC/BY,SAAS,EAAEC,MAAM;EACjBC,SAAS,EAAEb,eAAe,CAAC,CAAC,CAAC;EAC7Bc,YAAY,EAAEd,eAAe,CAAC,CAAC;AACjC,CAAC;AACD,IAAIe,aAAa,GAAG3B,eAAe,CAAC;EAClCkB,IAAI;EACJU,KAAK,EAAER,WAAW;EAClBS,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC3BC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAGtC,GAAG,CAAC,CAAC;IAClB,MAAMuC,YAAY,GAAGlB,eAAe,CAACiB,IAAI,CAAC;IAC1C,MAAME,KAAK,GAAGpC,QAAQ,CAAC;MACrBqC,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE,CAAC;MACR;MACAC,MAAM,EAAE,CAAC;MACT;MACAC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMC,OAAO,GAAG7C,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAM8C,MAAM,GAAG5C,QAAQ,CAAC,MAAMQ,QAAQ,CAACuB,KAAK,CAACN,QAAQ,KAAK,KAAK,GAAGM,KAAK,CAACH,SAAS,GAAGG,KAAK,CAACF,YAAY,CAAC,CAAC;IACxG,MAAMgB,SAAS,GAAG7C,QAAQ,CAAC,MAAM;MAC/B,IAAI2C,OAAO,CAACG,KAAK,EAAE;QACjB;MACF;MACA,MAAM;QACJP,KAAK;QACLE,MAAM;QACND;MACF,CAAC,GAAGF,KAAK;MACT,IAAIC,KAAK,EAAE;QACT,OAAO;UACLC,KAAK,EAAE,GAAGA,KAAK,IAAI;UACnBC,MAAM,EAAE,GAAGA,MAAM;QACnB,CAAC;MACH;IACF,CAAC,CAAC;IACF,MAAMM,WAAW,GAAG/C,QAAQ,CAAC,MAAM;MACjC,IAAI,CAACsC,KAAK,CAACC,KAAK,IAAII,OAAO,CAACG,KAAK,EAAE;QACjC;MACF;MACA,MAAME,KAAK,GAAG1C,MAAM,CAACO,cAAc,CAACkB,KAAK,CAACP,MAAM,CAAC,EAAE;QACjDgB,KAAK,EAAE,GAAGF,KAAK,CAACE,KAAK,IAAI;QACzBC,MAAM,EAAE,GAAGH,KAAK,CAACG,MAAM,IAAI;QAC3B,CAACV,KAAK,CAACN,QAAQ,GAAG,GAAGmB,MAAM,CAACE,KAAK;MACnC,CAAC,CAAC;MACF,IAAIR,KAAK,CAACI,SAAS,EAAE;QACnBM,KAAK,CAACN,SAAS,GAAG,kBAAkBJ,KAAK,CAACI,SAAS,QAAQ;MAC7D;MACA,OAAOM,KAAK;IACd,CAAC,CAAC;IACF,MAAMC,UAAU,GAAIC,SAAS,IAAKhB,IAAI,CAAC,QAAQ,EAAE;MAC/CgB,SAAS;MACTC,OAAO,EAAEb,KAAK,CAACC;IACjB,CAAC,CAAC;IACF,MAAMa,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI,CAAChB,IAAI,CAACU,KAAK,IAAIvC,QAAQ,CAAC6B,IAAI,CAAC,EAAE;QACjC;MACF;MACA,MAAM;QACJV,SAAS;QACTD;MACF,CAAC,GAAGM,KAAK;MACT,MAAMsB,QAAQ,GAAGpC,OAAO,CAACmB,IAAI,CAAC;MAC9B,MAAMc,SAAS,GAAGtC,YAAY,CAAC0C,MAAM,CAAC;MACtChB,KAAK,CAACE,KAAK,GAAGa,QAAQ,CAACb,KAAK;MAC5BF,KAAK,CAACG,MAAM,GAAGY,QAAQ,CAACZ,MAAM;MAC9B,IAAIhB,QAAQ,KAAK,KAAK,EAAE;QACtB,IAAIC,SAAS,EAAE;UACb,MAAM6B,aAAa,GAAGtC,OAAO,CAACS,SAAS,CAAC;UACxC,MAAM8B,UAAU,GAAGD,aAAa,CAACE,MAAM,GAAGb,MAAM,CAACE,KAAK,GAAGR,KAAK,CAACG,MAAM;UACrEH,KAAK,CAACC,KAAK,GAAGK,MAAM,CAACE,KAAK,GAAGO,QAAQ,CAACK,GAAG,IAAIH,aAAa,CAACE,MAAM,GAAG,CAAC;UACrEnB,KAAK,CAACI,SAAS,GAAGc,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC;QACnD,CAAC,MAAM;UACLlB,KAAK,CAACC,KAAK,GAAGK,MAAM,CAACE,KAAK,GAAGO,QAAQ,CAACK,GAAG;QAC3C;MACF,CAAC,MAAM;QACL,MAAM;UACJC;QACF,CAAC,GAAGC,QAAQ,CAACC,eAAe;QAC5B,IAAInC,SAAS,EAAE;UACb,MAAM6B,aAAa,GAAGtC,OAAO,CAACS,SAAS,CAAC;UACxC,MAAM8B,UAAU,GAAGG,YAAY,GAAGJ,aAAa,CAACG,GAAG,GAAGd,MAAM,CAACE,KAAK,GAAGR,KAAK,CAACG,MAAM;UACjFH,KAAK,CAACC,KAAK,GAAGoB,YAAY,GAAGf,MAAM,CAACE,KAAK,GAAGO,QAAQ,CAACI,MAAM,IAAIE,YAAY,GAAGJ,aAAa,CAACG,GAAG;UAC/FpB,KAAK,CAACI,SAAS,GAAGc,UAAU,GAAG,CAAC,GAAG,CAACA,UAAU,GAAG,CAAC;QACpD,CAAC,MAAM;UACLlB,KAAK,CAACC,KAAK,GAAGoB,YAAY,GAAGf,MAAM,CAACE,KAAK,GAAGO,QAAQ,CAACI,MAAM;QAC7D;MACF;MACAR,UAAU,CAACC,SAAS,CAAC;IACvB,CAAC;IACDnD,KAAK,CAAC,MAAMuC,KAAK,CAACC,KAAK,EAAGO,KAAK,IAAKZ,IAAI,CAAC,QAAQ,EAAEY,KAAK,CAAC,CAAC;IAC1D5B,gBAAgB,CAAC,QAAQ,EAAEkC,QAAQ,EAAE;MACnCU,MAAM,EAAEzB,YAAY;MACpB0B,OAAO,EAAE;IACX,CAAC,CAAC;IACF3C,mBAAmB,CAACgB,IAAI,EAAEgB,QAAQ,CAAC;IACnCrD,KAAK,CAAC,CAACW,WAAW,EAAEC,YAAY,CAAC,EAAE,MAAM;MACvC,IAAI,CAACyB,IAAI,CAACU,KAAK,IAAIvC,QAAQ,CAAC6B,IAAI,CAAC,IAAI,CAACE,KAAK,CAACC,KAAK,EAAE;QACjD;MACF;MACAI,OAAO,CAACG,KAAK,GAAG,IAAI;MACpB7C,QAAQ,CAAC,MAAM;QACb,MAAMoD,QAAQ,GAAGpC,OAAO,CAACmB,IAAI,CAAC;QAC9BE,KAAK,CAACE,KAAK,GAAGa,QAAQ,CAACb,KAAK;QAC5BF,KAAK,CAACG,MAAM,GAAGY,QAAQ,CAACZ,MAAM;QAC9BE,OAAO,CAACG,KAAK,GAAG,KAAK;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIkB,EAAE;MACN,OAAO3D,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAE+B,IAAI;QACX,OAAO,EAAES,SAAS,CAACC;MACrB,CAAC,EAAE,CAACzC,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEiB,GAAG,CAAC;UACXiB,KAAK,EAAED,KAAK,CAACC,KAAK,IAAI,CAACI,OAAO,CAACG;QACjC,CAAC,CAAC;QACF,OAAO,EAAEC,WAAW,CAACD;MACvB,CAAC,EAAE,CAAC,CAACkB,EAAE,GAAG7B,KAAK,CAAC8B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAAC/B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEL,aAAa,IAAImC,OAAO,EACxB1C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}