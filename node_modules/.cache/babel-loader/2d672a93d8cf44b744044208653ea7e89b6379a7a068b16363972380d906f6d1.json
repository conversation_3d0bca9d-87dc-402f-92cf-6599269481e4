{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _DropdownMenu from \"./DropdownMenu.mjs\";\nconst DropdownMenu = withInstall(_DropdownMenu);\nvar stdin_default = DropdownMenu;\nimport { dropdownMenuProps } from \"./DropdownMenu.mjs\";\nexport { DropdownMenu, stdin_default as default, dropdownMenuProps };", "map": {"version": 3, "names": ["withInstall", "_DropdownMenu", "DropdownMenu", "stdin_default", "dropdownMenuProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/dropdown-menu/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _DropdownMenu from \"./DropdownMenu.mjs\";\nconst DropdownMenu = withInstall(_DropdownMenu);\nvar stdin_default = DropdownMenu;\nimport { dropdownMenuProps } from \"./DropdownMenu.mjs\";\nexport {\n  DropdownMenu,\n  stdin_default as default,\n  dropdownMenuProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,MAAMC,YAAY,GAAGF,WAAW,CAACC,aAAa,CAAC;AAC/C,IAAIE,aAAa,GAAGD,YAAY;AAChC,SAASE,iBAAiB,QAAQ,oBAAoB;AACtD,SACEF,YAAY,EACZC,aAAa,IAAIE,OAAO,EACxBD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}