{"ast": null, "code": "import { watch, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { unknownProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren, useCustomFieldValue } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"radio-group\");\nconst radioGroupProps = {\n  shape: String,\n  disabled: Boolean,\n  iconSize: numericProp,\n  direction: String,\n  modelValue: unknownProp,\n  checkedColor: String\n};\nconst RADIO_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: radioGroupProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      linkChildren\n    } = useChildren(RADIO_KEY);\n    const updateValue = value => emit(\"update:modelValue\", value);\n    watch(() => props.modelValue, value => emit(\"change\", value));\n    linkChildren({\n      props,\n      updateValue\n    });\n    useCustomFieldValue(() => props.modelValue);\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem([props.direction]),\n        \"role\": \"radiogroup\"\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { RADIO_KEY, stdin_default as default, radioGroupProps };", "map": {"version": 3, "names": ["watch", "defineComponent", "createVNode", "_createVNode", "unknownProp", "numericProp", "createNamespace", "useChildren", "useCustomFieldValue", "name", "bem", "radioGroupProps", "shape", "String", "disabled", "Boolean", "iconSize", "direction", "modelValue", "checkedColor", "RADIO_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "emit", "slots", "linkChildren", "updateValue", "value", "_a", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/radio-group/RadioGroup.mjs"], "sourcesContent": ["import { watch, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { unknownProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren, useCustomFieldValue } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"radio-group\");\nconst radioGroupProps = {\n  shape: String,\n  disabled: Boolean,\n  iconSize: numericProp,\n  direction: String,\n  modelValue: unknownProp,\n  checkedColor: String\n};\nconst RADIO_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: radioGroupProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      linkChildren\n    } = useChildren(RADIO_KEY);\n    const updateValue = (value) => emit(\"update:modelValue\", value);\n    watch(() => props.modelValue, (value) => emit(\"change\", value));\n    linkChildren({\n      props,\n      updateValue\n    });\n    useCustomFieldValue(() => props.modelValue);\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem([props.direction]),\n        \"role\": \"radiogroup\"\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  RADIO_KEY,\n  stdin_default as default,\n  radioGroupProps\n};\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACzE,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AAC9E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,WAAW;AAC5D,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,aAAa,CAAC;AAClD,MAAMK,eAAe,GAAG;EACtBC,KAAK,EAAEC,MAAM;EACbC,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAEX,WAAW;EACrBY,SAAS,EAAEJ,MAAM;EACjBK,UAAU,EAAEd,WAAW;EACvBe,YAAY,EAAEN;AAChB,CAAC;AACD,MAAMO,SAAS,GAAGC,MAAM,CAACZ,IAAI,CAAC;AAC9B,IAAIa,aAAa,GAAGrB,eAAe,CAAC;EAClCQ,IAAI;EACJc,KAAK,EAAEZ,eAAe;EACtBa,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAM;MACJC;IACF,CAAC,GAAGrB,WAAW,CAACa,SAAS,CAAC;IAC1B,MAAMS,WAAW,GAAIC,KAAK,IAAKJ,IAAI,CAAC,mBAAmB,EAAEI,KAAK,CAAC;IAC/D9B,KAAK,CAAC,MAAMuB,KAAK,CAACL,UAAU,EAAGY,KAAK,IAAKJ,IAAI,CAAC,QAAQ,EAAEI,KAAK,CAAC,CAAC;IAC/DF,YAAY,CAAC;MACXL,KAAK;MACLM;IACF,CAAC,CAAC;IACFrB,mBAAmB,CAAC,MAAMe,KAAK,CAACL,UAAU,CAAC;IAC3C,OAAO,MAAM;MACX,IAAIa,EAAE;MACN,OAAO5B,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEO,GAAG,CAAC,CAACa,KAAK,CAACN,SAAS,CAAC,CAAC;QAC/B,MAAM,EAAE;MACV,CAAC,EAAE,CAAC,CAACc,EAAE,GAAGJ,KAAK,CAACK,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEP,SAAS,EACTE,aAAa,IAAIU,OAAO,EACxBrB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}