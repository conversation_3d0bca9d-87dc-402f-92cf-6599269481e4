{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CheckboxGroup from \"./CheckboxGroup.mjs\";\nconst CheckboxGroup = withInstall(_CheckboxGroup);\nvar stdin_default = CheckboxGroup;\nimport { checkboxGroupProps } from \"./CheckboxGroup.mjs\";\nexport { CheckboxGroup, checkboxGroupProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CheckboxGroup", "CheckboxGroup", "stdin_default", "checkboxGroupProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/checkbox-group/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CheckboxGroup from \"./CheckboxGroup.mjs\";\nconst CheckboxGroup = withInstall(_CheckboxGroup);\nvar stdin_default = CheckboxGroup;\nimport { checkboxGroupProps } from \"./CheckboxGroup.mjs\";\nexport {\n  CheckboxGroup,\n  checkboxGroupProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,MAAMC,aAAa,GAAGF,WAAW,CAACC,cAAc,CAAC;AACjD,IAAIE,aAAa,GAAGD,aAAa;AACjC,SAASE,kBAAkB,QAAQ,qBAAqB;AACxD,SACEF,aAAa,EACbE,kBAAkB,EAClBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}