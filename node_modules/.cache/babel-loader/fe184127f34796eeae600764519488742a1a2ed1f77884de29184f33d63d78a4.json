{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { watch, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { raf, cancelRaf } from \"@vant/use\";\nimport { isObject, truthProp, numericProp, getSizeStyle, makeStringProp, makeNumberProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"circle\");\nlet uid = 0;\nconst format = rate => Math.min(Math.max(+rate, 0), 100);\nfunction getPath(clockwise, viewBoxSize) {\n  const sweepFlag = clockwise ? 1 : 0;\n  return `M ${viewBoxSize / 2} ${viewBoxSize / 2} m 0, -500 a 500, 500 0 1, ${sweepFlag} 0, 1000 a 500, 500 0 1, ${sweepFlag} 0, -1000`;\n}\nconst circleProps = {\n  text: String,\n  size: numericProp,\n  fill: makeStringProp(\"none\"),\n  rate: makeNumericProp(100),\n  speed: makeNumericProp(0),\n  color: [String, Object],\n  clockwise: truthProp,\n  layerColor: String,\n  currentRate: makeNumberProp(0),\n  strokeWidth: makeNumericProp(40),\n  strokeLinecap: String,\n  startPosition: makeStringProp(\"top\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: circleProps,\n  emits: [\"update:currentRate\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const id = `van-circle-${uid++}`;\n    const viewBoxSize = computed(() => +props.strokeWidth + 1e3);\n    const path = computed(() => getPath(props.clockwise, viewBoxSize.value));\n    const svgStyle = computed(() => {\n      const ROTATE_ANGLE_MAP = {\n        top: 0,\n        right: 90,\n        bottom: 180,\n        left: 270\n      };\n      const angleValue = ROTATE_ANGLE_MAP[props.startPosition];\n      if (angleValue) {\n        return {\n          transform: `rotate(${angleValue}deg)`\n        };\n      }\n    });\n    watch(() => props.rate, rate => {\n      let rafId;\n      const startTime = Date.now();\n      const startRate = props.currentRate;\n      const endRate = format(rate);\n      const duration = Math.abs((startRate - endRate) * 1e3 / +props.speed);\n      const animate = () => {\n        const now = Date.now();\n        const progress = Math.min((now - startTime) / duration, 1);\n        const rate2 = progress * (endRate - startRate) + startRate;\n        emit(\"update:currentRate\", format(parseFloat(rate2.toFixed(1))));\n        if (endRate > startRate ? rate2 < endRate : rate2 > endRate) {\n          rafId = raf(animate);\n        }\n      };\n      if (props.speed) {\n        if (rafId) {\n          cancelRaf(rafId);\n        }\n        rafId = raf(animate);\n      } else {\n        emit(\"update:currentRate\", endRate);\n      }\n    }, {\n      immediate: true\n    });\n    const renderHover = () => {\n      const PERIMETER = 3140;\n      const {\n        strokeWidth,\n        currentRate,\n        strokeLinecap\n      } = props;\n      const offset = PERIMETER * currentRate / 100;\n      const color = isObject(props.color) ? `url(#${id})` : props.color;\n      const style = {\n        stroke: color,\n        strokeWidth: `${+strokeWidth + 1}px`,\n        strokeLinecap,\n        strokeDasharray: `${offset}px ${PERIMETER}px`\n      };\n      return _createVNode(\"path\", {\n        \"d\": path.value,\n        \"style\": style,\n        \"class\": bem(\"hover\"),\n        \"stroke\": color\n      }, null);\n    };\n    const renderLayer = () => {\n      const style = {\n        fill: props.fill,\n        stroke: props.layerColor,\n        strokeWidth: `${props.strokeWidth}px`\n      };\n      return _createVNode(\"path\", {\n        \"class\": bem(\"layer\"),\n        \"style\": style,\n        \"d\": path.value\n      }, null);\n    };\n    const renderGradient = () => {\n      const {\n        color\n      } = props;\n      if (!isObject(color)) {\n        return;\n      }\n      const Stops = Object.keys(color).sort((a, b) => parseFloat(a) - parseFloat(b)).map((key, index) => _createVNode(\"stop\", {\n        \"key\": index,\n        \"offset\": key,\n        \"stop-color\": color[key]\n      }, null));\n      return _createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n        \"id\": id,\n        \"x1\": \"100%\",\n        \"y1\": \"0%\",\n        \"x2\": \"0%\",\n        \"y2\": \"0%\"\n      }, [Stops])]);\n    };\n    const renderText = () => {\n      if (slots.default) {\n        return slots.default();\n      }\n      if (props.text) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"text\")\n        }, [props.text]);\n      }\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(),\n      \"style\": getSizeStyle(props.size)\n    }, [_createVNode(\"svg\", {\n      \"viewBox\": `0 0 ${viewBoxSize.value} ${viewBoxSize.value}`,\n      \"style\": svgStyle.value\n    }, [renderGradient(), renderLayer(), renderHover()]), renderText()]);\n  }\n});\nexport { circleProps, stdin_default as default };", "map": {"version": 3, "names": ["watch", "computed", "defineComponent", "createVNode", "_createVNode", "raf", "cancelRaf", "isObject", "truthProp", "numericProp", "getSizeStyle", "makeStringProp", "makeNumberProp", "makeNumericProp", "createNamespace", "name", "bem", "uid", "format", "rate", "Math", "min", "max", "<PERSON><PERSON><PERSON>", "clockwise", "viewBoxSize", "sweepFlag", "circleProps", "text", "String", "size", "fill", "speed", "color", "Object", "layerColor", "currentRate", "strokeWidth", "strokeLinecap", "startPosition", "stdin_default", "props", "emits", "setup", "emit", "slots", "id", "path", "value", "svgStyle", "ROTATE_ANGLE_MAP", "top", "right", "bottom", "left", "angleValue", "transform", "rafId", "startTime", "Date", "now", "startRate", "endRate", "duration", "abs", "animate", "progress", "rate2", "parseFloat", "toFixed", "immediate", "renderHover", "PERIMETER", "offset", "style", "stroke", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "renderGradient", "Stops", "keys", "sort", "a", "b", "map", "key", "index", "renderText", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/circle/Circle.mjs"], "sourcesContent": ["import { watch, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { raf, cancelRaf } from \"@vant/use\";\nimport { isObject, truthProp, numericProp, getSizeStyle, makeStringProp, makeNumberProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"circle\");\nlet uid = 0;\nconst format = (rate) => Math.min(Math.max(+rate, 0), 100);\nfunction getPath(clockwise, viewBoxSize) {\n  const sweepFlag = clockwise ? 1 : 0;\n  return `M ${viewBoxSize / 2} ${viewBoxSize / 2} m 0, -500 a 500, 500 0 1, ${sweepFlag} 0, 1000 a 500, 500 0 1, ${sweepFlag} 0, -1000`;\n}\nconst circleProps = {\n  text: String,\n  size: numericProp,\n  fill: makeStringProp(\"none\"),\n  rate: makeNumericProp(100),\n  speed: makeNumericProp(0),\n  color: [String, Object],\n  clockwise: truthProp,\n  layerColor: String,\n  currentRate: makeNumberProp(0),\n  strokeWidth: makeNumericProp(40),\n  strokeLinecap: String,\n  startPosition: makeStringProp(\"top\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: circleProps,\n  emits: [\"update:currentRate\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const id = `van-circle-${uid++}`;\n    const viewBoxSize = computed(() => +props.strokeWidth + 1e3);\n    const path = computed(() => getPath(props.clockwise, viewBoxSize.value));\n    const svgStyle = computed(() => {\n      const ROTATE_ANGLE_MAP = {\n        top: 0,\n        right: 90,\n        bottom: 180,\n        left: 270\n      };\n      const angleValue = ROTATE_ANGLE_MAP[props.startPosition];\n      if (angleValue) {\n        return {\n          transform: `rotate(${angleValue}deg)`\n        };\n      }\n    });\n    watch(() => props.rate, (rate) => {\n      let rafId;\n      const startTime = Date.now();\n      const startRate = props.currentRate;\n      const endRate = format(rate);\n      const duration = Math.abs((startRate - endRate) * 1e3 / +props.speed);\n      const animate = () => {\n        const now = Date.now();\n        const progress = Math.min((now - startTime) / duration, 1);\n        const rate2 = progress * (endRate - startRate) + startRate;\n        emit(\"update:currentRate\", format(parseFloat(rate2.toFixed(1))));\n        if (endRate > startRate ? rate2 < endRate : rate2 > endRate) {\n          rafId = raf(animate);\n        }\n      };\n      if (props.speed) {\n        if (rafId) {\n          cancelRaf(rafId);\n        }\n        rafId = raf(animate);\n      } else {\n        emit(\"update:currentRate\", endRate);\n      }\n    }, {\n      immediate: true\n    });\n    const renderHover = () => {\n      const PERIMETER = 3140;\n      const {\n        strokeWidth,\n        currentRate,\n        strokeLinecap\n      } = props;\n      const offset = PERIMETER * currentRate / 100;\n      const color = isObject(props.color) ? `url(#${id})` : props.color;\n      const style = {\n        stroke: color,\n        strokeWidth: `${+strokeWidth + 1}px`,\n        strokeLinecap,\n        strokeDasharray: `${offset}px ${PERIMETER}px`\n      };\n      return _createVNode(\"path\", {\n        \"d\": path.value,\n        \"style\": style,\n        \"class\": bem(\"hover\"),\n        \"stroke\": color\n      }, null);\n    };\n    const renderLayer = () => {\n      const style = {\n        fill: props.fill,\n        stroke: props.layerColor,\n        strokeWidth: `${props.strokeWidth}px`\n      };\n      return _createVNode(\"path\", {\n        \"class\": bem(\"layer\"),\n        \"style\": style,\n        \"d\": path.value\n      }, null);\n    };\n    const renderGradient = () => {\n      const {\n        color\n      } = props;\n      if (!isObject(color)) {\n        return;\n      }\n      const Stops = Object.keys(color).sort((a, b) => parseFloat(a) - parseFloat(b)).map((key, index) => _createVNode(\"stop\", {\n        \"key\": index,\n        \"offset\": key,\n        \"stop-color\": color[key]\n      }, null));\n      return _createVNode(\"defs\", null, [_createVNode(\"linearGradient\", {\n        \"id\": id,\n        \"x1\": \"100%\",\n        \"y1\": \"0%\",\n        \"x2\": \"0%\",\n        \"y2\": \"0%\"\n      }, [Stops])]);\n    };\n    const renderText = () => {\n      if (slots.default) {\n        return slots.default();\n      }\n      if (props.text) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"text\")\n        }, [props.text]);\n      }\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(),\n      \"style\": getSizeStyle(props.size)\n    }, [_createVNode(\"svg\", {\n      \"viewBox\": `0 0 ${viewBoxSize.value} ${viewBoxSize.value}`,\n      \"style\": svgStyle.value\n    }, [renderGradient(), renderLayer(), renderHover()]), renderText()]);\n  }\n});\nexport {\n  circleProps,\n  stdin_default as default\n};\n"], "mappings": ";;AAAA,SAASA,KAAK,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACnF,SAASC,GAAG,EAAEC,SAAS,QAAQ,WAAW;AAC1C,SAASC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACrJ,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGF,eAAe,CAAC,QAAQ,CAAC;AAC7C,IAAIG,GAAG,GAAG,CAAC;AACX,MAAMC,MAAM,GAAIC,IAAI,IAAKC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAACH,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;AAC1D,SAASI,OAAOA,CAACC,SAAS,EAAEC,WAAW,EAAE;EACvC,MAAMC,SAAS,GAAGF,SAAS,GAAG,CAAC,GAAG,CAAC;EACnC,OAAO,KAAKC,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,CAAC,8BAA8BC,SAAS,4BAA4BA,SAAS,WAAW;AACvI;AACA,MAAMC,WAAW,GAAG;EAClBC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAErB,WAAW;EACjBsB,IAAI,EAAEpB,cAAc,CAAC,MAAM,CAAC;EAC5BQ,IAAI,EAAEN,eAAe,CAAC,GAAG,CAAC;EAC1BmB,KAAK,EAAEnB,eAAe,CAAC,CAAC,CAAC;EACzBoB,KAAK,EAAE,CAACJ,MAAM,EAAEK,MAAM,CAAC;EACvBV,SAAS,EAAEhB,SAAS;EACpB2B,UAAU,EAAEN,MAAM;EAClBO,WAAW,EAAExB,cAAc,CAAC,CAAC,CAAC;EAC9ByB,WAAW,EAAExB,eAAe,CAAC,EAAE,CAAC;EAChCyB,aAAa,EAAET,MAAM;EACrBU,aAAa,EAAE5B,cAAc,CAAC,KAAK;AACrC,CAAC;AACD,IAAI6B,aAAa,GAAGtC,eAAe,CAAC;EAClCa,IAAI;EACJ0B,KAAK,EAAEd,WAAW;EAClBe,KAAK,EAAE,CAAC,oBAAoB,CAAC;EAC7BC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,EAAE,GAAG,cAAc7B,GAAG,EAAE,EAAE;IAChC,MAAMQ,WAAW,GAAGxB,QAAQ,CAAC,MAAM,CAACwC,KAAK,CAACJ,WAAW,GAAG,GAAG,CAAC;IAC5D,MAAMU,IAAI,GAAG9C,QAAQ,CAAC,MAAMsB,OAAO,CAACkB,KAAK,CAACjB,SAAS,EAAEC,WAAW,CAACuB,KAAK,CAAC,CAAC;IACxE,MAAMC,QAAQ,GAAGhD,QAAQ,CAAC,MAAM;MAC9B,MAAMiD,gBAAgB,GAAG;QACvBC,GAAG,EAAE,CAAC;QACNC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,GAAG;QACXC,IAAI,EAAE;MACR,CAAC;MACD,MAAMC,UAAU,GAAGL,gBAAgB,CAACT,KAAK,CAACF,aAAa,CAAC;MACxD,IAAIgB,UAAU,EAAE;QACd,OAAO;UACLC,SAAS,EAAE,UAAUD,UAAU;QACjC,CAAC;MACH;IACF,CAAC,CAAC;IACFvD,KAAK,CAAC,MAAMyC,KAAK,CAACtB,IAAI,EAAGA,IAAI,IAAK;MAChC,IAAIsC,KAAK;MACT,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,MAAMC,SAAS,GAAGpB,KAAK,CAACL,WAAW;MACnC,MAAM0B,OAAO,GAAG5C,MAAM,CAACC,IAAI,CAAC;MAC5B,MAAM4C,QAAQ,GAAG3C,IAAI,CAAC4C,GAAG,CAAC,CAACH,SAAS,GAAGC,OAAO,IAAI,GAAG,GAAG,CAACrB,KAAK,CAACT,KAAK,CAAC;MACrE,MAAMiC,OAAO,GAAGA,CAAA,KAAM;QACpB,MAAML,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAMM,QAAQ,GAAG9C,IAAI,CAACC,GAAG,CAAC,CAACuC,GAAG,GAAGF,SAAS,IAAIK,QAAQ,EAAE,CAAC,CAAC;QAC1D,MAAMI,KAAK,GAAGD,QAAQ,IAAIJ,OAAO,GAAGD,SAAS,CAAC,GAAGA,SAAS;QAC1DjB,IAAI,CAAC,oBAAoB,EAAE1B,MAAM,CAACkD,UAAU,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,IAAIP,OAAO,GAAGD,SAAS,GAAGM,KAAK,GAAGL,OAAO,GAAGK,KAAK,GAAGL,OAAO,EAAE;UAC3DL,KAAK,GAAGpD,GAAG,CAAC4D,OAAO,CAAC;QACtB;MACF,CAAC;MACD,IAAIxB,KAAK,CAACT,KAAK,EAAE;QACf,IAAIyB,KAAK,EAAE;UACTnD,SAAS,CAACmD,KAAK,CAAC;QAClB;QACAA,KAAK,GAAGpD,GAAG,CAAC4D,OAAO,CAAC;MACtB,CAAC,MAAM;QACLrB,IAAI,CAAC,oBAAoB,EAAEkB,OAAO,CAAC;MACrC;IACF,CAAC,EAAE;MACDQ,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,SAAS,GAAG,IAAI;MACtB,MAAM;QACJnC,WAAW;QACXD,WAAW;QACXE;MACF,CAAC,GAAGG,KAAK;MACT,MAAMgC,MAAM,GAAGD,SAAS,GAAGpC,WAAW,GAAG,GAAG;MAC5C,MAAMH,KAAK,GAAG1B,QAAQ,CAACkC,KAAK,CAACR,KAAK,CAAC,GAAG,QAAQa,EAAE,GAAG,GAAGL,KAAK,CAACR,KAAK;MACjE,MAAMyC,KAAK,GAAG;QACZC,MAAM,EAAE1C,KAAK;QACbI,WAAW,EAAE,GAAG,CAACA,WAAW,GAAG,CAAC,IAAI;QACpCC,aAAa;QACbsC,eAAe,EAAE,GAAGH,MAAM,MAAMD,SAAS;MAC3C,CAAC;MACD,OAAOpE,YAAY,CAAC,MAAM,EAAE;QAC1B,GAAG,EAAE2C,IAAI,CAACC,KAAK;QACf,OAAO,EAAE0B,KAAK;QACd,OAAO,EAAE1D,GAAG,CAAC,OAAO,CAAC;QACrB,QAAQ,EAAEiB;MACZ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,MAAM4C,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMH,KAAK,GAAG;QACZ3C,IAAI,EAAEU,KAAK,CAACV,IAAI;QAChB4C,MAAM,EAAElC,KAAK,CAACN,UAAU;QACxBE,WAAW,EAAE,GAAGI,KAAK,CAACJ,WAAW;MACnC,CAAC;MACD,OAAOjC,YAAY,CAAC,MAAM,EAAE;QAC1B,OAAO,EAAEY,GAAG,CAAC,OAAO,CAAC;QACrB,OAAO,EAAE0D,KAAK;QACd,GAAG,EAAE3B,IAAI,CAACC;MACZ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,MAAM8B,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAM;QACJ7C;MACF,CAAC,GAAGQ,KAAK;MACT,IAAI,CAAClC,QAAQ,CAAC0B,KAAK,CAAC,EAAE;QACpB;MACF;MACA,MAAM8C,KAAK,GAAG7C,MAAM,CAAC8C,IAAI,CAAC/C,KAAK,CAAC,CAACgD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKf,UAAU,CAACc,CAAC,CAAC,GAAGd,UAAU,CAACe,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKlF,YAAY,CAAC,MAAM,EAAE;QACtH,KAAK,EAAEkF,KAAK;QACZ,QAAQ,EAAED,GAAG;QACb,YAAY,EAAEpD,KAAK,CAACoD,GAAG;MACzB,CAAC,EAAE,IAAI,CAAC,CAAC;MACT,OAAOjF,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACA,YAAY,CAAC,gBAAgB,EAAE;QAChE,IAAI,EAAE0C,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,IAAI;QACV,IAAI,EAAE;MACR,CAAC,EAAE,CAACiC,KAAK,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,MAAMQ,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI1C,KAAK,CAAC2C,OAAO,EAAE;QACjB,OAAO3C,KAAK,CAAC2C,OAAO,CAAC,CAAC;MACxB;MACA,IAAI/C,KAAK,CAACb,IAAI,EAAE;QACd,OAAOxB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEY,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAACyB,KAAK,CAACb,IAAI,CAAC,CAAC;MAClB;IACF,CAAC;IACD,OAAO,MAAMxB,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEY,GAAG,CAAC,CAAC;MACd,OAAO,EAAEN,YAAY,CAAC+B,KAAK,CAACX,IAAI;IAClC,CAAC,EAAE,CAAC1B,YAAY,CAAC,KAAK,EAAE;MACtB,SAAS,EAAE,OAAOqB,WAAW,CAACuB,KAAK,IAAIvB,WAAW,CAACuB,KAAK,EAAE;MAC1D,OAAO,EAAEC,QAAQ,CAACD;IACpB,CAAC,EAAE,CAAC8B,cAAc,CAAC,CAAC,EAAED,WAAW,CAAC,CAAC,EAAEN,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEgB,UAAU,CAAC,CAAC,CAAC,CAAC;EACtE;AACF,CAAC,CAAC;AACF,SACE5D,WAAW,EACXa,aAAa,IAAIgD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}