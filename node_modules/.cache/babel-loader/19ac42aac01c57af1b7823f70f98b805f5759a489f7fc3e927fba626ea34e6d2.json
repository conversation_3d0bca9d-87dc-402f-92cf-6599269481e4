{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ContactEdit from \"./ContactEdit.mjs\";\nconst ContactEdit = withInstall(_ContactEdit);\nvar stdin_default = ContactEdit;\nimport { contactEditProps } from \"./ContactEdit.mjs\";\nexport { ContactEdit, contactEditProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ContactEdit", "ContactEdit", "stdin_default", "contactEditProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/contact-edit/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ContactEdit from \"./ContactEdit.mjs\";\nconst ContactEdit = withInstall(_ContactEdit);\nvar stdin_default = ContactEdit;\nimport { contactEditProps } from \"./ContactEdit.mjs\";\nexport {\n  ContactEdit,\n  contactEditProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXE,gBAAgB,EAChBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}