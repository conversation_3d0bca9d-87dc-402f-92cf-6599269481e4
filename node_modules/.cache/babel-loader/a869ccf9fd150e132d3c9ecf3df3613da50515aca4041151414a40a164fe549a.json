{"ast": null, "code": "import { useRect } from \"@vant/use\";\nimport { ref, onMounted, nextTick, watch } from \"vue\";\nimport { windowHeight, windowWidth } from \"../utils/index.mjs\";\nimport { onPopupReopen } from \"./on-popup-reopen.mjs\";\nconst useHeight = (element, withSafeArea) => {\n  const height = ref();\n  const setHeight = () => {\n    height.value = useRect(element).height;\n  };\n  onMounted(() => {\n    nextTick(setHeight);\n    if (withSafeArea) {\n      for (let i = 1; i <= 3; i++) {\n        setTimeout(setHeight, 100 * i);\n      }\n    }\n  });\n  onPopupReopen(() => nextTick(setHeight));\n  watch([windowWidth, windowHeight], setHeight);\n  return height;\n};\nexport { useHeight };", "map": {"version": 3, "names": ["useRect", "ref", "onMounted", "nextTick", "watch", "windowHeight", "windowWidth", "onPopupReopen", "useHeight", "element", "withSafeArea", "height", "setHeight", "value", "i", "setTimeout"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/composables/use-height.mjs"], "sourcesContent": ["import { useRect } from \"@vant/use\";\nimport { ref, onMounted, nextTick, watch } from \"vue\";\nimport { windowHeight, windowWidth } from \"../utils/index.mjs\";\nimport { onPopupReopen } from \"./on-popup-reopen.mjs\";\nconst useHeight = (element, withSafeArea) => {\n  const height = ref();\n  const setHeight = () => {\n    height.value = useRect(element).height;\n  };\n  onMounted(() => {\n    nextTick(setHeight);\n    if (withSafeArea) {\n      for (let i = 1; i <= 3; i++) {\n        setTimeout(setHeight, 100 * i);\n      }\n    }\n  });\n  onPopupReopen(() => nextTick(setHeight));\n  watch([windowWidth, windowHeight], setHeight);\n  return height;\n};\nexport {\n  useHeight\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,KAAK;AACrD,SAASC,YAAY,EAAEC,WAAW,QAAQ,oBAAoB;AAC9D,SAASC,aAAa,QAAQ,uBAAuB;AACrD,MAAMC,SAAS,GAAGA,CAACC,OAAO,EAAEC,YAAY,KAAK;EAC3C,MAAMC,MAAM,GAAGV,GAAG,CAAC,CAAC;EACpB,MAAMW,SAAS,GAAGA,CAAA,KAAM;IACtBD,MAAM,CAACE,KAAK,GAAGb,OAAO,CAACS,OAAO,CAAC,CAACE,MAAM;EACxC,CAAC;EACDT,SAAS,CAAC,MAAM;IACdC,QAAQ,CAACS,SAAS,CAAC;IACnB,IAAIF,YAAY,EAAE;MAChB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3BC,UAAU,CAACH,SAAS,EAAE,GAAG,GAAGE,CAAC,CAAC;MAChC;IACF;EACF,CAAC,CAAC;EACFP,aAAa,CAAC,MAAMJ,QAAQ,CAACS,SAAS,CAAC,CAAC;EACxCR,KAAK,CAAC,CAACE,WAAW,EAAED,YAAY,CAAC,EAAEO,SAAS,CAAC;EAC7C,OAAOD,MAAM;AACf,CAAC;AACD,SACEH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}