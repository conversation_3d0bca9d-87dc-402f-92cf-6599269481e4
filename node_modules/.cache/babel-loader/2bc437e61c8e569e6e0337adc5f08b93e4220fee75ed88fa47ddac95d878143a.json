{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Popup from \"./Popup.mjs\";\nconst Popup = withInstall(_Popup);\nvar stdin_default = Popup;\nimport { popupProps } from \"./Popup.mjs\";\nexport { Popup, stdin_default as default, popupProps };", "map": {"version": 3, "names": ["withInstall", "_Popup", "Popup", "stdin_default", "popupProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/popup/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Popup from \"./Popup.mjs\";\nconst Popup = withInstall(_Popup);\nvar stdin_default = Popup;\nimport { popupProps } from \"./Popup.mjs\";\nexport {\n  Popup,\n  stdin_default as default,\n  popupProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SAASE,UAAU,QAAQ,aAAa;AACxC,SACEF,KAAK,EACLC,aAAa,IAAIE,OAAO,EACxBD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}