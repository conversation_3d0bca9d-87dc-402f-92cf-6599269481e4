{"ast": null, "code": "import { getCurrentInstance } from \"vue\";\nimport { extend } from \"../utils/index.mjs\";\nfunction useExpose(apis) {\n  const instance = getCurrentInstance();\n  if (instance) {\n    extend(instance.proxy, apis);\n  }\n}\nexport { useExpose };", "map": {"version": 3, "names": ["getCurrentInstance", "extend", "useExpose", "apis", "instance", "proxy"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-expose.mjs"], "sourcesContent": ["import { getCurrentInstance } from \"vue\";\nimport { extend } from \"../utils/index.mjs\";\nfunction useExpose(apis) {\n  const instance = getCurrentInstance();\n  if (instance) {\n    extend(instance.proxy, apis);\n  }\n}\nexport {\n  useExpose\n};\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,KAAK;AACxC,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,MAAMC,QAAQ,GAAGJ,kBAAkB,CAAC,CAAC;EACrC,IAAII,QAAQ,EAAE;IACZH,MAAM,CAACG,QAAQ,CAACC,KAAK,EAAEF,IAAI,CAAC;EAC9B;AACF;AACA,SACED,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}