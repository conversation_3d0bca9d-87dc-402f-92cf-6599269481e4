{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Slider from \"./Slider.mjs\";\nconst Slider = withInstall(_Slider);\nvar stdin_default = Slider;\nimport { sliderProps } from \"./Slider.mjs\";\nexport { Slider, stdin_default as default, sliderProps };", "map": {"version": 3, "names": ["withInstall", "_<PERSON><PERSON><PERSON>", "Slide<PERSON>", "stdin_default", "sliderProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/slider/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Slider from \"./Slider.mjs\";\nconst Slider = withInstall(_Slider);\nvar stdin_default = Slider;\nimport { sliderProps } from \"./Slider.mjs\";\nexport {\n  Slider,\n  stdin_default as default,\n  sliderProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEF,MAAM,EACNC,aAAa,IAAIE,OAAO,EACxBD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}