{"ast": null, "code": "import { computed, nextTick, reactive, onMounted, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace } from \"../utils/index.mjs\";\nimport { SWIPE_KEY } from \"../swipe/Swipe.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"swipe-item\");\nvar stdin_default = defineComponent({\n  name,\n  setup(props, {\n    slots\n  }) {\n    let rendered;\n    const state = reactive({\n      offset: 0,\n      inited: false,\n      mounted: false\n    });\n    const {\n      parent,\n      index\n    } = useParent(SWIPE_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <SwipeItem> must be a child component of <Swipe>.\");\n      }\n      return;\n    }\n    const style = computed(() => {\n      const style2 = {};\n      const {\n        vertical\n      } = parent.props;\n      if (parent.size.value) {\n        style2[vertical ? \"height\" : \"width\"] = `${parent.size.value}px`;\n      }\n      if (state.offset) {\n        style2.transform = `translate${vertical ? \"Y\" : \"X\"}(${state.offset}px)`;\n      }\n      return style2;\n    });\n    const shouldRender = computed(() => {\n      const {\n        loop,\n        lazyRender\n      } = parent.props;\n      if (!lazyRender || rendered) {\n        return true;\n      }\n      if (!state.mounted) {\n        return false;\n      }\n      const active = parent.activeIndicator.value;\n      const maxActive = parent.count.value - 1;\n      const prevActive = active === 0 && loop ? maxActive : active - 1;\n      const nextActive = active === maxActive && loop ? 0 : active + 1;\n      rendered = index.value === active || index.value === prevActive || index.value === nextActive;\n      return rendered;\n    });\n    const setOffset = offset => {\n      state.offset = offset;\n    };\n    onMounted(() => {\n      nextTick(() => {\n        state.mounted = true;\n      });\n    });\n    useExpose({\n      setOffset\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"style\": style.value\n      }, [shouldRender.value ? (_a = slots.default) == null ? void 0 : _a.call(slots) : null]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["computed", "nextTick", "reactive", "onMounted", "defineComponent", "createVNode", "_createVNode", "createNamespace", "SWIPE_KEY", "useParent", "useExpose", "name", "bem", "stdin_default", "setup", "props", "slots", "rendered", "state", "offset", "inited", "mounted", "parent", "index", "process", "env", "NODE_ENV", "console", "error", "style", "style2", "vertical", "size", "value", "transform", "shouldRender", "loop", "lazy<PERSON>ender", "active", "activeIndicator", "maxActive", "count", "prevActive", "nextActive", "setOffset", "_a", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/swipe-item/SwipeItem.mjs"], "sourcesContent": ["import { computed, nextTick, reactive, onMounted, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace } from \"../utils/index.mjs\";\nimport { SWIPE_KEY } from \"../swipe/Swipe.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"swipe-item\");\nvar stdin_default = defineComponent({\n  name,\n  setup(props, {\n    slots\n  }) {\n    let rendered;\n    const state = reactive({\n      offset: 0,\n      inited: false,\n      mounted: false\n    });\n    const {\n      parent,\n      index\n    } = useParent(SWIPE_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <SwipeItem> must be a child component of <Swipe>.\");\n      }\n      return;\n    }\n    const style = computed(() => {\n      const style2 = {};\n      const {\n        vertical\n      } = parent.props;\n      if (parent.size.value) {\n        style2[vertical ? \"height\" : \"width\"] = `${parent.size.value}px`;\n      }\n      if (state.offset) {\n        style2.transform = `translate${vertical ? \"Y\" : \"X\"}(${state.offset}px)`;\n      }\n      return style2;\n    });\n    const shouldRender = computed(() => {\n      const {\n        loop,\n        lazyRender\n      } = parent.props;\n      if (!lazyRender || rendered) {\n        return true;\n      }\n      if (!state.mounted) {\n        return false;\n      }\n      const active = parent.activeIndicator.value;\n      const maxActive = parent.count.value - 1;\n      const prevActive = active === 0 && loop ? maxActive : active - 1;\n      const nextActive = active === maxActive && loop ? 0 : active + 1;\n      rendered = index.value === active || index.value === prevActive || index.value === nextActive;\n      return rendered;\n    });\n    const setOffset = (offset) => {\n      state.offset = offset;\n    };\n    onMounted(() => {\n      nextTick(() => {\n        state.mounted = true;\n      });\n    });\n    useExpose({\n      setOffset\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"style\": style.value\n      }, [shouldRender.value ? (_a = slots.default) == null ? void 0 : _a.call(slots) : null]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC3G,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,YAAY,CAAC;AACjD,IAAIM,aAAa,GAAGT,eAAe,CAAC;EAClCO,IAAI;EACJG,KAAKA,CAACC,KAAK,EAAE;IACXC;EACF,CAAC,EAAE;IACD,IAAIC,QAAQ;IACZ,MAAMC,KAAK,GAAGhB,QAAQ,CAAC;MACrBiB,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;IACX,CAAC,CAAC;IACF,MAAM;MACJC,MAAM;MACNC;IACF,CAAC,GAAGd,SAAS,CAACD,SAAS,CAAC;IACxB,IAAI,CAACc,MAAM,EAAE;MACX,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,0DAA0D,CAAC;MAC3E;MACA;IACF;IACA,MAAMC,KAAK,GAAG7B,QAAQ,CAAC,MAAM;MAC3B,MAAM8B,MAAM,GAAG,CAAC,CAAC;MACjB,MAAM;QACJC;MACF,CAAC,GAAGT,MAAM,CAACP,KAAK;MAChB,IAAIO,MAAM,CAACU,IAAI,CAACC,KAAK,EAAE;QACrBH,MAAM,CAACC,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,GAAG,GAAGT,MAAM,CAACU,IAAI,CAACC,KAAK,IAAI;MAClE;MACA,IAAIf,KAAK,CAACC,MAAM,EAAE;QAChBW,MAAM,CAACI,SAAS,GAAG,YAAYH,QAAQ,GAAG,GAAG,GAAG,GAAG,IAAIb,KAAK,CAACC,MAAM,KAAK;MAC1E;MACA,OAAOW,MAAM;IACf,CAAC,CAAC;IACF,MAAMK,YAAY,GAAGnC,QAAQ,CAAC,MAAM;MAClC,MAAM;QACJoC,IAAI;QACJC;MACF,CAAC,GAAGf,MAAM,CAACP,KAAK;MAChB,IAAI,CAACsB,UAAU,IAAIpB,QAAQ,EAAE;QAC3B,OAAO,IAAI;MACb;MACA,IAAI,CAACC,KAAK,CAACG,OAAO,EAAE;QAClB,OAAO,KAAK;MACd;MACA,MAAMiB,MAAM,GAAGhB,MAAM,CAACiB,eAAe,CAACN,KAAK;MAC3C,MAAMO,SAAS,GAAGlB,MAAM,CAACmB,KAAK,CAACR,KAAK,GAAG,CAAC;MACxC,MAAMS,UAAU,GAAGJ,MAAM,KAAK,CAAC,IAAIF,IAAI,GAAGI,SAAS,GAAGF,MAAM,GAAG,CAAC;MAChE,MAAMK,UAAU,GAAGL,MAAM,KAAKE,SAAS,IAAIJ,IAAI,GAAG,CAAC,GAAGE,MAAM,GAAG,CAAC;MAChErB,QAAQ,GAAGM,KAAK,CAACU,KAAK,KAAKK,MAAM,IAAIf,KAAK,CAACU,KAAK,KAAKS,UAAU,IAAInB,KAAK,CAACU,KAAK,KAAKU,UAAU;MAC7F,OAAO1B,QAAQ;IACjB,CAAC,CAAC;IACF,MAAM2B,SAAS,GAAIzB,MAAM,IAAK;MAC5BD,KAAK,CAACC,MAAM,GAAGA,MAAM;IACvB,CAAC;IACDhB,SAAS,CAAC,MAAM;MACdF,QAAQ,CAAC,MAAM;QACbiB,KAAK,CAACG,OAAO,GAAG,IAAI;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFX,SAAS,CAAC;MACRkC;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIC,EAAE;MACN,OAAOvC,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEM,GAAG,CAAC,CAAC;QACd,OAAO,EAAEiB,KAAK,CAACI;MACjB,CAAC,EAAE,CAACE,YAAY,CAACF,KAAK,GAAG,CAACY,EAAE,GAAG7B,KAAK,CAAC8B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAAC/B,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;IAC1F,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEH,aAAa,IAAIiC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}