{"ast": null, "code": "import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { makeNumberProp, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { bem, isLastRowInMonth } from \"./utils.mjs\";\nconst [name] = createNamespace(\"calendar-day\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    item: makeRequiredProp(Object),\n    color: String,\n    index: Number,\n    offset: makeNumberProp(0),\n    rowHeight: String\n  },\n  emits: [\"click\", \"clickDisabledDate\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const style = computed(() => {\n      const {\n        item,\n        index,\n        color,\n        offset,\n        rowHeight\n      } = props;\n      const style2 = {\n        height: rowHeight\n      };\n      if (item.type === \"placeholder\") {\n        style2.width = \"100%\";\n        return style2;\n      }\n      if (index === 0) {\n        style2.marginLeft = `${100 * offset / 7}%`;\n      }\n      if (color) {\n        switch (item.type) {\n          case \"end\":\n          case \"start\":\n          case \"start-end\":\n          case \"multiple-middle\":\n          case \"multiple-selected\":\n            style2.background = color;\n            break;\n          case \"middle\":\n            style2.color = color;\n            break;\n        }\n      }\n      if (item.date && isLastRowInMonth(item.date, offset)) {\n        style2.marginBottom = 0;\n      }\n      return style2;\n    });\n    const onClick = () => {\n      if (props.item.type !== \"disabled\") {\n        emit(\"click\", props.item);\n      } else {\n        emit(\"clickDisabledDate\", props.item);\n      }\n    };\n    const renderTopInfo = () => {\n      const {\n        topInfo\n      } = props.item;\n      if (topInfo || slots[\"top-info\"]) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"top-info\")\n        }, [slots[\"top-info\"] ? slots[\"top-info\"](props.item) : topInfo]);\n      }\n    };\n    const renderBottomInfo = () => {\n      const {\n        bottomInfo\n      } = props.item;\n      if (bottomInfo || slots[\"bottom-info\"]) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"bottom-info\")\n        }, [slots[\"bottom-info\"] ? slots[\"bottom-info\"](props.item) : bottomInfo]);\n      }\n    };\n    const renderText = () => {\n      return slots.text ? slots.text(props.item) : props.item.text;\n    };\n    const renderContent = () => {\n      const {\n        item,\n        color,\n        rowHeight\n      } = props;\n      const {\n        type\n      } = item;\n      const Nodes = [renderTopInfo(), renderText(), renderBottomInfo()];\n      if (type === \"selected\") {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"selected-day\"),\n          \"style\": {\n            width: rowHeight,\n            height: rowHeight,\n            background: color\n          }\n        }, [Nodes]);\n      }\n      return Nodes;\n    };\n    return () => {\n      const {\n        type,\n        className\n      } = props.item;\n      if (type === \"placeholder\") {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"day\"),\n          \"style\": style.value\n        }, null);\n      }\n      return _createVNode(\"div\", {\n        \"role\": \"gridcell\",\n        \"style\": style.value,\n        \"class\": [bem(\"day\", type), className],\n        \"tabindex\": type === \"disabled\" ? void 0 : -1,\n        \"onClick\": onClick\n      }, [renderContent()]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "makeNumberProp", "createNamespace", "makeRequiredProp", "bem", "isLastRowInMonth", "name", "stdin_default", "props", "item", "Object", "color", "String", "index", "Number", "offset", "rowHeight", "emits", "setup", "emit", "slots", "style", "style2", "height", "type", "width", "marginLeft", "background", "date", "marginBottom", "onClick", "renderTopInfo", "topInfo", "renderBottomInfo", "bottomInfo", "renderText", "text", "renderContent", "Nodes", "className", "value", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/calendar/CalendarDay.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { makeNumberProp, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { bem, isLastRowInMonth } from \"./utils.mjs\";\nconst [name] = createNamespace(\"calendar-day\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    item: makeRequiredProp(Object),\n    color: String,\n    index: Number,\n    offset: makeNumberProp(0),\n    rowHeight: String\n  },\n  emits: [\"click\", \"clickDisabledDate\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const style = computed(() => {\n      const {\n        item,\n        index,\n        color,\n        offset,\n        rowHeight\n      } = props;\n      const style2 = {\n        height: rowHeight\n      };\n      if (item.type === \"placeholder\") {\n        style2.width = \"100%\";\n        return style2;\n      }\n      if (index === 0) {\n        style2.marginLeft = `${100 * offset / 7}%`;\n      }\n      if (color) {\n        switch (item.type) {\n          case \"end\":\n          case \"start\":\n          case \"start-end\":\n          case \"multiple-middle\":\n          case \"multiple-selected\":\n            style2.background = color;\n            break;\n          case \"middle\":\n            style2.color = color;\n            break;\n        }\n      }\n      if (item.date && isLastRowInMonth(item.date, offset)) {\n        style2.marginBottom = 0;\n      }\n      return style2;\n    });\n    const onClick = () => {\n      if (props.item.type !== \"disabled\") {\n        emit(\"click\", props.item);\n      } else {\n        emit(\"clickDisabledDate\", props.item);\n      }\n    };\n    const renderTopInfo = () => {\n      const {\n        topInfo\n      } = props.item;\n      if (topInfo || slots[\"top-info\"]) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"top-info\")\n        }, [slots[\"top-info\"] ? slots[\"top-info\"](props.item) : topInfo]);\n      }\n    };\n    const renderBottomInfo = () => {\n      const {\n        bottomInfo\n      } = props.item;\n      if (bottomInfo || slots[\"bottom-info\"]) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"bottom-info\")\n        }, [slots[\"bottom-info\"] ? slots[\"bottom-info\"](props.item) : bottomInfo]);\n      }\n    };\n    const renderText = () => {\n      return slots.text ? slots.text(props.item) : props.item.text;\n    };\n    const renderContent = () => {\n      const {\n        item,\n        color,\n        rowHeight\n      } = props;\n      const {\n        type\n      } = item;\n      const Nodes = [renderTopInfo(), renderText(), renderBottomInfo()];\n      if (type === \"selected\") {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"selected-day\"),\n          \"style\": {\n            width: rowHeight,\n            height: rowHeight,\n            background: color\n          }\n        }, [Nodes]);\n      }\n      return Nodes;\n    };\n    return () => {\n      const {\n        type,\n        className\n      } = props.item;\n      if (type === \"placeholder\") {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"day\"),\n          \"style\": style.value\n        }, null);\n      }\n      return _createVNode(\"div\", {\n        \"role\": \"gridcell\",\n        \"style\": style.value,\n        \"class\": [bem(\"day\", type), className],\n        \"tabindex\": type === \"disabled\" ? void 0 : -1,\n        \"onClick\": onClick\n      }, [renderContent()]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AACtF,SAASC,GAAG,EAAEC,gBAAgB,QAAQ,aAAa;AACnD,MAAM,CAACC,IAAI,CAAC,GAAGJ,eAAe,CAAC,cAAc,CAAC;AAC9C,IAAIK,aAAa,GAAGT,eAAe,CAAC;EAClCQ,IAAI;EACJE,KAAK,EAAE;IACLC,IAAI,EAAEN,gBAAgB,CAACO,MAAM,CAAC;IAC9BC,KAAK,EAAEC,MAAM;IACbC,KAAK,EAAEC,MAAM;IACbC,MAAM,EAAEd,cAAc,CAAC,CAAC,CAAC;IACzBe,SAAS,EAAEJ;EACb,CAAC;EACDK,KAAK,EAAE,CAAC,OAAO,EAAE,mBAAmB,CAAC;EACrCC,KAAKA,CAACV,KAAK,EAAE;IACXW,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGxB,QAAQ,CAAC,MAAM;MAC3B,MAAM;QACJY,IAAI;QACJI,KAAK;QACLF,KAAK;QACLI,MAAM;QACNC;MACF,CAAC,GAAGR,KAAK;MACT,MAAMc,MAAM,GAAG;QACbC,MAAM,EAAEP;MACV,CAAC;MACD,IAAIP,IAAI,CAACe,IAAI,KAAK,aAAa,EAAE;QAC/BF,MAAM,CAACG,KAAK,GAAG,MAAM;QACrB,OAAOH,MAAM;MACf;MACA,IAAIT,KAAK,KAAK,CAAC,EAAE;QACfS,MAAM,CAACI,UAAU,GAAG,GAAG,GAAG,GAAGX,MAAM,GAAG,CAAC,GAAG;MAC5C;MACA,IAAIJ,KAAK,EAAE;QACT,QAAQF,IAAI,CAACe,IAAI;UACf,KAAK,KAAK;UACV,KAAK,OAAO;UACZ,KAAK,WAAW;UAChB,KAAK,iBAAiB;UACtB,KAAK,mBAAmB;YACtBF,MAAM,CAACK,UAAU,GAAGhB,KAAK;YACzB;UACF,KAAK,QAAQ;YACXW,MAAM,CAACX,KAAK,GAAGA,KAAK;YACpB;QACJ;MACF;MACA,IAAIF,IAAI,CAACmB,IAAI,IAAIvB,gBAAgB,CAACI,IAAI,CAACmB,IAAI,EAAEb,MAAM,CAAC,EAAE;QACpDO,MAAM,CAACO,YAAY,GAAG,CAAC;MACzB;MACA,OAAOP,MAAM;IACf,CAAC,CAAC;IACF,MAAMQ,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAItB,KAAK,CAACC,IAAI,CAACe,IAAI,KAAK,UAAU,EAAE;QAClCL,IAAI,CAAC,OAAO,EAAEX,KAAK,CAACC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLU,IAAI,CAAC,mBAAmB,EAAEX,KAAK,CAACC,IAAI,CAAC;MACvC;IACF,CAAC;IACD,MAAMsB,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAM;QACJC;MACF,CAAC,GAAGxB,KAAK,CAACC,IAAI;MACd,IAAIuB,OAAO,IAAIZ,KAAK,CAAC,UAAU,CAAC,EAAE;QAChC,OAAOpB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEI,GAAG,CAAC,UAAU;QACzB,CAAC,EAAE,CAACgB,KAAK,CAAC,UAAU,CAAC,GAAGA,KAAK,CAAC,UAAU,CAAC,CAACZ,KAAK,CAACC,IAAI,CAAC,GAAGuB,OAAO,CAAC,CAAC;MACnE;IACF,CAAC;IACD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,MAAM;QACJC;MACF,CAAC,GAAG1B,KAAK,CAACC,IAAI;MACd,IAAIyB,UAAU,IAAId,KAAK,CAAC,aAAa,CAAC,EAAE;QACtC,OAAOpB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEI,GAAG,CAAC,aAAa;QAC5B,CAAC,EAAE,CAACgB,KAAK,CAAC,aAAa,CAAC,GAAGA,KAAK,CAAC,aAAa,CAAC,CAACZ,KAAK,CAACC,IAAI,CAAC,GAAGyB,UAAU,CAAC,CAAC;MAC5E;IACF,CAAC;IACD,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,OAAOf,KAAK,CAACgB,IAAI,GAAGhB,KAAK,CAACgB,IAAI,CAAC5B,KAAK,CAACC,IAAI,CAAC,GAAGD,KAAK,CAACC,IAAI,CAAC2B,IAAI;IAC9D,CAAC;IACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAM;QACJ5B,IAAI;QACJE,KAAK;QACLK;MACF,CAAC,GAAGR,KAAK;MACT,MAAM;QACJgB;MACF,CAAC,GAAGf,IAAI;MACR,MAAM6B,KAAK,GAAG,CAACP,aAAa,CAAC,CAAC,EAAEI,UAAU,CAAC,CAAC,EAAEF,gBAAgB,CAAC,CAAC,CAAC;MACjE,IAAIT,IAAI,KAAK,UAAU,EAAE;QACvB,OAAOxB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEI,GAAG,CAAC,cAAc,CAAC;UAC5B,OAAO,EAAE;YACPqB,KAAK,EAAET,SAAS;YAChBO,MAAM,EAAEP,SAAS;YACjBW,UAAU,EAAEhB;UACd;QACF,CAAC,EAAE,CAAC2B,KAAK,CAAC,CAAC;MACb;MACA,OAAOA,KAAK;IACd,CAAC;IACD,OAAO,MAAM;MACX,MAAM;QACJd,IAAI;QACJe;MACF,CAAC,GAAG/B,KAAK,CAACC,IAAI;MACd,IAAIe,IAAI,KAAK,aAAa,EAAE;QAC1B,OAAOxB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEI,GAAG,CAAC,KAAK,CAAC;UACnB,OAAO,EAAEiB,KAAK,CAACmB;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;MACA,OAAOxC,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,UAAU;QAClB,OAAO,EAAEqB,KAAK,CAACmB,KAAK;QACpB,OAAO,EAAE,CAACpC,GAAG,CAAC,KAAK,EAAEoB,IAAI,CAAC,EAAEe,SAAS,CAAC;QACtC,UAAU,EAAEf,IAAI,KAAK,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7C,SAAS,EAAEM;MACb,CAAC,EAAE,CAACO,aAAa,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE9B,aAAa,IAAIkC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}