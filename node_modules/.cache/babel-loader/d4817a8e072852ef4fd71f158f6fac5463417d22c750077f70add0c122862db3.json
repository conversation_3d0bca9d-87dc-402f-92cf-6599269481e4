{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ConfigProvider from \"./ConfigProvider.mjs\";\nconst ConfigProvider = withInstall(_ConfigProvider);\nvar stdin_default = ConfigProvider;\nimport { configProviderProps } from \"./ConfigProvider.mjs\";\nexport { ConfigProvider, configProviderProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ConfigProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stdin_default", "configProviderProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/config-provider/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ConfigProvider from \"./ConfigProvider.mjs\";\nconst ConfigProvider = withInstall(_ConfigProvider);\nvar stdin_default = ConfigProvider;\nimport { configProviderProps } from \"./ConfigProvider.mjs\";\nexport {\n  ConfigProvider,\n  configProviderProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,MAAMC,cAAc,GAAGF,WAAW,CAACC,eAAe,CAAC;AACnD,IAAIE,aAAa,GAAGD,cAAc;AAClC,SAASE,mBAAmB,QAAQ,sBAAsB;AAC1D,SACEF,cAAc,EACdE,mBAAmB,EACnBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}