{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _SwipeItem from \"./SwipeItem.mjs\";\nconst SwipeItem = withInstall(_SwipeItem);\nvar stdin_default = SwipeItem;\nexport { SwipeItem, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_SwipeItem", "SwipeItem", "stdin_default", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/swipe-item/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _SwipeItem from \"./SwipeItem.mjs\";\nconst SwipeItem = withInstall(_SwipeItem);\nvar stdin_default = SwipeItem;\nexport {\n  SwipeItem,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SACEA,SAAS,EACTC,aAAa,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}