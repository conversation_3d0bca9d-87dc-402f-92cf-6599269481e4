{"ast": null, "code": "import { watch, onMounted, onUnmounted, defineComponent, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { pick, isDef, unknownProp, numericProp, makeStringProp, makeNumberProp, createNamespace } from \"../utils/index.mjs\";\nimport { lockClick } from \"./lock-click.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"toast\");\nconst popupInheritProps = [\"show\", \"overlay\", \"teleport\", \"transition\", \"overlayClass\", \"overlayStyle\", \"closeOnClickOverlay\", \"zIndex\"];\nconst toastProps = {\n  icon: String,\n  show: Boolean,\n  type: makeStringProp(\"text\"),\n  overlay: Boolean,\n  message: numericProp,\n  iconSize: numericProp,\n  duration: makeNumberProp(2e3),\n  position: makeStringProp(\"middle\"),\n  teleport: [String, Object],\n  wordBreak: String,\n  className: unknownProp,\n  iconPrefix: String,\n  transition: makeStringProp(\"van-fade\"),\n  loadingType: String,\n  forbidClick: Boolean,\n  overlayClass: unknownProp,\n  overlayStyle: Object,\n  closeOnClick: Boolean,\n  closeOnClickOverlay: Boolean,\n  zIndex: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: toastProps,\n  emits: [\"update:show\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let timer;\n    let clickable = false;\n    const toggleClickable = () => {\n      const newValue = props.show && props.forbidClick;\n      if (clickable !== newValue) {\n        clickable = newValue;\n        lockClick(clickable);\n      }\n    };\n    const updateShow = show => emit(\"update:show\", show);\n    const onClick = () => {\n      if (props.closeOnClick) {\n        updateShow(false);\n      }\n    };\n    const clearTimer = () => clearTimeout(timer);\n    const renderIcon = () => {\n      const {\n        icon,\n        type,\n        iconSize,\n        iconPrefix,\n        loadingType\n      } = props;\n      const hasIcon = icon || type === \"success\" || type === \"fail\";\n      if (hasIcon) {\n        return _createVNode(Icon, {\n          \"name\": icon || type,\n          \"size\": iconSize,\n          \"class\": bem(\"icon\"),\n          \"classPrefix\": iconPrefix\n        }, null);\n      }\n      if (type === \"loading\") {\n        return _createVNode(Loading, {\n          \"class\": bem(\"loading\"),\n          \"size\": iconSize,\n          \"type\": loadingType\n        }, null);\n      }\n    };\n    const renderMessage = () => {\n      const {\n        type,\n        message\n      } = props;\n      if (slots.message) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"text\")\n        }, [slots.message()]);\n      }\n      if (isDef(message) && message !== \"\") {\n        return type === \"html\" ? _createVNode(\"div\", {\n          \"key\": 0,\n          \"class\": bem(\"text\"),\n          \"innerHTML\": String(message)\n        }, null) : _createVNode(\"div\", {\n          \"class\": bem(\"text\")\n        }, [message]);\n      }\n    };\n    watch(() => [props.show, props.forbidClick], toggleClickable);\n    watch(() => [props.show, props.type, props.message, props.duration], () => {\n      clearTimer();\n      if (props.show && props.duration > 0) {\n        timer = setTimeout(() => {\n          updateShow(false);\n        }, props.duration);\n      }\n    });\n    onMounted(toggleClickable);\n    onUnmounted(toggleClickable);\n    return () => _createVNode(Popup, _mergeProps({\n      \"class\": [bem([props.position, props.wordBreak === \"normal\" ? \"break-normal\" : props.wordBreak, {\n        [props.type]: !props.icon\n      }]), props.className],\n      \"lockScroll\": false,\n      \"onClick\": onClick,\n      \"onClosed\": clearTimer,\n      \"onUpdate:show\": updateShow\n    }, pick(props, popupInheritProps)), {\n      default: () => [renderIcon(), renderMessage()]\n    });\n  }\n});\nexport { stdin_default as default, toastProps };", "map": {"version": 3, "names": ["watch", "onMounted", "onUnmounted", "defineComponent", "createVNode", "_createVNode", "mergeProps", "_mergeProps", "pick", "isDef", "unknownProp", "numericProp", "makeStringProp", "makeNumberProp", "createNamespace", "lockClick", "Icon", "Popup", "Loading", "name", "bem", "popupInheritProps", "toastProps", "icon", "String", "show", "Boolean", "type", "overlay", "message", "iconSize", "duration", "position", "teleport", "Object", "wordBreak", "className", "iconPrefix", "transition", "loadingType", "forbidClick", "overlayClass", "overlayStyle", "closeOnClick", "closeOnClickOverlay", "zIndex", "stdin_default", "props", "emits", "setup", "emit", "slots", "timer", "clickable", "toggleClickable", "newValue", "updateShow", "onClick", "clearTimer", "clearTimeout", "renderIcon", "hasIcon", "renderMessage", "setTimeout", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/toast/Toast.mjs"], "sourcesContent": ["import { watch, onMounted, onUnmounted, defineComponent, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { pick, isDef, unknownProp, numericProp, makeStringProp, makeNumberProp, createNamespace } from \"../utils/index.mjs\";\nimport { lockClick } from \"./lock-click.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"toast\");\nconst popupInheritProps = [\"show\", \"overlay\", \"teleport\", \"transition\", \"overlayClass\", \"overlayStyle\", \"closeOnClickOverlay\", \"zIndex\"];\nconst toastProps = {\n  icon: String,\n  show: Boolean,\n  type: makeStringProp(\"text\"),\n  overlay: Boolean,\n  message: numericProp,\n  iconSize: numericProp,\n  duration: makeNumberProp(2e3),\n  position: makeStringProp(\"middle\"),\n  teleport: [String, Object],\n  wordBreak: String,\n  className: unknownProp,\n  iconPrefix: String,\n  transition: makeStringProp(\"van-fade\"),\n  loadingType: String,\n  forbidClick: Boolean,\n  overlayClass: unknownProp,\n  overlayStyle: Object,\n  closeOnClick: Boolean,\n  closeOnClickOverlay: Boolean,\n  zIndex: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: toastProps,\n  emits: [\"update:show\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let timer;\n    let clickable = false;\n    const toggleClickable = () => {\n      const newValue = props.show && props.forbidClick;\n      if (clickable !== newValue) {\n        clickable = newValue;\n        lockClick(clickable);\n      }\n    };\n    const updateShow = (show) => emit(\"update:show\", show);\n    const onClick = () => {\n      if (props.closeOnClick) {\n        updateShow(false);\n      }\n    };\n    const clearTimer = () => clearTimeout(timer);\n    const renderIcon = () => {\n      const {\n        icon,\n        type,\n        iconSize,\n        iconPrefix,\n        loadingType\n      } = props;\n      const hasIcon = icon || type === \"success\" || type === \"fail\";\n      if (hasIcon) {\n        return _createVNode(Icon, {\n          \"name\": icon || type,\n          \"size\": iconSize,\n          \"class\": bem(\"icon\"),\n          \"classPrefix\": iconPrefix\n        }, null);\n      }\n      if (type === \"loading\") {\n        return _createVNode(Loading, {\n          \"class\": bem(\"loading\"),\n          \"size\": iconSize,\n          \"type\": loadingType\n        }, null);\n      }\n    };\n    const renderMessage = () => {\n      const {\n        type,\n        message\n      } = props;\n      if (slots.message) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"text\")\n        }, [slots.message()]);\n      }\n      if (isDef(message) && message !== \"\") {\n        return type === \"html\" ? _createVNode(\"div\", {\n          \"key\": 0,\n          \"class\": bem(\"text\"),\n          \"innerHTML\": String(message)\n        }, null) : _createVNode(\"div\", {\n          \"class\": bem(\"text\")\n        }, [message]);\n      }\n    };\n    watch(() => [props.show, props.forbidClick], toggleClickable);\n    watch(() => [props.show, props.type, props.message, props.duration], () => {\n      clearTimer();\n      if (props.show && props.duration > 0) {\n        timer = setTimeout(() => {\n          updateShow(false);\n        }, props.duration);\n      }\n    });\n    onMounted(toggleClickable);\n    onUnmounted(toggleClickable);\n    return () => _createVNode(Popup, _mergeProps({\n      \"class\": [bem([props.position, props.wordBreak === \"normal\" ? \"break-normal\" : props.wordBreak, {\n        [props.type]: !props.icon\n      }]), props.className],\n      \"lockScroll\": false,\n      \"onClick\": onClick,\n      \"onClosed\": clearTimer,\n      \"onUpdate:show\": updateShow\n    }, pick(props, popupInheritProps)), {\n      default: () => [renderIcon(), renderMessage()]\n    });\n  }\n});\nexport {\n  stdin_default as default,\n  toastProps\n};\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC5H,SAASC,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC3H,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGN,eAAe,CAAC,OAAO,CAAC;AAC5C,MAAMO,iBAAiB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,qBAAqB,EAAE,QAAQ,CAAC;AACxI,MAAMC,UAAU,GAAG;EACjBC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAEC,OAAO;EACbC,IAAI,EAAEf,cAAc,CAAC,MAAM,CAAC;EAC5BgB,OAAO,EAAEF,OAAO;EAChBG,OAAO,EAAElB,WAAW;EACpBmB,QAAQ,EAAEnB,WAAW;EACrBoB,QAAQ,EAAElB,cAAc,CAAC,GAAG,CAAC;EAC7BmB,QAAQ,EAAEpB,cAAc,CAAC,QAAQ,CAAC;EAClCqB,QAAQ,EAAE,CAACT,MAAM,EAAEU,MAAM,CAAC;EAC1BC,SAAS,EAAEX,MAAM;EACjBY,SAAS,EAAE1B,WAAW;EACtB2B,UAAU,EAAEb,MAAM;EAClBc,UAAU,EAAE1B,cAAc,CAAC,UAAU,CAAC;EACtC2B,WAAW,EAAEf,MAAM;EACnBgB,WAAW,EAAEd,OAAO;EACpBe,YAAY,EAAE/B,WAAW;EACzBgC,YAAY,EAAER,MAAM;EACpBS,YAAY,EAAEjB,OAAO;EACrBkB,mBAAmB,EAAElB,OAAO;EAC5BmB,MAAM,EAAElC;AACV,CAAC;AACD,IAAImC,aAAa,GAAG3C,eAAe,CAAC;EAClCgB,IAAI;EACJ4B,KAAK,EAAEzB,UAAU;EACjB0B,KAAK,EAAE,CAAC,aAAa,CAAC;EACtBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,IAAIC,KAAK;IACT,IAAIC,SAAS,GAAG,KAAK;IACrB,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,QAAQ,GAAGR,KAAK,CAACtB,IAAI,IAAIsB,KAAK,CAACP,WAAW;MAChD,IAAIa,SAAS,KAAKE,QAAQ,EAAE;QAC1BF,SAAS,GAAGE,QAAQ;QACpBxC,SAAS,CAACsC,SAAS,CAAC;MACtB;IACF,CAAC;IACD,MAAMG,UAAU,GAAI/B,IAAI,IAAKyB,IAAI,CAAC,aAAa,EAAEzB,IAAI,CAAC;IACtD,MAAMgC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAIV,KAAK,CAACJ,YAAY,EAAE;QACtBa,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACD,MAAME,UAAU,GAAGA,CAAA,KAAMC,YAAY,CAACP,KAAK,CAAC;IAC5C,MAAMQ,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAM;QACJrC,IAAI;QACJI,IAAI;QACJG,QAAQ;QACRO,UAAU;QACVE;MACF,CAAC,GAAGQ,KAAK;MACT,MAAMc,OAAO,GAAGtC,IAAI,IAAII,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,MAAM;MAC7D,IAAIkC,OAAO,EAAE;QACX,OAAOxD,YAAY,CAACW,IAAI,EAAE;UACxB,MAAM,EAAEO,IAAI,IAAII,IAAI;UACpB,MAAM,EAAEG,QAAQ;UAChB,OAAO,EAAEV,GAAG,CAAC,MAAM,CAAC;UACpB,aAAa,EAAEiB;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;MACA,IAAIV,IAAI,KAAK,SAAS,EAAE;QACtB,OAAOtB,YAAY,CAACa,OAAO,EAAE;UAC3B,OAAO,EAAEE,GAAG,CAAC,SAAS,CAAC;UACvB,MAAM,EAAEU,QAAQ;UAChB,MAAM,EAAES;QACV,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,MAAMuB,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAM;QACJnC,IAAI;QACJE;MACF,CAAC,GAAGkB,KAAK;MACT,IAAII,KAAK,CAACtB,OAAO,EAAE;QACjB,OAAOxB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEe,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAAC+B,KAAK,CAACtB,OAAO,CAAC,CAAC,CAAC,CAAC;MACvB;MACA,IAAIpB,KAAK,CAACoB,OAAO,CAAC,IAAIA,OAAO,KAAK,EAAE,EAAE;QACpC,OAAOF,IAAI,KAAK,MAAM,GAAGtB,YAAY,CAAC,KAAK,EAAE;UAC3C,KAAK,EAAE,CAAC;UACR,OAAO,EAAEe,GAAG,CAAC,MAAM,CAAC;UACpB,WAAW,EAAEI,MAAM,CAACK,OAAO;QAC7B,CAAC,EAAE,IAAI,CAAC,GAAGxB,YAAY,CAAC,KAAK,EAAE;UAC7B,OAAO,EAAEe,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAACS,OAAO,CAAC,CAAC;MACf;IACF,CAAC;IACD7B,KAAK,CAAC,MAAM,CAAC+C,KAAK,CAACtB,IAAI,EAAEsB,KAAK,CAACP,WAAW,CAAC,EAAEc,eAAe,CAAC;IAC7DtD,KAAK,CAAC,MAAM,CAAC+C,KAAK,CAACtB,IAAI,EAAEsB,KAAK,CAACpB,IAAI,EAAEoB,KAAK,CAAClB,OAAO,EAAEkB,KAAK,CAAChB,QAAQ,CAAC,EAAE,MAAM;MACzE2B,UAAU,CAAC,CAAC;MACZ,IAAIX,KAAK,CAACtB,IAAI,IAAIsB,KAAK,CAAChB,QAAQ,GAAG,CAAC,EAAE;QACpCqB,KAAK,GAAGW,UAAU,CAAC,MAAM;UACvBP,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,EAAET,KAAK,CAAChB,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC;IACF9B,SAAS,CAACqD,eAAe,CAAC;IAC1BpD,WAAW,CAACoD,eAAe,CAAC;IAC5B,OAAO,MAAMjD,YAAY,CAACY,KAAK,EAAEV,WAAW,CAAC;MAC3C,OAAO,EAAE,CAACa,GAAG,CAAC,CAAC2B,KAAK,CAACf,QAAQ,EAAEe,KAAK,CAACZ,SAAS,KAAK,QAAQ,GAAG,cAAc,GAAGY,KAAK,CAACZ,SAAS,EAAE;QAC9F,CAACY,KAAK,CAACpB,IAAI,GAAG,CAACoB,KAAK,CAACxB;MACvB,CAAC,CAAC,CAAC,EAAEwB,KAAK,CAACX,SAAS,CAAC;MACrB,YAAY,EAAE,KAAK;MACnB,SAAS,EAAEqB,OAAO;MAClB,UAAU,EAAEC,UAAU;MACtB,eAAe,EAAEF;IACnB,CAAC,EAAEhD,IAAI,CAACuC,KAAK,EAAE1B,iBAAiB,CAAC,CAAC,EAAE;MAClC2C,OAAO,EAAEA,CAAA,KAAM,CAACJ,UAAU,CAAC,CAAC,EAAEE,aAAa,CAAC,CAAC;IAC/C,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AACF,SACEhB,aAAa,IAAIkB,OAAO,EACxB1C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}