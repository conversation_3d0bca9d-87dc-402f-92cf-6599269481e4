{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, watch, computed, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { pick, isDate, truthProp, numericProp, getScrollTop, makeStringProp, makeNumericProp } from \"../utils/index.mjs\";\nimport { t, bem, name, getToday, cloneDate, cloneDates, getPrevDay, getNextDay, compareDay, calcDateNum, compareMonth, getDayByOffset, getMonthByOffset } from \"./utils.mjs\";\nimport { raf, useRect, onMountedOrActivated } from \"@vant/use\";\nimport { useRefs } from \"../composables/use-refs.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { showToast } from \"../toast/index.mjs\";\nimport CalendarMonth from \"./CalendarMonth.mjs\";\nimport CalendarHeader from \"./CalendarHeader.mjs\";\nconst calendarProps = {\n  show: Boolean,\n  type: makeStringProp(\"single\"),\n  switchMode: makeStringProp(\"none\"),\n  title: String,\n  color: String,\n  round: truthProp,\n  readonly: Boolean,\n  poppable: truthProp,\n  maxRange: makeNumericProp(null),\n  position: makeStringProp(\"bottom\"),\n  teleport: [String, Object],\n  showMark: truthProp,\n  showTitle: truthProp,\n  formatter: Function,\n  rowHeight: numericProp,\n  confirmText: String,\n  rangePrompt: String,\n  lazyRender: truthProp,\n  showConfirm: truthProp,\n  defaultDate: [Date, Array],\n  allowSameDay: Boolean,\n  showSubtitle: truthProp,\n  closeOnPopstate: truthProp,\n  showRangePrompt: truthProp,\n  confirmDisabledText: String,\n  closeOnClickOverlay: truthProp,\n  safeAreaInsetTop: Boolean,\n  safeAreaInsetBottom: truthProp,\n  minDate: {\n    type: Date,\n    validator: isDate\n  },\n  maxDate: {\n    type: Date,\n    validator: isDate\n  },\n  firstDayOfWeek: {\n    type: numericProp,\n    default: 0,\n    validator: val => val >= 0 && val <= 6\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: calendarProps,\n  emits: [\"select\", \"confirm\", \"unselect\", \"monthShow\", \"overRange\", \"update:show\", \"clickSubtitle\", \"clickDisabledDate\", \"clickOverlay\", \"panelChange\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const canSwitch = computed(() => props.switchMode !== \"none\");\n    const minDate = computed(() => {\n      if (!props.minDate && !canSwitch.value) {\n        return getToday();\n      }\n      return props.minDate;\n    });\n    const maxDate = computed(() => {\n      if (!props.maxDate && !canSwitch.value) {\n        return getMonthByOffset(getToday(), 6);\n      }\n      return props.maxDate;\n    });\n    const limitDateRange = (date, min = minDate.value, max = maxDate.value) => {\n      if (min && compareDay(date, min) === -1) {\n        return min;\n      }\n      if (max && compareDay(date, max) === 1) {\n        return max;\n      }\n      return date;\n    };\n    const getInitialDate = (defaultDate = props.defaultDate) => {\n      const {\n        type,\n        allowSameDay\n      } = props;\n      if (defaultDate === null) {\n        return defaultDate;\n      }\n      const now = getToday();\n      if (type === \"range\") {\n        if (!Array.isArray(defaultDate)) {\n          defaultDate = [];\n        }\n        if (defaultDate.length === 1 && compareDay(defaultDate[0], now) === 1) {\n          defaultDate = [];\n        }\n        const min = minDate.value;\n        const max = maxDate.value;\n        const start = limitDateRange(defaultDate[0] || now, min, max ? allowSameDay ? max : getPrevDay(max) : void 0);\n        const end = limitDateRange(defaultDate[1] || (allowSameDay ? now : getNextDay(now)), min ? allowSameDay ? min : getNextDay(min) : void 0);\n        return [start, end];\n      }\n      if (type === \"multiple\") {\n        if (Array.isArray(defaultDate)) {\n          return defaultDate.map(date => limitDateRange(date));\n        }\n        return [limitDateRange(now)];\n      }\n      if (!defaultDate || Array.isArray(defaultDate)) {\n        defaultDate = now;\n      }\n      return limitDateRange(defaultDate);\n    };\n    const getInitialPanelDate = () => {\n      const date = Array.isArray(currentDate.value) ? currentDate.value[0] : currentDate.value;\n      return date ? date : limitDateRange(getToday());\n    };\n    let bodyHeight;\n    const bodyRef = ref();\n    const currentDate = ref(getInitialDate());\n    const currentPanelDate = ref(getInitialPanelDate());\n    const currentMonthRef = ref();\n    const [monthRefs, setMonthRefs] = useRefs();\n    const dayOffset = computed(() => props.firstDayOfWeek ? +props.firstDayOfWeek % 7 : 0);\n    const months = computed(() => {\n      const months2 = [];\n      if (!minDate.value || !maxDate.value) {\n        return months2;\n      }\n      const cursor = new Date(minDate.value);\n      cursor.setDate(1);\n      do {\n        months2.push(new Date(cursor));\n        cursor.setMonth(cursor.getMonth() + 1);\n      } while (compareMonth(cursor, maxDate.value) !== 1);\n      return months2;\n    });\n    const buttonDisabled = computed(() => {\n      if (currentDate.value) {\n        if (props.type === \"range\") {\n          return !currentDate.value[0] || !currentDate.value[1];\n        }\n        if (props.type === \"multiple\") {\n          return !currentDate.value.length;\n        }\n      }\n      return !currentDate.value;\n    });\n    const getSelectedDate = () => currentDate.value;\n    const onScroll = () => {\n      const top = getScrollTop(bodyRef.value);\n      const bottom = top + bodyHeight;\n      const heights = months.value.map((item, index) => monthRefs.value[index].getHeight());\n      const heightSum = heights.reduce((a, b) => a + b, 0);\n      if (bottom > heightSum && top > 0) {\n        return;\n      }\n      let height = 0;\n      let currentMonth;\n      const visibleRange = [-1, -1];\n      for (let i = 0; i < months.value.length; i++) {\n        const month = monthRefs.value[i];\n        const visible = height <= bottom && height + heights[i] >= top;\n        if (visible) {\n          visibleRange[1] = i;\n          if (!currentMonth) {\n            currentMonth = month;\n            visibleRange[0] = i;\n          }\n          if (!monthRefs.value[i].showed) {\n            monthRefs.value[i].showed = true;\n            emit(\"monthShow\", {\n              date: month.date,\n              title: month.getTitle()\n            });\n          }\n        }\n        height += heights[i];\n      }\n      months.value.forEach((month, index) => {\n        const visible = index >= visibleRange[0] - 1 && index <= visibleRange[1] + 1;\n        monthRefs.value[index].setVisible(visible);\n      });\n      if (currentMonth) {\n        currentMonthRef.value = currentMonth;\n      }\n    };\n    const scrollToDate = targetDate => {\n      if (canSwitch.value) {\n        currentPanelDate.value = targetDate;\n      } else {\n        raf(() => {\n          months.value.some((month, index) => {\n            if (compareMonth(month, targetDate) === 0) {\n              if (bodyRef.value) {\n                monthRefs.value[index].scrollToDate(bodyRef.value, targetDate);\n              }\n              return true;\n            }\n            return false;\n          });\n          onScroll();\n        });\n      }\n    };\n    const scrollToCurrentDate = () => {\n      if (props.poppable && !props.show) {\n        return;\n      }\n      if (currentDate.value) {\n        const targetDate = props.type === \"single\" ? currentDate.value : currentDate.value[0];\n        if (isDate(targetDate)) {\n          scrollToDate(targetDate);\n        }\n      } else if (!canSwitch.value) {\n        raf(onScroll);\n      }\n    };\n    const init = () => {\n      if (props.poppable && !props.show) {\n        return;\n      }\n      if (!canSwitch.value) {\n        raf(() => {\n          bodyHeight = Math.floor(useRect(bodyRef).height);\n        });\n      }\n      scrollToCurrentDate();\n    };\n    const reset = (date = getInitialDate()) => {\n      currentDate.value = date;\n      scrollToCurrentDate();\n    };\n    const checkRange = date => {\n      const {\n        maxRange,\n        rangePrompt,\n        showRangePrompt\n      } = props;\n      if (maxRange && calcDateNum(date) > +maxRange) {\n        if (showRangePrompt) {\n          showToast(rangePrompt || t(\"rangePrompt\", maxRange));\n        }\n        emit(\"overRange\");\n        return false;\n      }\n      return true;\n    };\n    const onPanelChange = date => {\n      currentPanelDate.value = date;\n      emit(\"panelChange\", {\n        date\n      });\n    };\n    const onConfirm = () => {\n      var _a;\n      return emit(\"confirm\", (_a = currentDate.value) != null ? _a : cloneDates(currentDate.value));\n    };\n    const select = (date, complete) => {\n      const setCurrentDate = date2 => {\n        currentDate.value = date2;\n        emit(\"select\", cloneDates(date2));\n      };\n      if (complete && props.type === \"range\") {\n        const valid = checkRange(date);\n        if (!valid) {\n          setCurrentDate([date[0], getDayByOffset(date[0], +props.maxRange - 1)]);\n          return;\n        }\n      }\n      setCurrentDate(date);\n      if (complete && !props.showConfirm) {\n        onConfirm();\n      }\n    };\n    const getDisabledDate = (disabledDays2, startDay, date) => {\n      var _a;\n      return (_a = disabledDays2.find(day => compareDay(startDay, day.date) === -1 && compareDay(day.date, date) === -1)) == null ? void 0 : _a.date;\n    };\n    const disabledDays = computed(() => monthRefs.value.reduce((arr, ref2) => {\n      var _a, _b;\n      arr.push(...((_b = (_a = ref2.disabledDays) == null ? void 0 : _a.value) != null ? _b : []));\n      return arr;\n    }, []));\n    const onClickDay = item => {\n      if (props.readonly || !item.date) {\n        return;\n      }\n      const {\n        date\n      } = item;\n      const {\n        type\n      } = props;\n      if (type === \"range\") {\n        if (!currentDate.value) {\n          select([date]);\n          return;\n        }\n        const [startDay, endDay] = currentDate.value;\n        if (startDay && !endDay) {\n          const compareToStart = compareDay(date, startDay);\n          if (compareToStart === 1) {\n            const disabledDay = getDisabledDate(disabledDays.value, startDay, date);\n            if (disabledDay) {\n              const endDay2 = getPrevDay(disabledDay);\n              if (compareDay(startDay, endDay2) === -1) {\n                select([startDay, endDay2]);\n              } else {\n                select([date]);\n              }\n            } else {\n              select([startDay, date], true);\n            }\n          } else if (compareToStart === -1) {\n            select([date]);\n          } else if (props.allowSameDay) {\n            select([date, date], true);\n          }\n        } else {\n          select([date]);\n        }\n      } else if (type === \"multiple\") {\n        if (!currentDate.value) {\n          select([date]);\n          return;\n        }\n        const dates = currentDate.value;\n        const selectedIndex = dates.findIndex(dateItem => compareDay(dateItem, date) === 0);\n        if (selectedIndex !== -1) {\n          const [unselectedDate] = dates.splice(selectedIndex, 1);\n          emit(\"unselect\", cloneDate(unselectedDate));\n        } else if (props.maxRange && dates.length >= +props.maxRange) {\n          showToast(props.rangePrompt || t(\"rangePrompt\", props.maxRange));\n        } else {\n          select([...dates, date]);\n        }\n      } else {\n        select(date, true);\n      }\n    };\n    const onClickOverlay = event => emit(\"clickOverlay\", event);\n    const updateShow = value => emit(\"update:show\", value);\n    const renderMonth = (date, index) => {\n      const showMonthTitle = index !== 0 || !props.showSubtitle;\n      return _createVNode(CalendarMonth, _mergeProps({\n        \"ref\": canSwitch.value ? currentMonthRef : setMonthRefs(index),\n        \"date\": date,\n        \"currentDate\": currentDate.value,\n        \"showMonthTitle\": showMonthTitle,\n        \"firstDayOfWeek\": dayOffset.value,\n        \"lazyRender\": canSwitch.value ? false : props.lazyRender,\n        \"maxDate\": maxDate.value,\n        \"minDate\": minDate.value\n      }, pick(props, [\"type\", \"color\", \"showMark\", \"formatter\", \"rowHeight\", \"showSubtitle\", \"allowSameDay\"]), {\n        \"onClick\": onClickDay,\n        \"onClickDisabledDate\": item => emit(\"clickDisabledDate\", item)\n      }), pick(slots, [\"top-info\", \"bottom-info\", \"month-title\", \"text\"]));\n    };\n    const renderFooterButton = () => {\n      if (slots.footer) {\n        return slots.footer();\n      }\n      if (props.showConfirm) {\n        const slot = slots[\"confirm-text\"];\n        const disabled = buttonDisabled.value;\n        const text = disabled ? props.confirmDisabledText : props.confirmText;\n        return _createVNode(Button, {\n          \"round\": true,\n          \"block\": true,\n          \"type\": \"primary\",\n          \"color\": props.color,\n          \"class\": bem(\"confirm\"),\n          \"disabled\": disabled,\n          \"nativeType\": \"button\",\n          \"onClick\": onConfirm\n        }, {\n          default: () => [slot ? slot({\n            disabled\n          }) : text || t(\"confirm\")]\n        });\n      }\n    };\n    const renderFooter = () => _createVNode(\"div\", {\n      \"class\": [bem(\"footer\"), {\n        \"van-safe-area-bottom\": props.safeAreaInsetBottom\n      }]\n    }, [renderFooterButton()]);\n    const renderCalendar = () => {\n      var _a, _b;\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(CalendarHeader, {\n        \"date\": (_a = currentMonthRef.value) == null ? void 0 : _a.date,\n        \"maxDate\": maxDate.value,\n        \"minDate\": minDate.value,\n        \"title\": props.title,\n        \"subtitle\": (_b = currentMonthRef.value) == null ? void 0 : _b.getTitle(),\n        \"showTitle\": props.showTitle,\n        \"showSubtitle\": props.showSubtitle,\n        \"switchMode\": props.switchMode,\n        \"firstDayOfWeek\": dayOffset.value,\n        \"onClickSubtitle\": event => emit(\"clickSubtitle\", event),\n        \"onPanelChange\": onPanelChange\n      }, pick(slots, [\"title\", \"subtitle\", \"prev-month\", \"prev-year\", \"next-month\", \"next-year\"])), _createVNode(\"div\", {\n        \"ref\": bodyRef,\n        \"class\": bem(\"body\"),\n        \"onScroll\": canSwitch.value ? void 0 : onScroll\n      }, [canSwitch.value ? renderMonth(currentPanelDate.value, 0) : months.value.map(renderMonth)]), renderFooter()]);\n    };\n    watch(() => props.show, init);\n    watch(() => [props.type, props.minDate, props.maxDate, props.switchMode], () => reset(getInitialDate(currentDate.value)));\n    watch(() => props.defaultDate, value => {\n      reset(value);\n    });\n    useExpose({\n      reset,\n      scrollToDate,\n      getSelectedDate\n    });\n    onMountedOrActivated(init);\n    return () => {\n      if (props.poppable) {\n        return _createVNode(Popup, {\n          \"show\": props.show,\n          \"class\": bem(\"popup\"),\n          \"round\": props.round,\n          \"position\": props.position,\n          \"closeable\": props.showTitle || props.showSubtitle,\n          \"teleport\": props.teleport,\n          \"closeOnPopstate\": props.closeOnPopstate,\n          \"safeAreaInsetTop\": props.safeAreaInsetTop,\n          \"closeOnClickOverlay\": props.closeOnClickOverlay,\n          \"onClickOverlay\": onClickOverlay,\n          \"onUpdate:show\": updateShow\n        }, {\n          default: renderCalendar\n        });\n      }\n      return renderCalendar();\n    };\n  }\n});\nexport { calendarProps, stdin_default as default };", "map": {"version": 3, "names": ["ref", "watch", "computed", "defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "pick", "isDate", "truthProp", "numericProp", "getScrollTop", "makeStringProp", "makeNumericProp", "t", "bem", "name", "get<PERSON><PERSON>y", "cloneDate", "cloneDates", "getPrevDay", "getNextDay", "compareDay", "calcDateNum", "compareMonth", "getDayByOffset", "getMonthByOffset", "raf", "useRect", "onMountedOrActivated", "useRefs", "useExpose", "Popup", "<PERSON><PERSON>", "showToast", "CalendarMonth", "CalendarHeader", "calendarProps", "show", "Boolean", "type", "switchMode", "title", "String", "color", "round", "readonly", "poppable", "max<PERSON><PERSON><PERSON>", "position", "teleport", "Object", "showMark", "showTitle", "formatter", "Function", "rowHeight", "confirmText", "rangePrompt", "lazy<PERSON>ender", "showConfirm", "defaultDate", "Date", "Array", "allowSameDay", "showSubtitle", "closeOnPopstate", "showRangePrompt", "confirmDisabledText", "closeOnClickOverlay", "safeAreaInsetTop", "safeAreaInsetBottom", "minDate", "validator", "maxDate", "firstDayOfWeek", "default", "val", "stdin_default", "props", "emits", "setup", "emit", "slots", "canSwitch", "value", "limitDateRange", "date", "min", "max", "getInitialDate", "now", "isArray", "length", "start", "end", "map", "getInitialPanelDate", "currentDate", "bodyHeight", "bodyRef", "currentPanelDate", "currentMonthRef", "monthRefs", "setMonthRefs", "dayOffset", "months", "months2", "cursor", "setDate", "push", "setMonth", "getMonth", "buttonDisabled", "getSelectedDate", "onScroll", "top", "bottom", "heights", "item", "index", "getHeight", "heightSum", "reduce", "a", "b", "height", "currentMonth", "visibleRange", "i", "month", "visible", "showed", "getTitle", "for<PERSON>ach", "setVisible", "scrollToDate", "targetDate", "some", "scrollToCurrentDate", "init", "Math", "floor", "reset", "checkRange", "onPanelChange", "onConfirm", "_a", "select", "complete", "setCurrentDate", "date2", "valid", "getDisabledDate", "disabledDays2", "startDay", "find", "day", "disabledDays", "arr", "ref2", "_b", "onClickDay", "endDay", "compareToStart", "disabledDay", "endDay2", "dates", "selectedIndex", "findIndex", "dateItem", "unselectedDate", "splice", "onClickOverlay", "event", "updateShow", "renderMonth", "showMonthTitle", "renderFooterButton", "footer", "slot", "disabled", "text", "renderFooter", "renderCalendar"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/calendar/Calendar.mjs"], "sourcesContent": ["import { ref, watch, computed, defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { pick, isDate, truthProp, numericProp, getScrollTop, makeStringProp, makeNumericProp } from \"../utils/index.mjs\";\nimport { t, bem, name, getToday, cloneDate, cloneDates, getPrevDay, getNextDay, compareDay, calcDateNum, compareMonth, getDayByOffset, getMonthByOffset } from \"./utils.mjs\";\nimport { raf, useRect, onMountedOrActivated } from \"@vant/use\";\nimport { useRefs } from \"../composables/use-refs.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nimport { Button } from \"../button/index.mjs\";\nimport { showToast } from \"../toast/index.mjs\";\nimport CalendarMonth from \"./CalendarMonth.mjs\";\nimport CalendarHeader from \"./CalendarHeader.mjs\";\nconst calendarProps = {\n  show: Boolean,\n  type: makeStringProp(\"single\"),\n  switchMode: makeStringProp(\"none\"),\n  title: String,\n  color: String,\n  round: truthProp,\n  readonly: Boolean,\n  poppable: truthProp,\n  maxRange: makeNumericProp(null),\n  position: makeStringProp(\"bottom\"),\n  teleport: [String, Object],\n  showMark: truthProp,\n  showTitle: truthProp,\n  formatter: Function,\n  rowHeight: numericProp,\n  confirmText: String,\n  rangePrompt: String,\n  lazyRender: truthProp,\n  showConfirm: truthProp,\n  defaultDate: [Date, Array],\n  allowSameDay: Boolean,\n  showSubtitle: truthProp,\n  closeOnPopstate: truthProp,\n  showRangePrompt: truthProp,\n  confirmDisabledText: String,\n  closeOnClickOverlay: truthProp,\n  safeAreaInsetTop: Boolean,\n  safeAreaInsetBottom: truthProp,\n  minDate: {\n    type: Date,\n    validator: isDate\n  },\n  maxDate: {\n    type: Date,\n    validator: isDate\n  },\n  firstDayOfWeek: {\n    type: numericProp,\n    default: 0,\n    validator: (val) => val >= 0 && val <= 6\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  props: calendarProps,\n  emits: [\"select\", \"confirm\", \"unselect\", \"monthShow\", \"overRange\", \"update:show\", \"clickSubtitle\", \"clickDisabledDate\", \"clickOverlay\", \"panelChange\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const canSwitch = computed(() => props.switchMode !== \"none\");\n    const minDate = computed(() => {\n      if (!props.minDate && !canSwitch.value) {\n        return getToday();\n      }\n      return props.minDate;\n    });\n    const maxDate = computed(() => {\n      if (!props.maxDate && !canSwitch.value) {\n        return getMonthByOffset(getToday(), 6);\n      }\n      return props.maxDate;\n    });\n    const limitDateRange = (date, min = minDate.value, max = maxDate.value) => {\n      if (min && compareDay(date, min) === -1) {\n        return min;\n      }\n      if (max && compareDay(date, max) === 1) {\n        return max;\n      }\n      return date;\n    };\n    const getInitialDate = (defaultDate = props.defaultDate) => {\n      const {\n        type,\n        allowSameDay\n      } = props;\n      if (defaultDate === null) {\n        return defaultDate;\n      }\n      const now = getToday();\n      if (type === \"range\") {\n        if (!Array.isArray(defaultDate)) {\n          defaultDate = [];\n        }\n        if (defaultDate.length === 1 && compareDay(defaultDate[0], now) === 1) {\n          defaultDate = [];\n        }\n        const min = minDate.value;\n        const max = maxDate.value;\n        const start = limitDateRange(defaultDate[0] || now, min, max ? allowSameDay ? max : getPrevDay(max) : void 0);\n        const end = limitDateRange(defaultDate[1] || (allowSameDay ? now : getNextDay(now)), min ? allowSameDay ? min : getNextDay(min) : void 0);\n        return [start, end];\n      }\n      if (type === \"multiple\") {\n        if (Array.isArray(defaultDate)) {\n          return defaultDate.map((date) => limitDateRange(date));\n        }\n        return [limitDateRange(now)];\n      }\n      if (!defaultDate || Array.isArray(defaultDate)) {\n        defaultDate = now;\n      }\n      return limitDateRange(defaultDate);\n    };\n    const getInitialPanelDate = () => {\n      const date = Array.isArray(currentDate.value) ? currentDate.value[0] : currentDate.value;\n      return date ? date : limitDateRange(getToday());\n    };\n    let bodyHeight;\n    const bodyRef = ref();\n    const currentDate = ref(getInitialDate());\n    const currentPanelDate = ref(getInitialPanelDate());\n    const currentMonthRef = ref();\n    const [monthRefs, setMonthRefs] = useRefs();\n    const dayOffset = computed(() => props.firstDayOfWeek ? +props.firstDayOfWeek % 7 : 0);\n    const months = computed(() => {\n      const months2 = [];\n      if (!minDate.value || !maxDate.value) {\n        return months2;\n      }\n      const cursor = new Date(minDate.value);\n      cursor.setDate(1);\n      do {\n        months2.push(new Date(cursor));\n        cursor.setMonth(cursor.getMonth() + 1);\n      } while (compareMonth(cursor, maxDate.value) !== 1);\n      return months2;\n    });\n    const buttonDisabled = computed(() => {\n      if (currentDate.value) {\n        if (props.type === \"range\") {\n          return !currentDate.value[0] || !currentDate.value[1];\n        }\n        if (props.type === \"multiple\") {\n          return !currentDate.value.length;\n        }\n      }\n      return !currentDate.value;\n    });\n    const getSelectedDate = () => currentDate.value;\n    const onScroll = () => {\n      const top = getScrollTop(bodyRef.value);\n      const bottom = top + bodyHeight;\n      const heights = months.value.map((item, index) => monthRefs.value[index].getHeight());\n      const heightSum = heights.reduce((a, b) => a + b, 0);\n      if (bottom > heightSum && top > 0) {\n        return;\n      }\n      let height = 0;\n      let currentMonth;\n      const visibleRange = [-1, -1];\n      for (let i = 0; i < months.value.length; i++) {\n        const month = monthRefs.value[i];\n        const visible = height <= bottom && height + heights[i] >= top;\n        if (visible) {\n          visibleRange[1] = i;\n          if (!currentMonth) {\n            currentMonth = month;\n            visibleRange[0] = i;\n          }\n          if (!monthRefs.value[i].showed) {\n            monthRefs.value[i].showed = true;\n            emit(\"monthShow\", {\n              date: month.date,\n              title: month.getTitle()\n            });\n          }\n        }\n        height += heights[i];\n      }\n      months.value.forEach((month, index) => {\n        const visible = index >= visibleRange[0] - 1 && index <= visibleRange[1] + 1;\n        monthRefs.value[index].setVisible(visible);\n      });\n      if (currentMonth) {\n        currentMonthRef.value = currentMonth;\n      }\n    };\n    const scrollToDate = (targetDate) => {\n      if (canSwitch.value) {\n        currentPanelDate.value = targetDate;\n      } else {\n        raf(() => {\n          months.value.some((month, index) => {\n            if (compareMonth(month, targetDate) === 0) {\n              if (bodyRef.value) {\n                monthRefs.value[index].scrollToDate(bodyRef.value, targetDate);\n              }\n              return true;\n            }\n            return false;\n          });\n          onScroll();\n        });\n      }\n    };\n    const scrollToCurrentDate = () => {\n      if (props.poppable && !props.show) {\n        return;\n      }\n      if (currentDate.value) {\n        const targetDate = props.type === \"single\" ? currentDate.value : currentDate.value[0];\n        if (isDate(targetDate)) {\n          scrollToDate(targetDate);\n        }\n      } else if (!canSwitch.value) {\n        raf(onScroll);\n      }\n    };\n    const init = () => {\n      if (props.poppable && !props.show) {\n        return;\n      }\n      if (!canSwitch.value) {\n        raf(() => {\n          bodyHeight = Math.floor(useRect(bodyRef).height);\n        });\n      }\n      scrollToCurrentDate();\n    };\n    const reset = (date = getInitialDate()) => {\n      currentDate.value = date;\n      scrollToCurrentDate();\n    };\n    const checkRange = (date) => {\n      const {\n        maxRange,\n        rangePrompt,\n        showRangePrompt\n      } = props;\n      if (maxRange && calcDateNum(date) > +maxRange) {\n        if (showRangePrompt) {\n          showToast(rangePrompt || t(\"rangePrompt\", maxRange));\n        }\n        emit(\"overRange\");\n        return false;\n      }\n      return true;\n    };\n    const onPanelChange = (date) => {\n      currentPanelDate.value = date;\n      emit(\"panelChange\", {\n        date\n      });\n    };\n    const onConfirm = () => {\n      var _a;\n      return emit(\"confirm\", (_a = currentDate.value) != null ? _a : cloneDates(currentDate.value));\n    };\n    const select = (date, complete) => {\n      const setCurrentDate = (date2) => {\n        currentDate.value = date2;\n        emit(\"select\", cloneDates(date2));\n      };\n      if (complete && props.type === \"range\") {\n        const valid = checkRange(date);\n        if (!valid) {\n          setCurrentDate([date[0], getDayByOffset(date[0], +props.maxRange - 1)]);\n          return;\n        }\n      }\n      setCurrentDate(date);\n      if (complete && !props.showConfirm) {\n        onConfirm();\n      }\n    };\n    const getDisabledDate = (disabledDays2, startDay, date) => {\n      var _a;\n      return (_a = disabledDays2.find((day) => compareDay(startDay, day.date) === -1 && compareDay(day.date, date) === -1)) == null ? void 0 : _a.date;\n    };\n    const disabledDays = computed(() => monthRefs.value.reduce((arr, ref2) => {\n      var _a, _b;\n      arr.push(...(_b = (_a = ref2.disabledDays) == null ? void 0 : _a.value) != null ? _b : []);\n      return arr;\n    }, []));\n    const onClickDay = (item) => {\n      if (props.readonly || !item.date) {\n        return;\n      }\n      const {\n        date\n      } = item;\n      const {\n        type\n      } = props;\n      if (type === \"range\") {\n        if (!currentDate.value) {\n          select([date]);\n          return;\n        }\n        const [startDay, endDay] = currentDate.value;\n        if (startDay && !endDay) {\n          const compareToStart = compareDay(date, startDay);\n          if (compareToStart === 1) {\n            const disabledDay = getDisabledDate(disabledDays.value, startDay, date);\n            if (disabledDay) {\n              const endDay2 = getPrevDay(disabledDay);\n              if (compareDay(startDay, endDay2) === -1) {\n                select([startDay, endDay2]);\n              } else {\n                select([date]);\n              }\n            } else {\n              select([startDay, date], true);\n            }\n          } else if (compareToStart === -1) {\n            select([date]);\n          } else if (props.allowSameDay) {\n            select([date, date], true);\n          }\n        } else {\n          select([date]);\n        }\n      } else if (type === \"multiple\") {\n        if (!currentDate.value) {\n          select([date]);\n          return;\n        }\n        const dates = currentDate.value;\n        const selectedIndex = dates.findIndex((dateItem) => compareDay(dateItem, date) === 0);\n        if (selectedIndex !== -1) {\n          const [unselectedDate] = dates.splice(selectedIndex, 1);\n          emit(\"unselect\", cloneDate(unselectedDate));\n        } else if (props.maxRange && dates.length >= +props.maxRange) {\n          showToast(props.rangePrompt || t(\"rangePrompt\", props.maxRange));\n        } else {\n          select([...dates, date]);\n        }\n      } else {\n        select(date, true);\n      }\n    };\n    const onClickOverlay = (event) => emit(\"clickOverlay\", event);\n    const updateShow = (value) => emit(\"update:show\", value);\n    const renderMonth = (date, index) => {\n      const showMonthTitle = index !== 0 || !props.showSubtitle;\n      return _createVNode(CalendarMonth, _mergeProps({\n        \"ref\": canSwitch.value ? currentMonthRef : setMonthRefs(index),\n        \"date\": date,\n        \"currentDate\": currentDate.value,\n        \"showMonthTitle\": showMonthTitle,\n        \"firstDayOfWeek\": dayOffset.value,\n        \"lazyRender\": canSwitch.value ? false : props.lazyRender,\n        \"maxDate\": maxDate.value,\n        \"minDate\": minDate.value\n      }, pick(props, [\"type\", \"color\", \"showMark\", \"formatter\", \"rowHeight\", \"showSubtitle\", \"allowSameDay\"]), {\n        \"onClick\": onClickDay,\n        \"onClickDisabledDate\": (item) => emit(\"clickDisabledDate\", item)\n      }), pick(slots, [\"top-info\", \"bottom-info\", \"month-title\", \"text\"]));\n    };\n    const renderFooterButton = () => {\n      if (slots.footer) {\n        return slots.footer();\n      }\n      if (props.showConfirm) {\n        const slot = slots[\"confirm-text\"];\n        const disabled = buttonDisabled.value;\n        const text = disabled ? props.confirmDisabledText : props.confirmText;\n        return _createVNode(Button, {\n          \"round\": true,\n          \"block\": true,\n          \"type\": \"primary\",\n          \"color\": props.color,\n          \"class\": bem(\"confirm\"),\n          \"disabled\": disabled,\n          \"nativeType\": \"button\",\n          \"onClick\": onConfirm\n        }, {\n          default: () => [slot ? slot({\n            disabled\n          }) : text || t(\"confirm\")]\n        });\n      }\n    };\n    const renderFooter = () => _createVNode(\"div\", {\n      \"class\": [bem(\"footer\"), {\n        \"van-safe-area-bottom\": props.safeAreaInsetBottom\n      }]\n    }, [renderFooterButton()]);\n    const renderCalendar = () => {\n      var _a, _b;\n      return _createVNode(\"div\", {\n        \"class\": bem()\n      }, [_createVNode(CalendarHeader, {\n        \"date\": (_a = currentMonthRef.value) == null ? void 0 : _a.date,\n        \"maxDate\": maxDate.value,\n        \"minDate\": minDate.value,\n        \"title\": props.title,\n        \"subtitle\": (_b = currentMonthRef.value) == null ? void 0 : _b.getTitle(),\n        \"showTitle\": props.showTitle,\n        \"showSubtitle\": props.showSubtitle,\n        \"switchMode\": props.switchMode,\n        \"firstDayOfWeek\": dayOffset.value,\n        \"onClickSubtitle\": (event) => emit(\"clickSubtitle\", event),\n        \"onPanelChange\": onPanelChange\n      }, pick(slots, [\"title\", \"subtitle\", \"prev-month\", \"prev-year\", \"next-month\", \"next-year\"])), _createVNode(\"div\", {\n        \"ref\": bodyRef,\n        \"class\": bem(\"body\"),\n        \"onScroll\": canSwitch.value ? void 0 : onScroll\n      }, [canSwitch.value ? renderMonth(currentPanelDate.value, 0) : months.value.map(renderMonth)]), renderFooter()]);\n    };\n    watch(() => props.show, init);\n    watch(() => [props.type, props.minDate, props.maxDate, props.switchMode], () => reset(getInitialDate(currentDate.value)));\n    watch(() => props.defaultDate, (value) => {\n      reset(value);\n    });\n    useExpose({\n      reset,\n      scrollToDate,\n      getSelectedDate\n    });\n    onMountedOrActivated(init);\n    return () => {\n      if (props.poppable) {\n        return _createVNode(Popup, {\n          \"show\": props.show,\n          \"class\": bem(\"popup\"),\n          \"round\": props.round,\n          \"position\": props.position,\n          \"closeable\": props.showTitle || props.showSubtitle,\n          \"teleport\": props.teleport,\n          \"closeOnPopstate\": props.closeOnPopstate,\n          \"safeAreaInsetTop\": props.safeAreaInsetTop,\n          \"closeOnClickOverlay\": props.closeOnClickOverlay,\n          \"onClickOverlay\": onClickOverlay,\n          \"onUpdate:show\": updateShow\n        }, {\n          default: renderCalendar\n        });\n      }\n      return renderCalendar();\n    };\n  }\n});\nexport {\n  calendarProps,\n  stdin_default as default\n};\n"], "mappings": ";;;;;;;AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACnH,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACxH,SAASC,CAAC,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,aAAa;AAC5K,SAASC,GAAG,EAAEC,OAAO,EAAEC,oBAAoB,QAAQ,WAAW;AAC9D,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,cAAc,MAAM,sBAAsB;AACjD,MAAMC,aAAa,GAAG;EACpBC,IAAI,EAAEC,OAAO;EACbC,IAAI,EAAE5B,cAAc,CAAC,QAAQ,CAAC;EAC9B6B,UAAU,EAAE7B,cAAc,CAAC,MAAM,CAAC;EAClC8B,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAED,MAAM;EACbE,KAAK,EAAEpC,SAAS;EAChBqC,QAAQ,EAAEP,OAAO;EACjBQ,QAAQ,EAAEtC,SAAS;EACnBuC,QAAQ,EAAEnC,eAAe,CAAC,IAAI,CAAC;EAC/BoC,QAAQ,EAAErC,cAAc,CAAC,QAAQ,CAAC;EAClCsC,QAAQ,EAAE,CAACP,MAAM,EAAEQ,MAAM,CAAC;EAC1BC,QAAQ,EAAE3C,SAAS;EACnB4C,SAAS,EAAE5C,SAAS;EACpB6C,SAAS,EAAEC,QAAQ;EACnBC,SAAS,EAAE9C,WAAW;EACtB+C,WAAW,EAAEd,MAAM;EACnBe,WAAW,EAAEf,MAAM;EACnBgB,UAAU,EAAElD,SAAS;EACrBmD,WAAW,EAAEnD,SAAS;EACtBoD,WAAW,EAAE,CAACC,IAAI,EAAEC,KAAK,CAAC;EAC1BC,YAAY,EAAEzB,OAAO;EACrB0B,YAAY,EAAExD,SAAS;EACvByD,eAAe,EAAEzD,SAAS;EAC1B0D,eAAe,EAAE1D,SAAS;EAC1B2D,mBAAmB,EAAEzB,MAAM;EAC3B0B,mBAAmB,EAAE5D,SAAS;EAC9B6D,gBAAgB,EAAE/B,OAAO;EACzBgC,mBAAmB,EAAE9D,SAAS;EAC9B+D,OAAO,EAAE;IACPhC,IAAI,EAAEsB,IAAI;IACVW,SAAS,EAAEjE;EACb,CAAC;EACDkE,OAAO,EAAE;IACPlC,IAAI,EAAEsB,IAAI;IACVW,SAAS,EAAEjE;EACb,CAAC;EACDmE,cAAc,EAAE;IACdnC,IAAI,EAAE9B,WAAW;IACjBkE,OAAO,EAAE,CAAC;IACVH,SAAS,EAAGI,GAAG,IAAKA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI;EACzC;AACF,CAAC;AACD,IAAIC,aAAa,GAAG5E,eAAe,CAAC;EAClCc,IAAI;EACJ+D,KAAK,EAAE1C,aAAa;EACpB2C,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,eAAe,EAAE,mBAAmB,EAAE,cAAc,EAAE,aAAa,CAAC;EACtJC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,SAAS,GAAGnF,QAAQ,CAAC,MAAM8E,KAAK,CAACtC,UAAU,KAAK,MAAM,CAAC;IAC7D,MAAM+B,OAAO,GAAGvE,QAAQ,CAAC,MAAM;MAC7B,IAAI,CAAC8E,KAAK,CAACP,OAAO,IAAI,CAACY,SAAS,CAACC,KAAK,EAAE;QACtC,OAAOpE,QAAQ,CAAC,CAAC;MACnB;MACA,OAAO8D,KAAK,CAACP,OAAO;IACtB,CAAC,CAAC;IACF,MAAME,OAAO,GAAGzE,QAAQ,CAAC,MAAM;MAC7B,IAAI,CAAC8E,KAAK,CAACL,OAAO,IAAI,CAACU,SAAS,CAACC,KAAK,EAAE;QACtC,OAAO3D,gBAAgB,CAACT,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACxC;MACA,OAAO8D,KAAK,CAACL,OAAO;IACtB,CAAC,CAAC;IACF,MAAMY,cAAc,GAAGA,CAACC,IAAI,EAAEC,GAAG,GAAGhB,OAAO,CAACa,KAAK,EAAEI,GAAG,GAAGf,OAAO,CAACW,KAAK,KAAK;MACzE,IAAIG,GAAG,IAAIlE,UAAU,CAACiE,IAAI,EAAEC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC,OAAOA,GAAG;MACZ;MACA,IAAIC,GAAG,IAAInE,UAAU,CAACiE,IAAI,EAAEE,GAAG,CAAC,KAAK,CAAC,EAAE;QACtC,OAAOA,GAAG;MACZ;MACA,OAAOF,IAAI;IACb,CAAC;IACD,MAAMG,cAAc,GAAGA,CAAC7B,WAAW,GAAGkB,KAAK,CAAClB,WAAW,KAAK;MAC1D,MAAM;QACJrB,IAAI;QACJwB;MACF,CAAC,GAAGe,KAAK;MACT,IAAIlB,WAAW,KAAK,IAAI,EAAE;QACxB,OAAOA,WAAW;MACpB;MACA,MAAM8B,GAAG,GAAG1E,QAAQ,CAAC,CAAC;MACtB,IAAIuB,IAAI,KAAK,OAAO,EAAE;QACpB,IAAI,CAACuB,KAAK,CAAC6B,OAAO,CAAC/B,WAAW,CAAC,EAAE;UAC/BA,WAAW,GAAG,EAAE;QAClB;QACA,IAAIA,WAAW,CAACgC,MAAM,KAAK,CAAC,IAAIvE,UAAU,CAACuC,WAAW,CAAC,CAAC,CAAC,EAAE8B,GAAG,CAAC,KAAK,CAAC,EAAE;UACrE9B,WAAW,GAAG,EAAE;QAClB;QACA,MAAM2B,GAAG,GAAGhB,OAAO,CAACa,KAAK;QACzB,MAAMI,GAAG,GAAGf,OAAO,CAACW,KAAK;QACzB,MAAMS,KAAK,GAAGR,cAAc,CAACzB,WAAW,CAAC,CAAC,CAAC,IAAI8B,GAAG,EAAEH,GAAG,EAAEC,GAAG,GAAGzB,YAAY,GAAGyB,GAAG,GAAGrE,UAAU,CAACqE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7G,MAAMM,GAAG,GAAGT,cAAc,CAACzB,WAAW,CAAC,CAAC,CAAC,KAAKG,YAAY,GAAG2B,GAAG,GAAGtE,UAAU,CAACsE,GAAG,CAAC,CAAC,EAAEH,GAAG,GAAGxB,YAAY,GAAGwB,GAAG,GAAGnE,UAAU,CAACmE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QACzI,OAAO,CAACM,KAAK,EAAEC,GAAG,CAAC;MACrB;MACA,IAAIvD,IAAI,KAAK,UAAU,EAAE;QACvB,IAAIuB,KAAK,CAAC6B,OAAO,CAAC/B,WAAW,CAAC,EAAE;UAC9B,OAAOA,WAAW,CAACmC,GAAG,CAAET,IAAI,IAAKD,cAAc,CAACC,IAAI,CAAC,CAAC;QACxD;QACA,OAAO,CAACD,cAAc,CAACK,GAAG,CAAC,CAAC;MAC9B;MACA,IAAI,CAAC9B,WAAW,IAAIE,KAAK,CAAC6B,OAAO,CAAC/B,WAAW,CAAC,EAAE;QAC9CA,WAAW,GAAG8B,GAAG;MACnB;MACA,OAAOL,cAAc,CAACzB,WAAW,CAAC;IACpC,CAAC;IACD,MAAMoC,mBAAmB,GAAGA,CAAA,KAAM;MAChC,MAAMV,IAAI,GAAGxB,KAAK,CAAC6B,OAAO,CAACM,WAAW,CAACb,KAAK,CAAC,GAAGa,WAAW,CAACb,KAAK,CAAC,CAAC,CAAC,GAAGa,WAAW,CAACb,KAAK;MACxF,OAAOE,IAAI,GAAGA,IAAI,GAAGD,cAAc,CAACrE,QAAQ,CAAC,CAAC,CAAC;IACjD,CAAC;IACD,IAAIkF,UAAU;IACd,MAAMC,OAAO,GAAGrG,GAAG,CAAC,CAAC;IACrB,MAAMmG,WAAW,GAAGnG,GAAG,CAAC2F,cAAc,CAAC,CAAC,CAAC;IACzC,MAAMW,gBAAgB,GAAGtG,GAAG,CAACkG,mBAAmB,CAAC,CAAC,CAAC;IACnD,MAAMK,eAAe,GAAGvG,GAAG,CAAC,CAAC;IAC7B,MAAM,CAACwG,SAAS,EAAEC,YAAY,CAAC,GAAG1E,OAAO,CAAC,CAAC;IAC3C,MAAM2E,SAAS,GAAGxG,QAAQ,CAAC,MAAM8E,KAAK,CAACJ,cAAc,GAAG,CAACI,KAAK,CAACJ,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;IACtF,MAAM+B,MAAM,GAAGzG,QAAQ,CAAC,MAAM;MAC5B,MAAM0G,OAAO,GAAG,EAAE;MAClB,IAAI,CAACnC,OAAO,CAACa,KAAK,IAAI,CAACX,OAAO,CAACW,KAAK,EAAE;QACpC,OAAOsB,OAAO;MAChB;MACA,MAAMC,MAAM,GAAG,IAAI9C,IAAI,CAACU,OAAO,CAACa,KAAK,CAAC;MACtCuB,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;MACjB,GAAG;QACDF,OAAO,CAACG,IAAI,CAAC,IAAIhD,IAAI,CAAC8C,MAAM,CAAC,CAAC;QAC9BA,MAAM,CAACG,QAAQ,CAACH,MAAM,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MACxC,CAAC,QAAQxF,YAAY,CAACoF,MAAM,EAAElC,OAAO,CAACW,KAAK,CAAC,KAAK,CAAC;MAClD,OAAOsB,OAAO;IAChB,CAAC,CAAC;IACF,MAAMM,cAAc,GAAGhH,QAAQ,CAAC,MAAM;MACpC,IAAIiG,WAAW,CAACb,KAAK,EAAE;QACrB,IAAIN,KAAK,CAACvC,IAAI,KAAK,OAAO,EAAE;UAC1B,OAAO,CAAC0D,WAAW,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI,CAACa,WAAW,CAACb,KAAK,CAAC,CAAC,CAAC;QACvD;QACA,IAAIN,KAAK,CAACvC,IAAI,KAAK,UAAU,EAAE;UAC7B,OAAO,CAAC0D,WAAW,CAACb,KAAK,CAACQ,MAAM;QAClC;MACF;MACA,OAAO,CAACK,WAAW,CAACb,KAAK;IAC3B,CAAC,CAAC;IACF,MAAM6B,eAAe,GAAGA,CAAA,KAAMhB,WAAW,CAACb,KAAK;IAC/C,MAAM8B,QAAQ,GAAGA,CAAA,KAAM;MACrB,MAAMC,GAAG,GAAGzG,YAAY,CAACyF,OAAO,CAACf,KAAK,CAAC;MACvC,MAAMgC,MAAM,GAAGD,GAAG,GAAGjB,UAAU;MAC/B,MAAMmB,OAAO,GAAGZ,MAAM,CAACrB,KAAK,CAACW,GAAG,CAAC,CAACuB,IAAI,EAAEC,KAAK,KAAKjB,SAAS,CAAClB,KAAK,CAACmC,KAAK,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;MACrF,MAAMC,SAAS,GAAGJ,OAAO,CAACK,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;MACpD,IAAIR,MAAM,GAAGK,SAAS,IAAIN,GAAG,GAAG,CAAC,EAAE;QACjC;MACF;MACA,IAAIU,MAAM,GAAG,CAAC;MACd,IAAIC,YAAY;MAChB,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,MAAM,CAACrB,KAAK,CAACQ,MAAM,EAAEoC,CAAC,EAAE,EAAE;QAC5C,MAAMC,KAAK,GAAG3B,SAAS,CAAClB,KAAK,CAAC4C,CAAC,CAAC;QAChC,MAAME,OAAO,GAAGL,MAAM,IAAIT,MAAM,IAAIS,MAAM,GAAGR,OAAO,CAACW,CAAC,CAAC,IAAIb,GAAG;QAC9D,IAAIe,OAAO,EAAE;UACXH,YAAY,CAAC,CAAC,CAAC,GAAGC,CAAC;UACnB,IAAI,CAACF,YAAY,EAAE;YACjBA,YAAY,GAAGG,KAAK;YACpBF,YAAY,CAAC,CAAC,CAAC,GAAGC,CAAC;UACrB;UACA,IAAI,CAAC1B,SAAS,CAAClB,KAAK,CAAC4C,CAAC,CAAC,CAACG,MAAM,EAAE;YAC9B7B,SAAS,CAAClB,KAAK,CAAC4C,CAAC,CAAC,CAACG,MAAM,GAAG,IAAI;YAChClD,IAAI,CAAC,WAAW,EAAE;cAChBK,IAAI,EAAE2C,KAAK,CAAC3C,IAAI;cAChB7C,KAAK,EAAEwF,KAAK,CAACG,QAAQ,CAAC;YACxB,CAAC,CAAC;UACJ;QACF;QACAP,MAAM,IAAIR,OAAO,CAACW,CAAC,CAAC;MACtB;MACAvB,MAAM,CAACrB,KAAK,CAACiD,OAAO,CAAC,CAACJ,KAAK,EAAEV,KAAK,KAAK;QACrC,MAAMW,OAAO,GAAGX,KAAK,IAAIQ,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIR,KAAK,IAAIQ,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;QAC5EzB,SAAS,CAAClB,KAAK,CAACmC,KAAK,CAAC,CAACe,UAAU,CAACJ,OAAO,CAAC;MAC5C,CAAC,CAAC;MACF,IAAIJ,YAAY,EAAE;QAChBzB,eAAe,CAACjB,KAAK,GAAG0C,YAAY;MACtC;IACF,CAAC;IACD,MAAMS,YAAY,GAAIC,UAAU,IAAK;MACnC,IAAIrD,SAAS,CAACC,KAAK,EAAE;QACnBgB,gBAAgB,CAAChB,KAAK,GAAGoD,UAAU;MACrC,CAAC,MAAM;QACL9G,GAAG,CAAC,MAAM;UACR+E,MAAM,CAACrB,KAAK,CAACqD,IAAI,CAAC,CAACR,KAAK,EAAEV,KAAK,KAAK;YAClC,IAAIhG,YAAY,CAAC0G,KAAK,EAAEO,UAAU,CAAC,KAAK,CAAC,EAAE;cACzC,IAAIrC,OAAO,CAACf,KAAK,EAAE;gBACjBkB,SAAS,CAAClB,KAAK,CAACmC,KAAK,CAAC,CAACgB,YAAY,CAACpC,OAAO,CAACf,KAAK,EAAEoD,UAAU,CAAC;cAChE;cACA,OAAO,IAAI;YACb;YACA,OAAO,KAAK;UACd,CAAC,CAAC;UACFtB,QAAQ,CAAC,CAAC;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;MAChC,IAAI5D,KAAK,CAAChC,QAAQ,IAAI,CAACgC,KAAK,CAACzC,IAAI,EAAE;QACjC;MACF;MACA,IAAI4D,WAAW,CAACb,KAAK,EAAE;QACrB,MAAMoD,UAAU,GAAG1D,KAAK,CAACvC,IAAI,KAAK,QAAQ,GAAG0D,WAAW,CAACb,KAAK,GAAGa,WAAW,CAACb,KAAK,CAAC,CAAC,CAAC;QACrF,IAAI7E,MAAM,CAACiI,UAAU,CAAC,EAAE;UACtBD,YAAY,CAACC,UAAU,CAAC;QAC1B;MACF,CAAC,MAAM,IAAI,CAACrD,SAAS,CAACC,KAAK,EAAE;QAC3B1D,GAAG,CAACwF,QAAQ,CAAC;MACf;IACF,CAAC;IACD,MAAMyB,IAAI,GAAGA,CAAA,KAAM;MACjB,IAAI7D,KAAK,CAAChC,QAAQ,IAAI,CAACgC,KAAK,CAACzC,IAAI,EAAE;QACjC;MACF;MACA,IAAI,CAAC8C,SAAS,CAACC,KAAK,EAAE;QACpB1D,GAAG,CAAC,MAAM;UACRwE,UAAU,GAAG0C,IAAI,CAACC,KAAK,CAAClH,OAAO,CAACwE,OAAO,CAAC,CAAC0B,MAAM,CAAC;QAClD,CAAC,CAAC;MACJ;MACAa,mBAAmB,CAAC,CAAC;IACvB,CAAC;IACD,MAAMI,KAAK,GAAGA,CAACxD,IAAI,GAAGG,cAAc,CAAC,CAAC,KAAK;MACzCQ,WAAW,CAACb,KAAK,GAAGE,IAAI;MACxBoD,mBAAmB,CAAC,CAAC;IACvB,CAAC;IACD,MAAMK,UAAU,GAAIzD,IAAI,IAAK;MAC3B,MAAM;QACJvC,QAAQ;QACRU,WAAW;QACXS;MACF,CAAC,GAAGY,KAAK;MACT,IAAI/B,QAAQ,IAAIzB,WAAW,CAACgE,IAAI,CAAC,GAAG,CAACvC,QAAQ,EAAE;QAC7C,IAAImB,eAAe,EAAE;UACnBjC,SAAS,CAACwB,WAAW,IAAI5C,CAAC,CAAC,aAAa,EAAEkC,QAAQ,CAAC,CAAC;QACtD;QACAkC,IAAI,CAAC,WAAW,CAAC;QACjB,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAM+D,aAAa,GAAI1D,IAAI,IAAK;MAC9Bc,gBAAgB,CAAChB,KAAK,GAAGE,IAAI;MAC7BL,IAAI,CAAC,aAAa,EAAE;QAClBK;MACF,CAAC,CAAC;IACJ,CAAC;IACD,MAAM2D,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAIC,EAAE;MACN,OAAOjE,IAAI,CAAC,SAAS,EAAE,CAACiE,EAAE,GAAGjD,WAAW,CAACb,KAAK,KAAK,IAAI,GAAG8D,EAAE,GAAGhI,UAAU,CAAC+E,WAAW,CAACb,KAAK,CAAC,CAAC;IAC/F,CAAC;IACD,MAAM+D,MAAM,GAAGA,CAAC7D,IAAI,EAAE8D,QAAQ,KAAK;MACjC,MAAMC,cAAc,GAAIC,KAAK,IAAK;QAChCrD,WAAW,CAACb,KAAK,GAAGkE,KAAK;QACzBrE,IAAI,CAAC,QAAQ,EAAE/D,UAAU,CAACoI,KAAK,CAAC,CAAC;MACnC,CAAC;MACD,IAAIF,QAAQ,IAAItE,KAAK,CAACvC,IAAI,KAAK,OAAO,EAAE;QACtC,MAAMgH,KAAK,GAAGR,UAAU,CAACzD,IAAI,CAAC;QAC9B,IAAI,CAACiE,KAAK,EAAE;UACVF,cAAc,CAAC,CAAC/D,IAAI,CAAC,CAAC,CAAC,EAAE9D,cAAc,CAAC8D,IAAI,CAAC,CAAC,CAAC,EAAE,CAACR,KAAK,CAAC/B,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;UACvE;QACF;MACF;MACAsG,cAAc,CAAC/D,IAAI,CAAC;MACpB,IAAI8D,QAAQ,IAAI,CAACtE,KAAK,CAACnB,WAAW,EAAE;QAClCsF,SAAS,CAAC,CAAC;MACb;IACF,CAAC;IACD,MAAMO,eAAe,GAAGA,CAACC,aAAa,EAAEC,QAAQ,EAAEpE,IAAI,KAAK;MACzD,IAAI4D,EAAE;MACN,OAAO,CAACA,EAAE,GAAGO,aAAa,CAACE,IAAI,CAAEC,GAAG,IAAKvI,UAAU,CAACqI,QAAQ,EAAEE,GAAG,CAACtE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAIjE,UAAU,CAACuI,GAAG,CAACtE,IAAI,EAAEA,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4D,EAAE,CAAC5D,IAAI;IAClJ,CAAC;IACD,MAAMuE,YAAY,GAAG7J,QAAQ,CAAC,MAAMsG,SAAS,CAAClB,KAAK,CAACsC,MAAM,CAAC,CAACoC,GAAG,EAAEC,IAAI,KAAK;MACxE,IAAIb,EAAE,EAAEc,EAAE;MACVF,GAAG,CAACjD,IAAI,CAAC,IAAG,CAACmD,EAAE,GAAG,CAACd,EAAE,GAAGa,IAAI,CAACF,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGX,EAAE,CAAC9D,KAAK,KAAK,IAAI,GAAG4E,EAAE,GAAG,EAAE,EAAC;MAC1F,OAAOF,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,MAAMG,UAAU,GAAI3C,IAAI,IAAK;MAC3B,IAAIxC,KAAK,CAACjC,QAAQ,IAAI,CAACyE,IAAI,CAAChC,IAAI,EAAE;QAChC;MACF;MACA,MAAM;QACJA;MACF,CAAC,GAAGgC,IAAI;MACR,MAAM;QACJ/E;MACF,CAAC,GAAGuC,KAAK;MACT,IAAIvC,IAAI,KAAK,OAAO,EAAE;QACpB,IAAI,CAAC0D,WAAW,CAACb,KAAK,EAAE;UACtB+D,MAAM,CAAC,CAAC7D,IAAI,CAAC,CAAC;UACd;QACF;QACA,MAAM,CAACoE,QAAQ,EAAEQ,MAAM,CAAC,GAAGjE,WAAW,CAACb,KAAK;QAC5C,IAAIsE,QAAQ,IAAI,CAACQ,MAAM,EAAE;UACvB,MAAMC,cAAc,GAAG9I,UAAU,CAACiE,IAAI,EAAEoE,QAAQ,CAAC;UACjD,IAAIS,cAAc,KAAK,CAAC,EAAE;YACxB,MAAMC,WAAW,GAAGZ,eAAe,CAACK,YAAY,CAACzE,KAAK,EAAEsE,QAAQ,EAAEpE,IAAI,CAAC;YACvE,IAAI8E,WAAW,EAAE;cACf,MAAMC,OAAO,GAAGlJ,UAAU,CAACiJ,WAAW,CAAC;cACvC,IAAI/I,UAAU,CAACqI,QAAQ,EAAEW,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxClB,MAAM,CAAC,CAACO,QAAQ,EAAEW,OAAO,CAAC,CAAC;cAC7B,CAAC,MAAM;gBACLlB,MAAM,CAAC,CAAC7D,IAAI,CAAC,CAAC;cAChB;YACF,CAAC,MAAM;cACL6D,MAAM,CAAC,CAACO,QAAQ,EAAEpE,IAAI,CAAC,EAAE,IAAI,CAAC;YAChC;UACF,CAAC,MAAM,IAAI6E,cAAc,KAAK,CAAC,CAAC,EAAE;YAChChB,MAAM,CAAC,CAAC7D,IAAI,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIR,KAAK,CAACf,YAAY,EAAE;YAC7BoF,MAAM,CAAC,CAAC7D,IAAI,EAAEA,IAAI,CAAC,EAAE,IAAI,CAAC;UAC5B;QACF,CAAC,MAAM;UACL6D,MAAM,CAAC,CAAC7D,IAAI,CAAC,CAAC;QAChB;MACF,CAAC,MAAM,IAAI/C,IAAI,KAAK,UAAU,EAAE;QAC9B,IAAI,CAAC0D,WAAW,CAACb,KAAK,EAAE;UACtB+D,MAAM,CAAC,CAAC7D,IAAI,CAAC,CAAC;UACd;QACF;QACA,MAAMgF,KAAK,GAAGrE,WAAW,CAACb,KAAK;QAC/B,MAAMmF,aAAa,GAAGD,KAAK,CAACE,SAAS,CAAEC,QAAQ,IAAKpJ,UAAU,CAACoJ,QAAQ,EAAEnF,IAAI,CAAC,KAAK,CAAC,CAAC;QACrF,IAAIiF,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,MAAM,CAACG,cAAc,CAAC,GAAGJ,KAAK,CAACK,MAAM,CAACJ,aAAa,EAAE,CAAC,CAAC;UACvDtF,IAAI,CAAC,UAAU,EAAEhE,SAAS,CAACyJ,cAAc,CAAC,CAAC;QAC7C,CAAC,MAAM,IAAI5F,KAAK,CAAC/B,QAAQ,IAAIuH,KAAK,CAAC1E,MAAM,IAAI,CAACd,KAAK,CAAC/B,QAAQ,EAAE;UAC5Dd,SAAS,CAAC6C,KAAK,CAACrB,WAAW,IAAI5C,CAAC,CAAC,aAAa,EAAEiE,KAAK,CAAC/B,QAAQ,CAAC,CAAC;QAClE,CAAC,MAAM;UACLoG,MAAM,CAAC,CAAC,GAAGmB,KAAK,EAAEhF,IAAI,CAAC,CAAC;QAC1B;MACF,CAAC,MAAM;QACL6D,MAAM,CAAC7D,IAAI,EAAE,IAAI,CAAC;MACpB;IACF,CAAC;IACD,MAAMsF,cAAc,GAAIC,KAAK,IAAK5F,IAAI,CAAC,cAAc,EAAE4F,KAAK,CAAC;IAC7D,MAAMC,UAAU,GAAI1F,KAAK,IAAKH,IAAI,CAAC,aAAa,EAAEG,KAAK,CAAC;IACxD,MAAM2F,WAAW,GAAGA,CAACzF,IAAI,EAAEiC,KAAK,KAAK;MACnC,MAAMyD,cAAc,GAAGzD,KAAK,KAAK,CAAC,IAAI,CAACzC,KAAK,CAACd,YAAY;MACzD,OAAO3D,YAAY,CAAC6B,aAAa,EAAE/B,WAAW,CAAC;QAC7C,KAAK,EAAEgF,SAAS,CAACC,KAAK,GAAGiB,eAAe,GAAGE,YAAY,CAACgB,KAAK,CAAC;QAC9D,MAAM,EAAEjC,IAAI;QACZ,aAAa,EAAEW,WAAW,CAACb,KAAK;QAChC,gBAAgB,EAAE4F,cAAc;QAChC,gBAAgB,EAAExE,SAAS,CAACpB,KAAK;QACjC,YAAY,EAAED,SAAS,CAACC,KAAK,GAAG,KAAK,GAAGN,KAAK,CAACpB,UAAU;QACxD,SAAS,EAAEe,OAAO,CAACW,KAAK;QACxB,SAAS,EAAEb,OAAO,CAACa;MACrB,CAAC,EAAE9E,IAAI,CAACwE,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC,EAAE;QACvG,SAAS,EAAEmF,UAAU;QACrB,qBAAqB,EAAG3C,IAAI,IAAKrC,IAAI,CAAC,mBAAmB,EAAEqC,IAAI;MACjE,CAAC,CAAC,EAAEhH,IAAI,CAAC4E,KAAK,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;IACtE,CAAC;IACD,MAAM+F,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAI/F,KAAK,CAACgG,MAAM,EAAE;QAChB,OAAOhG,KAAK,CAACgG,MAAM,CAAC,CAAC;MACvB;MACA,IAAIpG,KAAK,CAACnB,WAAW,EAAE;QACrB,MAAMwH,IAAI,GAAGjG,KAAK,CAAC,cAAc,CAAC;QAClC,MAAMkG,QAAQ,GAAGpE,cAAc,CAAC5B,KAAK;QACrC,MAAMiG,IAAI,GAAGD,QAAQ,GAAGtG,KAAK,CAACX,mBAAmB,GAAGW,KAAK,CAACtB,WAAW;QACrE,OAAOnD,YAAY,CAAC2B,MAAM,EAAE;UAC1B,OAAO,EAAE,IAAI;UACb,OAAO,EAAE,IAAI;UACb,MAAM,EAAE,SAAS;UACjB,OAAO,EAAE8C,KAAK,CAACnC,KAAK;UACpB,OAAO,EAAE7B,GAAG,CAAC,SAAS,CAAC;UACvB,UAAU,EAAEsK,QAAQ;UACpB,YAAY,EAAE,QAAQ;UACtB,SAAS,EAAEnC;QACb,CAAC,EAAE;UACDtE,OAAO,EAAEA,CAAA,KAAM,CAACwG,IAAI,GAAGA,IAAI,CAAC;YAC1BC;UACF,CAAC,CAAC,GAAGC,IAAI,IAAIxK,CAAC,CAAC,SAAS,CAAC;QAC3B,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAMyK,YAAY,GAAGA,CAAA,KAAMjL,YAAY,CAAC,KAAK,EAAE;MAC7C,OAAO,EAAE,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAE;QACvB,sBAAsB,EAAEgE,KAAK,CAACR;MAChC,CAAC;IACH,CAAC,EAAE,CAAC2G,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAMM,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIrC,EAAE,EAAEc,EAAE;MACV,OAAO3J,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAES,GAAG,CAAC;MACf,CAAC,EAAE,CAACT,YAAY,CAAC8B,cAAc,EAAE;QAC/B,MAAM,EAAE,CAAC+G,EAAE,GAAG7C,eAAe,CAACjB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8D,EAAE,CAAC5D,IAAI;QAC/D,SAAS,EAAEb,OAAO,CAACW,KAAK;QACxB,SAAS,EAAEb,OAAO,CAACa,KAAK;QACxB,OAAO,EAAEN,KAAK,CAACrC,KAAK;QACpB,UAAU,EAAE,CAACuH,EAAE,GAAG3D,eAAe,CAACjB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4E,EAAE,CAAC5B,QAAQ,CAAC,CAAC;QACzE,WAAW,EAAEtD,KAAK,CAAC1B,SAAS;QAC5B,cAAc,EAAE0B,KAAK,CAACd,YAAY;QAClC,YAAY,EAAEc,KAAK,CAACtC,UAAU;QAC9B,gBAAgB,EAAEgE,SAAS,CAACpB,KAAK;QACjC,iBAAiB,EAAGyF,KAAK,IAAK5F,IAAI,CAAC,eAAe,EAAE4F,KAAK,CAAC;QAC1D,eAAe,EAAE7B;MACnB,CAAC,EAAE1I,IAAI,CAAC4E,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE7E,YAAY,CAAC,KAAK,EAAE;QAChH,KAAK,EAAE8F,OAAO;QACd,OAAO,EAAErF,GAAG,CAAC,MAAM,CAAC;QACpB,UAAU,EAAEqE,SAAS,CAACC,KAAK,GAAG,KAAK,CAAC,GAAG8B;MACzC,CAAC,EAAE,CAAC/B,SAAS,CAACC,KAAK,GAAG2F,WAAW,CAAC3E,gBAAgB,CAAChB,KAAK,EAAE,CAAC,CAAC,GAAGqB,MAAM,CAACrB,KAAK,CAACW,GAAG,CAACgF,WAAW,CAAC,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,CAAC;IAClH,CAAC;IACDvL,KAAK,CAAC,MAAM+E,KAAK,CAACzC,IAAI,EAAEsG,IAAI,CAAC;IAC7B5I,KAAK,CAAC,MAAM,CAAC+E,KAAK,CAACvC,IAAI,EAAEuC,KAAK,CAACP,OAAO,EAAEO,KAAK,CAACL,OAAO,EAAEK,KAAK,CAACtC,UAAU,CAAC,EAAE,MAAMsG,KAAK,CAACrD,cAAc,CAACQ,WAAW,CAACb,KAAK,CAAC,CAAC,CAAC;IACzHrF,KAAK,CAAC,MAAM+E,KAAK,CAAClB,WAAW,EAAGwB,KAAK,IAAK;MACxC0D,KAAK,CAAC1D,KAAK,CAAC;IACd,CAAC,CAAC;IACFtD,SAAS,CAAC;MACRgH,KAAK;MACLP,YAAY;MACZtB;IACF,CAAC,CAAC;IACFrF,oBAAoB,CAAC+G,IAAI,CAAC;IAC1B,OAAO,MAAM;MACX,IAAI7D,KAAK,CAAChC,QAAQ,EAAE;QAClB,OAAOzC,YAAY,CAAC0B,KAAK,EAAE;UACzB,MAAM,EAAE+C,KAAK,CAACzC,IAAI;UAClB,OAAO,EAAEvB,GAAG,CAAC,OAAO,CAAC;UACrB,OAAO,EAAEgE,KAAK,CAAClC,KAAK;UACpB,UAAU,EAAEkC,KAAK,CAAC9B,QAAQ;UAC1B,WAAW,EAAE8B,KAAK,CAAC1B,SAAS,IAAI0B,KAAK,CAACd,YAAY;UAClD,UAAU,EAAEc,KAAK,CAAC7B,QAAQ;UAC1B,iBAAiB,EAAE6B,KAAK,CAACb,eAAe;UACxC,kBAAkB,EAAEa,KAAK,CAACT,gBAAgB;UAC1C,qBAAqB,EAAES,KAAK,CAACV,mBAAmB;UAChD,gBAAgB,EAAEwG,cAAc;UAChC,eAAe,EAAEE;QACnB,CAAC,EAAE;UACDnG,OAAO,EAAE4G;QACX,CAAC,CAAC;MACJ;MACA,OAAOA,cAAc,CAAC,CAAC;IACzB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEnJ,aAAa,EACbyC,aAAa,IAAIF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}