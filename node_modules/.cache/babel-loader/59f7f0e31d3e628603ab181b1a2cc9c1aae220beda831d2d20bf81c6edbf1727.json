{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, numericProp, addUnit } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton-title\");\nconst skeletonTitleProps = {\n  round: Boolean,\n  titleWidth: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: skeletonTitleProps,\n  setup(props) {\n    return () => _createVNode(\"h3\", {\n      \"class\": bem([{\n        round: props.round\n      }]),\n      \"style\": {\n        width: addUnit(props.titleWidth)\n      }\n    }, null);\n  }\n});\nexport { stdin_default as default, skeletonTitleProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "createNamespace", "numericProp", "addUnit", "name", "bem", "skeletonTitleProps", "round", "Boolean", "titleWidth", "stdin_default", "props", "setup", "width", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/skeleton-title/SkeletonTitle.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, numericProp, addUnit } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton-title\");\nconst skeletonTitleProps = {\n  round: Boolean,\n  titleWidth: numericProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: skeletonTitleProps,\n  setup(props) {\n    return () => _createVNode(\"h3\", {\n      \"class\": bem([{\n        round: props.round\n      }]),\n      \"style\": {\n        width: addUnit(props.titleWidth)\n      }\n    }, null);\n  }\n});\nexport {\n  stdin_default as default,\n  skeletonTitleProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,eAAe,EAAEC,WAAW,EAAEC,OAAO,QAAQ,oBAAoB;AAC1E,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,gBAAgB,CAAC;AACrD,MAAMK,kBAAkB,GAAG;EACzBC,KAAK,EAAEC,OAAO;EACdC,UAAU,EAAEP;AACd,CAAC;AACD,IAAIQ,aAAa,GAAGZ,eAAe,CAAC;EAClCM,IAAI;EACJO,KAAK,EAAEL,kBAAkB;EACzBM,KAAKA,CAACD,KAAK,EAAE;IACX,OAAO,MAAMX,YAAY,CAAC,IAAI,EAAE;MAC9B,OAAO,EAAEK,GAAG,CAAC,CAAC;QACZE,KAAK,EAAEI,KAAK,CAACJ;MACf,CAAC,CAAC,CAAC;MACH,OAAO,EAAE;QACPM,KAAK,EAAEV,OAAO,CAACQ,KAAK,CAACF,UAAU;MACjC;IACF,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC,CAAC;AACF,SACEC,aAAa,IAAII,OAAO,EACxBR,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}