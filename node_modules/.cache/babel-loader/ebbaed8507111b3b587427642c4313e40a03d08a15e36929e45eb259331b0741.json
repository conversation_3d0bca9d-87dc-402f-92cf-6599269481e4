{"ast": null, "code": "import { isObject, isPromise, isFunction, getRootScrollTop, setRootScrollTop } from \"../utils/index.mjs\";\nfunction isEmptyValue(value) {\n  if (Array.isArray(value)) {\n    return !value.length;\n  }\n  if (value === 0) {\n    return false;\n  }\n  return !value;\n}\nfunction runSyncRule(value, rule) {\n  if (isEmptyValue(value)) {\n    if (rule.required) {\n      return false;\n    }\n    if (rule.validateEmpty === false) {\n      return true;\n    }\n  }\n  if (rule.pattern && !rule.pattern.test(String(value))) {\n    return false;\n  }\n  return true;\n}\nfunction runRuleValidator(value, rule) {\n  return new Promise(resolve => {\n    const returnVal = rule.validator(value, rule);\n    if (isPromise(returnVal)) {\n      returnVal.then(resolve);\n      return;\n    }\n    resolve(returnVal);\n  });\n}\nfunction getRuleMessage(value, rule) {\n  const {\n    message\n  } = rule;\n  if (isFunction(message)) {\n    return message(value, rule);\n  }\n  return message || \"\";\n}\nfunction startComposing({\n  target\n}) {\n  target.composing = true;\n}\nfunction endComposing({\n  target\n}) {\n  if (target.composing) {\n    target.composing = false;\n    target.dispatchEvent(new Event(\"input\"));\n  }\n}\nfunction resizeTextarea(input, autosize) {\n  const scrollTop = getRootScrollTop();\n  input.style.height = \"auto\";\n  let height = input.scrollHeight;\n  if (isObject(autosize)) {\n    const {\n      maxHeight,\n      minHeight\n    } = autosize;\n    if (maxHeight !== void 0) {\n      height = Math.min(height, maxHeight);\n    }\n    if (minHeight !== void 0) {\n      height = Math.max(height, minHeight);\n    }\n  }\n  if (height) {\n    input.style.height = `${height}px`;\n    setRootScrollTop(scrollTop);\n  }\n}\nfunction mapInputType(type, inputmode) {\n  if (type === \"number\") {\n    type = \"text\";\n    inputmode != null ? inputmode : inputmode = \"decimal\";\n  }\n  if (type === \"digit\") {\n    type = \"tel\";\n    inputmode != null ? inputmode : inputmode = \"numeric\";\n  }\n  return {\n    type,\n    inputmode\n  };\n}\nfunction getStringLength(str) {\n  return [...str].length;\n}\nfunction cutString(str, maxlength) {\n  return [...str].slice(0, maxlength).join(\"\");\n}\nexport { cutString, endComposing, getRuleMessage, getStringLength, isEmptyValue, mapInputType, resizeTextarea, runRuleValidator, runSyncRule, startComposing };", "map": {"version": 3, "names": ["isObject", "isPromise", "isFunction", "getRootScrollTop", "setRootScrollTop", "isEmptyValue", "value", "Array", "isArray", "length", "runSyncRule", "rule", "required", "validateEmpty", "pattern", "test", "String", "runRuleValidator", "Promise", "resolve", "returnVal", "validator", "then", "getRuleMessage", "message", "startComposing", "target", "composing", "endComposing", "dispatchEvent", "Event", "resizeTextarea", "input", "autosize", "scrollTop", "style", "height", "scrollHeight", "maxHeight", "minHeight", "Math", "min", "max", "mapInputType", "type", "inputmode", "getStringLength", "str", "cutString", "maxlength", "slice", "join"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/field/utils.mjs"], "sourcesContent": ["import {\n  isObject,\n  isPromise,\n  isFunction,\n  getRootScrollTop,\n  setRootScrollTop\n} from \"../utils/index.mjs\";\nfunction isEmptyValue(value) {\n  if (Array.isArray(value)) {\n    return !value.length;\n  }\n  if (value === 0) {\n    return false;\n  }\n  return !value;\n}\nfunction runSyncRule(value, rule) {\n  if (isEmptyValue(value)) {\n    if (rule.required) {\n      return false;\n    }\n    if (rule.validateEmpty === false) {\n      return true;\n    }\n  }\n  if (rule.pattern && !rule.pattern.test(String(value))) {\n    return false;\n  }\n  return true;\n}\nfunction runRuleValidator(value, rule) {\n  return new Promise((resolve) => {\n    const returnVal = rule.validator(value, rule);\n    if (isPromise(returnVal)) {\n      returnVal.then(resolve);\n      return;\n    }\n    resolve(returnVal);\n  });\n}\nfunction getRuleMessage(value, rule) {\n  const { message } = rule;\n  if (isFunction(message)) {\n    return message(value, rule);\n  }\n  return message || \"\";\n}\nfunction startComposing({ target }) {\n  target.composing = true;\n}\nfunction endComposing({ target }) {\n  if (target.composing) {\n    target.composing = false;\n    target.dispatchEvent(new Event(\"input\"));\n  }\n}\nfunction resizeTextarea(input, autosize) {\n  const scrollTop = getRootScrollTop();\n  input.style.height = \"auto\";\n  let height = input.scrollHeight;\n  if (isObject(autosize)) {\n    const { maxHeight, minHeight } = autosize;\n    if (maxHeight !== void 0) {\n      height = Math.min(height, maxHeight);\n    }\n    if (minHeight !== void 0) {\n      height = Math.max(height, minHeight);\n    }\n  }\n  if (height) {\n    input.style.height = `${height}px`;\n    setRootScrollTop(scrollTop);\n  }\n}\nfunction mapInputType(type, inputmode) {\n  if (type === \"number\") {\n    type = \"text\";\n    inputmode != null ? inputmode : inputmode = \"decimal\";\n  }\n  if (type === \"digit\") {\n    type = \"tel\";\n    inputmode != null ? inputmode : inputmode = \"numeric\";\n  }\n  return { type, inputmode };\n}\nfunction getStringLength(str) {\n  return [...str].length;\n}\nfunction cutString(str, maxlength) {\n  return [...str].slice(0, maxlength).join(\"\");\n}\nexport {\n  cutString,\n  endComposing,\n  getRuleMessage,\n  getStringLength,\n  isEmptyValue,\n  mapInputType,\n  resizeTextarea,\n  runRuleValidator,\n  runSyncRule,\n  startComposing\n};\n"], "mappings": "AAAA,SACEA,QAAQ,EACRC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,gBAAgB,QACX,oBAAoB;AAC3B,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACxB,OAAO,CAACA,KAAK,CAACG,MAAM;EACtB;EACA,IAAIH,KAAK,KAAK,CAAC,EAAE;IACf,OAAO,KAAK;EACd;EACA,OAAO,CAACA,KAAK;AACf;AACA,SAASI,WAAWA,CAACJ,KAAK,EAAEK,IAAI,EAAE;EAChC,IAAIN,YAAY,CAACC,KAAK,CAAC,EAAE;IACvB,IAAIK,IAAI,CAACC,QAAQ,EAAE;MACjB,OAAO,KAAK;IACd;IACA,IAAID,IAAI,CAACE,aAAa,KAAK,KAAK,EAAE;MAChC,OAAO,IAAI;IACb;EACF;EACA,IAAIF,IAAI,CAACG,OAAO,IAAI,CAACH,IAAI,CAACG,OAAO,CAACC,IAAI,CAACC,MAAM,CAACV,KAAK,CAAC,CAAC,EAAE;IACrD,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACA,SAASW,gBAAgBA,CAACX,KAAK,EAAEK,IAAI,EAAE;EACrC,OAAO,IAAIO,OAAO,CAAEC,OAAO,IAAK;IAC9B,MAAMC,SAAS,GAAGT,IAAI,CAACU,SAAS,CAACf,KAAK,EAAEK,IAAI,CAAC;IAC7C,IAAIV,SAAS,CAACmB,SAAS,CAAC,EAAE;MACxBA,SAAS,CAACE,IAAI,CAACH,OAAO,CAAC;MACvB;IACF;IACAA,OAAO,CAACC,SAAS,CAAC;EACpB,CAAC,CAAC;AACJ;AACA,SAASG,cAAcA,CAACjB,KAAK,EAAEK,IAAI,EAAE;EACnC,MAAM;IAAEa;EAAQ,CAAC,GAAGb,IAAI;EACxB,IAAIT,UAAU,CAACsB,OAAO,CAAC,EAAE;IACvB,OAAOA,OAAO,CAAClB,KAAK,EAAEK,IAAI,CAAC;EAC7B;EACA,OAAOa,OAAO,IAAI,EAAE;AACtB;AACA,SAASC,cAAcA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAClCA,MAAM,CAACC,SAAS,GAAG,IAAI;AACzB;AACA,SAASC,YAAYA,CAAC;EAAEF;AAAO,CAAC,EAAE;EAChC,IAAIA,MAAM,CAACC,SAAS,EAAE;IACpBD,MAAM,CAACC,SAAS,GAAG,KAAK;IACxBD,MAAM,CAACG,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO,CAAC,CAAC;EAC1C;AACF;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACvC,MAAMC,SAAS,GAAG/B,gBAAgB,CAAC,CAAC;EACpC6B,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,MAAM;EAC3B,IAAIA,MAAM,GAAGJ,KAAK,CAACK,YAAY;EAC/B,IAAIrC,QAAQ,CAACiC,QAAQ,CAAC,EAAE;IACtB,MAAM;MAAEK,SAAS;MAAEC;IAAU,CAAC,GAAGN,QAAQ;IACzC,IAAIK,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBF,MAAM,GAAGI,IAAI,CAACC,GAAG,CAACL,MAAM,EAAEE,SAAS,CAAC;IACtC;IACA,IAAIC,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBH,MAAM,GAAGI,IAAI,CAACE,GAAG,CAACN,MAAM,EAAEG,SAAS,CAAC;IACtC;EACF;EACA,IAAIH,MAAM,EAAE;IACVJ,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,GAAGA,MAAM,IAAI;IAClChC,gBAAgB,CAAC8B,SAAS,CAAC;EAC7B;AACF;AACA,SAASS,YAAYA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACrC,IAAID,IAAI,KAAK,QAAQ,EAAE;IACrBA,IAAI,GAAG,MAAM;IACbC,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGA,SAAS,GAAG,SAAS;EACvD;EACA,IAAID,IAAI,KAAK,OAAO,EAAE;IACpBA,IAAI,GAAG,KAAK;IACZC,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGA,SAAS,GAAG,SAAS;EACvD;EACA,OAAO;IAAED,IAAI;IAAEC;EAAU,CAAC;AAC5B;AACA,SAASC,eAAeA,CAACC,GAAG,EAAE;EAC5B,OAAO,CAAC,GAAGA,GAAG,CAAC,CAACtC,MAAM;AACxB;AACA,SAASuC,SAASA,CAACD,GAAG,EAAEE,SAAS,EAAE;EACjC,OAAO,CAAC,GAAGF,GAAG,CAAC,CAACG,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AAC9C;AACA,SACEH,SAAS,EACTpB,YAAY,EACZL,cAAc,EACduB,eAAe,EACfzC,YAAY,EACZsC,YAAY,EACZZ,cAAc,EACdd,gBAAgB,EAChBP,WAAW,EACXe,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}