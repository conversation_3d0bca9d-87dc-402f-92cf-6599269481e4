{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ContactCard from \"./ContactCard.mjs\";\nconst ContactCard = withInstall(_ContactCard);\nvar stdin_default = ContactCard;\nimport { contactCardProps } from \"./ContactCard.mjs\";\nexport { ContactCard, contactCardProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_ContactCard", "ContactCard", "stdin_default", "contactCardProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/contact-card/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ContactCard from \"./ContactCard.mjs\";\nconst ContactCard = withInstall(_ContactCard);\nvar stdin_default = ContactCard;\nimport { contactCardProps } from \"./ContactCard.mjs\";\nexport {\n  ContactCard,\n  contactCardProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXE,gBAAgB,EAChBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}