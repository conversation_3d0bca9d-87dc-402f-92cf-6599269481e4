{"ast": null, "code": "import Lazy from \"./lazy.mjs\";\nimport LazyComponent from \"./lazy-component.mjs\";\nimport <PERSON><PERSON><PERSON>ontaine<PERSON> from \"./lazy-container.mjs\";\nimport LazyImage from \"./lazy-image.mjs\";\nconst Lazyload = {\n  /*\n   * install function\n   * @param  {App} app\n   * @param  {object} options lazyload options\n   */\n  install(app, options = {}) {\n    const LazyClass = Lazy();\n    const lazy = new LazyClass(options);\n    const lazyContainer = new LazyContainer({\n      lazy\n    });\n    app.config.globalProperties.$Lazyload = lazy;\n    if (options.lazyComponent) {\n      app.component(\"LazyComponent\", LazyComponent(lazy));\n    }\n    if (options.lazyImage) {\n      app.component(\"LazyImage\", LazyImage(lazy));\n    }\n    app.directive(\"lazy\", {\n      beforeMount: lazy.add.bind(lazy),\n      updated: lazy.update.bind(lazy),\n      unmounted: lazy.remove.bind(lazy)\n    });\n    app.directive(\"lazy-container\", {\n      beforeMount: lazyContainer.bind.bind(lazyContainer),\n      updated: lazyContainer.update.bind(lazyContainer),\n      unmounted: lazyContainer.unbind.bind(lazyContainer)\n    });\n  }\n};\nexport { Lazyload };", "map": {"version": 3, "names": ["Lazy", "LazyComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LazyImage", "Lazyload", "install", "app", "options", "LazyClass", "lazy", "<PERSON><PERSON><PERSON><PERSON>", "config", "globalProperties", "$Lazyload", "lazyComponent", "component", "lazyImage", "directive", "beforeMount", "add", "bind", "updated", "update", "unmounted", "remove", "unbind"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/lazyload/vue-lazyload/index.mjs"], "sourcesContent": ["import Lazy from \"./lazy.mjs\";\nimport LazyComponent from \"./lazy-component.mjs\";\nimport <PERSON><PERSON><PERSON>ontaine<PERSON> from \"./lazy-container.mjs\";\nimport LazyImage from \"./lazy-image.mjs\";\nconst Lazyload = {\n  /*\n   * install function\n   * @param  {App} app\n   * @param  {object} options lazyload options\n   */\n  install(app, options = {}) {\n    const LazyClass = Lazy();\n    const lazy = new LazyClass(options);\n    const lazyContainer = new LazyContainer({ lazy });\n    app.config.globalProperties.$Lazyload = lazy;\n    if (options.lazyComponent) {\n      app.component(\"LazyComponent\", LazyComponent(lazy));\n    }\n    if (options.lazyImage) {\n      app.component(\"LazyImage\", LazyImage(lazy));\n    }\n    app.directive(\"lazy\", {\n      beforeMount: lazy.add.bind(lazy),\n      updated: lazy.update.bind(lazy),\n      unmounted: lazy.remove.bind(lazy)\n    });\n    app.directive(\"lazy-container\", {\n      beforeMount: lazyContainer.bind.bind(lazyContainer),\n      updated: lazyContainer.update.bind(lazyContainer),\n      unmounted: lazyContainer.unbind.bind(lazyContainer)\n    });\n  }\n};\nexport {\n  Lazyload\n};\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,MAAMC,QAAQ,GAAG;EACf;AACF;AACA;AACA;AACA;EACEC,OAAOA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzB,MAAMC,SAAS,GAAGR,IAAI,CAAC,CAAC;IACxB,MAAMS,IAAI,GAAG,IAAID,SAAS,CAACD,OAAO,CAAC;IACnC,MAAMG,aAAa,GAAG,IAAIR,aAAa,CAAC;MAAEO;IAAK,CAAC,CAAC;IACjDH,GAAG,CAACK,MAAM,CAACC,gBAAgB,CAACC,SAAS,GAAGJ,IAAI;IAC5C,IAAIF,OAAO,CAACO,aAAa,EAAE;MACzBR,GAAG,CAACS,SAAS,CAAC,eAAe,EAAEd,aAAa,CAACQ,IAAI,CAAC,CAAC;IACrD;IACA,IAAIF,OAAO,CAACS,SAAS,EAAE;MACrBV,GAAG,CAACS,SAAS,CAAC,WAAW,EAAEZ,SAAS,CAACM,IAAI,CAAC,CAAC;IAC7C;IACAH,GAAG,CAACW,SAAS,CAAC,MAAM,EAAE;MACpBC,WAAW,EAAET,IAAI,CAACU,GAAG,CAACC,IAAI,CAACX,IAAI,CAAC;MAChCY,OAAO,EAAEZ,IAAI,CAACa,MAAM,CAACF,IAAI,CAACX,IAAI,CAAC;MAC/Bc,SAAS,EAAEd,IAAI,CAACe,MAAM,CAACJ,IAAI,CAACX,IAAI;IAClC,CAAC,CAAC;IACFH,GAAG,CAACW,SAAS,CAAC,gBAAgB,EAAE;MAC9BC,WAAW,EAAER,aAAa,CAACU,IAAI,CAACA,IAAI,CAACV,aAAa,CAAC;MACnDW,OAAO,EAAEX,aAAa,CAACY,MAAM,CAACF,IAAI,CAACV,aAAa,CAAC;MACjDa,SAAS,EAAEb,aAAa,CAACe,MAAM,CAACL,IAAI,CAACV,aAAa;IACpD,CAAC,CAAC;EACJ;AACF,CAAC;AACD,SACEN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}