{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Col from \"./Col.mjs\";\nconst Col = withInstall(_Col);\nvar stdin_default = Col;\nimport { colProps } from \"./Col.mjs\";\nexport { Col, colProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Col", "Col", "stdin_default", "colProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/col/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Col from \"./Col.mjs\";\nconst Col = withInstall(_Col);\nvar stdin_default = Col;\nimport { colProps } from \"./Col.mjs\";\nexport {\n  Col,\n  colProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,IAAI,MAAM,WAAW;AAC5B,MAAMC,GAAG,GAAGF,WAAW,CAACC,IAAI,CAAC;AAC7B,IAAIE,aAAa,GAAGD,GAAG;AACvB,SAASE,QAAQ,QAAQ,WAAW;AACpC,SACEF,GAAG,EACHE,QAAQ,EACRD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}