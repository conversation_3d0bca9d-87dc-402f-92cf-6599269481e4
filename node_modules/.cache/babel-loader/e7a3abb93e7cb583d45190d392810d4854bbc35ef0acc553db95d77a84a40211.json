{"ast": null, "code": "import { inBrowser } from \"../utils/index.mjs\";\nimport { onDeactivated, onBeforeUnmount } from \"vue\";\nimport { onMountedOrActivated } from \"@vant/use\";\nfunction useVisibilityChange(target, onChange) {\n  if (!inBrowser || !window.IntersectionObserver) {\n    return;\n  }\n  const observer = new IntersectionObserver(entries => {\n    onChange(entries[0].intersectionRatio > 0);\n  }, {\n    root: document.body\n  });\n  const observe = () => {\n    if (target.value) {\n      observer.observe(target.value);\n    }\n  };\n  const unobserve = () => {\n    if (target.value) {\n      observer.unobserve(target.value);\n    }\n  };\n  onDeactivated(unobserve);\n  onBeforeUnmount(unobserve);\n  onMountedOrActivated(observe);\n}\nexport { useVisibilityChange };", "map": {"version": 3, "names": ["inBrowser", "onDeactivated", "onBeforeUnmount", "onMountedOrActivated", "useVisibilityChange", "target", "onChange", "window", "IntersectionObserver", "observer", "entries", "intersectionRatio", "root", "document", "body", "observe", "value", "unobserve"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/composables/use-visibility-change.mjs"], "sourcesContent": ["import { inBrowser } from \"../utils/index.mjs\";\nimport { onDeactivated, onBeforeUnmount } from \"vue\";\nimport { onMountedOrActivated } from \"@vant/use\";\nfunction useVisibilityChange(target, onChange) {\n  if (!inBrowser || !window.IntersectionObserver) {\n    return;\n  }\n  const observer = new IntersectionObserver(\n    (entries) => {\n      onChange(entries[0].intersectionRatio > 0);\n    },\n    { root: document.body }\n  );\n  const observe = () => {\n    if (target.value) {\n      observer.observe(target.value);\n    }\n  };\n  const unobserve = () => {\n    if (target.value) {\n      observer.unobserve(target.value);\n    }\n  };\n  onDeactivated(unobserve);\n  onBeforeUnmount(unobserve);\n  onMountedOrActivated(observe);\n}\nexport {\n  useVisibilityChange\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,aAAa,EAAEC,eAAe,QAAQ,KAAK;AACpD,SAASC,oBAAoB,QAAQ,WAAW;AAChD,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC7C,IAAI,CAACN,SAAS,IAAI,CAACO,MAAM,CAACC,oBAAoB,EAAE;IAC9C;EACF;EACA,MAAMC,QAAQ,GAAG,IAAID,oBAAoB,CACtCE,OAAO,IAAK;IACXJ,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,iBAAiB,GAAG,CAAC,CAAC;EAC5C,CAAC,EACD;IAAEC,IAAI,EAAEC,QAAQ,CAACC;EAAK,CACxB,CAAC;EACD,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIV,MAAM,CAACW,KAAK,EAAE;MAChBP,QAAQ,CAACM,OAAO,CAACV,MAAM,CAACW,KAAK,CAAC;IAChC;EACF,CAAC;EACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIZ,MAAM,CAACW,KAAK,EAAE;MAChBP,QAAQ,CAACQ,SAAS,CAACZ,MAAM,CAACW,KAAK,CAAC;IAClC;EACF,CAAC;EACDf,aAAa,CAACgB,SAAS,CAAC;EACxBf,eAAe,CAACe,SAAS,CAAC;EAC1Bd,oBAAoB,CAACY,OAAO,CAAC;AAC/B;AACA,SACEX,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}