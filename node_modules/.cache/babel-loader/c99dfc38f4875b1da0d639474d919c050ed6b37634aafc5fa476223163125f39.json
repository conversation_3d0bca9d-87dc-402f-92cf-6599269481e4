{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _List from \"./List.mjs\";\nconst List = withInstall(_List);\nvar stdin_default = List;\nimport { listProps } from \"./List.mjs\";\nexport { List, stdin_default as default, listProps };", "map": {"version": 3, "names": ["withInstall", "_List", "List", "stdin_default", "listProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/list/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _List from \"./List.mjs\";\nconst List = withInstall(_List);\nvar stdin_default = List;\nimport { listProps } from \"./List.mjs\";\nexport {\n  List,\n  stdin_default as default,\n  listProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SAASE,SAAS,QAAQ,YAAY;AACtC,SACEF,IAAI,EACJC,aAAa,IAAIE,OAAO,EACxBD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}