{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isDef, isObject } from \"./basic.mjs\";\nconst {\n  hasOwnProperty\n} = Object.prototype;\nfunction assignKey(to, from, key) {\n  const val = from[key];\n  if (!isDef(val)) {\n    return;\n  }\n  if (!hasOwnProperty.call(to, key) || !isObject(val)) {\n    to[key] = val;\n  } else {\n    to[key] = deepAssign(Object(to[key]), val);\n  }\n}\nfunction deepAssign(to, from) {\n  Object.keys(from).forEach(key => {\n    assignKey(to, from, key);\n  });\n  return to;\n}\nexport { deepAssign };", "map": {"version": 3, "names": ["isDef", "isObject", "hasOwnProperty", "Object", "prototype", "<PERSON><PERSON><PERSON>", "to", "from", "key", "val", "call", "deepAssign", "keys", "for<PERSON>ach"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/utils/deep-assign.mjs"], "sourcesContent": ["import { isDef, isObject } from \"./basic.mjs\";\nconst { hasOwnProperty } = Object.prototype;\nfunction assignKey(to, from, key) {\n  const val = from[key];\n  if (!isDef(val)) {\n    return;\n  }\n  if (!hasOwnProperty.call(to, key) || !isObject(val)) {\n    to[key] = val;\n  } else {\n    to[key] = deepAssign(Object(to[key]), val);\n  }\n}\nfunction deepAssign(to, from) {\n  Object.keys(from).forEach((key) => {\n    assignKey(to, from, key);\n  });\n  return to;\n}\nexport {\n  deepAssign\n};\n"], "mappings": ";;AAAA,SAASA,KAAK,EAAEC,QAAQ,QAAQ,aAAa;AAC7C,MAAM;EAAEC;AAAe,CAAC,GAAGC,MAAM,CAACC,SAAS;AAC3C,SAASC,SAASA,CAACC,EAAE,EAAEC,IAAI,EAAEC,GAAG,EAAE;EAChC,MAAMC,GAAG,GAAGF,IAAI,CAACC,GAAG,CAAC;EACrB,IAAI,CAACR,KAAK,CAACS,GAAG,CAAC,EAAE;IACf;EACF;EACA,IAAI,CAACP,cAAc,CAACQ,IAAI,CAACJ,EAAE,EAAEE,GAAG,CAAC,IAAI,CAACP,QAAQ,CAACQ,GAAG,CAAC,EAAE;IACnDH,EAAE,CAACE,GAAG,CAAC,GAAGC,GAAG;EACf,CAAC,MAAM;IACLH,EAAE,CAACE,GAAG,CAAC,GAAGG,UAAU,CAACR,MAAM,CAACG,EAAE,CAACE,GAAG,CAAC,CAAC,EAAEC,GAAG,CAAC;EAC5C;AACF;AACA,SAASE,UAAUA,CAACL,EAAE,EAAEC,IAAI,EAAE;EAC5BJ,MAAM,CAACS,IAAI,CAACL,IAAI,CAAC,CAACM,OAAO,CAAEL,GAAG,IAAK;IACjCH,SAAS,CAACC,EAAE,EAAEC,IAAI,EAAEC,GAAG,CAAC;EAC1B,CAAC,CAAC;EACF,OAAOF,EAAE;AACX;AACA,SACEK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}