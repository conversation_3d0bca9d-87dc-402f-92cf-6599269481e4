{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Cascader from \"./Cascader.mjs\";\nconst Cascader = withInstall(_Cascader);\nvar stdin_default = Cascader;\nimport { cascaderProps } from \"./Cascader.mjs\";\nexport { Cascader, cascaderProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Cascader", "<PERSON>r", "stdin_default", "cascaderProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/cascader/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Cascader from \"./Cascader.mjs\";\nconst Cascader = withInstall(_Cascader);\nvar stdin_default = Cascader;\nimport { cascaderProps } from \"./Cascader.mjs\";\nexport {\n  Cascader,\n  cascaderProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SAASE,aAAa,QAAQ,gBAAgB;AAC9C,SACEF,QAAQ,EACRE,aAAa,EACbD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}