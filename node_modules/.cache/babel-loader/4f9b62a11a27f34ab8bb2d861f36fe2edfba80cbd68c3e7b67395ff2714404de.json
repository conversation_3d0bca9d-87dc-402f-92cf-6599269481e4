{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Badge from \"./Badge.mjs\";\nconst Badge = withInstall(_Badge);\nvar stdin_default = Badge;\nimport { badgeProps } from \"./Badge.mjs\";\nexport { Badge, badgeProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Badge", "Badge", "stdin_default", "badgeProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/badge/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Badge from \"./Badge.mjs\";\nconst Badge = withInstall(_Badge);\nvar stdin_default = Badge;\nimport { badgeProps } from \"./Badge.mjs\";\nexport {\n  Badge,\n  badgeProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SAASE,UAAU,QAAQ,aAAa;AACxC,SACEF,KAAK,EACLE,UAAU,EACVD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}