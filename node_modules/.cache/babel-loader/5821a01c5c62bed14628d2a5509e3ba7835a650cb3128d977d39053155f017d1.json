{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { isDef, clamp, extend, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem, t] = createNamespace(\"picker\");\nconst getFirstEnabledOption = options => options.find(option => !option.disabled) || options[0];\nfunction getColumnsType(columns, fields) {\n  const firstColumn = columns[0];\n  if (firstColumn) {\n    if (Array.isArray(firstColumn)) {\n      return \"multiple\";\n    }\n    if (fields.children in firstColumn) {\n      return \"cascade\";\n    }\n  }\n  return \"default\";\n}\nfunction findIndexOfEnabledOption(options, index) {\n  index = clamp(index, 0, options.length);\n  for (let i = index; i < options.length; i++) {\n    if (!options[i].disabled) return i;\n  }\n  for (let i = index - 1; i >= 0; i--) {\n    if (!options[i].disabled) return i;\n  }\n  return 0;\n}\nconst isOptionExist = (options, value, fields) => value !== void 0 && options.some(option => option[fields.value] === value);\nfunction findOptionByValue(options, value, fields) {\n  const index = options.findIndex(option => option[fields.value] === value);\n  const enabledIndex = findIndexOfEnabledOption(options, index);\n  return options[enabledIndex];\n}\nfunction formatCascadeColumns(columns, fields, selectedValues) {\n  const formatted = [];\n  let cursor = {\n    [fields.children]: columns\n  };\n  let columnIndex = 0;\n  while (cursor && cursor[fields.children]) {\n    const options = cursor[fields.children];\n    const value = selectedValues.value[columnIndex];\n    cursor = isDef(value) ? findOptionByValue(options, value, fields) : void 0;\n    if (!cursor && options.length) {\n      const firstValue = getFirstEnabledOption(options)[fields.value];\n      cursor = findOptionByValue(options, firstValue, fields);\n    }\n    columnIndex++;\n    formatted.push(options);\n  }\n  return formatted;\n}\nfunction getElementTranslateY(element) {\n  const {\n    transform\n  } = window.getComputedStyle(element);\n  const translateY = transform.slice(7, transform.length - 1).split(\", \")[5];\n  return Number(translateY);\n}\nfunction assignDefaultFields(fields) {\n  return extend({\n    text: \"text\",\n    value: \"value\",\n    children: \"children\"\n  }, fields);\n}\nexport { assignDefaultFields, bem, findIndexOfEnabledOption, findOptionByValue, formatCascadeColumns, getColumnsType, getElementTranslateY, getFirstEnabledOption, isOptionExist, name, t };", "map": {"version": 3, "names": ["isDef", "clamp", "extend", "createNamespace", "name", "bem", "t", "getFirstEnabledOption", "options", "find", "option", "disabled", "getColumnsType", "columns", "fields", "firstColumn", "Array", "isArray", "children", "findIndexOfEnabledOption", "index", "length", "i", "isOptionExist", "value", "some", "findOptionByValue", "findIndex", "enabledIndex", "formatCascadeColumns", "<PERSON><PERSON><PERSON><PERSON>", "formatted", "cursor", "columnIndex", "firstValue", "push", "getElementTranslateY", "element", "transform", "window", "getComputedStyle", "translateY", "slice", "split", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/picker/utils.mjs"], "sourcesContent": ["import { isDef, clamp, extend, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem, t] = createNamespace(\"picker\");\nconst getFirstEnabledOption = (options) => options.find((option) => !option.disabled) || options[0];\nfunction getColumnsType(columns, fields) {\n  const firstColumn = columns[0];\n  if (firstColumn) {\n    if (Array.isArray(firstColumn)) {\n      return \"multiple\";\n    }\n    if (fields.children in firstColumn) {\n      return \"cascade\";\n    }\n  }\n  return \"default\";\n}\nfunction findIndexOfEnabledOption(options, index) {\n  index = clamp(index, 0, options.length);\n  for (let i = index; i < options.length; i++) {\n    if (!options[i].disabled) return i;\n  }\n  for (let i = index - 1; i >= 0; i--) {\n    if (!options[i].disabled) return i;\n  }\n  return 0;\n}\nconst isOptionExist = (options, value, fields) => value !== void 0 && options.some((option) => option[fields.value] === value);\nfunction findOptionByValue(options, value, fields) {\n  const index = options.findIndex((option) => option[fields.value] === value);\n  const enabledIndex = findIndexOfEnabledOption(options, index);\n  return options[enabledIndex];\n}\nfunction formatCascadeColumns(columns, fields, selectedValues) {\n  const formatted = [];\n  let cursor = {\n    [fields.children]: columns\n  };\n  let columnIndex = 0;\n  while (cursor && cursor[fields.children]) {\n    const options = cursor[fields.children];\n    const value = selectedValues.value[columnIndex];\n    cursor = isDef(value) ? findOptionByValue(options, value, fields) : void 0;\n    if (!cursor && options.length) {\n      const firstValue = getFirstEnabledOption(options)[fields.value];\n      cursor = findOptionByValue(options, firstValue, fields);\n    }\n    columnIndex++;\n    formatted.push(options);\n  }\n  return formatted;\n}\nfunction getElementTranslateY(element) {\n  const { transform } = window.getComputedStyle(element);\n  const translateY = transform.slice(7, transform.length - 1).split(\", \")[5];\n  return Number(translateY);\n}\nfunction assignDefaultFields(fields) {\n  return extend(\n    {\n      text: \"text\",\n      value: \"value\",\n      children: \"children\"\n    },\n    fields\n  );\n}\nexport {\n  assignDefaultFields,\n  bem,\n  findIndexOfEnabledOption,\n  findOptionByValue,\n  formatCascadeColumns,\n  getColumnsType,\n  getElementTranslateY,\n  getFirstEnabledOption,\n  isOptionExist,\n  name,\n  t\n};\n"], "mappings": ";;;;AAAA,SAASA,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,QAAQ,oBAAoB;AAC1E,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGH,eAAe,CAAC,QAAQ,CAAC;AAChD,MAAMI,qBAAqB,GAAIC,OAAO,IAAKA,OAAO,CAACC,IAAI,CAAEC,MAAM,IAAK,CAACA,MAAM,CAACC,QAAQ,CAAC,IAAIH,OAAO,CAAC,CAAC,CAAC;AACnG,SAASI,cAAcA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACvC,MAAMC,WAAW,GAAGF,OAAO,CAAC,CAAC,CAAC;EAC9B,IAAIE,WAAW,EAAE;IACf,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;MAC9B,OAAO,UAAU;IACnB;IACA,IAAID,MAAM,CAACI,QAAQ,IAAIH,WAAW,EAAE;MAClC,OAAO,SAAS;IAClB;EACF;EACA,OAAO,SAAS;AAClB;AACA,SAASI,wBAAwBA,CAACX,OAAO,EAAEY,KAAK,EAAE;EAChDA,KAAK,GAAGnB,KAAK,CAACmB,KAAK,EAAE,CAAC,EAAEZ,OAAO,CAACa,MAAM,CAAC;EACvC,KAAK,IAAIC,CAAC,GAAGF,KAAK,EAAEE,CAAC,GAAGd,OAAO,CAACa,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC3C,IAAI,CAACd,OAAO,CAACc,CAAC,CAAC,CAACX,QAAQ,EAAE,OAAOW,CAAC;EACpC;EACA,KAAK,IAAIA,CAAC,GAAGF,KAAK,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnC,IAAI,CAACd,OAAO,CAACc,CAAC,CAAC,CAACX,QAAQ,EAAE,OAAOW,CAAC;EACpC;EACA,OAAO,CAAC;AACV;AACA,MAAMC,aAAa,GAAGA,CAACf,OAAO,EAAEgB,KAAK,EAAEV,MAAM,KAAKU,KAAK,KAAK,KAAK,CAAC,IAAIhB,OAAO,CAACiB,IAAI,CAAEf,MAAM,IAAKA,MAAM,CAACI,MAAM,CAACU,KAAK,CAAC,KAAKA,KAAK,CAAC;AAC9H,SAASE,iBAAiBA,CAAClB,OAAO,EAAEgB,KAAK,EAAEV,MAAM,EAAE;EACjD,MAAMM,KAAK,GAAGZ,OAAO,CAACmB,SAAS,CAAEjB,MAAM,IAAKA,MAAM,CAACI,MAAM,CAACU,KAAK,CAAC,KAAKA,KAAK,CAAC;EAC3E,MAAMI,YAAY,GAAGT,wBAAwB,CAACX,OAAO,EAAEY,KAAK,CAAC;EAC7D,OAAOZ,OAAO,CAACoB,YAAY,CAAC;AAC9B;AACA,SAASC,oBAAoBA,CAAChB,OAAO,EAAEC,MAAM,EAAEgB,cAAc,EAAE;EAC7D,MAAMC,SAAS,GAAG,EAAE;EACpB,IAAIC,MAAM,GAAG;IACX,CAAClB,MAAM,CAACI,QAAQ,GAAGL;EACrB,CAAC;EACD,IAAIoB,WAAW,GAAG,CAAC;EACnB,OAAOD,MAAM,IAAIA,MAAM,CAAClB,MAAM,CAACI,QAAQ,CAAC,EAAE;IACxC,MAAMV,OAAO,GAAGwB,MAAM,CAAClB,MAAM,CAACI,QAAQ,CAAC;IACvC,MAAMM,KAAK,GAAGM,cAAc,CAACN,KAAK,CAACS,WAAW,CAAC;IAC/CD,MAAM,GAAGhC,KAAK,CAACwB,KAAK,CAAC,GAAGE,iBAAiB,CAAClB,OAAO,EAAEgB,KAAK,EAAEV,MAAM,CAAC,GAAG,KAAK,CAAC;IAC1E,IAAI,CAACkB,MAAM,IAAIxB,OAAO,CAACa,MAAM,EAAE;MAC7B,MAAMa,UAAU,GAAG3B,qBAAqB,CAACC,OAAO,CAAC,CAACM,MAAM,CAACU,KAAK,CAAC;MAC/DQ,MAAM,GAAGN,iBAAiB,CAAClB,OAAO,EAAE0B,UAAU,EAAEpB,MAAM,CAAC;IACzD;IACAmB,WAAW,EAAE;IACbF,SAAS,CAACI,IAAI,CAAC3B,OAAO,CAAC;EACzB;EACA,OAAOuB,SAAS;AAClB;AACA,SAASK,oBAAoBA,CAACC,OAAO,EAAE;EACrC,MAAM;IAAEC;EAAU,CAAC,GAAGC,MAAM,CAACC,gBAAgB,CAACH,OAAO,CAAC;EACtD,MAAMI,UAAU,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,EAAEJ,SAAS,CAACjB,MAAM,GAAG,CAAC,CAAC,CAACsB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1E,OAAOC,MAAM,CAACH,UAAU,CAAC;AAC3B;AACA,SAASI,mBAAmBA,CAAC/B,MAAM,EAAE;EACnC,OAAOZ,MAAM,CACX;IACE4C,IAAI,EAAE,MAAM;IACZtB,KAAK,EAAE,OAAO;IACdN,QAAQ,EAAE;EACZ,CAAC,EACDJ,MACF,CAAC;AACH;AACA,SACE+B,mBAAmB,EACnBxC,GAAG,EACHc,wBAAwB,EACxBO,iBAAiB,EACjBG,oBAAoB,EACpBjB,cAAc,EACdwB,oBAAoB,EACpB7B,qBAAqB,EACrBgB,aAAa,EACbnB,IAAI,EACJE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}