{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _RadioGroup from \"./RadioGroup.mjs\";\nconst RadioGroup = withInstall(_RadioGroup);\nvar stdin_default = RadioGroup;\nimport { radioGroupProps } from \"./RadioGroup.mjs\";\nexport { RadioGroup, stdin_default as default, radioGroupProps };", "map": {"version": 3, "names": ["withInstall", "_RadioGroup", "RadioGroup", "stdin_default", "radioGroupProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/radio-group/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _RadioGroup from \"./RadioGroup.mjs\";\nconst RadioGroup = withInstall(_RadioGroup);\nvar stdin_default = RadioGroup;\nimport { radioGroupProps } from \"./RadioGroup.mjs\";\nexport {\n  RadioGroup,\n  stdin_default as default,\n  radioGroupProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVC,aAAa,IAAIE,OAAO,EACxBD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}