{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, watch, computed, reactive, nextTick, onActivated, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, addUnit, isHidden, unitToPx, truthProp, numericProp, windowWidth, getElementTop, makeStringProp, callInterceptor, createNamespace, makeNumericProp, setRootScrollTop, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { scrollLeftTo, scrollTopTo } from \"./utils.mjs\";\nimport { useRect, useChildren, useScrollParent, useEventListener, onMountedOrActivated } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { route } from \"../composables/use-route.mjs\";\nimport { useRefs } from \"../composables/use-refs.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReopen } from \"../composables/on-popup-reopen.mjs\";\nimport { useVisibilityChange } from \"../composables/use-visibility-change.mjs\";\nimport { Sticky } from \"../sticky/index.mjs\";\nimport TabsContent from \"./TabsContent.mjs\";\nconst [name, bem] = createNamespace(\"tabs\");\nconst tabsProps = {\n  type: makeStringProp(\"line\"),\n  color: String,\n  border: Boolean,\n  sticky: Boolean,\n  shrink: Boolean,\n  active: makeNumericProp(0),\n  duration: makeNumericProp(0.3),\n  animated: Boolean,\n  ellipsis: truthProp,\n  swipeable: Boolean,\n  scrollspy: Boolean,\n  offsetTop: makeNumericProp(0),\n  background: String,\n  lazyRender: truthProp,\n  showHeader: truthProp,\n  lineWidth: numericProp,\n  lineHeight: numericProp,\n  beforeChange: Function,\n  swipeThreshold: makeNumericProp(5),\n  titleActiveColor: String,\n  titleInactiveColor: String\n};\nconst TABS_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: tabsProps,\n  emits: [\"change\", \"scroll\", \"rendered\", \"clickTab\", \"update:active\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let tabHeight;\n    let lockScroll;\n    let stickyFixed;\n    let cancelScrollLeftToRaf;\n    let cancelScrollTopToRaf;\n    const root = ref();\n    const navRef = ref();\n    const wrapRef = ref();\n    const contentRef = ref();\n    const id = useId();\n    const scroller = useScrollParent(root);\n    const [titleRefs, setTitleRefs] = useRefs();\n    const {\n      children,\n      linkChildren\n    } = useChildren(TABS_KEY);\n    const state = reactive({\n      inited: false,\n      position: \"\",\n      lineStyle: {},\n      currentIndex: -1\n    });\n    const scrollable = computed(() => children.length > +props.swipeThreshold || !props.ellipsis || props.shrink);\n    const navStyle = computed(() => ({\n      borderColor: props.color,\n      background: props.background\n    }));\n    const getTabName = (tab, index) => {\n      var _a;\n      return (_a = tab.name) != null ? _a : index;\n    };\n    const currentName = computed(() => {\n      const activeTab = children[state.currentIndex];\n      if (activeTab) {\n        return getTabName(activeTab, state.currentIndex);\n      }\n    });\n    const offsetTopPx = computed(() => unitToPx(props.offsetTop));\n    const scrollOffset = computed(() => {\n      if (props.sticky) {\n        return offsetTopPx.value + tabHeight;\n      }\n      return 0;\n    });\n    const scrollIntoView = immediate => {\n      const nav = navRef.value;\n      const titles = titleRefs.value;\n      if (!scrollable.value || !nav || !titles || !titles[state.currentIndex]) {\n        return;\n      }\n      const title = titles[state.currentIndex].$el;\n      const to = title.offsetLeft - (nav.offsetWidth - title.offsetWidth) / 2;\n      if (cancelScrollLeftToRaf) cancelScrollLeftToRaf();\n      cancelScrollLeftToRaf = scrollLeftTo(nav, to, immediate ? 0 : +props.duration);\n    };\n    const setLine = () => {\n      const shouldAnimate = state.inited;\n      nextTick(() => {\n        const titles = titleRefs.value;\n        if (!titles || !titles[state.currentIndex] || props.type !== \"line\" || isHidden(root.value)) {\n          return;\n        }\n        const title = titles[state.currentIndex].$el;\n        const {\n          lineWidth,\n          lineHeight\n        } = props;\n        const left = title.offsetLeft + title.offsetWidth / 2;\n        const lineStyle = {\n          width: addUnit(lineWidth),\n          backgroundColor: props.color,\n          transform: `translateX(${left}px) translateX(-50%)`\n        };\n        if (shouldAnimate) {\n          lineStyle.transitionDuration = `${props.duration}s`;\n        }\n        if (isDef(lineHeight)) {\n          const height = addUnit(lineHeight);\n          lineStyle.height = height;\n          lineStyle.borderRadius = height;\n        }\n        state.lineStyle = lineStyle;\n      });\n    };\n    const findAvailableTab = index => {\n      const diff = index < state.currentIndex ? -1 : 1;\n      while (index >= 0 && index < children.length) {\n        if (!children[index].disabled) {\n          return index;\n        }\n        index += diff;\n      }\n    };\n    const setCurrentIndex = (currentIndex, skipScrollIntoView) => {\n      const newIndex = findAvailableTab(currentIndex);\n      if (!isDef(newIndex)) {\n        return;\n      }\n      const newTab = children[newIndex];\n      const newName = getTabName(newTab, newIndex);\n      const shouldEmitChange = state.currentIndex !== null;\n      if (state.currentIndex !== newIndex) {\n        state.currentIndex = newIndex;\n        if (!skipScrollIntoView) {\n          scrollIntoView();\n        }\n        setLine();\n      }\n      if (newName !== props.active) {\n        emit(\"update:active\", newName);\n        if (shouldEmitChange) {\n          emit(\"change\", newName, newTab.title);\n        }\n      }\n      if (stickyFixed && !props.scrollspy) {\n        setRootScrollTop(Math.ceil(getElementTop(root.value) - offsetTopPx.value));\n      }\n    };\n    const setCurrentIndexByName = (name2, skipScrollIntoView) => {\n      const index = children.findIndex((tab, index2) => getTabName(tab, index2) === name2);\n      setCurrentIndex(index === -1 ? 0 : index, skipScrollIntoView);\n    };\n    const scrollToCurrentContent = (immediate = false) => {\n      if (props.scrollspy) {\n        const target = children[state.currentIndex].$el;\n        if (target && scroller.value) {\n          const to = getElementTop(target, scroller.value) - scrollOffset.value;\n          lockScroll = true;\n          if (cancelScrollTopToRaf) cancelScrollTopToRaf();\n          cancelScrollTopToRaf = scrollTopTo(scroller.value, to, immediate ? 0 : +props.duration, () => {\n            lockScroll = false;\n          });\n        }\n      }\n    };\n    const onClickTab = (item, index, event) => {\n      const {\n        title,\n        disabled\n      } = children[index];\n      const name2 = getTabName(children[index], index);\n      if (!disabled) {\n        callInterceptor(props.beforeChange, {\n          args: [name2],\n          done: () => {\n            setCurrentIndex(index);\n            scrollToCurrentContent();\n          }\n        });\n        route(item);\n      }\n      emit(\"clickTab\", {\n        name: name2,\n        title,\n        event,\n        disabled\n      });\n    };\n    const onStickyScroll = params => {\n      stickyFixed = params.isFixed;\n      emit(\"scroll\", params);\n    };\n    const scrollTo = name2 => {\n      nextTick(() => {\n        setCurrentIndexByName(name2);\n        scrollToCurrentContent(true);\n      });\n    };\n    const getCurrentIndexOnScroll = () => {\n      for (let index = 0; index < children.length; index++) {\n        const {\n          top\n        } = useRect(children[index].$el);\n        if (top > scrollOffset.value) {\n          return index === 0 ? 0 : index - 1;\n        }\n      }\n      return children.length - 1;\n    };\n    const onScroll = () => {\n      if (props.scrollspy && !lockScroll) {\n        const index = getCurrentIndexOnScroll();\n        setCurrentIndex(index);\n      }\n    };\n    const renderLine = () => {\n      if (props.type === \"line\" && children.length) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"line\"),\n          \"style\": state.lineStyle\n        }, null);\n      }\n    };\n    const renderHeader = () => {\n      var _a, _b, _c;\n      const {\n        type,\n        border,\n        sticky\n      } = props;\n      const Header = [_createVNode(\"div\", {\n        \"ref\": sticky ? void 0 : wrapRef,\n        \"class\": [bem(\"wrap\"), {\n          [BORDER_TOP_BOTTOM]: type === \"line\" && border\n        }]\n      }, [_createVNode(\"div\", {\n        \"ref\": navRef,\n        \"role\": \"tablist\",\n        \"class\": bem(\"nav\", [type, {\n          shrink: props.shrink,\n          complete: scrollable.value\n        }]),\n        \"style\": navStyle.value,\n        \"aria-orientation\": \"horizontal\"\n      }, [(_a = slots[\"nav-left\"]) == null ? void 0 : _a.call(slots), children.map(item => item.renderTitle(onClickTab)), renderLine(), (_b = slots[\"nav-right\"]) == null ? void 0 : _b.call(slots)])]), (_c = slots[\"nav-bottom\"]) == null ? void 0 : _c.call(slots)];\n      if (sticky) {\n        return _createVNode(\"div\", {\n          \"ref\": wrapRef\n        }, [Header]);\n      }\n      return Header;\n    };\n    const resize = () => {\n      setLine();\n      nextTick(() => {\n        var _a, _b;\n        scrollIntoView(true);\n        (_b = (_a = contentRef.value) == null ? void 0 : _a.swipeRef.value) == null ? void 0 : _b.resize();\n      });\n    };\n    watch(() => [props.color, props.duration, props.lineWidth, props.lineHeight], setLine);\n    watch(windowWidth, resize);\n    watch(() => props.active, value => {\n      if (value !== currentName.value) {\n        setCurrentIndexByName(value);\n      }\n    });\n    watch(() => children.length, () => {\n      if (state.inited) {\n        setCurrentIndexByName(props.active);\n        setLine();\n        nextTick(() => {\n          scrollIntoView(true);\n        });\n      }\n    });\n    const init = () => {\n      setCurrentIndexByName(props.active, true);\n      nextTick(() => {\n        state.inited = true;\n        if (wrapRef.value) {\n          tabHeight = useRect(wrapRef.value).height;\n        }\n        scrollIntoView(true);\n      });\n    };\n    const onRendered = (name2, title) => emit(\"rendered\", name2, title);\n    useExpose({\n      resize,\n      scrollTo\n    });\n    onActivated(setLine);\n    onPopupReopen(setLine);\n    onMountedOrActivated(init);\n    useVisibilityChange(root, setLine);\n    useEventListener(\"scroll\", onScroll, {\n      target: scroller,\n      passive: true\n    });\n    linkChildren({\n      id,\n      props,\n      setLine,\n      scrollable,\n      onRendered,\n      currentName,\n      setTitleRefs,\n      scrollIntoView\n    });\n    return () => _createVNode(\"div\", {\n      \"ref\": root,\n      \"class\": bem([props.type])\n    }, [props.showHeader ? props.sticky ? _createVNode(Sticky, {\n      \"container\": root.value,\n      \"offsetTop\": offsetTopPx.value,\n      \"onScroll\": onStickyScroll\n    }, {\n      default: () => [renderHeader()]\n    }) : renderHeader() : null, _createVNode(TabsContent, {\n      \"ref\": contentRef,\n      \"count\": children.length,\n      \"inited\": state.inited,\n      \"animated\": props.animated,\n      \"duration\": props.duration,\n      \"swipeable\": props.swipeable,\n      \"lazyRender\": props.lazyRender,\n      \"currentIndex\": state.currentIndex,\n      \"onChange\": setCurrentIndex\n    }, {\n      default: () => {\n        var _a;\n        return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n      }\n    })]);\n  }\n});\nexport { TABS_KEY, stdin_default as default, tabsProps };", "map": {"version": 3, "names": ["ref", "watch", "computed", "reactive", "nextTick", "onActivated", "defineComponent", "createVNode", "_createVNode", "isDef", "addUnit", "isHidden", "unitToPx", "truthProp", "numericProp", "windowWidth", "getElementTop", "makeStringProp", "callInterceptor", "createNamespace", "makeNumericProp", "setRootScrollTop", "BORDER_TOP_BOTTOM", "scrollLeftTo", "scrollTopTo", "useRect", "useChildren", "useScrollParent", "useEventListener", "onMountedOrActivated", "useId", "route", "useRefs", "useExpose", "onPopupReopen", "useVisibilityChange", "<PERSON>y", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "bem", "tabsProps", "type", "color", "String", "border", "Boolean", "sticky", "shrink", "active", "duration", "animated", "ellipsis", "swipeable", "scrollspy", "offsetTop", "background", "lazy<PERSON>ender", "showHeader", "lineWidth", "lineHeight", "beforeChange", "Function", "swipe<PERSON><PERSON><PERSON><PERSON>", "titleActiveColor", "titleInactiveColor", "TABS_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "emit", "slots", "tabHeight", "lockScroll", "stickyFixed", "cancelScrollLeftToRaf", "cancelScrollTopToRaf", "root", "navRef", "wrapRef", "contentRef", "id", "scroller", "titleRefs", "setTitleRefs", "children", "linkChildren", "state", "inited", "position", "lineStyle", "currentIndex", "scrollable", "length", "navStyle", "borderColor", "getTabName", "tab", "index", "_a", "currentName", "activeTab", "offsetTopPx", "scrollOffset", "value", "scrollIntoView", "immediate", "nav", "titles", "title", "$el", "to", "offsetLeft", "offsetWidth", "setLine", "shouldAnimate", "left", "width", "backgroundColor", "transform", "transitionDuration", "height", "borderRadius", "findAvailableTab", "diff", "disabled", "setCurrentIndex", "skipScrollIntoView", "newIndex", "newTab", "newName", "shouldEmitChange", "Math", "ceil", "setCurrentIndexByName", "name2", "findIndex", "index2", "scrollToCurrentContent", "target", "onClickTab", "item", "event", "args", "done", "onStickyScroll", "params", "isFixed", "scrollTo", "getCurrentIndexOnScroll", "top", "onScroll", "renderLine", "renderHeader", "_b", "_c", "Header", "complete", "call", "map", "renderTitle", "resize", "swipeRef", "init", "onRendered", "passive", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tabs/Tabs.mjs"], "sourcesContent": ["import { ref, watch, computed, reactive, nextTick, onActivated, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, addUnit, isHidden, unitToPx, truthProp, numericProp, windowWidth, getElementTop, makeStringProp, callInterceptor, createNamespace, makeNumericProp, setRootScrollTop, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { scrollLeftTo, scrollTopTo } from \"./utils.mjs\";\nimport { useRect, useChildren, useScrollParent, useEventListener, onMountedOrActivated } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { route } from \"../composables/use-route.mjs\";\nimport { useRefs } from \"../composables/use-refs.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { onPopupReo<PERSON> } from \"../composables/on-popup-reopen.mjs\";\nimport { useVisibilityChange } from \"../composables/use-visibility-change.mjs\";\nimport { Sticky } from \"../sticky/index.mjs\";\nimport TabsContent from \"./TabsContent.mjs\";\nconst [name, bem] = createNamespace(\"tabs\");\nconst tabsProps = {\n  type: makeStringProp(\"line\"),\n  color: String,\n  border: Boolean,\n  sticky: Boolean,\n  shrink: Boolean,\n  active: makeNumericProp(0),\n  duration: makeNumericProp(0.3),\n  animated: Boolean,\n  ellipsis: truthProp,\n  swipeable: Boolean,\n  scrollspy: Boolean,\n  offsetTop: makeNumericProp(0),\n  background: String,\n  lazyRender: truthProp,\n  showHeader: truthProp,\n  lineWidth: numericProp,\n  lineHeight: numericProp,\n  beforeChange: Function,\n  swipeThreshold: makeNumericProp(5),\n  titleActiveColor: String,\n  titleInactiveColor: String\n};\nconst TABS_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: tabsProps,\n  emits: [\"change\", \"scroll\", \"rendered\", \"clickTab\", \"update:active\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let tabHeight;\n    let lockScroll;\n    let stickyFixed;\n    let cancelScrollLeftToRaf;\n    let cancelScrollTopToRaf;\n    const root = ref();\n    const navRef = ref();\n    const wrapRef = ref();\n    const contentRef = ref();\n    const id = useId();\n    const scroller = useScrollParent(root);\n    const [titleRefs, setTitleRefs] = useRefs();\n    const {\n      children,\n      linkChildren\n    } = useChildren(TABS_KEY);\n    const state = reactive({\n      inited: false,\n      position: \"\",\n      lineStyle: {},\n      currentIndex: -1\n    });\n    const scrollable = computed(() => children.length > +props.swipeThreshold || !props.ellipsis || props.shrink);\n    const navStyle = computed(() => ({\n      borderColor: props.color,\n      background: props.background\n    }));\n    const getTabName = (tab, index) => {\n      var _a;\n      return (_a = tab.name) != null ? _a : index;\n    };\n    const currentName = computed(() => {\n      const activeTab = children[state.currentIndex];\n      if (activeTab) {\n        return getTabName(activeTab, state.currentIndex);\n      }\n    });\n    const offsetTopPx = computed(() => unitToPx(props.offsetTop));\n    const scrollOffset = computed(() => {\n      if (props.sticky) {\n        return offsetTopPx.value + tabHeight;\n      }\n      return 0;\n    });\n    const scrollIntoView = (immediate) => {\n      const nav = navRef.value;\n      const titles = titleRefs.value;\n      if (!scrollable.value || !nav || !titles || !titles[state.currentIndex]) {\n        return;\n      }\n      const title = titles[state.currentIndex].$el;\n      const to = title.offsetLeft - (nav.offsetWidth - title.offsetWidth) / 2;\n      if (cancelScrollLeftToRaf) cancelScrollLeftToRaf();\n      cancelScrollLeftToRaf = scrollLeftTo(nav, to, immediate ? 0 : +props.duration);\n    };\n    const setLine = () => {\n      const shouldAnimate = state.inited;\n      nextTick(() => {\n        const titles = titleRefs.value;\n        if (!titles || !titles[state.currentIndex] || props.type !== \"line\" || isHidden(root.value)) {\n          return;\n        }\n        const title = titles[state.currentIndex].$el;\n        const {\n          lineWidth,\n          lineHeight\n        } = props;\n        const left = title.offsetLeft + title.offsetWidth / 2;\n        const lineStyle = {\n          width: addUnit(lineWidth),\n          backgroundColor: props.color,\n          transform: `translateX(${left}px) translateX(-50%)`\n        };\n        if (shouldAnimate) {\n          lineStyle.transitionDuration = `${props.duration}s`;\n        }\n        if (isDef(lineHeight)) {\n          const height = addUnit(lineHeight);\n          lineStyle.height = height;\n          lineStyle.borderRadius = height;\n        }\n        state.lineStyle = lineStyle;\n      });\n    };\n    const findAvailableTab = (index) => {\n      const diff = index < state.currentIndex ? -1 : 1;\n      while (index >= 0 && index < children.length) {\n        if (!children[index].disabled) {\n          return index;\n        }\n        index += diff;\n      }\n    };\n    const setCurrentIndex = (currentIndex, skipScrollIntoView) => {\n      const newIndex = findAvailableTab(currentIndex);\n      if (!isDef(newIndex)) {\n        return;\n      }\n      const newTab = children[newIndex];\n      const newName = getTabName(newTab, newIndex);\n      const shouldEmitChange = state.currentIndex !== null;\n      if (state.currentIndex !== newIndex) {\n        state.currentIndex = newIndex;\n        if (!skipScrollIntoView) {\n          scrollIntoView();\n        }\n        setLine();\n      }\n      if (newName !== props.active) {\n        emit(\"update:active\", newName);\n        if (shouldEmitChange) {\n          emit(\"change\", newName, newTab.title);\n        }\n      }\n      if (stickyFixed && !props.scrollspy) {\n        setRootScrollTop(Math.ceil(getElementTop(root.value) - offsetTopPx.value));\n      }\n    };\n    const setCurrentIndexByName = (name2, skipScrollIntoView) => {\n      const index = children.findIndex((tab, index2) => getTabName(tab, index2) === name2);\n      setCurrentIndex(index === -1 ? 0 : index, skipScrollIntoView);\n    };\n    const scrollToCurrentContent = (immediate = false) => {\n      if (props.scrollspy) {\n        const target = children[state.currentIndex].$el;\n        if (target && scroller.value) {\n          const to = getElementTop(target, scroller.value) - scrollOffset.value;\n          lockScroll = true;\n          if (cancelScrollTopToRaf) cancelScrollTopToRaf();\n          cancelScrollTopToRaf = scrollTopTo(scroller.value, to, immediate ? 0 : +props.duration, () => {\n            lockScroll = false;\n          });\n        }\n      }\n    };\n    const onClickTab = (item, index, event) => {\n      const {\n        title,\n        disabled\n      } = children[index];\n      const name2 = getTabName(children[index], index);\n      if (!disabled) {\n        callInterceptor(props.beforeChange, {\n          args: [name2],\n          done: () => {\n            setCurrentIndex(index);\n            scrollToCurrentContent();\n          }\n        });\n        route(item);\n      }\n      emit(\"clickTab\", {\n        name: name2,\n        title,\n        event,\n        disabled\n      });\n    };\n    const onStickyScroll = (params) => {\n      stickyFixed = params.isFixed;\n      emit(\"scroll\", params);\n    };\n    const scrollTo = (name2) => {\n      nextTick(() => {\n        setCurrentIndexByName(name2);\n        scrollToCurrentContent(true);\n      });\n    };\n    const getCurrentIndexOnScroll = () => {\n      for (let index = 0; index < children.length; index++) {\n        const {\n          top\n        } = useRect(children[index].$el);\n        if (top > scrollOffset.value) {\n          return index === 0 ? 0 : index - 1;\n        }\n      }\n      return children.length - 1;\n    };\n    const onScroll = () => {\n      if (props.scrollspy && !lockScroll) {\n        const index = getCurrentIndexOnScroll();\n        setCurrentIndex(index);\n      }\n    };\n    const renderLine = () => {\n      if (props.type === \"line\" && children.length) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"line\"),\n          \"style\": state.lineStyle\n        }, null);\n      }\n    };\n    const renderHeader = () => {\n      var _a, _b, _c;\n      const {\n        type,\n        border,\n        sticky\n      } = props;\n      const Header = [_createVNode(\"div\", {\n        \"ref\": sticky ? void 0 : wrapRef,\n        \"class\": [bem(\"wrap\"), {\n          [BORDER_TOP_BOTTOM]: type === \"line\" && border\n        }]\n      }, [_createVNode(\"div\", {\n        \"ref\": navRef,\n        \"role\": \"tablist\",\n        \"class\": bem(\"nav\", [type, {\n          shrink: props.shrink,\n          complete: scrollable.value\n        }]),\n        \"style\": navStyle.value,\n        \"aria-orientation\": \"horizontal\"\n      }, [(_a = slots[\"nav-left\"]) == null ? void 0 : _a.call(slots), children.map((item) => item.renderTitle(onClickTab)), renderLine(), (_b = slots[\"nav-right\"]) == null ? void 0 : _b.call(slots)])]), (_c = slots[\"nav-bottom\"]) == null ? void 0 : _c.call(slots)];\n      if (sticky) {\n        return _createVNode(\"div\", {\n          \"ref\": wrapRef\n        }, [Header]);\n      }\n      return Header;\n    };\n    const resize = () => {\n      setLine();\n      nextTick(() => {\n        var _a, _b;\n        scrollIntoView(true);\n        (_b = (_a = contentRef.value) == null ? void 0 : _a.swipeRef.value) == null ? void 0 : _b.resize();\n      });\n    };\n    watch(() => [props.color, props.duration, props.lineWidth, props.lineHeight], setLine);\n    watch(windowWidth, resize);\n    watch(() => props.active, (value) => {\n      if (value !== currentName.value) {\n        setCurrentIndexByName(value);\n      }\n    });\n    watch(() => children.length, () => {\n      if (state.inited) {\n        setCurrentIndexByName(props.active);\n        setLine();\n        nextTick(() => {\n          scrollIntoView(true);\n        });\n      }\n    });\n    const init = () => {\n      setCurrentIndexByName(props.active, true);\n      nextTick(() => {\n        state.inited = true;\n        if (wrapRef.value) {\n          tabHeight = useRect(wrapRef.value).height;\n        }\n        scrollIntoView(true);\n      });\n    };\n    const onRendered = (name2, title) => emit(\"rendered\", name2, title);\n    useExpose({\n      resize,\n      scrollTo\n    });\n    onActivated(setLine);\n    onPopupReopen(setLine);\n    onMountedOrActivated(init);\n    useVisibilityChange(root, setLine);\n    useEventListener(\"scroll\", onScroll, {\n      target: scroller,\n      passive: true\n    });\n    linkChildren({\n      id,\n      props,\n      setLine,\n      scrollable,\n      onRendered,\n      currentName,\n      setTitleRefs,\n      scrollIntoView\n    });\n    return () => _createVNode(\"div\", {\n      \"ref\": root,\n      \"class\": bem([props.type])\n    }, [props.showHeader ? props.sticky ? _createVNode(Sticky, {\n      \"container\": root.value,\n      \"offsetTop\": offsetTopPx.value,\n      \"onScroll\": onStickyScroll\n    }, {\n      default: () => [renderHeader()]\n    }) : renderHeader() : null, _createVNode(TabsContent, {\n      \"ref\": contentRef,\n      \"count\": children.length,\n      \"inited\": state.inited,\n      \"animated\": props.animated,\n      \"duration\": props.duration,\n      \"swipeable\": props.swipeable,\n      \"lazyRender\": props.lazyRender,\n      \"currentIndex\": state.currentIndex,\n      \"onChange\": setCurrentIndex\n    }, {\n      default: () => {\n        var _a;\n        return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n      }\n    })]);\n  }\n});\nexport {\n  TABS_KEY,\n  stdin_default as default,\n  tabsProps\n};\n"], "mappings": ";;AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACzH,SAASC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,oBAAoB;AACnO,SAASC,YAAY,EAAEC,WAAW,QAAQ,aAAa;AACvD,SAASC,OAAO,EAAEC,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,oBAAoB,QAAQ,WAAW;AACzG,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGpB,eAAe,CAAC,MAAM,CAAC;AAC3C,MAAMqB,SAAS,GAAG;EAChBC,IAAI,EAAExB,cAAc,CAAC,MAAM,CAAC;EAC5ByB,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAEC,OAAO;EACfC,MAAM,EAAED,OAAO;EACfE,MAAM,EAAEF,OAAO;EACfG,MAAM,EAAE5B,eAAe,CAAC,CAAC,CAAC;EAC1B6B,QAAQ,EAAE7B,eAAe,CAAC,GAAG,CAAC;EAC9B8B,QAAQ,EAAEL,OAAO;EACjBM,QAAQ,EAAEtC,SAAS;EACnBuC,SAAS,EAAEP,OAAO;EAClBQ,SAAS,EAAER,OAAO;EAClBS,SAAS,EAAElC,eAAe,CAAC,CAAC,CAAC;EAC7BmC,UAAU,EAAEZ,MAAM;EAClBa,UAAU,EAAE3C,SAAS;EACrB4C,UAAU,EAAE5C,SAAS;EACrB6C,SAAS,EAAE5C,WAAW;EACtB6C,UAAU,EAAE7C,WAAW;EACvB8C,YAAY,EAAEC,QAAQ;EACtBC,cAAc,EAAE1C,eAAe,CAAC,CAAC,CAAC;EAClC2C,gBAAgB,EAAEpB,MAAM;EACxBqB,kBAAkB,EAAErB;AACtB,CAAC;AACD,MAAMsB,QAAQ,GAAGC,MAAM,CAAC5B,IAAI,CAAC;AAC7B,IAAI6B,aAAa,GAAG7D,eAAe,CAAC;EAClCgC,IAAI;EACJ8B,KAAK,EAAE5B,SAAS;EAChB6B,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC;EACpEC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,IAAIC,SAAS;IACb,IAAIC,UAAU;IACd,IAAIC,WAAW;IACf,IAAIC,qBAAqB;IACzB,IAAIC,oBAAoB;IACxB,MAAMC,IAAI,GAAG9E,GAAG,CAAC,CAAC;IAClB,MAAM+E,MAAM,GAAG/E,GAAG,CAAC,CAAC;IACpB,MAAMgF,OAAO,GAAGhF,GAAG,CAAC,CAAC;IACrB,MAAMiF,UAAU,GAAGjF,GAAG,CAAC,CAAC;IACxB,MAAMkF,EAAE,GAAGpD,KAAK,CAAC,CAAC;IAClB,MAAMqD,QAAQ,GAAGxD,eAAe,CAACmD,IAAI,CAAC;IACtC,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGrD,OAAO,CAAC,CAAC;IAC3C,MAAM;MACJsD,QAAQ;MACRC;IACF,CAAC,GAAG7D,WAAW,CAACuC,QAAQ,CAAC;IACzB,MAAMuB,KAAK,GAAGrF,QAAQ,CAAC;MACrBsF,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,CAAC,CAAC;MACbC,YAAY,EAAE,CAAC;IACjB,CAAC,CAAC;IACF,MAAMC,UAAU,GAAG3F,QAAQ,CAAC,MAAMoF,QAAQ,CAACQ,MAAM,GAAG,CAAC1B,KAAK,CAACN,cAAc,IAAI,CAACM,KAAK,CAACjB,QAAQ,IAAIiB,KAAK,CAACrB,MAAM,CAAC;IAC7G,MAAMgD,QAAQ,GAAG7F,QAAQ,CAAC,OAAO;MAC/B8F,WAAW,EAAE5B,KAAK,CAAC1B,KAAK;MACxBa,UAAU,EAAEa,KAAK,CAACb;IACpB,CAAC,CAAC,CAAC;IACH,MAAM0C,UAAU,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;MACjC,IAAIC,EAAE;MACN,OAAO,CAACA,EAAE,GAAGF,GAAG,CAAC5D,IAAI,KAAK,IAAI,GAAG8D,EAAE,GAAGD,KAAK;IAC7C,CAAC;IACD,MAAME,WAAW,GAAGnG,QAAQ,CAAC,MAAM;MACjC,MAAMoG,SAAS,GAAGhB,QAAQ,CAACE,KAAK,CAACI,YAAY,CAAC;MAC9C,IAAIU,SAAS,EAAE;QACb,OAAOL,UAAU,CAACK,SAAS,EAAEd,KAAK,CAACI,YAAY,CAAC;MAClD;IACF,CAAC,CAAC;IACF,MAAMW,WAAW,GAAGrG,QAAQ,CAAC,MAAMU,QAAQ,CAACwD,KAAK,CAACd,SAAS,CAAC,CAAC;IAC7D,MAAMkD,YAAY,GAAGtG,QAAQ,CAAC,MAAM;MAClC,IAAIkE,KAAK,CAACtB,MAAM,EAAE;QAChB,OAAOyD,WAAW,CAACE,KAAK,GAAGhC,SAAS;MACtC;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IACF,MAAMiC,cAAc,GAAIC,SAAS,IAAK;MACpC,MAAMC,GAAG,GAAG7B,MAAM,CAAC0B,KAAK;MACxB,MAAMI,MAAM,GAAGzB,SAAS,CAACqB,KAAK;MAC9B,IAAI,CAACZ,UAAU,CAACY,KAAK,IAAI,CAACG,GAAG,IAAI,CAACC,MAAM,IAAI,CAACA,MAAM,CAACrB,KAAK,CAACI,YAAY,CAAC,EAAE;QACvE;MACF;MACA,MAAMkB,KAAK,GAAGD,MAAM,CAACrB,KAAK,CAACI,YAAY,CAAC,CAACmB,GAAG;MAC5C,MAAMC,EAAE,GAAGF,KAAK,CAACG,UAAU,GAAG,CAACL,GAAG,CAACM,WAAW,GAAGJ,KAAK,CAACI,WAAW,IAAI,CAAC;MACvE,IAAItC,qBAAqB,EAAEA,qBAAqB,CAAC,CAAC;MAClDA,qBAAqB,GAAGrD,YAAY,CAACqF,GAAG,EAAEI,EAAE,EAAEL,SAAS,GAAG,CAAC,GAAG,CAACvC,KAAK,CAACnB,QAAQ,CAAC;IAChF,CAAC;IACD,MAAMkE,OAAO,GAAGA,CAAA,KAAM;MACpB,MAAMC,aAAa,GAAG5B,KAAK,CAACC,MAAM;MAClCrF,QAAQ,CAAC,MAAM;QACb,MAAMyG,MAAM,GAAGzB,SAAS,CAACqB,KAAK;QAC9B,IAAI,CAACI,MAAM,IAAI,CAACA,MAAM,CAACrB,KAAK,CAACI,YAAY,CAAC,IAAIxB,KAAK,CAAC3B,IAAI,KAAK,MAAM,IAAI9B,QAAQ,CAACmE,IAAI,CAAC2B,KAAK,CAAC,EAAE;UAC3F;QACF;QACA,MAAMK,KAAK,GAAGD,MAAM,CAACrB,KAAK,CAACI,YAAY,CAAC,CAACmB,GAAG;QAC5C,MAAM;UACJrD,SAAS;UACTC;QACF,CAAC,GAAGS,KAAK;QACT,MAAMiD,IAAI,GAAGP,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACI,WAAW,GAAG,CAAC;QACrD,MAAMvB,SAAS,GAAG;UAChB2B,KAAK,EAAE5G,OAAO,CAACgD,SAAS,CAAC;UACzB6D,eAAe,EAAEnD,KAAK,CAAC1B,KAAK;UAC5B8E,SAAS,EAAE,cAAcH,IAAI;QAC/B,CAAC;QACD,IAAID,aAAa,EAAE;UACjBzB,SAAS,CAAC8B,kBAAkB,GAAG,GAAGrD,KAAK,CAACnB,QAAQ,GAAG;QACrD;QACA,IAAIxC,KAAK,CAACkD,UAAU,CAAC,EAAE;UACrB,MAAM+D,MAAM,GAAGhH,OAAO,CAACiD,UAAU,CAAC;UAClCgC,SAAS,CAAC+B,MAAM,GAAGA,MAAM;UACzB/B,SAAS,CAACgC,YAAY,GAAGD,MAAM;QACjC;QACAlC,KAAK,CAACG,SAAS,GAAGA,SAAS;MAC7B,CAAC,CAAC;IACJ,CAAC;IACD,MAAMiC,gBAAgB,GAAIzB,KAAK,IAAK;MAClC,MAAM0B,IAAI,GAAG1B,KAAK,GAAGX,KAAK,CAACI,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;MAChD,OAAOO,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGb,QAAQ,CAACQ,MAAM,EAAE;QAC5C,IAAI,CAACR,QAAQ,CAACa,KAAK,CAAC,CAAC2B,QAAQ,EAAE;UAC7B,OAAO3B,KAAK;QACd;QACAA,KAAK,IAAI0B,IAAI;MACf;IACF,CAAC;IACD,MAAME,eAAe,GAAGA,CAACnC,YAAY,EAAEoC,kBAAkB,KAAK;MAC5D,MAAMC,QAAQ,GAAGL,gBAAgB,CAAChC,YAAY,CAAC;MAC/C,IAAI,CAACnF,KAAK,CAACwH,QAAQ,CAAC,EAAE;QACpB;MACF;MACA,MAAMC,MAAM,GAAG5C,QAAQ,CAAC2C,QAAQ,CAAC;MACjC,MAAME,OAAO,GAAGlC,UAAU,CAACiC,MAAM,EAAED,QAAQ,CAAC;MAC5C,MAAMG,gBAAgB,GAAG5C,KAAK,CAACI,YAAY,KAAK,IAAI;MACpD,IAAIJ,KAAK,CAACI,YAAY,KAAKqC,QAAQ,EAAE;QACnCzC,KAAK,CAACI,YAAY,GAAGqC,QAAQ;QAC7B,IAAI,CAACD,kBAAkB,EAAE;UACvBtB,cAAc,CAAC,CAAC;QAClB;QACAS,OAAO,CAAC,CAAC;MACX;MACA,IAAIgB,OAAO,KAAK/D,KAAK,CAACpB,MAAM,EAAE;QAC5BuB,IAAI,CAAC,eAAe,EAAE4D,OAAO,CAAC;QAC9B,IAAIC,gBAAgB,EAAE;UACpB7D,IAAI,CAAC,QAAQ,EAAE4D,OAAO,EAAED,MAAM,CAACpB,KAAK,CAAC;QACvC;MACF;MACA,IAAInC,WAAW,IAAI,CAACP,KAAK,CAACf,SAAS,EAAE;QACnChC,gBAAgB,CAACgH,IAAI,CAACC,IAAI,CAACtH,aAAa,CAAC8D,IAAI,CAAC2B,KAAK,CAAC,GAAGF,WAAW,CAACE,KAAK,CAAC,CAAC;MAC5E;IACF,CAAC;IACD,MAAM8B,qBAAqB,GAAGA,CAACC,KAAK,EAAER,kBAAkB,KAAK;MAC3D,MAAM7B,KAAK,GAAGb,QAAQ,CAACmD,SAAS,CAAC,CAACvC,GAAG,EAAEwC,MAAM,KAAKzC,UAAU,CAACC,GAAG,EAAEwC,MAAM,CAAC,KAAKF,KAAK,CAAC;MACpFT,eAAe,CAAC5B,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,KAAK,EAAE6B,kBAAkB,CAAC;IAC/D,CAAC;IACD,MAAMW,sBAAsB,GAAGA,CAAChC,SAAS,GAAG,KAAK,KAAK;MACpD,IAAIvC,KAAK,CAACf,SAAS,EAAE;QACnB,MAAMuF,MAAM,GAAGtD,QAAQ,CAACE,KAAK,CAACI,YAAY,CAAC,CAACmB,GAAG;QAC/C,IAAI6B,MAAM,IAAIzD,QAAQ,CAACsB,KAAK,EAAE;UAC5B,MAAMO,EAAE,GAAGhG,aAAa,CAAC4H,MAAM,EAAEzD,QAAQ,CAACsB,KAAK,CAAC,GAAGD,YAAY,CAACC,KAAK;UACrE/B,UAAU,GAAG,IAAI;UACjB,IAAIG,oBAAoB,EAAEA,oBAAoB,CAAC,CAAC;UAChDA,oBAAoB,GAAGrD,WAAW,CAAC2D,QAAQ,CAACsB,KAAK,EAAEO,EAAE,EAAEL,SAAS,GAAG,CAAC,GAAG,CAACvC,KAAK,CAACnB,QAAQ,EAAE,MAAM;YAC5FyB,UAAU,GAAG,KAAK;UACpB,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IACD,MAAMmE,UAAU,GAAGA,CAACC,IAAI,EAAE3C,KAAK,EAAE4C,KAAK,KAAK;MACzC,MAAM;QACJjC,KAAK;QACLgB;MACF,CAAC,GAAGxC,QAAQ,CAACa,KAAK,CAAC;MACnB,MAAMqC,KAAK,GAAGvC,UAAU,CAACX,QAAQ,CAACa,KAAK,CAAC,EAAEA,KAAK,CAAC;MAChD,IAAI,CAAC2B,QAAQ,EAAE;QACb5G,eAAe,CAACkD,KAAK,CAACR,YAAY,EAAE;UAClCoF,IAAI,EAAE,CAACR,KAAK,CAAC;UACbS,IAAI,EAAEA,CAAA,KAAM;YACVlB,eAAe,CAAC5B,KAAK,CAAC;YACtBwC,sBAAsB,CAAC,CAAC;UAC1B;QACF,CAAC,CAAC;QACF5G,KAAK,CAAC+G,IAAI,CAAC;MACb;MACAvE,IAAI,CAAC,UAAU,EAAE;QACfjC,IAAI,EAAEkG,KAAK;QACX1B,KAAK;QACLiC,KAAK;QACLjB;MACF,CAAC,CAAC;IACJ,CAAC;IACD,MAAMoB,cAAc,GAAIC,MAAM,IAAK;MACjCxE,WAAW,GAAGwE,MAAM,CAACC,OAAO;MAC5B7E,IAAI,CAAC,QAAQ,EAAE4E,MAAM,CAAC;IACxB,CAAC;IACD,MAAME,QAAQ,GAAIb,KAAK,IAAK;MAC1BpI,QAAQ,CAAC,MAAM;QACbmI,qBAAqB,CAACC,KAAK,CAAC;QAC5BG,sBAAsB,CAAC,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC;IACD,MAAMW,uBAAuB,GAAGA,CAAA,KAAM;MACpC,KAAK,IAAInD,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGb,QAAQ,CAACQ,MAAM,EAAEK,KAAK,EAAE,EAAE;QACpD,MAAM;UACJoD;QACF,CAAC,GAAG9H,OAAO,CAAC6D,QAAQ,CAACa,KAAK,CAAC,CAACY,GAAG,CAAC;QAChC,IAAIwC,GAAG,GAAG/C,YAAY,CAACC,KAAK,EAAE;UAC5B,OAAON,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;QACpC;MACF;MACA,OAAOb,QAAQ,CAACQ,MAAM,GAAG,CAAC;IAC5B,CAAC;IACD,MAAM0D,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAIpF,KAAK,CAACf,SAAS,IAAI,CAACqB,UAAU,EAAE;QAClC,MAAMyB,KAAK,GAAGmD,uBAAuB,CAAC,CAAC;QACvCvB,eAAe,CAAC5B,KAAK,CAAC;MACxB;IACF,CAAC;IACD,MAAMsD,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIrF,KAAK,CAAC3B,IAAI,KAAK,MAAM,IAAI6C,QAAQ,CAACQ,MAAM,EAAE;QAC5C,OAAOtF,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE+B,GAAG,CAAC,MAAM,CAAC;UACpB,OAAO,EAAEiD,KAAK,CAACG;QACjB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,MAAM+D,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAItD,EAAE,EAAEuD,EAAE,EAAEC,EAAE;MACd,MAAM;QACJnH,IAAI;QACJG,MAAM;QACNE;MACF,CAAC,GAAGsB,KAAK;MACT,MAAMyF,MAAM,GAAG,CAACrJ,YAAY,CAAC,KAAK,EAAE;QAClC,KAAK,EAAEsC,MAAM,GAAG,KAAK,CAAC,GAAGkC,OAAO;QAChC,OAAO,EAAE,CAACzC,GAAG,CAAC,MAAM,CAAC,EAAE;UACrB,CAACjB,iBAAiB,GAAGmB,IAAI,KAAK,MAAM,IAAIG;QAC1C,CAAC;MACH,CAAC,EAAE,CAACpC,YAAY,CAAC,KAAK,EAAE;QACtB,KAAK,EAAEuE,MAAM;QACb,MAAM,EAAE,SAAS;QACjB,OAAO,EAAExC,GAAG,CAAC,KAAK,EAAE,CAACE,IAAI,EAAE;UACzBM,MAAM,EAAEqB,KAAK,CAACrB,MAAM;UACpB+G,QAAQ,EAAEjE,UAAU,CAACY;QACvB,CAAC,CAAC,CAAC;QACH,OAAO,EAAEV,QAAQ,CAACU,KAAK;QACvB,kBAAkB,EAAE;MACtB,CAAC,EAAE,CAAC,CAACL,EAAE,GAAG5B,KAAK,CAAC,UAAU,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4B,EAAE,CAAC2D,IAAI,CAACvF,KAAK,CAAC,EAAEc,QAAQ,CAAC0E,GAAG,CAAElB,IAAI,IAAKA,IAAI,CAACmB,WAAW,CAACpB,UAAU,CAAC,CAAC,EAAEY,UAAU,CAAC,CAAC,EAAE,CAACE,EAAE,GAAGnF,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmF,EAAE,CAACI,IAAI,CAACvF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACoF,EAAE,GAAGpF,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoF,EAAE,CAACG,IAAI,CAACvF,KAAK,CAAC,CAAC;MAClQ,IAAI1B,MAAM,EAAE;QACV,OAAOtC,YAAY,CAAC,KAAK,EAAE;UACzB,KAAK,EAAEwE;QACT,CAAC,EAAE,CAAC6E,MAAM,CAAC,CAAC;MACd;MACA,OAAOA,MAAM;IACf,CAAC;IACD,MAAMK,MAAM,GAAGA,CAAA,KAAM;MACnB/C,OAAO,CAAC,CAAC;MACT/G,QAAQ,CAAC,MAAM;QACb,IAAIgG,EAAE,EAAEuD,EAAE;QACVjD,cAAc,CAAC,IAAI,CAAC;QACpB,CAACiD,EAAE,GAAG,CAACvD,EAAE,GAAGnB,UAAU,CAACwB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,EAAE,CAAC+D,QAAQ,CAAC1D,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkD,EAAE,CAACO,MAAM,CAAC,CAAC;MACpG,CAAC,CAAC;IACJ,CAAC;IACDjK,KAAK,CAAC,MAAM,CAACmE,KAAK,CAAC1B,KAAK,EAAE0B,KAAK,CAACnB,QAAQ,EAAEmB,KAAK,CAACV,SAAS,EAAEU,KAAK,CAACT,UAAU,CAAC,EAAEwD,OAAO,CAAC;IACtFlH,KAAK,CAACc,WAAW,EAAEmJ,MAAM,CAAC;IAC1BjK,KAAK,CAAC,MAAMmE,KAAK,CAACpB,MAAM,EAAGyD,KAAK,IAAK;MACnC,IAAIA,KAAK,KAAKJ,WAAW,CAACI,KAAK,EAAE;QAC/B8B,qBAAqB,CAAC9B,KAAK,CAAC;MAC9B;IACF,CAAC,CAAC;IACFxG,KAAK,CAAC,MAAMqF,QAAQ,CAACQ,MAAM,EAAE,MAAM;MACjC,IAAIN,KAAK,CAACC,MAAM,EAAE;QAChB8C,qBAAqB,CAACnE,KAAK,CAACpB,MAAM,CAAC;QACnCmE,OAAO,CAAC,CAAC;QACT/G,QAAQ,CAAC,MAAM;UACbsG,cAAc,CAAC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,MAAM0D,IAAI,GAAGA,CAAA,KAAM;MACjB7B,qBAAqB,CAACnE,KAAK,CAACpB,MAAM,EAAE,IAAI,CAAC;MACzC5C,QAAQ,CAAC,MAAM;QACboF,KAAK,CAACC,MAAM,GAAG,IAAI;QACnB,IAAIT,OAAO,CAACyB,KAAK,EAAE;UACjBhC,SAAS,GAAGhD,OAAO,CAACuD,OAAO,CAACyB,KAAK,CAAC,CAACiB,MAAM;QAC3C;QACAhB,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IACD,MAAM2D,UAAU,GAAGA,CAAC7B,KAAK,EAAE1B,KAAK,KAAKvC,IAAI,CAAC,UAAU,EAAEiE,KAAK,EAAE1B,KAAK,CAAC;IACnE7E,SAAS,CAAC;MACRiI,MAAM;MACNb;IACF,CAAC,CAAC;IACFhJ,WAAW,CAAC8G,OAAO,CAAC;IACpBjF,aAAa,CAACiF,OAAO,CAAC;IACtBtF,oBAAoB,CAACuI,IAAI,CAAC;IAC1BjI,mBAAmB,CAAC2C,IAAI,EAAEqC,OAAO,CAAC;IAClCvF,gBAAgB,CAAC,QAAQ,EAAE4H,QAAQ,EAAE;MACnCZ,MAAM,EAAEzD,QAAQ;MAChBmF,OAAO,EAAE;IACX,CAAC,CAAC;IACF/E,YAAY,CAAC;MACXL,EAAE;MACFd,KAAK;MACL+C,OAAO;MACPtB,UAAU;MACVwE,UAAU;MACVhE,WAAW;MACXhB,YAAY;MACZqB;IACF,CAAC,CAAC;IACF,OAAO,MAAMlG,YAAY,CAAC,KAAK,EAAE;MAC/B,KAAK,EAAEsE,IAAI;MACX,OAAO,EAAEvC,GAAG,CAAC,CAAC6B,KAAK,CAAC3B,IAAI,CAAC;IAC3B,CAAC,EAAE,CAAC2B,KAAK,CAACX,UAAU,GAAGW,KAAK,CAACtB,MAAM,GAAGtC,YAAY,CAAC4B,MAAM,EAAE;MACzD,WAAW,EAAE0C,IAAI,CAAC2B,KAAK;MACvB,WAAW,EAAEF,WAAW,CAACE,KAAK;MAC9B,UAAU,EAAEyC;IACd,CAAC,EAAE;MACDqB,OAAO,EAAEA,CAAA,KAAM,CAACb,YAAY,CAAC,CAAC;IAChC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,GAAG,IAAI,EAAElJ,YAAY,CAAC6B,WAAW,EAAE;MACpD,KAAK,EAAE4C,UAAU;MACjB,OAAO,EAAEK,QAAQ,CAACQ,MAAM;MACxB,QAAQ,EAAEN,KAAK,CAACC,MAAM;MACtB,UAAU,EAAErB,KAAK,CAAClB,QAAQ;MAC1B,UAAU,EAAEkB,KAAK,CAACnB,QAAQ;MAC1B,WAAW,EAAEmB,KAAK,CAAChB,SAAS;MAC5B,YAAY,EAAEgB,KAAK,CAACZ,UAAU;MAC9B,cAAc,EAAEgC,KAAK,CAACI,YAAY;MAClC,UAAU,EAAEmC;IACd,CAAC,EAAE;MACDwC,OAAO,EAAEA,CAAA,KAAM;QACb,IAAInE,EAAE;QACN,OAAO,CAAC,CAACA,EAAE,GAAG5B,KAAK,CAAC+F,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGnE,EAAE,CAAC2D,IAAI,CAACvF,KAAK,CAAC,CAAC;MACjE;IACF,CAAC,CAAC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AACF,SACEP,QAAQ,EACRE,aAAa,IAAIoG,OAAO,EACxB/H,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}