{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Space from \"./Space.mjs\";\nconst Space = withInstall(_Space);\nvar stdin_default = Space;\nimport { spaceProps } from \"./Space.mjs\";\nexport { Space, stdin_default as default, spaceProps };", "map": {"version": 3, "names": ["withInstall", "_Space", "Space", "stdin_default", "spaceProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/space/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Space from \"./Space.mjs\";\nconst Space = withInstall(_Space);\nvar stdin_default = Space;\nimport { spaceProps } from \"./Space.mjs\";\nexport {\n  Space,\n  stdin_default as default,\n  spaceProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SAASE,UAAU,QAAQ,aAAa;AACxC,SACEF,KAAK,EACLC,aAAa,IAAIE,OAAO,EACxBD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}