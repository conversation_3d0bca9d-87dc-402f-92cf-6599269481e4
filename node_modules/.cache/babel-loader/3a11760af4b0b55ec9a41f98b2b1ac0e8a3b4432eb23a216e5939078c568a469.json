{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, defineComponent, computed, watch, createVNode as _createVNode } from \"vue\";\nimport { raf } from \"@vant/use\";\nimport { createNamespace, makeArrayProp, makeNumberProp, makeStringProp, truthProp, padZero } from \"../utils/index.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport RollingTextItem from \"./RollingTextItem.mjs\";\nconst [name, bem] = createNamespace(\"rolling-text\");\nconst rollingTextProps = {\n  startNum: makeNumberProp(0),\n  targetNum: Number,\n  textList: makeArrayProp(),\n  duration: makeNumberProp(2),\n  autoStart: truthProp,\n  direction: makeStringProp(\"down\"),\n  stopOrder: makeStringProp(\"ltr\"),\n  height: makeNumberProp(40)\n};\nconst CIRCLE_NUM = 2;\nvar stdin_default = defineComponent({\n  name,\n  props: rollingTextProps,\n  setup(props) {\n    const isCustomType = computed(() => Array.isArray(props.textList) && props.textList.length);\n    const itemLength = computed(() => {\n      if (isCustomType.value) return props.textList[0].length;\n      return `${Math.max(props.startNum, props.targetNum)}`.length;\n    });\n    const getTextArrByIdx = idx => {\n      const result = [];\n      for (let i = 0; i < props.textList.length; i++) {\n        result.push(props.textList[i][idx]);\n      }\n      return result;\n    };\n    const targetNumArr = computed(() => {\n      if (isCustomType.value) return new Array(itemLength.value).fill(\"\");\n      return padZero(props.targetNum, itemLength.value).split(\"\");\n    });\n    const startNumArr = computed(() => padZero(props.startNum, itemLength.value).split(\"\"));\n    const getFigureArr = i => {\n      const start2 = +startNumArr.value[i];\n      const target = +targetNumArr.value[i];\n      const result = [];\n      for (let i2 = start2; i2 <= 9; i2++) {\n        result.push(i2);\n      }\n      for (let i2 = 0; i2 <= CIRCLE_NUM; i2++) {\n        for (let j = 0; j <= 9; j++) {\n          result.push(j);\n        }\n      }\n      for (let i2 = 0; i2 <= target; i2++) {\n        result.push(i2);\n      }\n      return result;\n    };\n    const getDelay = (i, len) => {\n      if (props.stopOrder === \"ltr\") return 0.2 * i;\n      return 0.2 * (len - 1 - i);\n    };\n    const rolling = ref(props.autoStart);\n    const start = () => {\n      rolling.value = true;\n    };\n    const reset = () => {\n      rolling.value = false;\n      if (props.autoStart) {\n        raf(() => start());\n      }\n    };\n    watch(() => props.autoStart, value => {\n      if (value) {\n        start();\n      }\n    });\n    useExpose({\n      start,\n      reset\n    });\n    return () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [targetNumArr.value.map((_, i) => _createVNode(RollingTextItem, {\n      \"figureArr\": isCustomType.value ? getTextArrByIdx(i) : getFigureArr(i),\n      \"duration\": props.duration,\n      \"direction\": props.direction,\n      \"isStart\": rolling.value,\n      \"height\": props.height,\n      \"delay\": getDelay(i, itemLength.value)\n    }, null))]);\n  }\n});\nexport { stdin_default as default, rollingTextProps };", "map": {"version": 3, "names": ["ref", "defineComponent", "computed", "watch", "createVNode", "_createVNode", "raf", "createNamespace", "makeArrayProp", "makeNumberProp", "makeStringProp", "truthProp", "padZero", "useExpose", "RollingTextItem", "name", "bem", "rollingTextProps", "startNum", "targetNum", "Number", "textList", "duration", "autoStart", "direction", "stopOrder", "height", "CIRCLE_NUM", "stdin_default", "props", "setup", "isCustomType", "Array", "isArray", "length", "itemLength", "value", "Math", "max", "getTextArrByIdx", "idx", "result", "i", "push", "targetNumArr", "fill", "split", "startNumArr", "getFigureArr", "start2", "target", "i2", "j", "get<PERSON>elay", "len", "rolling", "start", "reset", "map", "_", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/rolling-text/RollingText.mjs"], "sourcesContent": ["import { ref, defineComponent, computed, watch, createVNode as _createVNode } from \"vue\";\nimport { raf } from \"@vant/use\";\nimport { createNamespace, makeArrayProp, makeNumberProp, makeStringProp, truthProp, padZero } from \"../utils/index.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport RollingTextItem from \"./RollingTextItem.mjs\";\nconst [name, bem] = createNamespace(\"rolling-text\");\nconst rollingTextProps = {\n  startNum: makeNumberProp(0),\n  targetNum: Number,\n  textList: makeArrayProp(),\n  duration: makeNumberProp(2),\n  autoStart: truthProp,\n  direction: makeStringProp(\"down\"),\n  stopOrder: makeStringProp(\"ltr\"),\n  height: makeNumberProp(40)\n};\nconst CIRCLE_NUM = 2;\nvar stdin_default = defineComponent({\n  name,\n  props: rollingTextProps,\n  setup(props) {\n    const isCustomType = computed(() => Array.isArray(props.textList) && props.textList.length);\n    const itemLength = computed(() => {\n      if (isCustomType.value) return props.textList[0].length;\n      return `${Math.max(props.startNum, props.targetNum)}`.length;\n    });\n    const getTextArrByIdx = (idx) => {\n      const result = [];\n      for (let i = 0; i < props.textList.length; i++) {\n        result.push(props.textList[i][idx]);\n      }\n      return result;\n    };\n    const targetNumArr = computed(() => {\n      if (isCustomType.value) return new Array(itemLength.value).fill(\"\");\n      return padZero(props.targetNum, itemLength.value).split(\"\");\n    });\n    const startNumArr = computed(() => padZero(props.startNum, itemLength.value).split(\"\"));\n    const getFigureArr = (i) => {\n      const start2 = +startNumArr.value[i];\n      const target = +targetNumArr.value[i];\n      const result = [];\n      for (let i2 = start2; i2 <= 9; i2++) {\n        result.push(i2);\n      }\n      for (let i2 = 0; i2 <= CIRCLE_NUM; i2++) {\n        for (let j = 0; j <= 9; j++) {\n          result.push(j);\n        }\n      }\n      for (let i2 = 0; i2 <= target; i2++) {\n        result.push(i2);\n      }\n      return result;\n    };\n    const getDelay = (i, len) => {\n      if (props.stopOrder === \"ltr\") return 0.2 * i;\n      return 0.2 * (len - 1 - i);\n    };\n    const rolling = ref(props.autoStart);\n    const start = () => {\n      rolling.value = true;\n    };\n    const reset = () => {\n      rolling.value = false;\n      if (props.autoStart) {\n        raf(() => start());\n      }\n    };\n    watch(() => props.autoStart, (value) => {\n      if (value) {\n        start();\n      }\n    });\n    useExpose({\n      start,\n      reset\n    });\n    return () => _createVNode(\"div\", {\n      \"class\": bem()\n    }, [targetNumArr.value.map((_, i) => _createVNode(RollingTextItem, {\n      \"figureArr\": isCustomType.value ? getTextArrByIdx(i) : getFigureArr(i),\n      \"duration\": props.duration,\n      \"direction\": props.direction,\n      \"isStart\": rolling.value,\n      \"height\": props.height,\n      \"delay\": getDelay(i, itemLength.value)\n    }, null))]);\n  }\n});\nexport {\n  stdin_default as default,\n  rollingTextProps\n};\n"], "mappings": ";;;AAAA,SAASA,GAAG,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACxF,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,eAAe,EAAEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,SAAS,EAAEC,OAAO,QAAQ,oBAAoB;AACvH,SAASC,SAAS,QAAQ,+BAA+B;AACzD,OAAOC,eAAe,MAAM,uBAAuB;AACnD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGT,eAAe,CAAC,cAAc,CAAC;AACnD,MAAMU,gBAAgB,GAAG;EACvBC,QAAQ,EAAET,cAAc,CAAC,CAAC,CAAC;EAC3BU,SAAS,EAAEC,MAAM;EACjBC,QAAQ,EAAEb,aAAa,CAAC,CAAC;EACzBc,QAAQ,EAAEb,cAAc,CAAC,CAAC,CAAC;EAC3Bc,SAAS,EAAEZ,SAAS;EACpBa,SAAS,EAAEd,cAAc,CAAC,MAAM,CAAC;EACjCe,SAAS,EAAEf,cAAc,CAAC,KAAK,CAAC;EAChCgB,MAAM,EAAEjB,cAAc,CAAC,EAAE;AAC3B,CAAC;AACD,MAAMkB,UAAU,GAAG,CAAC;AACpB,IAAIC,aAAa,GAAG3B,eAAe,CAAC;EAClCc,IAAI;EACJc,KAAK,EAAEZ,gBAAgB;EACvBa,KAAKA,CAACD,KAAK,EAAE;IACX,MAAME,YAAY,GAAG7B,QAAQ,CAAC,MAAM8B,KAAK,CAACC,OAAO,CAACJ,KAAK,CAACR,QAAQ,CAAC,IAAIQ,KAAK,CAACR,QAAQ,CAACa,MAAM,CAAC;IAC3F,MAAMC,UAAU,GAAGjC,QAAQ,CAAC,MAAM;MAChC,IAAI6B,YAAY,CAACK,KAAK,EAAE,OAAOP,KAAK,CAACR,QAAQ,CAAC,CAAC,CAAC,CAACa,MAAM;MACvD,OAAO,GAAGG,IAAI,CAACC,GAAG,CAACT,KAAK,CAACX,QAAQ,EAAEW,KAAK,CAACV,SAAS,CAAC,EAAE,CAACe,MAAM;IAC9D,CAAC,CAAC;IACF,MAAMK,eAAe,GAAIC,GAAG,IAAK;MAC/B,MAAMC,MAAM,GAAG,EAAE;MACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,KAAK,CAACR,QAAQ,CAACa,MAAM,EAAEQ,CAAC,EAAE,EAAE;QAC9CD,MAAM,CAACE,IAAI,CAACd,KAAK,CAACR,QAAQ,CAACqB,CAAC,CAAC,CAACF,GAAG,CAAC,CAAC;MACrC;MACA,OAAOC,MAAM;IACf,CAAC;IACD,MAAMG,YAAY,GAAG1C,QAAQ,CAAC,MAAM;MAClC,IAAI6B,YAAY,CAACK,KAAK,EAAE,OAAO,IAAIJ,KAAK,CAACG,UAAU,CAACC,KAAK,CAAC,CAACS,IAAI,CAAC,EAAE,CAAC;MACnE,OAAOjC,OAAO,CAACiB,KAAK,CAACV,SAAS,EAAEgB,UAAU,CAACC,KAAK,CAAC,CAACU,KAAK,CAAC,EAAE,CAAC;IAC7D,CAAC,CAAC;IACF,MAAMC,WAAW,GAAG7C,QAAQ,CAAC,MAAMU,OAAO,CAACiB,KAAK,CAACX,QAAQ,EAAEiB,UAAU,CAACC,KAAK,CAAC,CAACU,KAAK,CAAC,EAAE,CAAC,CAAC;IACvF,MAAME,YAAY,GAAIN,CAAC,IAAK;MAC1B,MAAMO,MAAM,GAAG,CAACF,WAAW,CAACX,KAAK,CAACM,CAAC,CAAC;MACpC,MAAMQ,MAAM,GAAG,CAACN,YAAY,CAACR,KAAK,CAACM,CAAC,CAAC;MACrC,MAAMD,MAAM,GAAG,EAAE;MACjB,KAAK,IAAIU,EAAE,GAAGF,MAAM,EAAEE,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QACnCV,MAAM,CAACE,IAAI,CAACQ,EAAE,CAAC;MACjB;MACA,KAAK,IAAIA,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAIxB,UAAU,EAAEwB,EAAE,EAAE,EAAE;QACvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3BX,MAAM,CAACE,IAAI,CAACS,CAAC,CAAC;QAChB;MACF;MACA,KAAK,IAAID,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAID,MAAM,EAAEC,EAAE,EAAE,EAAE;QACnCV,MAAM,CAACE,IAAI,CAACQ,EAAE,CAAC;MACjB;MACA,OAAOV,MAAM;IACf,CAAC;IACD,MAAMY,QAAQ,GAAGA,CAACX,CAAC,EAAEY,GAAG,KAAK;MAC3B,IAAIzB,KAAK,CAACJ,SAAS,KAAK,KAAK,EAAE,OAAO,GAAG,GAAGiB,CAAC;MAC7C,OAAO,GAAG,IAAIY,GAAG,GAAG,CAAC,GAAGZ,CAAC,CAAC;IAC5B,CAAC;IACD,MAAMa,OAAO,GAAGvD,GAAG,CAAC6B,KAAK,CAACN,SAAS,CAAC;IACpC,MAAMiC,KAAK,GAAGA,CAAA,KAAM;MAClBD,OAAO,CAACnB,KAAK,GAAG,IAAI;IACtB,CAAC;IACD,MAAMqB,KAAK,GAAGA,CAAA,KAAM;MAClBF,OAAO,CAACnB,KAAK,GAAG,KAAK;MACrB,IAAIP,KAAK,CAACN,SAAS,EAAE;QACnBjB,GAAG,CAAC,MAAMkD,KAAK,CAAC,CAAC,CAAC;MACpB;IACF,CAAC;IACDrD,KAAK,CAAC,MAAM0B,KAAK,CAACN,SAAS,EAAGa,KAAK,IAAK;MACtC,IAAIA,KAAK,EAAE;QACToB,KAAK,CAAC,CAAC;MACT;IACF,CAAC,CAAC;IACF3C,SAAS,CAAC;MACR2C,KAAK;MACLC;IACF,CAAC,CAAC;IACF,OAAO,MAAMpD,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEW,GAAG,CAAC;IACf,CAAC,EAAE,CAAC4B,YAAY,CAACR,KAAK,CAACsB,GAAG,CAAC,CAACC,CAAC,EAAEjB,CAAC,KAAKrC,YAAY,CAACS,eAAe,EAAE;MACjE,WAAW,EAAEiB,YAAY,CAACK,KAAK,GAAGG,eAAe,CAACG,CAAC,CAAC,GAAGM,YAAY,CAACN,CAAC,CAAC;MACtE,UAAU,EAAEb,KAAK,CAACP,QAAQ;MAC1B,WAAW,EAAEO,KAAK,CAACL,SAAS;MAC5B,SAAS,EAAE+B,OAAO,CAACnB,KAAK;MACxB,QAAQ,EAAEP,KAAK,CAACH,MAAM;MACtB,OAAO,EAAE2B,QAAQ,CAACX,CAAC,EAAEP,UAAU,CAACC,KAAK;IACvC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;EACb;AACF,CAAC,CAAC;AACF,SACER,aAAa,IAAIgC,OAAO,EACxB3C,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}