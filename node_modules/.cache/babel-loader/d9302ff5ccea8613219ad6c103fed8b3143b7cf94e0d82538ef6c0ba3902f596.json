{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _TimePicker from \"./TimePicker.mjs\";\nconst TimePicker = withInstall(_TimePicker);\nvar stdin_default = TimePicker;\nimport { timePickerProps } from \"./TimePicker.mjs\";\nexport { TimePicker, stdin_default as default, timePickerProps };", "map": {"version": 3, "names": ["withInstall", "_TimePicker", "TimePicker", "stdin_default", "timePickerProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/time-picker/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _TimePicker from \"./TimePicker.mjs\";\nconst TimePicker = withInstall(_TimePicker);\nvar stdin_default = TimePicker;\nimport { timePickerProps } from \"./TimePicker.mjs\";\nexport {\n  TimePicker,\n  stdin_default as default,\n  timePickerProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVC,aAAa,IAAIE,OAAO,EACxBD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}