{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _ImagePreview from \"./ImagePreview.mjs\";\nconst ImagePreview = withInstall(_ImagePreview);\nvar stdin_default = ImagePreview;\nimport { imagePreviewProps } from \"./ImagePreview.mjs\";\nimport { showImagePreview } from \"./function-call.mjs\";\nexport { ImagePreview, stdin_default as default, imagePreviewProps, showImagePreview };", "map": {"version": 3, "names": ["withInstall", "_ImagePreview", "ImagePreview", "stdin_default", "imagePreviewProps", "showImagePreview", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/image-preview/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _ImagePreview from \"./ImagePreview.mjs\";\nconst ImagePreview = withInstall(_ImagePreview);\nvar stdin_default = ImagePreview;\nimport { imagePreviewProps } from \"./ImagePreview.mjs\";\nimport { showImagePreview } from \"./function-call.mjs\";\nexport {\n  ImagePreview,\n  stdin_default as default,\n  imagePreviewProps,\n  showImagePreview\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,MAAMC,YAAY,GAAGF,WAAW,CAACC,aAAa,CAAC;AAC/C,IAAIE,aAAa,GAAGD,YAAY;AAChC,SAASE,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SACEH,YAAY,EACZC,aAAa,IAAIG,OAAO,EACxBF,iBAAiB,EACjBC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}