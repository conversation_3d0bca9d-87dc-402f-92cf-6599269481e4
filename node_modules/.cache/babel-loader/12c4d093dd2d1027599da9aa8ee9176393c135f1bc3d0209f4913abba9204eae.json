{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nfunction closest(arr, target) {\n  return arr.reduce((pre, cur) => Math.abs(pre - target) < Math.abs(cur - target) ? pre : cur);\n}\nexport { closest };", "map": {"version": 3, "names": ["closest", "arr", "target", "reduce", "pre", "cur", "Math", "abs"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/utils/closest.mjs"], "sourcesContent": ["function closest(arr, target) {\n  return arr.reduce(\n    (pre, cur) => Math.abs(pre - target) < Math.abs(cur - target) ? pre : cur\n  );\n}\nexport {\n  closest\n};\n"], "mappings": ";;AAAA,SAASA,OAAOA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC5B,OAAOD,GAAG,CAACE,MAAM,CACf,CAACC,GAAG,EAAEC,GAAG,KAAKC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,MAAM,CAAC,GAAGI,IAAI,CAACC,GAAG,CAACF,GAAG,GAAGH,MAAM,CAAC,GAAGE,GAAG,GAAGC,GACxE,CAAC;AACH;AACA,SACEL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}