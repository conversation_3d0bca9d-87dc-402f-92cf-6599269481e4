{"ast": null, "code": "import { pad<PERSON>ero } from \"../utils/index.mjs\";\nfunction parseFormat(format, currentTime) {\n  const {\n    days\n  } = currentTime;\n  let {\n    hours,\n    minutes,\n    seconds,\n    milliseconds\n  } = currentTime;\n  if (format.includes(\"DD\")) {\n    format = format.replace(\"DD\", pad<PERSON>ero(days));\n  } else {\n    hours += days * 24;\n  }\n  if (format.includes(\"HH\")) {\n    format = format.replace(\"HH\", pad<PERSON>ero(hours));\n  } else {\n    minutes += hours * 60;\n  }\n  if (format.includes(\"mm\")) {\n    format = format.replace(\"mm\", padZero(minutes));\n  } else {\n    seconds += minutes * 60;\n  }\n  if (format.includes(\"ss\")) {\n    format = format.replace(\"ss\", padZero(seconds));\n  } else {\n    milliseconds += seconds * 1e3;\n  }\n  if (format.includes(\"S\")) {\n    const ms = padZero(milliseconds, 3);\n    if (format.includes(\"SSS\")) {\n      format = format.replace(\"SSS\", ms);\n    } else if (format.includes(\"SS\")) {\n      format = format.replace(\"SS\", ms.slice(0, 2));\n    } else {\n      format = format.replace(\"S\", ms.charAt(0));\n    }\n  }\n  return format;\n}\nexport { parseFormat };", "map": {"version": 3, "names": ["padZero", "parseFormat", "format", "currentTime", "days", "hours", "minutes", "seconds", "milliseconds", "includes", "replace", "ms", "slice", "char<PERSON>t"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/count-down/utils.mjs"], "sourcesContent": ["import { pad<PERSON>ero } from \"../utils/index.mjs\";\nfunction parseFormat(format, currentTime) {\n  const { days } = currentTime;\n  let { hours, minutes, seconds, milliseconds } = currentTime;\n  if (format.includes(\"DD\")) {\n    format = format.replace(\"DD\", pad<PERSON>ero(days));\n  } else {\n    hours += days * 24;\n  }\n  if (format.includes(\"HH\")) {\n    format = format.replace(\"HH\", pad<PERSON>ero(hours));\n  } else {\n    minutes += hours * 60;\n  }\n  if (format.includes(\"mm\")) {\n    format = format.replace(\"mm\", padZero(minutes));\n  } else {\n    seconds += minutes * 60;\n  }\n  if (format.includes(\"ss\")) {\n    format = format.replace(\"ss\", padZero(seconds));\n  } else {\n    milliseconds += seconds * 1e3;\n  }\n  if (format.includes(\"S\")) {\n    const ms = padZero(milliseconds, 3);\n    if (format.includes(\"SSS\")) {\n      format = format.replace(\"SSS\", ms);\n    } else if (format.includes(\"SS\")) {\n      format = format.replace(\"SS\", ms.slice(0, 2));\n    } else {\n      format = format.replace(\"S\", ms.charAt(0));\n    }\n  }\n  return format;\n}\nexport {\n  parseFormat\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,WAAWA,CAACC,MAAM,EAAEC,WAAW,EAAE;EACxC,MAAM;IAAEC;EAAK,CAAC,GAAGD,WAAW;EAC5B,IAAI;IAAEE,KAAK;IAAEC,OAAO;IAAEC,OAAO;IAAEC;EAAa,CAAC,GAAGL,WAAW;EAC3D,IAAID,MAAM,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAE;IACzBP,MAAM,GAAGA,MAAM,CAACQ,OAAO,CAAC,IAAI,EAAEV,OAAO,CAACI,IAAI,CAAC,CAAC;EAC9C,CAAC,MAAM;IACLC,KAAK,IAAID,IAAI,GAAG,EAAE;EACpB;EACA,IAAIF,MAAM,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAE;IACzBP,MAAM,GAAGA,MAAM,CAACQ,OAAO,CAAC,IAAI,EAAEV,OAAO,CAACK,KAAK,CAAC,CAAC;EAC/C,CAAC,MAAM;IACLC,OAAO,IAAID,KAAK,GAAG,EAAE;EACvB;EACA,IAAIH,MAAM,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAE;IACzBP,MAAM,GAAGA,MAAM,CAACQ,OAAO,CAAC,IAAI,EAAEV,OAAO,CAACM,OAAO,CAAC,CAAC;EACjD,CAAC,MAAM;IACLC,OAAO,IAAID,OAAO,GAAG,EAAE;EACzB;EACA,IAAIJ,MAAM,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAE;IACzBP,MAAM,GAAGA,MAAM,CAACQ,OAAO,CAAC,IAAI,EAAEV,OAAO,CAACO,OAAO,CAAC,CAAC;EACjD,CAAC,MAAM;IACLC,YAAY,IAAID,OAAO,GAAG,GAAG;EAC/B;EACA,IAAIL,MAAM,CAACO,QAAQ,CAAC,GAAG,CAAC,EAAE;IACxB,MAAME,EAAE,GAAGX,OAAO,CAACQ,YAAY,EAAE,CAAC,CAAC;IACnC,IAAIN,MAAM,CAACO,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1BP,MAAM,GAAGA,MAAM,CAACQ,OAAO,CAAC,KAAK,EAAEC,EAAE,CAAC;IACpC,CAAC,MAAM,IAAIT,MAAM,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAE;MAChCP,MAAM,GAAGA,MAAM,CAACQ,OAAO,CAAC,IAAI,EAAEC,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC,MAAM;MACLV,MAAM,GAAGA,MAAM,CAACQ,OAAO,CAAC,GAAG,EAAEC,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5C;EACF;EACA,OAAOX,MAAM;AACf;AACA,SACED,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}