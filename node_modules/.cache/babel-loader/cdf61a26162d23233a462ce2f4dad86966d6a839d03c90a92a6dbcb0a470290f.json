{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Checkbox from \"./Checkbox.mjs\";\nconst Checkbox = withInstall(_Checkbox);\nvar stdin_default = Checkbox;\nimport { checkboxProps } from \"./Checkbox.mjs\";\nexport { Checkbox, checkboxProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Checkbox", "Checkbox", "stdin_default", "checkboxProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/checkbox/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Checkbox from \"./Checkbox.mjs\";\nconst Checkbox = withInstall(_Checkbox);\nvar stdin_default = Checkbox;\nimport { checkboxProps } from \"./Checkbox.mjs\";\nexport {\n  Checkbox,\n  checkboxProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SAASE,aAAa,QAAQ,gBAAgB;AAC9C,SACEF,QAAQ,EACRE,aAAa,EACbD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}