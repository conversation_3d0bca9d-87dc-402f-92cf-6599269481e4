{"ast": null, "code": "import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, createNamespace } from \"../utils/index.mjs\";\nimport { ACTION_BAR_KEY } from \"../action-bar/ActionBar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Button } from \"../button/index.mjs\";\nconst [name, bem] = createNamespace(\"action-bar-button\");\nconst actionBarButtonProps = extend({}, routeProps, {\n  type: String,\n  text: String,\n  icon: String,\n  color: String,\n  loading: Boolean,\n  disabled: Boolean\n});\nvar stdin_default = defineComponent({\n  name,\n  props: actionBarButtonProps,\n  setup(props, {\n    slots\n  }) {\n    const route = useRoute();\n    const {\n      parent,\n      index\n    } = useParent(ACTION_BAR_KEY);\n    const isFirst = computed(() => {\n      if (parent) {\n        const prev = parent.children[index.value - 1];\n        return !(prev && \"isButton\" in prev);\n      }\n    });\n    const isLast = computed(() => {\n      if (parent) {\n        const next = parent.children[index.value + 1];\n        return !(next && \"isButton\" in next);\n      }\n    });\n    useExpose({\n      isButton: true\n    });\n    return () => {\n      const {\n        type,\n        icon,\n        text,\n        color,\n        loading,\n        disabled\n      } = props;\n      return _createVNode(Button, {\n        \"class\": bem([type, {\n          last: isLast.value,\n          first: isFirst.value\n        }]),\n        \"size\": \"large\",\n        \"type\": type,\n        \"icon\": icon,\n        \"color\": color,\n        \"loading\": loading,\n        \"disabled\": disabled,\n        \"onClick\": route\n      }, {\n        default: () => [slots.default ? slots.default() : text]\n      });\n    };\n  }\n});\nexport { actionBarButtonProps, stdin_default as default };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "extend", "createNamespace", "ACTION_BAR_KEY", "useParent", "useExpose", "useRoute", "routeProps", "<PERSON><PERSON>", "name", "bem", "actionBarButtonProps", "type", "String", "text", "icon", "color", "loading", "Boolean", "disabled", "stdin_default", "props", "setup", "slots", "route", "parent", "index", "<PERSON><PERSON><PERSON><PERSON>", "prev", "children", "value", "isLast", "next", "isButton", "last", "first", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/action-bar-button/ActionBarButton.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, createNamespace } from \"../utils/index.mjs\";\nimport { ACTION_BAR_KEY } from \"../action-bar/ActionBar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Button } from \"../button/index.mjs\";\nconst [name, bem] = createNamespace(\"action-bar-button\");\nconst actionBarButtonProps = extend({}, routeProps, {\n  type: String,\n  text: String,\n  icon: String,\n  color: String,\n  loading: Boolean,\n  disabled: Boolean\n});\nvar stdin_default = defineComponent({\n  name,\n  props: actionBarButtonProps,\n  setup(props, {\n    slots\n  }) {\n    const route = useRoute();\n    const {\n      parent,\n      index\n    } = useParent(ACTION_BAR_KEY);\n    const isFirst = computed(() => {\n      if (parent) {\n        const prev = parent.children[index.value - 1];\n        return !(prev && \"isButton\" in prev);\n      }\n    });\n    const isLast = computed(() => {\n      if (parent) {\n        const next = parent.children[index.value + 1];\n        return !(next && \"isButton\" in next);\n      }\n    });\n    useExpose({\n      isButton: true\n    });\n    return () => {\n      const {\n        type,\n        icon,\n        text,\n        color,\n        loading,\n        disabled\n      } = props;\n      return _createVNode(Button, {\n        \"class\": bem([type, {\n          last: isLast.value,\n          first: isFirst.value\n        }]),\n        \"size\": \"large\",\n        \"type\": type,\n        \"icon\": icon,\n        \"color\": color,\n        \"loading\": loading,\n        \"disabled\": disabled,\n        \"onClick\": route\n      }, {\n        default: () => [slots.default ? slots.default() : text]\n      });\n    };\n  }\n});\nexport {\n  actionBarButtonProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,MAAM,EAAEC,eAAe,QAAQ,oBAAoB;AAC5D,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,QAAQ,EAAEC,UAAU,QAAQ,8BAA8B;AACnE,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGR,eAAe,CAAC,mBAAmB,CAAC;AACxD,MAAMS,oBAAoB,GAAGV,MAAM,CAAC,CAAC,CAAC,EAAEM,UAAU,EAAE;EAClDK,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAED,MAAM;EACZE,IAAI,EAAEF,MAAM;EACZG,KAAK,EAAEH,MAAM;EACbI,OAAO,EAAEC,OAAO;EAChBC,QAAQ,EAAED;AACZ,CAAC,CAAC;AACF,IAAIE,aAAa,GAAGtB,eAAe,CAAC;EAClCW,IAAI;EACJY,KAAK,EAAEV,oBAAoB;EAC3BW,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGlB,QAAQ,CAAC,CAAC;IACxB,MAAM;MACJmB,MAAM;MACNC;IACF,CAAC,GAAGtB,SAAS,CAACD,cAAc,CAAC;IAC7B,MAAMwB,OAAO,GAAG9B,QAAQ,CAAC,MAAM;MAC7B,IAAI4B,MAAM,EAAE;QACV,MAAMG,IAAI,GAAGH,MAAM,CAACI,QAAQ,CAACH,KAAK,CAACI,KAAK,GAAG,CAAC,CAAC;QAC7C,OAAO,EAAEF,IAAI,IAAI,UAAU,IAAIA,IAAI,CAAC;MACtC;IACF,CAAC,CAAC;IACF,MAAMG,MAAM,GAAGlC,QAAQ,CAAC,MAAM;MAC5B,IAAI4B,MAAM,EAAE;QACV,MAAMO,IAAI,GAAGP,MAAM,CAACI,QAAQ,CAACH,KAAK,CAACI,KAAK,GAAG,CAAC,CAAC;QAC7C,OAAO,EAAEE,IAAI,IAAI,UAAU,IAAIA,IAAI,CAAC;MACtC;IACF,CAAC,CAAC;IACF3B,SAAS,CAAC;MACR4B,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,OAAO,MAAM;MACX,MAAM;QACJrB,IAAI;QACJG,IAAI;QACJD,IAAI;QACJE,KAAK;QACLC,OAAO;QACPE;MACF,CAAC,GAAGE,KAAK;MACT,OAAOrB,YAAY,CAACQ,MAAM,EAAE;QAC1B,OAAO,EAAEE,GAAG,CAAC,CAACE,IAAI,EAAE;UAClBsB,IAAI,EAAEH,MAAM,CAACD,KAAK;UAClBK,KAAK,EAAER,OAAO,CAACG;QACjB,CAAC,CAAC,CAAC;QACH,MAAM,EAAE,OAAO;QACf,MAAM,EAAElB,IAAI;QACZ,MAAM,EAAEG,IAAI;QACZ,OAAO,EAAEC,KAAK;QACd,SAAS,EAAEC,OAAO;QAClB,UAAU,EAAEE,QAAQ;QACpB,SAAS,EAAEK;MACb,CAAC,EAAE;QACDY,OAAO,EAAEA,CAAA,KAAM,CAACb,KAAK,CAACa,OAAO,GAAGb,KAAK,CAACa,OAAO,CAAC,CAAC,GAAGtB,IAAI;MACxD,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEH,oBAAoB,EACpBS,aAAa,IAAIgB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}