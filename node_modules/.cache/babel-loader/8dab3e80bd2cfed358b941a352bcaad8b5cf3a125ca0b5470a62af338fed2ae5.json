{"ast": null, "code": "import { ref, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, numericProp, getZIndexStyle, createNamespace, callInterceptor, makeNumericProp, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nconst [name, bem] = createNamespace(\"tabbar\");\nconst tabbarProps = {\n  route: Boolean,\n  fixed: truthProp,\n  border: truthProp,\n  zIndex: numericProp,\n  placeholder: Boolean,\n  activeColor: String,\n  beforeChange: Function,\n  inactiveColor: String,\n  modelValue: makeNumericProp(0),\n  safeAreaInsetBottom: {\n    type: Boolean,\n    default: null\n  }\n};\nconst TABBAR_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: tabbarProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const {\n      linkChildren\n    } = useChildren(TABBAR_KEY);\n    const renderPlaceholder = usePlaceholder(root, bem);\n    const enableSafeArea = () => {\n      var _a;\n      return (_a = props.safeAreaInsetBottom) != null ? _a : props.fixed;\n    };\n    const renderTabbar = () => {\n      var _a;\n      const {\n        fixed,\n        zIndex,\n        border\n      } = props;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"role\": \"tablist\",\n        \"style\": getZIndexStyle(zIndex),\n        \"class\": [bem({\n          fixed\n        }), {\n          [BORDER_TOP_BOTTOM]: border,\n          \"van-safe-area-bottom\": enableSafeArea()\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n    const setActive = (active, afterChange) => {\n      callInterceptor(props.beforeChange, {\n        args: [active],\n        done() {\n          emit(\"update:modelValue\", active);\n          emit(\"change\", active);\n          afterChange();\n        }\n      });\n    };\n    linkChildren({\n      props,\n      setActive\n    });\n    return () => {\n      if (props.fixed && props.placeholder) {\n        return renderPlaceholder(renderTabbar);\n      }\n      return renderTabbar();\n    };\n  }\n});\nexport { TABBAR_KEY, stdin_default as default, tabbarProps };", "map": {"version": 3, "names": ["ref", "defineComponent", "createVNode", "_createVNode", "truthProp", "numericProp", "getZIndexStyle", "createNamespace", "callInterceptor", "makeNumericProp", "BORDER_TOP_BOTTOM", "useChildren", "usePlaceholder", "name", "bem", "tabbarProps", "route", "Boolean", "fixed", "border", "zIndex", "placeholder", "activeColor", "String", "beforeChange", "Function", "inactiveColor", "modelValue", "safeAreaInsetBottom", "type", "default", "TABBAR_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "emit", "slots", "root", "linkChildren", "renderPlaceholder", "enableSafeArea", "_a", "renderTabbar", "call", "setActive", "active", "afterChange", "args", "done"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tabbar/Tabbar.mjs"], "sourcesContent": ["import { ref, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, numericProp, getZIndexStyle, createNamespace, callInterceptor, makeNumericProp, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nconst [name, bem] = createNamespace(\"tabbar\");\nconst tabbarProps = {\n  route: Boolean,\n  fixed: truthProp,\n  border: truthProp,\n  zIndex: numericProp,\n  placeholder: Boolean,\n  activeColor: String,\n  beforeChange: Function,\n  inactiveColor: String,\n  modelValue: makeNumericProp(0),\n  safeAreaInsetBottom: {\n    type: Boolean,\n    default: null\n  }\n};\nconst TABBAR_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: tabbarProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const {\n      linkChildren\n    } = useChildren(TABBAR_KEY);\n    const renderPlaceholder = usePlaceholder(root, bem);\n    const enableSafeArea = () => {\n      var _a;\n      return (_a = props.safeAreaInsetBottom) != null ? _a : props.fixed;\n    };\n    const renderTabbar = () => {\n      var _a;\n      const {\n        fixed,\n        zIndex,\n        border\n      } = props;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"role\": \"tablist\",\n        \"style\": getZIndexStyle(zIndex),\n        \"class\": [bem({\n          fixed\n        }), {\n          [BORDER_TOP_BOTTOM]: border,\n          \"van-safe-area-bottom\": enableSafeArea()\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n    const setActive = (active, afterChange) => {\n      callInterceptor(props.beforeChange, {\n        args: [active],\n        done() {\n          emit(\"update:modelValue\", active);\n          emit(\"change\", active);\n          afterChange();\n        }\n      });\n    };\n    linkChildren({\n      props,\n      setActive\n    });\n    return () => {\n      if (props.fixed && props.placeholder) {\n        return renderPlaceholder(renderTabbar);\n      }\n      return renderTabbar();\n    };\n  }\n});\nexport {\n  TABBAR_KEY,\n  stdin_default as default,\n  tabbarProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACvE,SAASC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,oBAAoB;AACjJ,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,cAAc,QAAQ,oCAAoC;AACnE,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGP,eAAe,CAAC,QAAQ,CAAC;AAC7C,MAAMQ,WAAW,GAAG;EAClBC,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAEd,SAAS;EAChBe,MAAM,EAAEf,SAAS;EACjBgB,MAAM,EAAEf,WAAW;EACnBgB,WAAW,EAAEJ,OAAO;EACpBK,WAAW,EAAEC,MAAM;EACnBC,YAAY,EAAEC,QAAQ;EACtBC,aAAa,EAAEH,MAAM;EACrBI,UAAU,EAAElB,eAAe,CAAC,CAAC,CAAC;EAC9BmB,mBAAmB,EAAE;IACnBC,IAAI,EAAEZ,OAAO;IACba,OAAO,EAAE;EACX;AACF,CAAC;AACD,MAAMC,UAAU,GAAGC,MAAM,CAACnB,IAAI,CAAC;AAC/B,IAAIoB,aAAa,GAAGhC,eAAe,CAAC;EAClCY,IAAI;EACJqB,KAAK,EAAEnB,WAAW;EAClBoB,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAGvC,GAAG,CAAC,CAAC;IAClB,MAAM;MACJwC;IACF,CAAC,GAAG7B,WAAW,CAACoB,UAAU,CAAC;IAC3B,MAAMU,iBAAiB,GAAG7B,cAAc,CAAC2B,IAAI,EAAEzB,GAAG,CAAC;IACnD,MAAM4B,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIC,EAAE;MACN,OAAO,CAACA,EAAE,GAAGT,KAAK,CAACN,mBAAmB,KAAK,IAAI,GAAGe,EAAE,GAAGT,KAAK,CAAChB,KAAK;IACpE,CAAC;IACD,MAAM0B,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAID,EAAE;MACN,MAAM;QACJzB,KAAK;QACLE,MAAM;QACND;MACF,CAAC,GAAGe,KAAK;MACT,OAAO/B,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEoC,IAAI;QACX,MAAM,EAAE,SAAS;QACjB,OAAO,EAAEjC,cAAc,CAACc,MAAM,CAAC;QAC/B,OAAO,EAAE,CAACN,GAAG,CAAC;UACZI;QACF,CAAC,CAAC,EAAE;UACF,CAACR,iBAAiB,GAAGS,MAAM;UAC3B,sBAAsB,EAAEuB,cAAc,CAAC;QACzC,CAAC;MACH,CAAC,EAAE,CAAC,CAACC,EAAE,GAAGL,KAAK,CAACR,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGa,EAAE,CAACE,IAAI,CAACP,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;IACD,MAAMQ,SAAS,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAK;MACzCxC,eAAe,CAAC0B,KAAK,CAACV,YAAY,EAAE;QAClCyB,IAAI,EAAE,CAACF,MAAM,CAAC;QACdG,IAAIA,CAAA,EAAG;UACLb,IAAI,CAAC,mBAAmB,EAAEU,MAAM,CAAC;UACjCV,IAAI,CAAC,QAAQ,EAAEU,MAAM,CAAC;UACtBC,WAAW,CAAC,CAAC;QACf;MACF,CAAC,CAAC;IACJ,CAAC;IACDR,YAAY,CAAC;MACXN,KAAK;MACLY;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIZ,KAAK,CAAChB,KAAK,IAAIgB,KAAK,CAACb,WAAW,EAAE;QACpC,OAAOoB,iBAAiB,CAACG,YAAY,CAAC;MACxC;MACA,OAAOA,YAAY,CAAC,CAAC;IACvB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEb,UAAU,EACVE,aAAa,IAAIH,OAAO,EACxBf,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}