{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CollapseItem from \"./CollapseItem.mjs\";\nconst CollapseItem = withInstall(_CollapseItem);\nvar stdin_default = CollapseItem;\nimport { collapseItemProps } from \"./CollapseItem.mjs\";\nexport { CollapseItem, collapseItemProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CollapseItem", "CollapseItem", "stdin_default", "collapseItemProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/collapse-item/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CollapseItem from \"./CollapseItem.mjs\";\nconst CollapseItem = withInstall(_CollapseItem);\nvar stdin_default = CollapseItem;\nimport { collapseItemProps } from \"./CollapseItem.mjs\";\nexport {\n  CollapseItem,\n  collapseItemProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,MAAMC,YAAY,GAAGF,WAAW,CAACC,aAAa,CAAC;AAC/C,IAAIE,aAAa,GAAGD,YAAY;AAChC,SAASE,iBAAiB,QAAQ,oBAAoB;AACtD,SACEF,YAAY,EACZE,iBAAiB,EACjBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}