{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { t, bem, isImageFile } from \"./utils.mjs\";\nimport { isDef, extend, numericProp, getSizeStyle, callInterceptor, makeRequiredProp } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Image } from \"../image/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nvar stdin_default = defineComponent({\n  props: {\n    name: numericProp,\n    item: makeRequiredProp(Object),\n    index: Number,\n    imageFit: String,\n    lazyLoad: Boolean,\n    deletable: Boolean,\n    reupload: Boolean,\n    previewSize: [Number, String, Array],\n    beforeDelete: Function\n  },\n  emits: [\"delete\", \"preview\", \"reupload\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const renderMask = () => {\n      const {\n        status,\n        message\n      } = props.item;\n      if (status === \"uploading\" || status === \"failed\") {\n        const MaskIcon = status === \"failed\" ? _createVNode(Icon, {\n          \"name\": \"close\",\n          \"class\": bem(\"mask-icon\")\n        }, null) : _createVNode(Loading, {\n          \"class\": bem(\"loading\")\n        }, null);\n        const showMessage = isDef(message) && message !== \"\";\n        return _createVNode(\"div\", {\n          \"class\": bem(\"mask\")\n        }, [MaskIcon, showMessage && _createVNode(\"div\", {\n          \"class\": bem(\"mask-message\")\n        }, [message])]);\n      }\n    };\n    const onDelete = event => {\n      const {\n        name,\n        item,\n        index,\n        beforeDelete\n      } = props;\n      event.stopPropagation();\n      callInterceptor(beforeDelete, {\n        args: [item, {\n          name,\n          index\n        }],\n        done: () => emit(\"delete\")\n      });\n    };\n    const onPreview = () => emit(\"preview\");\n    const onReupload = () => emit(\"reupload\");\n    const renderDeleteIcon = () => {\n      if (props.deletable && props.item.status !== \"uploading\") {\n        const slot = slots[\"preview-delete\"];\n        return _createVNode(\"div\", {\n          \"role\": \"button\",\n          \"class\": bem(\"preview-delete\", {\n            shadow: !slot\n          }),\n          \"tabindex\": 0,\n          \"aria-label\": t(\"delete\"),\n          \"onClick\": onDelete\n        }, [slot ? slot() : _createVNode(Icon, {\n          \"name\": \"cross\",\n          \"class\": bem(\"preview-delete-icon\")\n        }, null)]);\n      }\n    };\n    const renderCover = () => {\n      if (slots[\"preview-cover\"]) {\n        const {\n          index,\n          item\n        } = props;\n        return _createVNode(\"div\", {\n          \"class\": bem(\"preview-cover\")\n        }, [slots[\"preview-cover\"](extend({\n          index\n        }, item))]);\n      }\n    };\n    const renderPreview = () => {\n      const {\n        item,\n        lazyLoad,\n        imageFit,\n        previewSize,\n        reupload\n      } = props;\n      if (isImageFile(item)) {\n        return _createVNode(Image, {\n          \"fit\": imageFit,\n          \"src\": item.objectUrl || item.content || item.url,\n          \"class\": bem(\"preview-image\"),\n          \"width\": Array.isArray(previewSize) ? previewSize[0] : previewSize,\n          \"height\": Array.isArray(previewSize) ? previewSize[1] : previewSize,\n          \"lazyLoad\": lazyLoad,\n          \"onClick\": reupload ? onReupload : onPreview\n        }, {\n          default: renderCover\n        });\n      }\n      return _createVNode(\"div\", {\n        \"class\": bem(\"file\"),\n        \"style\": getSizeStyle(props.previewSize)\n      }, [_createVNode(Icon, {\n        \"class\": bem(\"file-icon\"),\n        \"name\": \"description\"\n      }, null), _createVNode(\"div\", {\n        \"class\": [bem(\"file-name\"), \"van-ellipsis\"]\n      }, [item.file ? item.file.name : item.url]), renderCover()]);\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(\"preview\")\n    }, [renderPreview(), renderMask(), renderDeleteIcon()]);\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "t", "bem", "isImageFile", "isDef", "extend", "numericProp", "getSizeStyle", "callInterceptor", "makeRequiredProp", "Icon", "Image", "Loading", "stdin_default", "props", "name", "item", "Object", "index", "Number", "imageFit", "String", "lazyLoad", "Boolean", "deletable", "reupload", "previewSize", "Array", "beforeDelete", "Function", "emits", "setup", "emit", "slots", "renderMask", "status", "message", "MaskIcon", "showMessage", "onDelete", "event", "stopPropagation", "args", "done", "onPreview", "onReupload", "renderDeleteIcon", "slot", "shadow", "renderCover", "renderPreview", "objectUrl", "content", "url", "isArray", "default", "file"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/uploader/UploaderPreviewItem.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { t, bem, isImageFile } from \"./utils.mjs\";\nimport { isDef, extend, numericProp, getSizeStyle, callInterceptor, makeRequiredProp } from \"../utils/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Image } from \"../image/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nvar stdin_default = defineComponent({\n  props: {\n    name: numericProp,\n    item: makeRequiredProp(Object),\n    index: Number,\n    imageFit: String,\n    lazyLoad: Boolean,\n    deletable: Boolean,\n    reupload: Boolean,\n    previewSize: [Number, String, Array],\n    beforeDelete: Function\n  },\n  emits: [\"delete\", \"preview\", \"reupload\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const renderMask = () => {\n      const {\n        status,\n        message\n      } = props.item;\n      if (status === \"uploading\" || status === \"failed\") {\n        const MaskIcon = status === \"failed\" ? _createVNode(Icon, {\n          \"name\": \"close\",\n          \"class\": bem(\"mask-icon\")\n        }, null) : _createVNode(Loading, {\n          \"class\": bem(\"loading\")\n        }, null);\n        const showMessage = isDef(message) && message !== \"\";\n        return _createVNode(\"div\", {\n          \"class\": bem(\"mask\")\n        }, [MaskIcon, showMessage && _createVNode(\"div\", {\n          \"class\": bem(\"mask-message\")\n        }, [message])]);\n      }\n    };\n    const onDelete = (event) => {\n      const {\n        name,\n        item,\n        index,\n        beforeDelete\n      } = props;\n      event.stopPropagation();\n      callInterceptor(beforeDelete, {\n        args: [item, {\n          name,\n          index\n        }],\n        done: () => emit(\"delete\")\n      });\n    };\n    const onPreview = () => emit(\"preview\");\n    const onReupload = () => emit(\"reupload\");\n    const renderDeleteIcon = () => {\n      if (props.deletable && props.item.status !== \"uploading\") {\n        const slot = slots[\"preview-delete\"];\n        return _createVNode(\"div\", {\n          \"role\": \"button\",\n          \"class\": bem(\"preview-delete\", {\n            shadow: !slot\n          }),\n          \"tabindex\": 0,\n          \"aria-label\": t(\"delete\"),\n          \"onClick\": onDelete\n        }, [slot ? slot() : _createVNode(Icon, {\n          \"name\": \"cross\",\n          \"class\": bem(\"preview-delete-icon\")\n        }, null)]);\n      }\n    };\n    const renderCover = () => {\n      if (slots[\"preview-cover\"]) {\n        const {\n          index,\n          item\n        } = props;\n        return _createVNode(\"div\", {\n          \"class\": bem(\"preview-cover\")\n        }, [slots[\"preview-cover\"](extend({\n          index\n        }, item))]);\n      }\n    };\n    const renderPreview = () => {\n      const {\n        item,\n        lazyLoad,\n        imageFit,\n        previewSize,\n        reupload\n      } = props;\n      if (isImageFile(item)) {\n        return _createVNode(Image, {\n          \"fit\": imageFit,\n          \"src\": item.objectUrl || item.content || item.url,\n          \"class\": bem(\"preview-image\"),\n          \"width\": Array.isArray(previewSize) ? previewSize[0] : previewSize,\n          \"height\": Array.isArray(previewSize) ? previewSize[1] : previewSize,\n          \"lazyLoad\": lazyLoad,\n          \"onClick\": reupload ? onReupload : onPreview\n        }, {\n          default: renderCover\n        });\n      }\n      return _createVNode(\"div\", {\n        \"class\": bem(\"file\"),\n        \"style\": getSizeStyle(props.previewSize)\n      }, [_createVNode(Icon, {\n        \"class\": bem(\"file-icon\"),\n        \"name\": \"description\"\n      }, null), _createVNode(\"div\", {\n        \"class\": [bem(\"file-name\"), \"van-ellipsis\"]\n      }, [item.file ? item.file.name : item.url]), renderCover()]);\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(\"preview\")\n    }, [renderPreview(), renderMask(), renderDeleteIcon()]);\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,CAAC,EAAEC,GAAG,EAAEC,WAAW,QAAQ,aAAa;AACjD,SAASC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AAChH,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,IAAIC,aAAa,GAAGf,eAAe,CAAC;EAClCgB,KAAK,EAAE;IACLC,IAAI,EAAET,WAAW;IACjBU,IAAI,EAAEP,gBAAgB,CAACQ,MAAM,CAAC;IAC9BC,KAAK,EAAEC,MAAM;IACbC,QAAQ,EAAEC,MAAM;IAChBC,QAAQ,EAAEC,OAAO;IACjBC,SAAS,EAAED,OAAO;IAClBE,QAAQ,EAAEF,OAAO;IACjBG,WAAW,EAAE,CAACP,MAAM,EAAEE,MAAM,EAAEM,KAAK,CAAC;IACpCC,YAAY,EAAEC;EAChB,CAAC;EACDC,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;EACxCC,KAAKA,CAACjB,KAAK,EAAE;IACXkB,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAM;QACJC,MAAM;QACNC;MACF,CAAC,GAAGtB,KAAK,CAACE,IAAI;MACd,IAAImB,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,QAAQ,EAAE;QACjD,MAAME,QAAQ,GAAGF,MAAM,KAAK,QAAQ,GAAGnC,YAAY,CAACU,IAAI,EAAE;UACxD,MAAM,EAAE,OAAO;UACf,OAAO,EAAER,GAAG,CAAC,WAAW;QAC1B,CAAC,EAAE,IAAI,CAAC,GAAGF,YAAY,CAACY,OAAO,EAAE;UAC/B,OAAO,EAAEV,GAAG,CAAC,SAAS;QACxB,CAAC,EAAE,IAAI,CAAC;QACR,MAAMoC,WAAW,GAAGlC,KAAK,CAACgC,OAAO,CAAC,IAAIA,OAAO,KAAK,EAAE;QACpD,OAAOpC,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEE,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAACmC,QAAQ,EAAEC,WAAW,IAAItC,YAAY,CAAC,KAAK,EAAE;UAC/C,OAAO,EAAEE,GAAG,CAAC,cAAc;QAC7B,CAAC,EAAE,CAACkC,OAAO,CAAC,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;IACD,MAAMG,QAAQ,GAAIC,KAAK,IAAK;MAC1B,MAAM;QACJzB,IAAI;QACJC,IAAI;QACJE,KAAK;QACLU;MACF,CAAC,GAAGd,KAAK;MACT0B,KAAK,CAACC,eAAe,CAAC,CAAC;MACvBjC,eAAe,CAACoB,YAAY,EAAE;QAC5Bc,IAAI,EAAE,CAAC1B,IAAI,EAAE;UACXD,IAAI;UACJG;QACF,CAAC,CAAC;QACFyB,IAAI,EAAEA,CAAA,KAAMX,IAAI,CAAC,QAAQ;MAC3B,CAAC,CAAC;IACJ,CAAC;IACD,MAAMY,SAAS,GAAGA,CAAA,KAAMZ,IAAI,CAAC,SAAS,CAAC;IACvC,MAAMa,UAAU,GAAGA,CAAA,KAAMb,IAAI,CAAC,UAAU,CAAC;IACzC,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAIhC,KAAK,CAACU,SAAS,IAAIV,KAAK,CAACE,IAAI,CAACmB,MAAM,KAAK,WAAW,EAAE;QACxD,MAAMY,IAAI,GAAGd,KAAK,CAAC,gBAAgB,CAAC;QACpC,OAAOjC,YAAY,CAAC,KAAK,EAAE;UACzB,MAAM,EAAE,QAAQ;UAChB,OAAO,EAAEE,GAAG,CAAC,gBAAgB,EAAE;YAC7B8C,MAAM,EAAE,CAACD;UACX,CAAC,CAAC;UACF,UAAU,EAAE,CAAC;UACb,YAAY,EAAE9C,CAAC,CAAC,QAAQ,CAAC;UACzB,SAAS,EAAEsC;QACb,CAAC,EAAE,CAACQ,IAAI,GAAGA,IAAI,CAAC,CAAC,GAAG/C,YAAY,CAACU,IAAI,EAAE;UACrC,MAAM,EAAE,OAAO;UACf,OAAO,EAAER,GAAG,CAAC,qBAAqB;QACpC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,MAAM+C,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIhB,KAAK,CAAC,eAAe,CAAC,EAAE;QAC1B,MAAM;UACJf,KAAK;UACLF;QACF,CAAC,GAAGF,KAAK;QACT,OAAOd,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEE,GAAG,CAAC,eAAe;QAC9B,CAAC,EAAE,CAAC+B,KAAK,CAAC,eAAe,CAAC,CAAC5B,MAAM,CAAC;UAChCa;QACF,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAC,CAAC;MACb;IACF,CAAC;IACD,MAAMkC,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAM;QACJlC,IAAI;QACJM,QAAQ;QACRF,QAAQ;QACRM,WAAW;QACXD;MACF,CAAC,GAAGX,KAAK;MACT,IAAIX,WAAW,CAACa,IAAI,CAAC,EAAE;QACrB,OAAOhB,YAAY,CAACW,KAAK,EAAE;UACzB,KAAK,EAAES,QAAQ;UACf,KAAK,EAAEJ,IAAI,CAACmC,SAAS,IAAInC,IAAI,CAACoC,OAAO,IAAIpC,IAAI,CAACqC,GAAG;UACjD,OAAO,EAAEnD,GAAG,CAAC,eAAe,CAAC;UAC7B,OAAO,EAAEyB,KAAK,CAAC2B,OAAO,CAAC5B,WAAW,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW;UAClE,QAAQ,EAAEC,KAAK,CAAC2B,OAAO,CAAC5B,WAAW,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW;UACnE,UAAU,EAAEJ,QAAQ;UACpB,SAAS,EAAEG,QAAQ,GAAGoB,UAAU,GAAGD;QACrC,CAAC,EAAE;UACDW,OAAO,EAAEN;QACX,CAAC,CAAC;MACJ;MACA,OAAOjD,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEE,GAAG,CAAC,MAAM,CAAC;QACpB,OAAO,EAAEK,YAAY,CAACO,KAAK,CAACY,WAAW;MACzC,CAAC,EAAE,CAAC1B,YAAY,CAACU,IAAI,EAAE;QACrB,OAAO,EAAER,GAAG,CAAC,WAAW,CAAC;QACzB,MAAM,EAAE;MACV,CAAC,EAAE,IAAI,CAAC,EAAEF,YAAY,CAAC,KAAK,EAAE;QAC5B,OAAO,EAAE,CAACE,GAAG,CAAC,WAAW,CAAC,EAAE,cAAc;MAC5C,CAAC,EAAE,CAACc,IAAI,CAACwC,IAAI,GAAGxC,IAAI,CAACwC,IAAI,CAACzC,IAAI,GAAGC,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAEJ,WAAW,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,MAAMjD,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEE,GAAG,CAAC,SAAS;IACxB,CAAC,EAAE,CAACgD,aAAa,CAAC,CAAC,EAAEhB,UAAU,CAAC,CAAC,EAAEY,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACzD;AACF,CAAC,CAAC;AACF,SACEjC,aAAa,IAAI0C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}