{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Highlight from \"./Highlight.mjs\";\nconst Highlight = withInstall(_Highlight);\nvar stdin_default = Highlight;\nimport { highlightProps } from \"./Highlight.mjs\";\nexport { Highlight, stdin_default as default, highlightProps };", "map": {"version": 3, "names": ["withInstall", "_Highlight", "Highlight", "stdin_default", "highlightProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/highlight/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Highlight from \"./Highlight.mjs\";\nconst Highlight = withInstall(_Highlight);\nvar stdin_default = Highlight;\nimport { highlightProps } from \"./Highlight.mjs\";\nexport {\n  Highlight,\n  stdin_default as default,\n  highlightProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SAASE,cAAc,QAAQ,iBAAiB;AAChD,SACEF,SAAS,EACTC,aAAa,IAAIE,OAAO,EACxBD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}