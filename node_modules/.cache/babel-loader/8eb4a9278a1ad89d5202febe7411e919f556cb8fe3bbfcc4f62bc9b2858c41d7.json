{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Popover from \"./Popover.mjs\";\nconst Popover = withInstall(_Popover);\nvar stdin_default = Popover;\nimport { popoverProps } from \"./Popover.mjs\";\nexport { Popover, stdin_default as default, popoverProps };", "map": {"version": 3, "names": ["withInstall", "_Popover", "Popover", "stdin_default", "popoverProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/popover/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Popover from \"./Popover.mjs\";\nconst Popover = withInstall(_Popover);\nvar stdin_default = Popover;\nimport { popoverProps } from \"./Popover.mjs\";\nexport {\n  Popover,\n  stdin_default as default,\n  popoverProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,MAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SAASE,YAAY,QAAQ,eAAe;AAC5C,SACEF,OAAO,EACPC,aAAa,IAAIE,OAAO,EACxBD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}