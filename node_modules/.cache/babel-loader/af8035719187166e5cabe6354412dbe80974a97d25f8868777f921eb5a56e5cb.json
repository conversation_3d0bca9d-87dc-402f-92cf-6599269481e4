{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, watch, getCurrentInstance, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, isObject, inBrowser } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanToast from \"./Toast.mjs\";\nconst defaultOptions = {\n  icon: \"\",\n  type: \"text\",\n  message: \"\",\n  className: \"\",\n  overlay: false,\n  onClose: void 0,\n  onOpened: void 0,\n  duration: 2e3,\n  teleport: \"body\",\n  iconSize: void 0,\n  iconPrefix: void 0,\n  position: \"middle\",\n  transition: \"van-fade\",\n  forbidClick: false,\n  loadingType: void 0,\n  overlayClass: \"\",\n  overlayStyle: void 0,\n  closeOnClick: false,\n  closeOnClickOverlay: false\n};\nlet queue = [];\nlet allowMultiple = false;\nlet currentOptions = extend({}, defaultOptions);\nconst defaultOptionsMap = /* @__PURE__ */new Map();\nfunction parseOptions(message) {\n  if (isObject(message)) {\n    return message;\n  }\n  return {\n    message\n  };\n}\nfunction createInstance() {\n  const {\n    instance,\n    unmount\n  } = mountComponent({\n    setup() {\n      const message = ref(\"\");\n      const {\n        open,\n        state,\n        close,\n        toggle\n      } = usePopupState();\n      const onClosed = () => {\n        if (allowMultiple) {\n          queue = queue.filter(item => item !== instance);\n          unmount();\n        }\n      };\n      const render = () => {\n        const attrs = {\n          onClosed,\n          \"onUpdate:show\": toggle\n        };\n        return _createVNode(VanToast, _mergeProps(state, attrs), null);\n      };\n      watch(message, val => {\n        state.message = val;\n      });\n      getCurrentInstance().render = render;\n      return {\n        open,\n        close,\n        message\n      };\n    }\n  });\n  return instance;\n}\nfunction getInstance() {\n  if (!queue.length || allowMultiple) {\n    const instance = createInstance();\n    queue.push(instance);\n  }\n  return queue[queue.length - 1];\n}\nfunction showToast(options = {}) {\n  if (!inBrowser) {\n    return {};\n  }\n  const toast = getInstance();\n  const parsedOptions = parseOptions(options);\n  toast.open(extend({}, currentOptions, defaultOptionsMap.get(parsedOptions.type || currentOptions.type), parsedOptions));\n  return toast;\n}\nconst createMethod = type => options => showToast(extend({\n  type\n}, parseOptions(options)));\nconst showLoadingToast = createMethod(\"loading\");\nconst showSuccessToast = createMethod(\"success\");\nconst showFailToast = createMethod(\"fail\");\nconst closeToast = all => {\n  var _a;\n  if (queue.length) {\n    if (all) {\n      queue.forEach(toast => {\n        toast.close();\n      });\n      queue = [];\n    } else if (!allowMultiple) {\n      queue[0].close();\n    } else {\n      (_a = queue.shift()) == null ? void 0 : _a.close();\n    }\n  }\n};\nfunction setToastDefaultOptions(type, options) {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.set(type, options);\n  } else {\n    extend(currentOptions, type);\n  }\n}\nconst resetToastDefaultOptions = type => {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.delete(type);\n  } else {\n    currentOptions = extend({}, defaultOptions);\n    defaultOptionsMap.clear();\n  }\n};\nconst allowMultipleToast = (value = true) => {\n  allowMultiple = value;\n};\nexport { allowMultipleToast, closeToast, resetToastDefaultOptions, setToastDefaultOptions, showFailToast, showLoadingToast, showSuccessToast, showToast };", "map": {"version": 3, "names": ["ref", "watch", "getCurrentInstance", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "extend", "isObject", "inBrowser", "mountComponent", "usePopupState", "VanToast", "defaultOptions", "icon", "type", "message", "className", "overlay", "onClose", "onOpened", "duration", "teleport", "iconSize", "iconPrefix", "position", "transition", "forbidClick", "loadingType", "overlayClass", "overlayStyle", "closeOnClick", "closeOnClickOverlay", "queue", "allowMultiple", "currentOptions", "defaultOptionsMap", "Map", "parseOptions", "createInstance", "instance", "unmount", "setup", "open", "state", "close", "toggle", "onClosed", "filter", "item", "render", "attrs", "val", "getInstance", "length", "push", "showToast", "options", "toast", "parsedOptions", "get", "createMethod", "showLoadingToast", "showSuccessToast", "showFailToast", "closeToast", "all", "_a", "for<PERSON>ach", "shift", "setToastDefaultOptions", "set", "resetToastDefaultOptions", "delete", "clear", "allowMultipleToast", "value"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/toast/function-call.mjs"], "sourcesContent": ["import { ref, watch, getCurrentInstance, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, isObject, inBrowser } from \"../utils/index.mjs\";\nimport { mountComponent, usePopupState } from \"../utils/mount-component.mjs\";\nimport VanToast from \"./Toast.mjs\";\nconst defaultOptions = {\n  icon: \"\",\n  type: \"text\",\n  message: \"\",\n  className: \"\",\n  overlay: false,\n  onClose: void 0,\n  onOpened: void 0,\n  duration: 2e3,\n  teleport: \"body\",\n  iconSize: void 0,\n  iconPrefix: void 0,\n  position: \"middle\",\n  transition: \"van-fade\",\n  forbidClick: false,\n  loadingType: void 0,\n  overlayClass: \"\",\n  overlayStyle: void 0,\n  closeOnClick: false,\n  closeOnClickOverlay: false\n};\nlet queue = [];\nlet allowMultiple = false;\nlet currentOptions = extend({}, defaultOptions);\nconst defaultOptionsMap = /* @__PURE__ */ new Map();\nfunction parseOptions(message) {\n  if (isObject(message)) {\n    return message;\n  }\n  return {\n    message\n  };\n}\nfunction createInstance() {\n  const {\n    instance,\n    unmount\n  } = mountComponent({\n    setup() {\n      const message = ref(\"\");\n      const {\n        open,\n        state,\n        close,\n        toggle\n      } = usePopupState();\n      const onClosed = () => {\n        if (allowMultiple) {\n          queue = queue.filter((item) => item !== instance);\n          unmount();\n        }\n      };\n      const render = () => {\n        const attrs = {\n          onClosed,\n          \"onUpdate:show\": toggle\n        };\n        return _createVNode(VanToast, _mergeProps(state, attrs), null);\n      };\n      watch(message, (val) => {\n        state.message = val;\n      });\n      getCurrentInstance().render = render;\n      return {\n        open,\n        close,\n        message\n      };\n    }\n  });\n  return instance;\n}\nfunction getInstance() {\n  if (!queue.length || allowMultiple) {\n    const instance = createInstance();\n    queue.push(instance);\n  }\n  return queue[queue.length - 1];\n}\nfunction showToast(options = {}) {\n  if (!inBrowser) {\n    return {};\n  }\n  const toast = getInstance();\n  const parsedOptions = parseOptions(options);\n  toast.open(extend({}, currentOptions, defaultOptionsMap.get(parsedOptions.type || currentOptions.type), parsedOptions));\n  return toast;\n}\nconst createMethod = (type) => (options) => showToast(extend({\n  type\n}, parseOptions(options)));\nconst showLoadingToast = createMethod(\"loading\");\nconst showSuccessToast = createMethod(\"success\");\nconst showFailToast = createMethod(\"fail\");\nconst closeToast = (all) => {\n  var _a;\n  if (queue.length) {\n    if (all) {\n      queue.forEach((toast) => {\n        toast.close();\n      });\n      queue = [];\n    } else if (!allowMultiple) {\n      queue[0].close();\n    } else {\n      (_a = queue.shift()) == null ? void 0 : _a.close();\n    }\n  }\n};\nfunction setToastDefaultOptions(type, options) {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.set(type, options);\n  } else {\n    extend(currentOptions, type);\n  }\n}\nconst resetToastDefaultOptions = (type) => {\n  if (typeof type === \"string\") {\n    defaultOptionsMap.delete(type);\n  } else {\n    currentOptions = extend({}, defaultOptions);\n    defaultOptionsMap.clear();\n  }\n};\nconst allowMultipleToast = (value = true) => {\n  allowMultiple = value;\n};\nexport {\n  allowMultipleToast,\n  closeToast,\n  resetToastDefaultOptions,\n  setToastDefaultOptions,\n  showFailToast,\n  showLoadingToast,\n  showSuccessToast,\n  showToast\n};\n"], "mappings": ";;;;AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,kBAAkB,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5G,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AAChE,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,OAAOC,QAAQ,MAAM,aAAa;AAClC,MAAMC,cAAc,GAAG;EACrBC,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK,CAAC;EACfC,QAAQ,EAAE,KAAK,CAAC;EAChBC,QAAQ,EAAE,GAAG;EACbC,QAAQ,EAAE,MAAM;EAChBC,QAAQ,EAAE,KAAK,CAAC;EAChBC,UAAU,EAAE,KAAK,CAAC;EAClBC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,UAAU;EACtBC,WAAW,EAAE,KAAK;EAClBC,WAAW,EAAE,KAAK,CAAC;EACnBC,YAAY,EAAE,EAAE;EAChBC,YAAY,EAAE,KAAK,CAAC;EACpBC,YAAY,EAAE,KAAK;EACnBC,mBAAmB,EAAE;AACvB,CAAC;AACD,IAAIC,KAAK,GAAG,EAAE;AACd,IAAIC,aAAa,GAAG,KAAK;AACzB,IAAIC,cAAc,GAAG5B,MAAM,CAAC,CAAC,CAAC,EAAEM,cAAc,CAAC;AAC/C,MAAMuB,iBAAiB,GAAG,eAAgB,IAAIC,GAAG,CAAC,CAAC;AACnD,SAASC,YAAYA,CAACtB,OAAO,EAAE;EAC7B,IAAIR,QAAQ,CAACQ,OAAO,CAAC,EAAE;IACrB,OAAOA,OAAO;EAChB;EACA,OAAO;IACLA;EACF,CAAC;AACH;AACA,SAASuB,cAAcA,CAAA,EAAG;EACxB,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAG/B,cAAc,CAAC;IACjBgC,KAAKA,CAAA,EAAG;MACN,MAAM1B,OAAO,GAAGhB,GAAG,CAAC,EAAE,CAAC;MACvB,MAAM;QACJ2C,IAAI;QACJC,KAAK;QACLC,KAAK;QACLC;MACF,CAAC,GAAGnC,aAAa,CAAC,CAAC;MACnB,MAAMoC,QAAQ,GAAGA,CAAA,KAAM;QACrB,IAAIb,aAAa,EAAE;UACjBD,KAAK,GAAGA,KAAK,CAACe,MAAM,CAAEC,IAAI,IAAKA,IAAI,KAAKT,QAAQ,CAAC;UACjDC,OAAO,CAAC,CAAC;QACX;MACF,CAAC;MACD,MAAMS,MAAM,GAAGA,CAAA,KAAM;QACnB,MAAMC,KAAK,GAAG;UACZJ,QAAQ;UACR,eAAe,EAAED;QACnB,CAAC;QACD,OAAOxC,YAAY,CAACM,QAAQ,EAAER,WAAW,CAACwC,KAAK,EAAEO,KAAK,CAAC,EAAE,IAAI,CAAC;MAChE,CAAC;MACDlD,KAAK,CAACe,OAAO,EAAGoC,GAAG,IAAK;QACtBR,KAAK,CAAC5B,OAAO,GAAGoC,GAAG;MACrB,CAAC,CAAC;MACFlD,kBAAkB,CAAC,CAAC,CAACgD,MAAM,GAAGA,MAAM;MACpC,OAAO;QACLP,IAAI;QACJE,KAAK;QACL7B;MACF,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAOwB,QAAQ;AACjB;AACA,SAASa,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACpB,KAAK,CAACqB,MAAM,IAAIpB,aAAa,EAAE;IAClC,MAAMM,QAAQ,GAAGD,cAAc,CAAC,CAAC;IACjCN,KAAK,CAACsB,IAAI,CAACf,QAAQ,CAAC;EACtB;EACA,OAAOP,KAAK,CAACA,KAAK,CAACqB,MAAM,GAAG,CAAC,CAAC;AAChC;AACA,SAASE,SAASA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC/B,IAAI,CAAChD,SAAS,EAAE;IACd,OAAO,CAAC,CAAC;EACX;EACA,MAAMiD,KAAK,GAAGL,WAAW,CAAC,CAAC;EAC3B,MAAMM,aAAa,GAAGrB,YAAY,CAACmB,OAAO,CAAC;EAC3CC,KAAK,CAACf,IAAI,CAACpC,MAAM,CAAC,CAAC,CAAC,EAAE4B,cAAc,EAAEC,iBAAiB,CAACwB,GAAG,CAACD,aAAa,CAAC5C,IAAI,IAAIoB,cAAc,CAACpB,IAAI,CAAC,EAAE4C,aAAa,CAAC,CAAC;EACvH,OAAOD,KAAK;AACd;AACA,MAAMG,YAAY,GAAI9C,IAAI,IAAM0C,OAAO,IAAKD,SAAS,CAACjD,MAAM,CAAC;EAC3DQ;AACF,CAAC,EAAEuB,YAAY,CAACmB,OAAO,CAAC,CAAC,CAAC;AAC1B,MAAMK,gBAAgB,GAAGD,YAAY,CAAC,SAAS,CAAC;AAChD,MAAME,gBAAgB,GAAGF,YAAY,CAAC,SAAS,CAAC;AAChD,MAAMG,aAAa,GAAGH,YAAY,CAAC,MAAM,CAAC;AAC1C,MAAMI,UAAU,GAAIC,GAAG,IAAK;EAC1B,IAAIC,EAAE;EACN,IAAIlC,KAAK,CAACqB,MAAM,EAAE;IAChB,IAAIY,GAAG,EAAE;MACPjC,KAAK,CAACmC,OAAO,CAAEV,KAAK,IAAK;QACvBA,KAAK,CAACb,KAAK,CAAC,CAAC;MACf,CAAC,CAAC;MACFZ,KAAK,GAAG,EAAE;IACZ,CAAC,MAAM,IAAI,CAACC,aAAa,EAAE;MACzBD,KAAK,CAAC,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,CAACsB,EAAE,GAAGlC,KAAK,CAACoC,KAAK,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACtB,KAAK,CAAC,CAAC;IACpD;EACF;AACF,CAAC;AACD,SAASyB,sBAAsBA,CAACvD,IAAI,EAAE0C,OAAO,EAAE;EAC7C,IAAI,OAAO1C,IAAI,KAAK,QAAQ,EAAE;IAC5BqB,iBAAiB,CAACmC,GAAG,CAACxD,IAAI,EAAE0C,OAAO,CAAC;EACtC,CAAC,MAAM;IACLlD,MAAM,CAAC4B,cAAc,EAAEpB,IAAI,CAAC;EAC9B;AACF;AACA,MAAMyD,wBAAwB,GAAIzD,IAAI,IAAK;EACzC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BqB,iBAAiB,CAACqC,MAAM,CAAC1D,IAAI,CAAC;EAChC,CAAC,MAAM;IACLoB,cAAc,GAAG5B,MAAM,CAAC,CAAC,CAAC,EAAEM,cAAc,CAAC;IAC3CuB,iBAAiB,CAACsC,KAAK,CAAC,CAAC;EAC3B;AACF,CAAC;AACD,MAAMC,kBAAkB,GAAGA,CAACC,KAAK,GAAG,IAAI,KAAK;EAC3C1C,aAAa,GAAG0C,KAAK;AACvB,CAAC;AACD,SACED,kBAAkB,EAClBV,UAAU,EACVO,wBAAwB,EACxBF,sBAAsB,EACtBN,aAAa,EACbF,gBAAgB,EAChBC,gBAAgB,EAChBP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}