{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _TabbarItem from \"./TabbarItem.mjs\";\nconst TabbarItem = withInstall(_TabbarItem);\nvar stdin_default = TabbarItem;\nimport { tabbarItemProps } from \"./TabbarItem.mjs\";\nexport { TabbarItem, stdin_default as default, tabbarItemProps };", "map": {"version": 3, "names": ["withInstall", "_TabbarItem", "TabbarItem", "stdin_default", "tabbarItemProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tabbar-item/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _TabbarItem from \"./TabbarItem.mjs\";\nconst TabbarItem = withInstall(_TabbarItem);\nvar stdin_default = TabbarItem;\nimport { tabbarItemProps } from \"./TabbarItem.mjs\";\nexport {\n  TabbarItem,\n  stdin_default as default,\n  tabbarItemProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVC,aAAa,IAAIE,OAAO,EACxBD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}