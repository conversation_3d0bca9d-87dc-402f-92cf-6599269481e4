{"ast": null, "code": "import { truthProp, unknownProp, numericProp } from \"../utils/index.mjs\";\nconst popupSharedProps = {\n  // whether to show popup\n  show: Boolean,\n  // z-index\n  zIndex: numericProp,\n  // whether to show overlay\n  overlay: truthProp,\n  // transition duration\n  duration: numericProp,\n  // teleport\n  teleport: [String, Object],\n  // prevent body scroll\n  lockScroll: truthProp,\n  // whether to lazy render\n  lazyRender: truthProp,\n  // callback function before close\n  beforeClose: Function,\n  // overlay props\n  overlayProps: Object,\n  // overlay custom style\n  overlayStyle: Object,\n  // overlay custom class name\n  overlayClass: unknownProp,\n  // Initial rendering animation\n  transitionAppear: Boolean,\n  // whether to close popup when overlay is clicked\n  closeOnClickOverlay: truthProp\n};\nconst popupSharedPropKeys = Object.keys(popupSharedProps);\nexport { popupSharedPropKeys, popupSharedProps };", "map": {"version": 3, "names": ["truthProp", "unknownProp", "numericProp", "popupSharedProps", "show", "Boolean", "zIndex", "overlay", "duration", "teleport", "String", "Object", "lockScroll", "lazy<PERSON>ender", "beforeClose", "Function", "overlayProps", "overlayStyle", "overlayClass", "transitionAppear", "closeOnClickOverlay", "popupSharedPropKeys", "keys"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/popup/shared.mjs"], "sourcesContent": ["import { truthProp, unknownProp, numericProp } from \"../utils/index.mjs\";\nconst popupSharedProps = {\n  // whether to show popup\n  show: Boolean,\n  // z-index\n  zIndex: numericProp,\n  // whether to show overlay\n  overlay: truthProp,\n  // transition duration\n  duration: numericProp,\n  // teleport\n  teleport: [String, Object],\n  // prevent body scroll\n  lockScroll: truthProp,\n  // whether to lazy render\n  lazyRender: truthProp,\n  // callback function before close\n  beforeClose: Function,\n  // overlay props\n  overlayProps: Object,\n  // overlay custom style\n  overlayStyle: Object,\n  // overlay custom class name\n  overlayClass: unknownProp,\n  // Initial rendering animation\n  transitionAppear: Boolean,\n  // whether to close popup when overlay is clicked\n  closeOnClickOverlay: truthProp\n};\nconst popupSharedPropKeys = Object.keys(\n  popupSharedProps\n);\nexport {\n  popupSharedPropKeys,\n  popupSharedProps\n};\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,oBAAoB;AACxE,MAAMC,gBAAgB,GAAG;EACvB;EACAC,IAAI,EAAEC,OAAO;EACb;EACAC,MAAM,EAAEJ,WAAW;EACnB;EACAK,OAAO,EAAEP,SAAS;EAClB;EACAQ,QAAQ,EAAEN,WAAW;EACrB;EACAO,QAAQ,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;EAC1B;EACAC,UAAU,EAAEZ,SAAS;EACrB;EACAa,UAAU,EAAEb,SAAS;EACrB;EACAc,WAAW,EAAEC,QAAQ;EACrB;EACAC,YAAY,EAAEL,MAAM;EACpB;EACAM,YAAY,EAAEN,MAAM;EACpB;EACAO,YAAY,EAAEjB,WAAW;EACzB;EACAkB,gBAAgB,EAAEd,OAAO;EACzB;EACAe,mBAAmB,EAAEpB;AACvB,CAAC;AACD,MAAMqB,mBAAmB,GAAGV,MAAM,CAACW,IAAI,CACrCnB,gBACF,CAAC;AACD,SACEkB,mBAAmB,EACnBlB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}