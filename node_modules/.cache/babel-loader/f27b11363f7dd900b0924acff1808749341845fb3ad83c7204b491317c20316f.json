{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CellGroup from \"./CellGroup.mjs\";\nconst CellGroup = withInstall(_CellGroup);\nvar stdin_default = CellGroup;\nimport { cellGroupProps } from \"./CellGroup.mjs\";\nexport { CellGroup, cellGroupProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CellGroup", "CellGroup", "stdin_default", "cellGroupProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/cell-group/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CellGroup from \"./CellGroup.mjs\";\nconst CellGroup = withInstall(_CellGroup);\nvar stdin_default = CellGroup;\nimport { cellGroupProps } from \"./CellGroup.mjs\";\nexport {\n  CellGroup,\n  cellGroupProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SAASE,cAAc,QAAQ,iBAAiB;AAChD,SACEF,SAAS,EACTE,cAAc,EACdD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}