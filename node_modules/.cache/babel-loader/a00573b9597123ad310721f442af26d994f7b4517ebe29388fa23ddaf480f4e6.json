{"ast": null, "code": "let globalZIndex = 2e3;\nconst useGlobalZIndex = () => ++globalZIndex;\nconst setGlobalZIndex = val => {\n  globalZIndex = val;\n};\nexport { setGlobalZIndex, useGlobalZIndex };", "map": {"version": 3, "names": ["globalZIndex", "useGlobalZIndex", "setGlobalZIndex", "val"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/use-global-z-index.mjs"], "sourcesContent": ["let globalZIndex = 2e3;\nconst useGlobalZIndex = () => ++globalZIndex;\nconst setGlobalZIndex = (val) => {\n  globalZIndex = val;\n};\nexport {\n  setGlobalZIndex,\n  useGlobalZIndex\n};\n"], "mappings": "AAAA,IAAIA,YAAY,GAAG,GAAG;AACtB,MAAMC,eAAe,GAAGA,CAAA,KAAM,EAAED,YAAY;AAC5C,MAAME,eAAe,GAAIC,GAAG,IAAK;EAC/BH,YAAY,GAAGG,GAAG;AACpB,CAAC;AACD,SACED,eAAe,EACfD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}