{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, watch, provide, computed, nextTick, reactive, onMounted, defineComponent, createVNode as _createVNode, mergeProps as _mergeProps, createTextVNode as _createTextVNode } from \"vue\";\nimport { isDef, extend, addUnit, toArray, FORM_KEY, numericProp, unknownProp, resetScroll, formatNumber, preventDefault, makeStringProp, makeNumericProp, createNamespace, clamp } from \"../utils/index.mjs\";\nimport { cutString, runSyncRule, endComposing, mapInputType, isEmptyValue, startComposing, getRuleMessage, resizeTextarea, getStringLength, runRuleValidator } from \"./utils.mjs\";\nimport { cellSharedProps } from \"../cell/Cell.mjs\";\nimport { useParent, useEventListener, CUSTOM_FIELD_INJECTION_KEY } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem] = createNamespace(\"field\");\nconst fieldSharedProps = {\n  id: String,\n  name: String,\n  leftIcon: String,\n  rightIcon: String,\n  autofocus: Boolean,\n  clearable: Boolean,\n  maxlength: numericProp,\n  max: Number,\n  min: Number,\n  formatter: Function,\n  clearIcon: makeStringProp(\"clear\"),\n  modelValue: makeNumericProp(\"\"),\n  inputAlign: String,\n  placeholder: String,\n  autocomplete: String,\n  autocapitalize: String,\n  autocorrect: String,\n  errorMessage: String,\n  enterkeyhint: String,\n  clearTrigger: makeStringProp(\"focus\"),\n  formatTrigger: makeStringProp(\"onChange\"),\n  spellcheck: {\n    type: Boolean,\n    default: null\n  },\n  error: {\n    type: Boolean,\n    default: null\n  },\n  disabled: {\n    type: Boolean,\n    default: null\n  },\n  readonly: {\n    type: Boolean,\n    default: null\n  },\n  inputmode: String\n};\nconst fieldProps = extend({}, cellSharedProps, fieldSharedProps, {\n  rows: numericProp,\n  type: makeStringProp(\"text\"),\n  rules: Array,\n  autosize: [Boolean, Object],\n  labelWidth: numericProp,\n  labelClass: unknownProp,\n  labelAlign: String,\n  showWordLimit: Boolean,\n  errorMessageAlign: String,\n  colon: {\n    type: Boolean,\n    default: null\n  }\n});\nvar stdin_default = defineComponent({\n  name,\n  props: fieldProps,\n  emits: [\"blur\", \"focus\", \"clear\", \"keypress\", \"clickInput\", \"endValidate\", \"startValidate\", \"clickLeftIcon\", \"clickRightIcon\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const id = useId();\n    const state = reactive({\n      status: \"unvalidated\",\n      focused: false,\n      validateMessage: \"\"\n    });\n    const inputRef = ref();\n    const clearIconRef = ref();\n    const customValue = ref();\n    const {\n      parent: form\n    } = useParent(FORM_KEY);\n    const getModelValue = () => {\n      var _a;\n      return String((_a = props.modelValue) != null ? _a : \"\");\n    };\n    const getProp = key => {\n      if (isDef(props[key])) {\n        return props[key];\n      }\n      if (form && isDef(form.props[key])) {\n        return form.props[key];\n      }\n    };\n    const showClear = computed(() => {\n      const readonly = getProp(\"readonly\");\n      if (props.clearable && !readonly) {\n        const hasValue = getModelValue() !== \"\";\n        const trigger = props.clearTrigger === \"always\" || props.clearTrigger === \"focus\" && state.focused;\n        return hasValue && trigger;\n      }\n      return false;\n    });\n    const formValue = computed(() => {\n      if (customValue.value && slots.input) {\n        return customValue.value();\n      }\n      return props.modelValue;\n    });\n    const showRequiredMark = computed(() => {\n      var _a;\n      const required = getProp(\"required\");\n      if (required === \"auto\") {\n        return (_a = props.rules) == null ? void 0 : _a.some(rule => rule.required);\n      }\n      return required;\n    });\n    const runRules = rules => rules.reduce((promise, rule) => promise.then(() => {\n      if (state.status === \"failed\") {\n        return;\n      }\n      let {\n        value\n      } = formValue;\n      if (rule.formatter) {\n        value = rule.formatter(value, rule);\n      }\n      if (!runSyncRule(value, rule)) {\n        state.status = \"failed\";\n        state.validateMessage = getRuleMessage(value, rule);\n        return;\n      }\n      if (rule.validator) {\n        if (isEmptyValue(value) && rule.validateEmpty === false) {\n          return;\n        }\n        return runRuleValidator(value, rule).then(result => {\n          if (result && typeof result === \"string\") {\n            state.status = \"failed\";\n            state.validateMessage = result;\n          } else if (result === false) {\n            state.status = \"failed\";\n            state.validateMessage = getRuleMessage(value, rule);\n          }\n        });\n      }\n    }), Promise.resolve());\n    const resetValidation = () => {\n      state.status = \"unvalidated\";\n      state.validateMessage = \"\";\n    };\n    const endValidate = () => emit(\"endValidate\", {\n      status: state.status,\n      message: state.validateMessage\n    });\n    const validate = (rules = props.rules) => new Promise(resolve => {\n      resetValidation();\n      if (rules) {\n        emit(\"startValidate\");\n        runRules(rules).then(() => {\n          if (state.status === \"failed\") {\n            resolve({\n              name: props.name,\n              message: state.validateMessage\n            });\n            endValidate();\n          } else {\n            state.status = \"passed\";\n            resolve();\n            endValidate();\n          }\n        });\n      } else {\n        resolve();\n      }\n    });\n    const validateWithTrigger = trigger => {\n      if (form && props.rules) {\n        const {\n          validateTrigger\n        } = form.props;\n        const defaultTrigger = toArray(validateTrigger).includes(trigger);\n        const rules = props.rules.filter(rule => {\n          if (rule.trigger) {\n            return toArray(rule.trigger).includes(trigger);\n          }\n          return defaultTrigger;\n        });\n        if (rules.length) {\n          validate(rules);\n        }\n      }\n    };\n    const limitValueLength = value => {\n      var _a;\n      const {\n        maxlength\n      } = props;\n      if (isDef(maxlength) && getStringLength(value) > +maxlength) {\n        const modelValue = getModelValue();\n        if (modelValue && getStringLength(modelValue) === +maxlength) {\n          return modelValue;\n        }\n        const selectionEnd = (_a = inputRef.value) == null ? void 0 : _a.selectionEnd;\n        if (state.focused && selectionEnd) {\n          const valueArr = [...value];\n          const exceededLength = valueArr.length - +maxlength;\n          valueArr.splice(selectionEnd - exceededLength, exceededLength);\n          return valueArr.join(\"\");\n        }\n        return cutString(value, +maxlength);\n      }\n      return value;\n    };\n    const updateValue = (value, trigger = \"onChange\") => {\n      var _a, _b;\n      const originalValue = value;\n      value = limitValueLength(value);\n      const limitDiffLen = getStringLength(originalValue) - getStringLength(value);\n      if (props.type === \"number\" || props.type === \"digit\") {\n        const isNumber = props.type === \"number\";\n        value = formatNumber(value, isNumber, isNumber);\n        if (trigger === \"onBlur\" && value !== \"\" && (props.min !== void 0 || props.max !== void 0)) {\n          const adjustedValue = clamp(+value, (_a = props.min) != null ? _a : -Infinity, (_b = props.max) != null ? _b : Infinity);\n          if (+value !== adjustedValue) {\n            value = adjustedValue.toString();\n          }\n        }\n      }\n      let formatterDiffLen = 0;\n      if (props.formatter && trigger === props.formatTrigger) {\n        const {\n          formatter,\n          maxlength\n        } = props;\n        value = formatter(value);\n        if (isDef(maxlength) && getStringLength(value) > +maxlength) {\n          value = cutString(value, +maxlength);\n        }\n        if (inputRef.value && state.focused) {\n          const {\n            selectionEnd\n          } = inputRef.value;\n          const bcoVal = cutString(originalValue, selectionEnd);\n          formatterDiffLen = getStringLength(formatter(bcoVal)) - getStringLength(bcoVal);\n        }\n      }\n      if (inputRef.value && inputRef.value.value !== value) {\n        if (state.focused) {\n          let {\n            selectionStart,\n            selectionEnd\n          } = inputRef.value;\n          inputRef.value.value = value;\n          if (isDef(selectionStart) && isDef(selectionEnd)) {\n            const valueLen = getStringLength(value);\n            if (limitDiffLen) {\n              selectionStart -= limitDiffLen;\n              selectionEnd -= limitDiffLen;\n            } else if (formatterDiffLen) {\n              selectionStart += formatterDiffLen;\n              selectionEnd += formatterDiffLen;\n            }\n            inputRef.value.setSelectionRange(Math.min(selectionStart, valueLen), Math.min(selectionEnd, valueLen));\n          }\n        } else {\n          inputRef.value.value = value;\n        }\n      }\n      if (value !== props.modelValue) {\n        emit(\"update:modelValue\", value);\n      }\n    };\n    const onInput = event => {\n      if (!event.target.composing) {\n        updateValue(event.target.value);\n      }\n    };\n    const blur = () => {\n      var _a;\n      return (_a = inputRef.value) == null ? void 0 : _a.blur();\n    };\n    const focus = () => {\n      var _a;\n      return (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    const adjustTextareaSize = () => {\n      const input = inputRef.value;\n      if (props.type === \"textarea\" && props.autosize && input) {\n        resizeTextarea(input, props.autosize);\n      }\n    };\n    const onFocus = event => {\n      state.focused = true;\n      emit(\"focus\", event);\n      nextTick(adjustTextareaSize);\n      if (getProp(\"readonly\")) {\n        blur();\n      }\n    };\n    const onBlur = event => {\n      state.focused = false;\n      updateValue(getModelValue(), \"onBlur\");\n      emit(\"blur\", event);\n      if (getProp(\"readonly\")) {\n        return;\n      }\n      validateWithTrigger(\"onBlur\");\n      nextTick(adjustTextareaSize);\n      resetScroll();\n    };\n    const onClickInput = event => emit(\"clickInput\", event);\n    const onClickLeftIcon = event => emit(\"clickLeftIcon\", event);\n    const onClickRightIcon = event => emit(\"clickRightIcon\", event);\n    const onClear = event => {\n      preventDefault(event);\n      emit(\"update:modelValue\", \"\");\n      emit(\"clear\", event);\n    };\n    const showError = computed(() => {\n      if (typeof props.error === \"boolean\") {\n        return props.error;\n      }\n      if (form && form.props.showError && state.status === \"failed\") {\n        return true;\n      }\n    });\n    const labelStyle = computed(() => {\n      const labelWidth = getProp(\"labelWidth\");\n      const labelAlign = getProp(\"labelAlign\");\n      if (labelWidth && labelAlign !== \"top\") {\n        return {\n          width: addUnit(labelWidth)\n        };\n      }\n    });\n    const onKeypress = event => {\n      const ENTER_CODE = 13;\n      if (event.keyCode === ENTER_CODE) {\n        const submitOnEnter = form && form.props.submitOnEnter;\n        if (!submitOnEnter && props.type !== \"textarea\") {\n          preventDefault(event);\n        }\n        if (props.type === \"search\") {\n          blur();\n        }\n      }\n      emit(\"keypress\", event);\n    };\n    const getInputId = () => props.id || `${id}-input`;\n    const getValidationStatus = () => state.status;\n    const renderInput = () => {\n      const controlClass = bem(\"control\", [getProp(\"inputAlign\"), {\n        error: showError.value,\n        custom: !!slots.input,\n        \"min-height\": props.type === \"textarea\" && !props.autosize\n      }]);\n      if (slots.input) {\n        return _createVNode(\"div\", {\n          \"class\": controlClass,\n          \"onClick\": onClickInput\n        }, [slots.input()]);\n      }\n      const inputAttrs = {\n        id: getInputId(),\n        ref: inputRef,\n        name: props.name,\n        rows: props.rows !== void 0 ? +props.rows : void 0,\n        class: controlClass,\n        disabled: getProp(\"disabled\"),\n        readonly: getProp(\"readonly\"),\n        autofocus: props.autofocus,\n        placeholder: props.placeholder,\n        autocomplete: props.autocomplete,\n        autocapitalize: props.autocapitalize,\n        autocorrect: props.autocorrect,\n        enterkeyhint: props.enterkeyhint,\n        spellcheck: props.spellcheck,\n        \"aria-labelledby\": props.label ? `${id}-label` : void 0,\n        \"data-allow-mismatch\": \"attribute\",\n        onBlur,\n        onFocus,\n        onInput,\n        onClick: onClickInput,\n        onChange: endComposing,\n        onKeypress,\n        onCompositionend: endComposing,\n        onCompositionstart: startComposing\n      };\n      if (props.type === \"textarea\") {\n        return _createVNode(\"textarea\", _mergeProps(inputAttrs, {\n          \"inputmode\": props.inputmode\n        }), null);\n      }\n      return _createVNode(\"input\", _mergeProps(mapInputType(props.type, props.inputmode), inputAttrs), null);\n    };\n    const renderLeftIcon = () => {\n      const leftIconSlot = slots[\"left-icon\"];\n      if (props.leftIcon || leftIconSlot) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"left-icon\"),\n          \"onClick\": onClickLeftIcon\n        }, [leftIconSlot ? leftIconSlot() : _createVNode(Icon, {\n          \"name\": props.leftIcon,\n          \"classPrefix\": props.iconPrefix\n        }, null)]);\n      }\n    };\n    const renderRightIcon = () => {\n      const rightIconSlot = slots[\"right-icon\"];\n      if (props.rightIcon || rightIconSlot) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"right-icon\"),\n          \"onClick\": onClickRightIcon\n        }, [rightIconSlot ? rightIconSlot() : _createVNode(Icon, {\n          \"name\": props.rightIcon,\n          \"classPrefix\": props.iconPrefix\n        }, null)]);\n      }\n    };\n    const renderWordLimit = () => {\n      if (props.showWordLimit && props.maxlength) {\n        const count = getStringLength(getModelValue());\n        return _createVNode(\"div\", {\n          \"class\": bem(\"word-limit\")\n        }, [_createVNode(\"span\", {\n          \"class\": bem(\"word-num\")\n        }, [count]), _createTextVNode(\"/\"), props.maxlength]);\n      }\n    };\n    const renderMessage = () => {\n      if (form && form.props.showErrorMessage === false) {\n        return;\n      }\n      const message = props.errorMessage || state.validateMessage;\n      if (message) {\n        const slot = slots[\"error-message\"];\n        const errorMessageAlign = getProp(\"errorMessageAlign\");\n        return _createVNode(\"div\", {\n          \"class\": bem(\"error-message\", errorMessageAlign)\n        }, [slot ? slot({\n          message\n        }) : message]);\n      }\n    };\n    const renderLabel = () => {\n      const labelWidth = getProp(\"labelWidth\");\n      const labelAlign = getProp(\"labelAlign\");\n      const colon = getProp(\"colon\") ? \":\" : \"\";\n      if (slots.label) {\n        return [slots.label(), colon];\n      }\n      if (props.label) {\n        return _createVNode(\"label\", {\n          \"id\": `${id}-label`,\n          \"for\": slots.input ? void 0 : getInputId(),\n          \"data-allow-mismatch\": \"attribute\",\n          \"onClick\": event => {\n            preventDefault(event);\n            focus();\n          },\n          \"style\": labelAlign === \"top\" && labelWidth ? {\n            width: addUnit(labelWidth)\n          } : void 0\n        }, [props.label + colon]);\n      }\n    };\n    const renderFieldBody = () => [_createVNode(\"div\", {\n      \"class\": bem(\"body\")\n    }, [renderInput(), showClear.value && _createVNode(Icon, {\n      \"ref\": clearIconRef,\n      \"name\": props.clearIcon,\n      \"class\": bem(\"clear\")\n    }, null), renderRightIcon(), slots.button && _createVNode(\"div\", {\n      \"class\": bem(\"button\")\n    }, [slots.button()])]), renderWordLimit(), renderMessage()];\n    useExpose({\n      blur,\n      focus,\n      validate,\n      formValue,\n      resetValidation,\n      getValidationStatus\n    });\n    provide(CUSTOM_FIELD_INJECTION_KEY, {\n      customValue,\n      resetValidation,\n      validateWithTrigger\n    });\n    watch(() => props.modelValue, () => {\n      updateValue(getModelValue());\n      resetValidation();\n      validateWithTrigger(\"onChange\");\n      nextTick(adjustTextareaSize);\n    });\n    onMounted(() => {\n      updateValue(getModelValue(), props.formatTrigger);\n      nextTick(adjustTextareaSize);\n    });\n    useEventListener(\"touchstart\", onClear, {\n      target: computed(() => {\n        var _a;\n        return (_a = clearIconRef.value) == null ? void 0 : _a.$el;\n      })\n    });\n    return () => {\n      const disabled = getProp(\"disabled\");\n      const labelAlign = getProp(\"labelAlign\");\n      const LeftIcon = renderLeftIcon();\n      const renderTitle = () => {\n        const Label = renderLabel();\n        if (labelAlign === \"top\") {\n          return [LeftIcon, Label].filter(Boolean);\n        }\n        return Label || [];\n      };\n      return _createVNode(Cell, {\n        \"size\": props.size,\n        \"class\": bem({\n          error: showError.value,\n          disabled,\n          [`label-${labelAlign}`]: labelAlign\n        }),\n        \"center\": props.center,\n        \"border\": props.border,\n        \"isLink\": props.isLink,\n        \"clickable\": props.clickable,\n        \"titleStyle\": labelStyle.value,\n        \"valueClass\": bem(\"value\"),\n        \"titleClass\": [bem(\"label\", [labelAlign, {\n          required: showRequiredMark.value\n        }]), props.labelClass],\n        \"arrowDirection\": props.arrowDirection\n      }, {\n        icon: LeftIcon && labelAlign !== \"top\" ? () => LeftIcon : null,\n        title: renderTitle,\n        value: renderFieldBody,\n        extra: slots.extra\n      });\n    };\n  }\n});\nexport { stdin_default as default, fieldProps, fieldSharedProps };", "map": {"version": 3, "names": ["ref", "watch", "provide", "computed", "nextTick", "reactive", "onMounted", "defineComponent", "createVNode", "_createVNode", "mergeProps", "_mergeProps", "createTextVNode", "_createTextVNode", "isDef", "extend", "addUnit", "toArray", "FORM_KEY", "numericProp", "unknownProp", "resetScroll", "formatNumber", "preventDefault", "makeStringProp", "makeNumericProp", "createNamespace", "clamp", "cutString", "runSyncRule", "endComposing", "mapInputType", "isEmptyValue", "startComposing", "getRuleMessage", "resizeTextarea", "getStringLength", "runRuleValidator", "cellSharedProps", "useParent", "useEventListener", "CUSTOM_FIELD_INJECTION_KEY", "useId", "useExpose", "Icon", "Cell", "name", "bem", "fieldSharedProps", "id", "String", "leftIcon", "rightIcon", "autofocus", "Boolean", "clearable", "maxlength", "max", "Number", "min", "formatter", "Function", "clearIcon", "modelValue", "inputAlign", "placeholder", "autocomplete", "autocapitalize", "autocorrect", "errorMessage", "enterkeyhint", "clearTrigger", "formatTrigger", "spellcheck", "type", "default", "error", "disabled", "readonly", "inputmode", "fieldProps", "rows", "rules", "Array", "autosize", "Object", "labelWidth", "labelClass", "labelAlign", "showWordLimit", "errorMessageAlign", "colon", "stdin_default", "props", "emits", "setup", "emit", "slots", "state", "status", "focused", "validateMessage", "inputRef", "clearIconRef", "customValue", "parent", "form", "getModelValue", "_a", "getProp", "key", "showClear", "hasValue", "trigger", "formValue", "value", "input", "showRequiredMark", "required", "some", "rule", "runRules", "reduce", "promise", "then", "validator", "validateEmpty", "result", "Promise", "resolve", "resetValidation", "endValidate", "message", "validate", "validateWithTrigger", "validate<PERSON><PERSON>ger", "defaultTrigger", "includes", "filter", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectionEnd", "valueArr", "<PERSON><PERSON><PERSON><PERSON>", "splice", "join", "updateValue", "_b", "originalValue", "limitDiffLen", "isNumber", "adjustedValue", "Infinity", "toString", "formatterDiffLen", "bcoVal", "selectionStart", "valueLen", "setSelectionRange", "Math", "onInput", "event", "target", "composing", "blur", "focus", "adjustTextareaSize", "onFocus", "onBlur", "onClickInput", "onClickLeftIcon", "onClickRightIcon", "onClear", "showError", "labelStyle", "width", "onKeypress", "ENTER_CODE", "keyCode", "submitOnEnter", "getInputId", "getValidationStatus", "renderInput", "controlClass", "custom", "inputAttrs", "class", "label", "onClick", "onChange", "onCompositionend", "onCompositionstart", "renderLeftIcon", "leftIconSlot", "iconPrefix", "renderRightIcon", "rightIconSlot", "renderWordLimit", "count", "renderMessage", "showErrorMessage", "slot", "renderLabel", "renderFieldBody", "button", "$el", "LeftIcon", "renderTitle", "Label", "size", "center", "border", "isLink", "clickable", "arrowDirection", "icon", "title", "extra"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/field/Field.mjs"], "sourcesContent": ["import { ref, watch, provide, computed, nextTick, reactive, onMounted, defineComponent, createVNode as _createVNode, mergeProps as _mergeProps, createTextVNode as _createTextVNode } from \"vue\";\nimport { isDef, extend, addUnit, toArray, FORM_KEY, numericProp, unknownProp, resetScroll, formatNumber, preventDefault, makeStringProp, makeNumericProp, createNamespace, clamp } from \"../utils/index.mjs\";\nimport { cutString, runSyncRule, endComposing, mapInputType, isEmptyValue, startComposing, getRuleMessage, resizeTextarea, getStringLength, runRuleValidator } from \"./utils.mjs\";\nimport { cellSharedProps } from \"../cell/Cell.mjs\";\nimport { useParent, useEventListener, CUSTOM_FIELD_INJECTION_KEY } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem] = createNamespace(\"field\");\nconst fieldSharedProps = {\n  id: String,\n  name: String,\n  leftIcon: String,\n  rightIcon: String,\n  autofocus: Boolean,\n  clearable: Boolean,\n  maxlength: numericProp,\n  max: Number,\n  min: Number,\n  formatter: Function,\n  clearIcon: makeStringProp(\"clear\"),\n  modelValue: makeNumericProp(\"\"),\n  inputAlign: String,\n  placeholder: String,\n  autocomplete: String,\n  autocapitalize: String,\n  autocorrect: String,\n  errorMessage: String,\n  enterkeyhint: String,\n  clearTrigger: makeStringProp(\"focus\"),\n  formatTrigger: makeStringProp(\"onChange\"),\n  spellcheck: {\n    type: Boolean,\n    default: null\n  },\n  error: {\n    type: Boolean,\n    default: null\n  },\n  disabled: {\n    type: Boolean,\n    default: null\n  },\n  readonly: {\n    type: Boolean,\n    default: null\n  },\n  inputmode: String\n};\nconst fieldProps = extend({}, cellSharedProps, fieldSharedProps, {\n  rows: numericProp,\n  type: makeStringProp(\"text\"),\n  rules: Array,\n  autosize: [Boolean, Object],\n  labelWidth: numericProp,\n  labelClass: unknownProp,\n  labelAlign: String,\n  showWordLimit: Boolean,\n  errorMessageAlign: String,\n  colon: {\n    type: Boolean,\n    default: null\n  }\n});\nvar stdin_default = defineComponent({\n  name,\n  props: fieldProps,\n  emits: [\"blur\", \"focus\", \"clear\", \"keypress\", \"clickInput\", \"endValidate\", \"startValidate\", \"clickLeftIcon\", \"clickRightIcon\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const id = useId();\n    const state = reactive({\n      status: \"unvalidated\",\n      focused: false,\n      validateMessage: \"\"\n    });\n    const inputRef = ref();\n    const clearIconRef = ref();\n    const customValue = ref();\n    const {\n      parent: form\n    } = useParent(FORM_KEY);\n    const getModelValue = () => {\n      var _a;\n      return String((_a = props.modelValue) != null ? _a : \"\");\n    };\n    const getProp = (key) => {\n      if (isDef(props[key])) {\n        return props[key];\n      }\n      if (form && isDef(form.props[key])) {\n        return form.props[key];\n      }\n    };\n    const showClear = computed(() => {\n      const readonly = getProp(\"readonly\");\n      if (props.clearable && !readonly) {\n        const hasValue = getModelValue() !== \"\";\n        const trigger = props.clearTrigger === \"always\" || props.clearTrigger === \"focus\" && state.focused;\n        return hasValue && trigger;\n      }\n      return false;\n    });\n    const formValue = computed(() => {\n      if (customValue.value && slots.input) {\n        return customValue.value();\n      }\n      return props.modelValue;\n    });\n    const showRequiredMark = computed(() => {\n      var _a;\n      const required = getProp(\"required\");\n      if (required === \"auto\") {\n        return (_a = props.rules) == null ? void 0 : _a.some((rule) => rule.required);\n      }\n      return required;\n    });\n    const runRules = (rules) => rules.reduce((promise, rule) => promise.then(() => {\n      if (state.status === \"failed\") {\n        return;\n      }\n      let {\n        value\n      } = formValue;\n      if (rule.formatter) {\n        value = rule.formatter(value, rule);\n      }\n      if (!runSyncRule(value, rule)) {\n        state.status = \"failed\";\n        state.validateMessage = getRuleMessage(value, rule);\n        return;\n      }\n      if (rule.validator) {\n        if (isEmptyValue(value) && rule.validateEmpty === false) {\n          return;\n        }\n        return runRuleValidator(value, rule).then((result) => {\n          if (result && typeof result === \"string\") {\n            state.status = \"failed\";\n            state.validateMessage = result;\n          } else if (result === false) {\n            state.status = \"failed\";\n            state.validateMessage = getRuleMessage(value, rule);\n          }\n        });\n      }\n    }), Promise.resolve());\n    const resetValidation = () => {\n      state.status = \"unvalidated\";\n      state.validateMessage = \"\";\n    };\n    const endValidate = () => emit(\"endValidate\", {\n      status: state.status,\n      message: state.validateMessage\n    });\n    const validate = (rules = props.rules) => new Promise((resolve) => {\n      resetValidation();\n      if (rules) {\n        emit(\"startValidate\");\n        runRules(rules).then(() => {\n          if (state.status === \"failed\") {\n            resolve({\n              name: props.name,\n              message: state.validateMessage\n            });\n            endValidate();\n          } else {\n            state.status = \"passed\";\n            resolve();\n            endValidate();\n          }\n        });\n      } else {\n        resolve();\n      }\n    });\n    const validateWithTrigger = (trigger) => {\n      if (form && props.rules) {\n        const {\n          validateTrigger\n        } = form.props;\n        const defaultTrigger = toArray(validateTrigger).includes(trigger);\n        const rules = props.rules.filter((rule) => {\n          if (rule.trigger) {\n            return toArray(rule.trigger).includes(trigger);\n          }\n          return defaultTrigger;\n        });\n        if (rules.length) {\n          validate(rules);\n        }\n      }\n    };\n    const limitValueLength = (value) => {\n      var _a;\n      const {\n        maxlength\n      } = props;\n      if (isDef(maxlength) && getStringLength(value) > +maxlength) {\n        const modelValue = getModelValue();\n        if (modelValue && getStringLength(modelValue) === +maxlength) {\n          return modelValue;\n        }\n        const selectionEnd = (_a = inputRef.value) == null ? void 0 : _a.selectionEnd;\n        if (state.focused && selectionEnd) {\n          const valueArr = [...value];\n          const exceededLength = valueArr.length - +maxlength;\n          valueArr.splice(selectionEnd - exceededLength, exceededLength);\n          return valueArr.join(\"\");\n        }\n        return cutString(value, +maxlength);\n      }\n      return value;\n    };\n    const updateValue = (value, trigger = \"onChange\") => {\n      var _a, _b;\n      const originalValue = value;\n      value = limitValueLength(value);\n      const limitDiffLen = getStringLength(originalValue) - getStringLength(value);\n      if (props.type === \"number\" || props.type === \"digit\") {\n        const isNumber = props.type === \"number\";\n        value = formatNumber(value, isNumber, isNumber);\n        if (trigger === \"onBlur\" && value !== \"\" && (props.min !== void 0 || props.max !== void 0)) {\n          const adjustedValue = clamp(+value, (_a = props.min) != null ? _a : -Infinity, (_b = props.max) != null ? _b : Infinity);\n          if (+value !== adjustedValue) {\n            value = adjustedValue.toString();\n          }\n        }\n      }\n      let formatterDiffLen = 0;\n      if (props.formatter && trigger === props.formatTrigger) {\n        const {\n          formatter,\n          maxlength\n        } = props;\n        value = formatter(value);\n        if (isDef(maxlength) && getStringLength(value) > +maxlength) {\n          value = cutString(value, +maxlength);\n        }\n        if (inputRef.value && state.focused) {\n          const {\n            selectionEnd\n          } = inputRef.value;\n          const bcoVal = cutString(originalValue, selectionEnd);\n          formatterDiffLen = getStringLength(formatter(bcoVal)) - getStringLength(bcoVal);\n        }\n      }\n      if (inputRef.value && inputRef.value.value !== value) {\n        if (state.focused) {\n          let {\n            selectionStart,\n            selectionEnd\n          } = inputRef.value;\n          inputRef.value.value = value;\n          if (isDef(selectionStart) && isDef(selectionEnd)) {\n            const valueLen = getStringLength(value);\n            if (limitDiffLen) {\n              selectionStart -= limitDiffLen;\n              selectionEnd -= limitDiffLen;\n            } else if (formatterDiffLen) {\n              selectionStart += formatterDiffLen;\n              selectionEnd += formatterDiffLen;\n            }\n            inputRef.value.setSelectionRange(Math.min(selectionStart, valueLen), Math.min(selectionEnd, valueLen));\n          }\n        } else {\n          inputRef.value.value = value;\n        }\n      }\n      if (value !== props.modelValue) {\n        emit(\"update:modelValue\", value);\n      }\n    };\n    const onInput = (event) => {\n      if (!event.target.composing) {\n        updateValue(event.target.value);\n      }\n    };\n    const blur = () => {\n      var _a;\n      return (_a = inputRef.value) == null ? void 0 : _a.blur();\n    };\n    const focus = () => {\n      var _a;\n      return (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    const adjustTextareaSize = () => {\n      const input = inputRef.value;\n      if (props.type === \"textarea\" && props.autosize && input) {\n        resizeTextarea(input, props.autosize);\n      }\n    };\n    const onFocus = (event) => {\n      state.focused = true;\n      emit(\"focus\", event);\n      nextTick(adjustTextareaSize);\n      if (getProp(\"readonly\")) {\n        blur();\n      }\n    };\n    const onBlur = (event) => {\n      state.focused = false;\n      updateValue(getModelValue(), \"onBlur\");\n      emit(\"blur\", event);\n      if (getProp(\"readonly\")) {\n        return;\n      }\n      validateWithTrigger(\"onBlur\");\n      nextTick(adjustTextareaSize);\n      resetScroll();\n    };\n    const onClickInput = (event) => emit(\"clickInput\", event);\n    const onClickLeftIcon = (event) => emit(\"clickLeftIcon\", event);\n    const onClickRightIcon = (event) => emit(\"clickRightIcon\", event);\n    const onClear = (event) => {\n      preventDefault(event);\n      emit(\"update:modelValue\", \"\");\n      emit(\"clear\", event);\n    };\n    const showError = computed(() => {\n      if (typeof props.error === \"boolean\") {\n        return props.error;\n      }\n      if (form && form.props.showError && state.status === \"failed\") {\n        return true;\n      }\n    });\n    const labelStyle = computed(() => {\n      const labelWidth = getProp(\"labelWidth\");\n      const labelAlign = getProp(\"labelAlign\");\n      if (labelWidth && labelAlign !== \"top\") {\n        return {\n          width: addUnit(labelWidth)\n        };\n      }\n    });\n    const onKeypress = (event) => {\n      const ENTER_CODE = 13;\n      if (event.keyCode === ENTER_CODE) {\n        const submitOnEnter = form && form.props.submitOnEnter;\n        if (!submitOnEnter && props.type !== \"textarea\") {\n          preventDefault(event);\n        }\n        if (props.type === \"search\") {\n          blur();\n        }\n      }\n      emit(\"keypress\", event);\n    };\n    const getInputId = () => props.id || `${id}-input`;\n    const getValidationStatus = () => state.status;\n    const renderInput = () => {\n      const controlClass = bem(\"control\", [getProp(\"inputAlign\"), {\n        error: showError.value,\n        custom: !!slots.input,\n        \"min-height\": props.type === \"textarea\" && !props.autosize\n      }]);\n      if (slots.input) {\n        return _createVNode(\"div\", {\n          \"class\": controlClass,\n          \"onClick\": onClickInput\n        }, [slots.input()]);\n      }\n      const inputAttrs = {\n        id: getInputId(),\n        ref: inputRef,\n        name: props.name,\n        rows: props.rows !== void 0 ? +props.rows : void 0,\n        class: controlClass,\n        disabled: getProp(\"disabled\"),\n        readonly: getProp(\"readonly\"),\n        autofocus: props.autofocus,\n        placeholder: props.placeholder,\n        autocomplete: props.autocomplete,\n        autocapitalize: props.autocapitalize,\n        autocorrect: props.autocorrect,\n        enterkeyhint: props.enterkeyhint,\n        spellcheck: props.spellcheck,\n        \"aria-labelledby\": props.label ? `${id}-label` : void 0,\n        \"data-allow-mismatch\": \"attribute\",\n        onBlur,\n        onFocus,\n        onInput,\n        onClick: onClickInput,\n        onChange: endComposing,\n        onKeypress,\n        onCompositionend: endComposing,\n        onCompositionstart: startComposing\n      };\n      if (props.type === \"textarea\") {\n        return _createVNode(\"textarea\", _mergeProps(inputAttrs, {\n          \"inputmode\": props.inputmode\n        }), null);\n      }\n      return _createVNode(\"input\", _mergeProps(mapInputType(props.type, props.inputmode), inputAttrs), null);\n    };\n    const renderLeftIcon = () => {\n      const leftIconSlot = slots[\"left-icon\"];\n      if (props.leftIcon || leftIconSlot) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"left-icon\"),\n          \"onClick\": onClickLeftIcon\n        }, [leftIconSlot ? leftIconSlot() : _createVNode(Icon, {\n          \"name\": props.leftIcon,\n          \"classPrefix\": props.iconPrefix\n        }, null)]);\n      }\n    };\n    const renderRightIcon = () => {\n      const rightIconSlot = slots[\"right-icon\"];\n      if (props.rightIcon || rightIconSlot) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"right-icon\"),\n          \"onClick\": onClickRightIcon\n        }, [rightIconSlot ? rightIconSlot() : _createVNode(Icon, {\n          \"name\": props.rightIcon,\n          \"classPrefix\": props.iconPrefix\n        }, null)]);\n      }\n    };\n    const renderWordLimit = () => {\n      if (props.showWordLimit && props.maxlength) {\n        const count = getStringLength(getModelValue());\n        return _createVNode(\"div\", {\n          \"class\": bem(\"word-limit\")\n        }, [_createVNode(\"span\", {\n          \"class\": bem(\"word-num\")\n        }, [count]), _createTextVNode(\"/\"), props.maxlength]);\n      }\n    };\n    const renderMessage = () => {\n      if (form && form.props.showErrorMessage === false) {\n        return;\n      }\n      const message = props.errorMessage || state.validateMessage;\n      if (message) {\n        const slot = slots[\"error-message\"];\n        const errorMessageAlign = getProp(\"errorMessageAlign\");\n        return _createVNode(\"div\", {\n          \"class\": bem(\"error-message\", errorMessageAlign)\n        }, [slot ? slot({\n          message\n        }) : message]);\n      }\n    };\n    const renderLabel = () => {\n      const labelWidth = getProp(\"labelWidth\");\n      const labelAlign = getProp(\"labelAlign\");\n      const colon = getProp(\"colon\") ? \":\" : \"\";\n      if (slots.label) {\n        return [slots.label(), colon];\n      }\n      if (props.label) {\n        return _createVNode(\"label\", {\n          \"id\": `${id}-label`,\n          \"for\": slots.input ? void 0 : getInputId(),\n          \"data-allow-mismatch\": \"attribute\",\n          \"onClick\": (event) => {\n            preventDefault(event);\n            focus();\n          },\n          \"style\": labelAlign === \"top\" && labelWidth ? {\n            width: addUnit(labelWidth)\n          } : void 0\n        }, [props.label + colon]);\n      }\n    };\n    const renderFieldBody = () => [_createVNode(\"div\", {\n      \"class\": bem(\"body\")\n    }, [renderInput(), showClear.value && _createVNode(Icon, {\n      \"ref\": clearIconRef,\n      \"name\": props.clearIcon,\n      \"class\": bem(\"clear\")\n    }, null), renderRightIcon(), slots.button && _createVNode(\"div\", {\n      \"class\": bem(\"button\")\n    }, [slots.button()])]), renderWordLimit(), renderMessage()];\n    useExpose({\n      blur,\n      focus,\n      validate,\n      formValue,\n      resetValidation,\n      getValidationStatus\n    });\n    provide(CUSTOM_FIELD_INJECTION_KEY, {\n      customValue,\n      resetValidation,\n      validateWithTrigger\n    });\n    watch(() => props.modelValue, () => {\n      updateValue(getModelValue());\n      resetValidation();\n      validateWithTrigger(\"onChange\");\n      nextTick(adjustTextareaSize);\n    });\n    onMounted(() => {\n      updateValue(getModelValue(), props.formatTrigger);\n      nextTick(adjustTextareaSize);\n    });\n    useEventListener(\"touchstart\", onClear, {\n      target: computed(() => {\n        var _a;\n        return (_a = clearIconRef.value) == null ? void 0 : _a.$el;\n      })\n    });\n    return () => {\n      const disabled = getProp(\"disabled\");\n      const labelAlign = getProp(\"labelAlign\");\n      const LeftIcon = renderLeftIcon();\n      const renderTitle = () => {\n        const Label = renderLabel();\n        if (labelAlign === \"top\") {\n          return [LeftIcon, Label].filter(Boolean);\n        }\n        return Label || [];\n      };\n      return _createVNode(Cell, {\n        \"size\": props.size,\n        \"class\": bem({\n          error: showError.value,\n          disabled,\n          [`label-${labelAlign}`]: labelAlign\n        }),\n        \"center\": props.center,\n        \"border\": props.border,\n        \"isLink\": props.isLink,\n        \"clickable\": props.clickable,\n        \"titleStyle\": labelStyle.value,\n        \"valueClass\": bem(\"value\"),\n        \"titleClass\": [bem(\"label\", [labelAlign, {\n          required: showRequiredMark.value\n        }]), props.labelClass],\n        \"arrowDirection\": props.arrowDirection\n      }, {\n        icon: LeftIcon && labelAlign !== \"top\" ? () => LeftIcon : null,\n        title: renderTitle,\n        value: renderFieldBody,\n        extra: slots.extra\n      });\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  fieldProps,\n  fieldSharedProps\n};\n"], "mappings": ";;;;AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,EAAEC,eAAe,IAAIC,gBAAgB,QAAQ,KAAK;AAChM,SAASC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,KAAK,QAAQ,oBAAoB;AAC5M,SAASC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,aAAa;AACjL,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,SAAS,EAAEC,gBAAgB,EAAEC,0BAA0B,QAAQ,WAAW;AACnF,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGrB,eAAe,CAAC,OAAO,CAAC;AAC5C,MAAMsB,gBAAgB,GAAG;EACvBC,EAAE,EAAEC,MAAM;EACVJ,IAAI,EAAEI,MAAM;EACZC,QAAQ,EAAED,MAAM;EAChBE,SAAS,EAAEF,MAAM;EACjBG,SAAS,EAAEC,OAAO;EAClBC,SAAS,EAAED,OAAO;EAClBE,SAAS,EAAErC,WAAW;EACtBsC,GAAG,EAAEC,MAAM;EACXC,GAAG,EAAED,MAAM;EACXE,SAAS,EAAEC,QAAQ;EACnBC,SAAS,EAAEtC,cAAc,CAAC,OAAO,CAAC;EAClCuC,UAAU,EAAEtC,eAAe,CAAC,EAAE,CAAC;EAC/BuC,UAAU,EAAEd,MAAM;EAClBe,WAAW,EAAEf,MAAM;EACnBgB,YAAY,EAAEhB,MAAM;EACpBiB,cAAc,EAAEjB,MAAM;EACtBkB,WAAW,EAAElB,MAAM;EACnBmB,YAAY,EAAEnB,MAAM;EACpBoB,YAAY,EAAEpB,MAAM;EACpBqB,YAAY,EAAE/C,cAAc,CAAC,OAAO,CAAC;EACrCgD,aAAa,EAAEhD,cAAc,CAAC,UAAU,CAAC;EACzCiD,UAAU,EAAE;IACVC,IAAI,EAAEpB,OAAO;IACbqB,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAE;IACLF,IAAI,EAAEpB,OAAO;IACbqB,OAAO,EAAE;EACX,CAAC;EACDE,QAAQ,EAAE;IACRH,IAAI,EAAEpB,OAAO;IACbqB,OAAO,EAAE;EACX,CAAC;EACDG,QAAQ,EAAE;IACRJ,IAAI,EAAEpB,OAAO;IACbqB,OAAO,EAAE;EACX,CAAC;EACDI,SAAS,EAAE7B;AACb,CAAC;AACD,MAAM8B,UAAU,GAAGjE,MAAM,CAAC,CAAC,CAAC,EAAEuB,eAAe,EAAEU,gBAAgB,EAAE;EAC/DiC,IAAI,EAAE9D,WAAW;EACjBuD,IAAI,EAAElD,cAAc,CAAC,MAAM,CAAC;EAC5B0D,KAAK,EAAEC,KAAK;EACZC,QAAQ,EAAE,CAAC9B,OAAO,EAAE+B,MAAM,CAAC;EAC3BC,UAAU,EAAEnE,WAAW;EACvBoE,UAAU,EAAEnE,WAAW;EACvBoE,UAAU,EAAEtC,MAAM;EAClBuC,aAAa,EAAEnC,OAAO;EACtBoC,iBAAiB,EAAExC,MAAM;EACzByC,KAAK,EAAE;IACLjB,IAAI,EAAEpB,OAAO;IACbqB,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,IAAIiB,aAAa,GAAGrF,eAAe,CAAC;EAClCuC,IAAI;EACJ+C,KAAK,EAAEb,UAAU;EACjBc,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;EACnJC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMhD,EAAE,GAAGP,KAAK,CAAC,CAAC;IAClB,MAAMwD,KAAK,GAAG7F,QAAQ,CAAC;MACrB8F,MAAM,EAAE,aAAa;MACrBC,OAAO,EAAE,KAAK;MACdC,eAAe,EAAE;IACnB,CAAC,CAAC;IACF,MAAMC,QAAQ,GAAGtG,GAAG,CAAC,CAAC;IACtB,MAAMuG,YAAY,GAAGvG,GAAG,CAAC,CAAC;IAC1B,MAAMwG,WAAW,GAAGxG,GAAG,CAAC,CAAC;IACzB,MAAM;MACJyG,MAAM,EAAEC;IACV,CAAC,GAAGnE,SAAS,CAACrB,QAAQ,CAAC;IACvB,MAAMyF,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIC,EAAE;MACN,OAAO1D,MAAM,CAAC,CAAC0D,EAAE,GAAGf,KAAK,CAAC9B,UAAU,KAAK,IAAI,GAAG6C,EAAE,GAAG,EAAE,CAAC;IAC1D,CAAC;IACD,MAAMC,OAAO,GAAIC,GAAG,IAAK;MACvB,IAAIhG,KAAK,CAAC+E,KAAK,CAACiB,GAAG,CAAC,CAAC,EAAE;QACrB,OAAOjB,KAAK,CAACiB,GAAG,CAAC;MACnB;MACA,IAAIJ,IAAI,IAAI5F,KAAK,CAAC4F,IAAI,CAACb,KAAK,CAACiB,GAAG,CAAC,CAAC,EAAE;QAClC,OAAOJ,IAAI,CAACb,KAAK,CAACiB,GAAG,CAAC;MACxB;IACF,CAAC;IACD,MAAMC,SAAS,GAAG5G,QAAQ,CAAC,MAAM;MAC/B,MAAM2E,QAAQ,GAAG+B,OAAO,CAAC,UAAU,CAAC;MACpC,IAAIhB,KAAK,CAACtC,SAAS,IAAI,CAACuB,QAAQ,EAAE;QAChC,MAAMkC,QAAQ,GAAGL,aAAa,CAAC,CAAC,KAAK,EAAE;QACvC,MAAMM,OAAO,GAAGpB,KAAK,CAACtB,YAAY,KAAK,QAAQ,IAAIsB,KAAK,CAACtB,YAAY,KAAK,OAAO,IAAI2B,KAAK,CAACE,OAAO;QAClG,OAAOY,QAAQ,IAAIC,OAAO;MAC5B;MACA,OAAO,KAAK;IACd,CAAC,CAAC;IACF,MAAMC,SAAS,GAAG/G,QAAQ,CAAC,MAAM;MAC/B,IAAIqG,WAAW,CAACW,KAAK,IAAIlB,KAAK,CAACmB,KAAK,EAAE;QACpC,OAAOZ,WAAW,CAACW,KAAK,CAAC,CAAC;MAC5B;MACA,OAAOtB,KAAK,CAAC9B,UAAU;IACzB,CAAC,CAAC;IACF,MAAMsD,gBAAgB,GAAGlH,QAAQ,CAAC,MAAM;MACtC,IAAIyG,EAAE;MACN,MAAMU,QAAQ,GAAGT,OAAO,CAAC,UAAU,CAAC;MACpC,IAAIS,QAAQ,KAAK,MAAM,EAAE;QACvB,OAAO,CAACV,EAAE,GAAGf,KAAK,CAACX,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0B,EAAE,CAACW,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACF,QAAQ,CAAC;MAC/E;MACA,OAAOA,QAAQ;IACjB,CAAC,CAAC;IACF,MAAMG,QAAQ,GAAIvC,KAAK,IAAKA,KAAK,CAACwC,MAAM,CAAC,CAACC,OAAO,EAAEH,IAAI,KAAKG,OAAO,CAACC,IAAI,CAAC,MAAM;MAC7E,IAAI1B,KAAK,CAACC,MAAM,KAAK,QAAQ,EAAE;QAC7B;MACF;MACA,IAAI;QACFgB;MACF,CAAC,GAAGD,SAAS;MACb,IAAIM,IAAI,CAAC5D,SAAS,EAAE;QAClBuD,KAAK,GAAGK,IAAI,CAAC5D,SAAS,CAACuD,KAAK,EAAEK,IAAI,CAAC;MACrC;MACA,IAAI,CAAC3F,WAAW,CAACsF,KAAK,EAAEK,IAAI,CAAC,EAAE;QAC7BtB,KAAK,CAACC,MAAM,GAAG,QAAQ;QACvBD,KAAK,CAACG,eAAe,GAAGnE,cAAc,CAACiF,KAAK,EAAEK,IAAI,CAAC;QACnD;MACF;MACA,IAAIA,IAAI,CAACK,SAAS,EAAE;QAClB,IAAI7F,YAAY,CAACmF,KAAK,CAAC,IAAIK,IAAI,CAACM,aAAa,KAAK,KAAK,EAAE;UACvD;QACF;QACA,OAAOzF,gBAAgB,CAAC8E,KAAK,EAAEK,IAAI,CAAC,CAACI,IAAI,CAAEG,MAAM,IAAK;UACpD,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;YACxC7B,KAAK,CAACC,MAAM,GAAG,QAAQ;YACvBD,KAAK,CAACG,eAAe,GAAG0B,MAAM;UAChC,CAAC,MAAM,IAAIA,MAAM,KAAK,KAAK,EAAE;YAC3B7B,KAAK,CAACC,MAAM,GAAG,QAAQ;YACvBD,KAAK,CAACG,eAAe,GAAGnE,cAAc,CAACiF,KAAK,EAAEK,IAAI,CAAC;UACrD;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,EAAEQ,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;IACtB,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5BhC,KAAK,CAACC,MAAM,GAAG,aAAa;MAC5BD,KAAK,CAACG,eAAe,GAAG,EAAE;IAC5B,CAAC;IACD,MAAM8B,WAAW,GAAGA,CAAA,KAAMnC,IAAI,CAAC,aAAa,EAAE;MAC5CG,MAAM,EAAED,KAAK,CAACC,MAAM;MACpBiC,OAAO,EAAElC,KAAK,CAACG;IACjB,CAAC,CAAC;IACF,MAAMgC,QAAQ,GAAGA,CAACnD,KAAK,GAAGW,KAAK,CAACX,KAAK,KAAK,IAAI8C,OAAO,CAAEC,OAAO,IAAK;MACjEC,eAAe,CAAC,CAAC;MACjB,IAAIhD,KAAK,EAAE;QACTc,IAAI,CAAC,eAAe,CAAC;QACrByB,QAAQ,CAACvC,KAAK,CAAC,CAAC0C,IAAI,CAAC,MAAM;UACzB,IAAI1B,KAAK,CAACC,MAAM,KAAK,QAAQ,EAAE;YAC7B8B,OAAO,CAAC;cACNnF,IAAI,EAAE+C,KAAK,CAAC/C,IAAI;cAChBsF,OAAO,EAAElC,KAAK,CAACG;YACjB,CAAC,CAAC;YACF8B,WAAW,CAAC,CAAC;UACf,CAAC,MAAM;YACLjC,KAAK,CAACC,MAAM,GAAG,QAAQ;YACvB8B,OAAO,CAAC,CAAC;YACTE,WAAW,CAAC,CAAC;UACf;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLF,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;IACF,MAAMK,mBAAmB,GAAIrB,OAAO,IAAK;MACvC,IAAIP,IAAI,IAAIb,KAAK,CAACX,KAAK,EAAE;QACvB,MAAM;UACJqD;QACF,CAAC,GAAG7B,IAAI,CAACb,KAAK;QACd,MAAM2C,cAAc,GAAGvH,OAAO,CAACsH,eAAe,CAAC,CAACE,QAAQ,CAACxB,OAAO,CAAC;QACjE,MAAM/B,KAAK,GAAGW,KAAK,CAACX,KAAK,CAACwD,MAAM,CAAElB,IAAI,IAAK;UACzC,IAAIA,IAAI,CAACP,OAAO,EAAE;YAChB,OAAOhG,OAAO,CAACuG,IAAI,CAACP,OAAO,CAAC,CAACwB,QAAQ,CAACxB,OAAO,CAAC;UAChD;UACA,OAAOuB,cAAc;QACvB,CAAC,CAAC;QACF,IAAItD,KAAK,CAACyD,MAAM,EAAE;UAChBN,QAAQ,CAACnD,KAAK,CAAC;QACjB;MACF;IACF,CAAC;IACD,MAAM0D,gBAAgB,GAAIzB,KAAK,IAAK;MAClC,IAAIP,EAAE;MACN,MAAM;QACJpD;MACF,CAAC,GAAGqC,KAAK;MACT,IAAI/E,KAAK,CAAC0C,SAAS,CAAC,IAAIpB,eAAe,CAAC+E,KAAK,CAAC,GAAG,CAAC3D,SAAS,EAAE;QAC3D,MAAMO,UAAU,GAAG4C,aAAa,CAAC,CAAC;QAClC,IAAI5C,UAAU,IAAI3B,eAAe,CAAC2B,UAAU,CAAC,KAAK,CAACP,SAAS,EAAE;UAC5D,OAAOO,UAAU;QACnB;QACA,MAAM8E,YAAY,GAAG,CAACjC,EAAE,GAAGN,QAAQ,CAACa,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACiC,YAAY;QAC7E,IAAI3C,KAAK,CAACE,OAAO,IAAIyC,YAAY,EAAE;UACjC,MAAMC,QAAQ,GAAG,CAAC,GAAG3B,KAAK,CAAC;UAC3B,MAAM4B,cAAc,GAAGD,QAAQ,CAACH,MAAM,GAAG,CAACnF,SAAS;UACnDsF,QAAQ,CAACE,MAAM,CAACH,YAAY,GAAGE,cAAc,EAAEA,cAAc,CAAC;UAC9D,OAAOD,QAAQ,CAACG,IAAI,CAAC,EAAE,CAAC;QAC1B;QACA,OAAOrH,SAAS,CAACuF,KAAK,EAAE,CAAC3D,SAAS,CAAC;MACrC;MACA,OAAO2D,KAAK;IACd,CAAC;IACD,MAAM+B,WAAW,GAAGA,CAAC/B,KAAK,EAAEF,OAAO,GAAG,UAAU,KAAK;MACnD,IAAIL,EAAE,EAAEuC,EAAE;MACV,MAAMC,aAAa,GAAGjC,KAAK;MAC3BA,KAAK,GAAGyB,gBAAgB,CAACzB,KAAK,CAAC;MAC/B,MAAMkC,YAAY,GAAGjH,eAAe,CAACgH,aAAa,CAAC,GAAGhH,eAAe,CAAC+E,KAAK,CAAC;MAC5E,IAAItB,KAAK,CAACnB,IAAI,KAAK,QAAQ,IAAImB,KAAK,CAACnB,IAAI,KAAK,OAAO,EAAE;QACrD,MAAM4E,QAAQ,GAAGzD,KAAK,CAACnB,IAAI,KAAK,QAAQ;QACxCyC,KAAK,GAAG7F,YAAY,CAAC6F,KAAK,EAAEmC,QAAQ,EAAEA,QAAQ,CAAC;QAC/C,IAAIrC,OAAO,KAAK,QAAQ,IAAIE,KAAK,KAAK,EAAE,KAAKtB,KAAK,CAAClC,GAAG,KAAK,KAAK,CAAC,IAAIkC,KAAK,CAACpC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE;UAC1F,MAAM8F,aAAa,GAAG5H,KAAK,CAAC,CAACwF,KAAK,EAAE,CAACP,EAAE,GAAGf,KAAK,CAAClC,GAAG,KAAK,IAAI,GAAGiD,EAAE,GAAG,CAAC4C,QAAQ,EAAE,CAACL,EAAE,GAAGtD,KAAK,CAACpC,GAAG,KAAK,IAAI,GAAG0F,EAAE,GAAGK,QAAQ,CAAC;UACxH,IAAI,CAACrC,KAAK,KAAKoC,aAAa,EAAE;YAC5BpC,KAAK,GAAGoC,aAAa,CAACE,QAAQ,CAAC,CAAC;UAClC;QACF;MACF;MACA,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAI7D,KAAK,CAACjC,SAAS,IAAIqD,OAAO,KAAKpB,KAAK,CAACrB,aAAa,EAAE;QACtD,MAAM;UACJZ,SAAS;UACTJ;QACF,CAAC,GAAGqC,KAAK;QACTsB,KAAK,GAAGvD,SAAS,CAACuD,KAAK,CAAC;QACxB,IAAIrG,KAAK,CAAC0C,SAAS,CAAC,IAAIpB,eAAe,CAAC+E,KAAK,CAAC,GAAG,CAAC3D,SAAS,EAAE;UAC3D2D,KAAK,GAAGvF,SAAS,CAACuF,KAAK,EAAE,CAAC3D,SAAS,CAAC;QACtC;QACA,IAAI8C,QAAQ,CAACa,KAAK,IAAIjB,KAAK,CAACE,OAAO,EAAE;UACnC,MAAM;YACJyC;UACF,CAAC,GAAGvC,QAAQ,CAACa,KAAK;UAClB,MAAMwC,MAAM,GAAG/H,SAAS,CAACwH,aAAa,EAAEP,YAAY,CAAC;UACrDa,gBAAgB,GAAGtH,eAAe,CAACwB,SAAS,CAAC+F,MAAM,CAAC,CAAC,GAAGvH,eAAe,CAACuH,MAAM,CAAC;QACjF;MACF;MACA,IAAIrD,QAAQ,CAACa,KAAK,IAAIb,QAAQ,CAACa,KAAK,CAACA,KAAK,KAAKA,KAAK,EAAE;QACpD,IAAIjB,KAAK,CAACE,OAAO,EAAE;UACjB,IAAI;YACFwD,cAAc;YACdf;UACF,CAAC,GAAGvC,QAAQ,CAACa,KAAK;UAClBb,QAAQ,CAACa,KAAK,CAACA,KAAK,GAAGA,KAAK;UAC5B,IAAIrG,KAAK,CAAC8I,cAAc,CAAC,IAAI9I,KAAK,CAAC+H,YAAY,CAAC,EAAE;YAChD,MAAMgB,QAAQ,GAAGzH,eAAe,CAAC+E,KAAK,CAAC;YACvC,IAAIkC,YAAY,EAAE;cAChBO,cAAc,IAAIP,YAAY;cAC9BR,YAAY,IAAIQ,YAAY;YAC9B,CAAC,MAAM,IAAIK,gBAAgB,EAAE;cAC3BE,cAAc,IAAIF,gBAAgB;cAClCb,YAAY,IAAIa,gBAAgB;YAClC;YACApD,QAAQ,CAACa,KAAK,CAAC2C,iBAAiB,CAACC,IAAI,CAACpG,GAAG,CAACiG,cAAc,EAAEC,QAAQ,CAAC,EAAEE,IAAI,CAACpG,GAAG,CAACkF,YAAY,EAAEgB,QAAQ,CAAC,CAAC;UACxG;QACF,CAAC,MAAM;UACLvD,QAAQ,CAACa,KAAK,CAACA,KAAK,GAAGA,KAAK;QAC9B;MACF;MACA,IAAIA,KAAK,KAAKtB,KAAK,CAAC9B,UAAU,EAAE;QAC9BiC,IAAI,CAAC,mBAAmB,EAAEmB,KAAK,CAAC;MAClC;IACF,CAAC;IACD,MAAM6C,OAAO,GAAIC,KAAK,IAAK;MACzB,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,SAAS,EAAE;QAC3BjB,WAAW,CAACe,KAAK,CAACC,MAAM,CAAC/C,KAAK,CAAC;MACjC;IACF,CAAC;IACD,MAAMiD,IAAI,GAAGA,CAAA,KAAM;MACjB,IAAIxD,EAAE;MACN,OAAO,CAACA,EAAE,GAAGN,QAAQ,CAACa,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACwD,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAClB,IAAIzD,EAAE;MACN,OAAO,CAACA,EAAE,GAAGN,QAAQ,CAACa,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACyD,KAAK,CAAC,CAAC;IAC5D,CAAC;IACD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,MAAMlD,KAAK,GAAGd,QAAQ,CAACa,KAAK;MAC5B,IAAItB,KAAK,CAACnB,IAAI,KAAK,UAAU,IAAImB,KAAK,CAACT,QAAQ,IAAIgC,KAAK,EAAE;QACxDjF,cAAc,CAACiF,KAAK,EAAEvB,KAAK,CAACT,QAAQ,CAAC;MACvC;IACF,CAAC;IACD,MAAMmF,OAAO,GAAIN,KAAK,IAAK;MACzB/D,KAAK,CAACE,OAAO,GAAG,IAAI;MACpBJ,IAAI,CAAC,OAAO,EAAEiE,KAAK,CAAC;MACpB7J,QAAQ,CAACkK,kBAAkB,CAAC;MAC5B,IAAIzD,OAAO,CAAC,UAAU,CAAC,EAAE;QACvBuD,IAAI,CAAC,CAAC;MACR;IACF,CAAC;IACD,MAAMI,MAAM,GAAIP,KAAK,IAAK;MACxB/D,KAAK,CAACE,OAAO,GAAG,KAAK;MACrB8C,WAAW,CAACvC,aAAa,CAAC,CAAC,EAAE,QAAQ,CAAC;MACtCX,IAAI,CAAC,MAAM,EAAEiE,KAAK,CAAC;MACnB,IAAIpD,OAAO,CAAC,UAAU,CAAC,EAAE;QACvB;MACF;MACAyB,mBAAmB,CAAC,QAAQ,CAAC;MAC7BlI,QAAQ,CAACkK,kBAAkB,CAAC;MAC5BjJ,WAAW,CAAC,CAAC;IACf,CAAC;IACD,MAAMoJ,YAAY,GAAIR,KAAK,IAAKjE,IAAI,CAAC,YAAY,EAAEiE,KAAK,CAAC;IACzD,MAAMS,eAAe,GAAIT,KAAK,IAAKjE,IAAI,CAAC,eAAe,EAAEiE,KAAK,CAAC;IAC/D,MAAMU,gBAAgB,GAAIV,KAAK,IAAKjE,IAAI,CAAC,gBAAgB,EAAEiE,KAAK,CAAC;IACjE,MAAMW,OAAO,GAAIX,KAAK,IAAK;MACzB1I,cAAc,CAAC0I,KAAK,CAAC;MACrBjE,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;MAC7BA,IAAI,CAAC,OAAO,EAAEiE,KAAK,CAAC;IACtB,CAAC;IACD,MAAMY,SAAS,GAAG1K,QAAQ,CAAC,MAAM;MAC/B,IAAI,OAAO0F,KAAK,CAACjB,KAAK,KAAK,SAAS,EAAE;QACpC,OAAOiB,KAAK,CAACjB,KAAK;MACpB;MACA,IAAI8B,IAAI,IAAIA,IAAI,CAACb,KAAK,CAACgF,SAAS,IAAI3E,KAAK,CAACC,MAAM,KAAK,QAAQ,EAAE;QAC7D,OAAO,IAAI;MACb;IACF,CAAC,CAAC;IACF,MAAM2E,UAAU,GAAG3K,QAAQ,CAAC,MAAM;MAChC,MAAMmF,UAAU,GAAGuB,OAAO,CAAC,YAAY,CAAC;MACxC,MAAMrB,UAAU,GAAGqB,OAAO,CAAC,YAAY,CAAC;MACxC,IAAIvB,UAAU,IAAIE,UAAU,KAAK,KAAK,EAAE;QACtC,OAAO;UACLuF,KAAK,EAAE/J,OAAO,CAACsE,UAAU;QAC3B,CAAC;MACH;IACF,CAAC,CAAC;IACF,MAAM0F,UAAU,GAAIf,KAAK,IAAK;MAC5B,MAAMgB,UAAU,GAAG,EAAE;MACrB,IAAIhB,KAAK,CAACiB,OAAO,KAAKD,UAAU,EAAE;QAChC,MAAME,aAAa,GAAGzE,IAAI,IAAIA,IAAI,CAACb,KAAK,CAACsF,aAAa;QACtD,IAAI,CAACA,aAAa,IAAItF,KAAK,CAACnB,IAAI,KAAK,UAAU,EAAE;UAC/CnD,cAAc,CAAC0I,KAAK,CAAC;QACvB;QACA,IAAIpE,KAAK,CAACnB,IAAI,KAAK,QAAQ,EAAE;UAC3B0F,IAAI,CAAC,CAAC;QACR;MACF;MACApE,IAAI,CAAC,UAAU,EAAEiE,KAAK,CAAC;IACzB,CAAC;IACD,MAAMmB,UAAU,GAAGA,CAAA,KAAMvF,KAAK,CAAC5C,EAAE,IAAI,GAAGA,EAAE,QAAQ;IAClD,MAAMoI,mBAAmB,GAAGA,CAAA,KAAMnF,KAAK,CAACC,MAAM;IAC9C,MAAMmF,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,YAAY,GAAGxI,GAAG,CAAC,SAAS,EAAE,CAAC8D,OAAO,CAAC,YAAY,CAAC,EAAE;QAC1DjC,KAAK,EAAEiG,SAAS,CAAC1D,KAAK;QACtBqE,MAAM,EAAE,CAAC,CAACvF,KAAK,CAACmB,KAAK;QACrB,YAAY,EAAEvB,KAAK,CAACnB,IAAI,KAAK,UAAU,IAAI,CAACmB,KAAK,CAACT;MACpD,CAAC,CAAC,CAAC;MACH,IAAIa,KAAK,CAACmB,KAAK,EAAE;QACf,OAAO3G,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAE8K,YAAY;UACrB,SAAS,EAAEd;QACb,CAAC,EAAE,CAACxE,KAAK,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC;MACrB;MACA,MAAMqE,UAAU,GAAG;QACjBxI,EAAE,EAAEmI,UAAU,CAAC,CAAC;QAChBpL,GAAG,EAAEsG,QAAQ;QACbxD,IAAI,EAAE+C,KAAK,CAAC/C,IAAI;QAChBmC,IAAI,EAAEY,KAAK,CAACZ,IAAI,KAAK,KAAK,CAAC,GAAG,CAACY,KAAK,CAACZ,IAAI,GAAG,KAAK,CAAC;QAClDyG,KAAK,EAAEH,YAAY;QACnB1G,QAAQ,EAAEgC,OAAO,CAAC,UAAU,CAAC;QAC7B/B,QAAQ,EAAE+B,OAAO,CAAC,UAAU,CAAC;QAC7BxD,SAAS,EAAEwC,KAAK,CAACxC,SAAS;QAC1BY,WAAW,EAAE4B,KAAK,CAAC5B,WAAW;QAC9BC,YAAY,EAAE2B,KAAK,CAAC3B,YAAY;QAChCC,cAAc,EAAE0B,KAAK,CAAC1B,cAAc;QACpCC,WAAW,EAAEyB,KAAK,CAACzB,WAAW;QAC9BE,YAAY,EAAEuB,KAAK,CAACvB,YAAY;QAChCG,UAAU,EAAEoB,KAAK,CAACpB,UAAU;QAC5B,iBAAiB,EAAEoB,KAAK,CAAC8F,KAAK,GAAG,GAAG1I,EAAE,QAAQ,GAAG,KAAK,CAAC;QACvD,qBAAqB,EAAE,WAAW;QAClCuH,MAAM;QACND,OAAO;QACPP,OAAO;QACP4B,OAAO,EAAEnB,YAAY;QACrBoB,QAAQ,EAAE/J,YAAY;QACtBkJ,UAAU;QACVc,gBAAgB,EAAEhK,YAAY;QAC9BiK,kBAAkB,EAAE9J;MACtB,CAAC;MACD,IAAI4D,KAAK,CAACnB,IAAI,KAAK,UAAU,EAAE;QAC7B,OAAOjE,YAAY,CAAC,UAAU,EAAEE,WAAW,CAAC8K,UAAU,EAAE;UACtD,WAAW,EAAE5F,KAAK,CAACd;QACrB,CAAC,CAAC,EAAE,IAAI,CAAC;MACX;MACA,OAAOtE,YAAY,CAAC,OAAO,EAAEE,WAAW,CAACoB,YAAY,CAAC8D,KAAK,CAACnB,IAAI,EAAEmB,KAAK,CAACd,SAAS,CAAC,EAAE0G,UAAU,CAAC,EAAE,IAAI,CAAC;IACxG,CAAC;IACD,MAAMO,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAMC,YAAY,GAAGhG,KAAK,CAAC,WAAW,CAAC;MACvC,IAAIJ,KAAK,CAAC1C,QAAQ,IAAI8I,YAAY,EAAE;QAClC,OAAOxL,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEsC,GAAG,CAAC,WAAW,CAAC;UACzB,SAAS,EAAE2H;QACb,CAAC,EAAE,CAACuB,YAAY,GAAGA,YAAY,CAAC,CAAC,GAAGxL,YAAY,CAACmC,IAAI,EAAE;UACrD,MAAM,EAAEiD,KAAK,CAAC1C,QAAQ;UACtB,aAAa,EAAE0C,KAAK,CAACqG;QACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,aAAa,GAAGnG,KAAK,CAAC,YAAY,CAAC;MACzC,IAAIJ,KAAK,CAACzC,SAAS,IAAIgJ,aAAa,EAAE;QACpC,OAAO3L,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEsC,GAAG,CAAC,YAAY,CAAC;UAC1B,SAAS,EAAE4H;QACb,CAAC,EAAE,CAACyB,aAAa,GAAGA,aAAa,CAAC,CAAC,GAAG3L,YAAY,CAACmC,IAAI,EAAE;UACvD,MAAM,EAAEiD,KAAK,CAACzC,SAAS;UACvB,aAAa,EAAEyC,KAAK,CAACqG;QACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,MAAMG,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIxG,KAAK,CAACJ,aAAa,IAAII,KAAK,CAACrC,SAAS,EAAE;QAC1C,MAAM8I,KAAK,GAAGlK,eAAe,CAACuE,aAAa,CAAC,CAAC,CAAC;QAC9C,OAAOlG,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEsC,GAAG,CAAC,YAAY;QAC3B,CAAC,EAAE,CAACtC,YAAY,CAAC,MAAM,EAAE;UACvB,OAAO,EAAEsC,GAAG,CAAC,UAAU;QACzB,CAAC,EAAE,CAACuJ,KAAK,CAAC,CAAC,EAAEzL,gBAAgB,CAAC,GAAG,CAAC,EAAEgF,KAAK,CAACrC,SAAS,CAAC,CAAC;MACvD;IACF,CAAC;IACD,MAAM+I,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI7F,IAAI,IAAIA,IAAI,CAACb,KAAK,CAAC2G,gBAAgB,KAAK,KAAK,EAAE;QACjD;MACF;MACA,MAAMpE,OAAO,GAAGvC,KAAK,CAACxB,YAAY,IAAI6B,KAAK,CAACG,eAAe;MAC3D,IAAI+B,OAAO,EAAE;QACX,MAAMqE,IAAI,GAAGxG,KAAK,CAAC,eAAe,CAAC;QACnC,MAAMP,iBAAiB,GAAGmB,OAAO,CAAC,mBAAmB,CAAC;QACtD,OAAOpG,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEsC,GAAG,CAAC,eAAe,EAAE2C,iBAAiB;QACjD,CAAC,EAAE,CAAC+G,IAAI,GAAGA,IAAI,CAAC;UACdrE;QACF,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC;MAChB;IACF,CAAC;IACD,MAAMsE,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMpH,UAAU,GAAGuB,OAAO,CAAC,YAAY,CAAC;MACxC,MAAMrB,UAAU,GAAGqB,OAAO,CAAC,YAAY,CAAC;MACxC,MAAMlB,KAAK,GAAGkB,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE;MACzC,IAAIZ,KAAK,CAAC0F,KAAK,EAAE;QACf,OAAO,CAAC1F,KAAK,CAAC0F,KAAK,CAAC,CAAC,EAAEhG,KAAK,CAAC;MAC/B;MACA,IAAIE,KAAK,CAAC8F,KAAK,EAAE;QACf,OAAOlL,YAAY,CAAC,OAAO,EAAE;UAC3B,IAAI,EAAE,GAAGwC,EAAE,QAAQ;UACnB,KAAK,EAAEgD,KAAK,CAACmB,KAAK,GAAG,KAAK,CAAC,GAAGgE,UAAU,CAAC,CAAC;UAC1C,qBAAqB,EAAE,WAAW;UAClC,SAAS,EAAGnB,KAAK,IAAK;YACpB1I,cAAc,CAAC0I,KAAK,CAAC;YACrBI,KAAK,CAAC,CAAC;UACT,CAAC;UACD,OAAO,EAAE7E,UAAU,KAAK,KAAK,IAAIF,UAAU,GAAG;YAC5CyF,KAAK,EAAE/J,OAAO,CAACsE,UAAU;UAC3B,CAAC,GAAG,KAAK;QACX,CAAC,EAAE,CAACO,KAAK,CAAC8F,KAAK,GAAGhG,KAAK,CAAC,CAAC;MAC3B;IACF,CAAC;IACD,MAAMgH,eAAe,GAAGA,CAAA,KAAM,CAAClM,YAAY,CAAC,KAAK,EAAE;MACjD,OAAO,EAAEsC,GAAG,CAAC,MAAM;IACrB,CAAC,EAAE,CAACuI,WAAW,CAAC,CAAC,EAAEvE,SAAS,CAACI,KAAK,IAAI1G,YAAY,CAACmC,IAAI,EAAE;MACvD,KAAK,EAAE2D,YAAY;MACnB,MAAM,EAAEV,KAAK,CAAC/B,SAAS;MACvB,OAAO,EAAEf,GAAG,CAAC,OAAO;IACtB,CAAC,EAAE,IAAI,CAAC,EAAEoJ,eAAe,CAAC,CAAC,EAAElG,KAAK,CAAC2G,MAAM,IAAInM,YAAY,CAAC,KAAK,EAAE;MAC/D,OAAO,EAAEsC,GAAG,CAAC,QAAQ;IACvB,CAAC,EAAE,CAACkD,KAAK,CAAC2G,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEP,eAAe,CAAC,CAAC,EAAEE,aAAa,CAAC,CAAC,CAAC;IAC3D5J,SAAS,CAAC;MACRyH,IAAI;MACJC,KAAK;MACLhC,QAAQ;MACRnB,SAAS;MACTgB,eAAe;MACfmD;IACF,CAAC,CAAC;IACFnL,OAAO,CAACuC,0BAA0B,EAAE;MAClC+D,WAAW;MACX0B,eAAe;MACfI;IACF,CAAC,CAAC;IACFrI,KAAK,CAAC,MAAM4F,KAAK,CAAC9B,UAAU,EAAE,MAAM;MAClCmF,WAAW,CAACvC,aAAa,CAAC,CAAC,CAAC;MAC5BuB,eAAe,CAAC,CAAC;MACjBI,mBAAmB,CAAC,UAAU,CAAC;MAC/BlI,QAAQ,CAACkK,kBAAkB,CAAC;IAC9B,CAAC,CAAC;IACFhK,SAAS,CAAC,MAAM;MACd4I,WAAW,CAACvC,aAAa,CAAC,CAAC,EAAEd,KAAK,CAACrB,aAAa,CAAC;MACjDpE,QAAQ,CAACkK,kBAAkB,CAAC;IAC9B,CAAC,CAAC;IACF9H,gBAAgB,CAAC,YAAY,EAAEoI,OAAO,EAAE;MACtCV,MAAM,EAAE/J,QAAQ,CAAC,MAAM;QACrB,IAAIyG,EAAE;QACN,OAAO,CAACA,EAAE,GAAGL,YAAY,CAACY,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACiG,GAAG;MAC5D,CAAC;IACH,CAAC,CAAC;IACF,OAAO,MAAM;MACX,MAAMhI,QAAQ,GAAGgC,OAAO,CAAC,UAAU,CAAC;MACpC,MAAMrB,UAAU,GAAGqB,OAAO,CAAC,YAAY,CAAC;MACxC,MAAMiG,QAAQ,GAAGd,cAAc,CAAC,CAAC;MACjC,MAAMe,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,KAAK,GAAGN,WAAW,CAAC,CAAC;QAC3B,IAAIlH,UAAU,KAAK,KAAK,EAAE;UACxB,OAAO,CAACsH,QAAQ,EAAEE,KAAK,CAAC,CAACtE,MAAM,CAACpF,OAAO,CAAC;QAC1C;QACA,OAAO0J,KAAK,IAAI,EAAE;MACpB,CAAC;MACD,OAAOvM,YAAY,CAACoC,IAAI,EAAE;QACxB,MAAM,EAAEgD,KAAK,CAACoH,IAAI;QAClB,OAAO,EAAElK,GAAG,CAAC;UACX6B,KAAK,EAAEiG,SAAS,CAAC1D,KAAK;UACtBtC,QAAQ;UACR,CAAC,SAASW,UAAU,EAAE,GAAGA;QAC3B,CAAC,CAAC;QACF,QAAQ,EAAEK,KAAK,CAACqH,MAAM;QACtB,QAAQ,EAAErH,KAAK,CAACsH,MAAM;QACtB,QAAQ,EAAEtH,KAAK,CAACuH,MAAM;QACtB,WAAW,EAAEvH,KAAK,CAACwH,SAAS;QAC5B,YAAY,EAAEvC,UAAU,CAAC3D,KAAK;QAC9B,YAAY,EAAEpE,GAAG,CAAC,OAAO,CAAC;QAC1B,YAAY,EAAE,CAACA,GAAG,CAAC,OAAO,EAAE,CAACyC,UAAU,EAAE;UACvC8B,QAAQ,EAAED,gBAAgB,CAACF;QAC7B,CAAC,CAAC,CAAC,EAAEtB,KAAK,CAACN,UAAU,CAAC;QACtB,gBAAgB,EAAEM,KAAK,CAACyH;MAC1B,CAAC,EAAE;QACDC,IAAI,EAAET,QAAQ,IAAItH,UAAU,KAAK,KAAK,GAAG,MAAMsH,QAAQ,GAAG,IAAI;QAC9DU,KAAK,EAAET,WAAW;QAClB5F,KAAK,EAAEwF,eAAe;QACtBc,KAAK,EAAExH,KAAK,CAACwH;MACf,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE7H,aAAa,IAAIjB,OAAO,EACxBK,UAAU,EACVhC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}