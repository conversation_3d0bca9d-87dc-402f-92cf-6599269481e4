{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Sidebar from \"./Sidebar.mjs\";\nconst Sidebar = withInstall(_Sidebar);\nvar stdin_default = Sidebar;\nimport { sidebarProps } from \"./Sidebar.mjs\";\nexport { Sidebar, stdin_default as default, sidebarProps };", "map": {"version": 3, "names": ["withInstall", "_Sidebar", "Sidebar", "stdin_default", "sidebarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/sidebar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Sidebar from \"./Sidebar.mjs\";\nconst Sidebar = withInstall(_Sidebar);\nvar stdin_default = Sidebar;\nimport { sidebarProps } from \"./Sidebar.mjs\";\nexport {\n  Sidebar,\n  stdin_default as default,\n  sidebarProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,MAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SAASE,YAAY,QAAQ,eAAe;AAC5C,SACEF,OAAO,EACPC,aAAa,IAAIE,OAAO,EACxBD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}