{"ast": null, "code": "import { ref, reactive, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { clamp, isDef, numericProp, preventDefault, callInterceptor, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { useRect, useClickAway, useEventListener } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"swipe-cell\");\nconst swipeCellProps = {\n  name: makeNumericProp(\"\"),\n  disabled: Boolean,\n  leftWidth: numericProp,\n  rightWidth: numericProp,\n  beforeClose: Function,\n  stopPropagation: Boolean\n};\nvar stdin_default = defineComponent({\n  name,\n  props: swipeCellProps,\n  emits: [\"open\", \"close\", \"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let opened;\n    let lockClick;\n    let startOffset;\n    let isInBeforeClosing;\n    const root = ref();\n    const leftRef = ref();\n    const rightRef = ref();\n    const state = reactive({\n      offset: 0,\n      dragging: false\n    });\n    const touch = useTouch();\n    const getWidthByRef = ref2 => ref2.value ? useRect(ref2).width : 0;\n    const leftWidth = computed(() => isDef(props.leftWidth) ? +props.leftWidth : getWidthByRef(leftRef));\n    const rightWidth = computed(() => isDef(props.rightWidth) ? +props.rightWidth : getWidthByRef(rightRef));\n    const open = side => {\n      state.offset = side === \"left\" ? leftWidth.value : -rightWidth.value;\n      if (!opened) {\n        opened = true;\n        emit(\"open\", {\n          name: props.name,\n          position: side\n        });\n      }\n    };\n    const close = position => {\n      state.offset = 0;\n      if (opened) {\n        opened = false;\n        emit(\"close\", {\n          name: props.name,\n          position\n        });\n      }\n    };\n    const toggle = side => {\n      const offset = Math.abs(state.offset);\n      const THRESHOLD = 0.15;\n      const threshold = opened ? 1 - THRESHOLD : THRESHOLD;\n      const width = side === \"left\" ? leftWidth.value : rightWidth.value;\n      if (width && offset > width * threshold) {\n        open(side);\n      } else {\n        close(side);\n      }\n    };\n    const onTouchStart = event => {\n      if (!props.disabled) {\n        startOffset = state.offset;\n        touch.start(event);\n      }\n    };\n    const onTouchMove = event => {\n      if (props.disabled) {\n        return;\n      }\n      const {\n        deltaX\n      } = touch;\n      touch.move(event);\n      if (touch.isHorizontal()) {\n        lockClick = true;\n        state.dragging = true;\n        const isEdge = !opened || deltaX.value * startOffset < 0;\n        if (isEdge) {\n          preventDefault(event, props.stopPropagation);\n        }\n        state.offset = clamp(deltaX.value + startOffset, -rightWidth.value, leftWidth.value);\n      }\n    };\n    const onTouchEnd = () => {\n      if (state.dragging) {\n        state.dragging = false;\n        toggle(state.offset > 0 ? \"left\" : \"right\");\n        setTimeout(() => {\n          lockClick = false;\n        }, 0);\n      }\n    };\n    const onClick = (position = \"outside\", event) => {\n      if (isInBeforeClosing) return;\n      emit(\"click\", position);\n      if (opened && !lockClick) {\n        isInBeforeClosing = true;\n        callInterceptor(props.beforeClose, {\n          args: [{\n            event,\n            name: props.name,\n            position\n          }],\n          done: () => {\n            isInBeforeClosing = false;\n            close(position);\n          },\n          canceled: () => isInBeforeClosing = false,\n          error: () => isInBeforeClosing = false\n        });\n      }\n    };\n    const getClickHandler = position => event => {\n      if (lockClick || opened) {\n        event.stopPropagation();\n      }\n      if (lockClick) {\n        return;\n      }\n      onClick(position, event);\n    };\n    const renderSideContent = (side, ref2) => {\n      const contentSlot = slots[side];\n      if (contentSlot) {\n        return _createVNode(\"div\", {\n          \"ref\": ref2,\n          \"class\": bem(side),\n          \"onClick\": getClickHandler(side)\n        }, [contentSlot()]);\n      }\n    };\n    useExpose({\n      open,\n      close\n    });\n    useClickAway(root, event => onClick(\"outside\", event), {\n      eventName: \"touchstart\"\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: root\n    });\n    return () => {\n      var _a;\n      const wrapperStyle = {\n        transform: `translate3d(${state.offset}px, 0, 0)`,\n        transitionDuration: state.dragging ? \"0s\" : \".6s\"\n      };\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem(),\n        \"onClick\": getClickHandler(\"cell\"),\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"wrapper\"),\n        \"style\": wrapperStyle\n      }, [renderSideContent(\"left\", leftRef), (_a = slots.default) == null ? void 0 : _a.call(slots), renderSideContent(\"right\", rightRef)])]);\n    };\n  }\n});\nexport { stdin_default as default, swipeCellProps };", "map": {"version": 3, "names": ["ref", "reactive", "computed", "defineComponent", "createVNode", "_createVNode", "clamp", "isDef", "numericProp", "preventDefault", "callInterceptor", "createNamespace", "makeNumericProp", "useRect", "useClickAway", "useEventListener", "useTouch", "useExpose", "name", "bem", "swipeCellProps", "disabled", "Boolean", "leftWidth", "rightWidth", "beforeClose", "Function", "stopPropagation", "stdin_default", "props", "emits", "setup", "emit", "slots", "opened", "lockClick", "startOffset", "isInBeforeClosing", "root", "leftRef", "rightRef", "state", "offset", "dragging", "touch", "getWidthByRef", "ref2", "value", "width", "open", "side", "position", "close", "toggle", "Math", "abs", "THRESHOLD", "threshold", "onTouchStart", "event", "start", "onTouchMove", "deltaX", "move", "isHorizontal", "isEdge", "onTouchEnd", "setTimeout", "onClick", "args", "done", "canceled", "error", "getClickHandler", "renderSideContent", "contentSlot", "eventName", "target", "_a", "wrapperStyle", "transform", "transitionDuration", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/swipe-cell/SwipeCell.mjs"], "sourcesContent": ["import { ref, reactive, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { clamp, isDef, numericProp, preventDefault, callInterceptor, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { useRect, useClickAway, useEventListener } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"swipe-cell\");\nconst swipeCellProps = {\n  name: makeNumericProp(\"\"),\n  disabled: Boolean,\n  leftWidth: numericProp,\n  rightWidth: numericProp,\n  beforeClose: Function,\n  stopPropagation: Boolean\n};\nvar stdin_default = defineComponent({\n  name,\n  props: swipeCellProps,\n  emits: [\"open\", \"close\", \"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    let opened;\n    let lockClick;\n    let startOffset;\n    let isInBeforeClosing;\n    const root = ref();\n    const leftRef = ref();\n    const rightRef = ref();\n    const state = reactive({\n      offset: 0,\n      dragging: false\n    });\n    const touch = useTouch();\n    const getWidthByRef = (ref2) => ref2.value ? useRect(ref2).width : 0;\n    const leftWidth = computed(() => isDef(props.leftWidth) ? +props.leftWidth : getWidthByRef(leftRef));\n    const rightWidth = computed(() => isDef(props.rightWidth) ? +props.rightWidth : getWidthByRef(rightRef));\n    const open = (side) => {\n      state.offset = side === \"left\" ? leftWidth.value : -rightWidth.value;\n      if (!opened) {\n        opened = true;\n        emit(\"open\", {\n          name: props.name,\n          position: side\n        });\n      }\n    };\n    const close = (position) => {\n      state.offset = 0;\n      if (opened) {\n        opened = false;\n        emit(\"close\", {\n          name: props.name,\n          position\n        });\n      }\n    };\n    const toggle = (side) => {\n      const offset = Math.abs(state.offset);\n      const THRESHOLD = 0.15;\n      const threshold = opened ? 1 - THRESHOLD : THRESHOLD;\n      const width = side === \"left\" ? leftWidth.value : rightWidth.value;\n      if (width && offset > width * threshold) {\n        open(side);\n      } else {\n        close(side);\n      }\n    };\n    const onTouchStart = (event) => {\n      if (!props.disabled) {\n        startOffset = state.offset;\n        touch.start(event);\n      }\n    };\n    const onTouchMove = (event) => {\n      if (props.disabled) {\n        return;\n      }\n      const {\n        deltaX\n      } = touch;\n      touch.move(event);\n      if (touch.isHorizontal()) {\n        lockClick = true;\n        state.dragging = true;\n        const isEdge = !opened || deltaX.value * startOffset < 0;\n        if (isEdge) {\n          preventDefault(event, props.stopPropagation);\n        }\n        state.offset = clamp(deltaX.value + startOffset, -rightWidth.value, leftWidth.value);\n      }\n    };\n    const onTouchEnd = () => {\n      if (state.dragging) {\n        state.dragging = false;\n        toggle(state.offset > 0 ? \"left\" : \"right\");\n        setTimeout(() => {\n          lockClick = false;\n        }, 0);\n      }\n    };\n    const onClick = (position = \"outside\", event) => {\n      if (isInBeforeClosing) return;\n      emit(\"click\", position);\n      if (opened && !lockClick) {\n        isInBeforeClosing = true;\n        callInterceptor(props.beforeClose, {\n          args: [{\n            event,\n            name: props.name,\n            position\n          }],\n          done: () => {\n            isInBeforeClosing = false;\n            close(position);\n          },\n          canceled: () => isInBeforeClosing = false,\n          error: () => isInBeforeClosing = false\n        });\n      }\n    };\n    const getClickHandler = (position) => (event) => {\n      if (lockClick || opened) {\n        event.stopPropagation();\n      }\n      if (lockClick) {\n        return;\n      }\n      onClick(position, event);\n    };\n    const renderSideContent = (side, ref2) => {\n      const contentSlot = slots[side];\n      if (contentSlot) {\n        return _createVNode(\"div\", {\n          \"ref\": ref2,\n          \"class\": bem(side),\n          \"onClick\": getClickHandler(side)\n        }, [contentSlot()]);\n      }\n    };\n    useExpose({\n      open,\n      close\n    });\n    useClickAway(root, (event) => onClick(\"outside\", event), {\n      eventName: \"touchstart\"\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: root\n    });\n    return () => {\n      var _a;\n      const wrapperStyle = {\n        transform: `translate3d(${state.offset}px, 0, 0)`,\n        transitionDuration: state.dragging ? \"0s\" : \".6s\"\n      };\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem(),\n        \"onClick\": getClickHandler(\"cell\"),\n        \"onTouchstartPassive\": onTouchStart,\n        \"onTouchend\": onTouchEnd,\n        \"onTouchcancel\": onTouchEnd\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"wrapper\"),\n        \"style\": wrapperStyle\n      }, [renderSideContent(\"left\", leftRef), (_a = slots.default) == null ? void 0 : _a.call(slots), renderSideContent(\"right\", rightRef)])]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  swipeCellProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC3F,SAASC,KAAK,EAAEC,KAAK,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACjI,SAASC,OAAO,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,WAAW;AACnE,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGR,eAAe,CAAC,YAAY,CAAC;AACjD,MAAMS,cAAc,GAAG;EACrBF,IAAI,EAAEN,eAAe,CAAC,EAAE,CAAC;EACzBS,QAAQ,EAAEC,OAAO;EACjBC,SAAS,EAAEf,WAAW;EACtBgB,UAAU,EAAEhB,WAAW;EACvBiB,WAAW,EAAEC,QAAQ;EACrBC,eAAe,EAAEL;AACnB,CAAC;AACD,IAAIM,aAAa,GAAGzB,eAAe,CAAC;EAClCe,IAAI;EACJW,KAAK,EAAET,cAAc;EACrBU,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;EACjCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,IAAIC,MAAM;IACV,IAAIC,SAAS;IACb,IAAIC,WAAW;IACf,IAAIC,iBAAiB;IACrB,MAAMC,IAAI,GAAGtC,GAAG,CAAC,CAAC;IAClB,MAAMuC,OAAO,GAAGvC,GAAG,CAAC,CAAC;IACrB,MAAMwC,QAAQ,GAAGxC,GAAG,CAAC,CAAC;IACtB,MAAMyC,KAAK,GAAGxC,QAAQ,CAAC;MACrByC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAMC,KAAK,GAAG5B,QAAQ,CAAC,CAAC;IACxB,MAAM6B,aAAa,GAAIC,IAAI,IAAKA,IAAI,CAACC,KAAK,GAAGlC,OAAO,CAACiC,IAAI,CAAC,CAACE,KAAK,GAAG,CAAC;IACpE,MAAMzB,SAAS,GAAGrB,QAAQ,CAAC,MAAMK,KAAK,CAACsB,KAAK,CAACN,SAAS,CAAC,GAAG,CAACM,KAAK,CAACN,SAAS,GAAGsB,aAAa,CAACN,OAAO,CAAC,CAAC;IACpG,MAAMf,UAAU,GAAGtB,QAAQ,CAAC,MAAMK,KAAK,CAACsB,KAAK,CAACL,UAAU,CAAC,GAAG,CAACK,KAAK,CAACL,UAAU,GAAGqB,aAAa,CAACL,QAAQ,CAAC,CAAC;IACxG,MAAMS,IAAI,GAAIC,IAAI,IAAK;MACrBT,KAAK,CAACC,MAAM,GAAGQ,IAAI,KAAK,MAAM,GAAG3B,SAAS,CAACwB,KAAK,GAAG,CAACvB,UAAU,CAACuB,KAAK;MACpE,IAAI,CAACb,MAAM,EAAE;QACXA,MAAM,GAAG,IAAI;QACbF,IAAI,CAAC,MAAM,EAAE;UACXd,IAAI,EAAEW,KAAK,CAACX,IAAI;UAChBiC,QAAQ,EAAED;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAME,KAAK,GAAID,QAAQ,IAAK;MAC1BV,KAAK,CAACC,MAAM,GAAG,CAAC;MAChB,IAAIR,MAAM,EAAE;QACVA,MAAM,GAAG,KAAK;QACdF,IAAI,CAAC,OAAO,EAAE;UACZd,IAAI,EAAEW,KAAK,CAACX,IAAI;UAChBiC;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAME,MAAM,GAAIH,IAAI,IAAK;MACvB,MAAMR,MAAM,GAAGY,IAAI,CAACC,GAAG,CAACd,KAAK,CAACC,MAAM,CAAC;MACrC,MAAMc,SAAS,GAAG,IAAI;MACtB,MAAMC,SAAS,GAAGvB,MAAM,GAAG,CAAC,GAAGsB,SAAS,GAAGA,SAAS;MACpD,MAAMR,KAAK,GAAGE,IAAI,KAAK,MAAM,GAAG3B,SAAS,CAACwB,KAAK,GAAGvB,UAAU,CAACuB,KAAK;MAClE,IAAIC,KAAK,IAAIN,MAAM,GAAGM,KAAK,GAAGS,SAAS,EAAE;QACvCR,IAAI,CAACC,IAAI,CAAC;MACZ,CAAC,MAAM;QACLE,KAAK,CAACF,IAAI,CAAC;MACb;IACF,CAAC;IACD,MAAMQ,YAAY,GAAIC,KAAK,IAAK;MAC9B,IAAI,CAAC9B,KAAK,CAACR,QAAQ,EAAE;QACnBe,WAAW,GAAGK,KAAK,CAACC,MAAM;QAC1BE,KAAK,CAACgB,KAAK,CAACD,KAAK,CAAC;MACpB;IACF,CAAC;IACD,MAAME,WAAW,GAAIF,KAAK,IAAK;MAC7B,IAAI9B,KAAK,CAACR,QAAQ,EAAE;QAClB;MACF;MACA,MAAM;QACJyC;MACF,CAAC,GAAGlB,KAAK;MACTA,KAAK,CAACmB,IAAI,CAACJ,KAAK,CAAC;MACjB,IAAIf,KAAK,CAACoB,YAAY,CAAC,CAAC,EAAE;QACxB7B,SAAS,GAAG,IAAI;QAChBM,KAAK,CAACE,QAAQ,GAAG,IAAI;QACrB,MAAMsB,MAAM,GAAG,CAAC/B,MAAM,IAAI4B,MAAM,CAACf,KAAK,GAAGX,WAAW,GAAG,CAAC;QACxD,IAAI6B,MAAM,EAAE;UACVxD,cAAc,CAACkD,KAAK,EAAE9B,KAAK,CAACF,eAAe,CAAC;QAC9C;QACAc,KAAK,CAACC,MAAM,GAAGpC,KAAK,CAACwD,MAAM,CAACf,KAAK,GAAGX,WAAW,EAAE,CAACZ,UAAU,CAACuB,KAAK,EAAExB,SAAS,CAACwB,KAAK,CAAC;MACtF;IACF,CAAC;IACD,MAAMmB,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIzB,KAAK,CAACE,QAAQ,EAAE;QAClBF,KAAK,CAACE,QAAQ,GAAG,KAAK;QACtBU,MAAM,CAACZ,KAAK,CAACC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;QAC3CyB,UAAU,CAAC,MAAM;UACfhC,SAAS,GAAG,KAAK;QACnB,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC;IACD,MAAMiC,OAAO,GAAGA,CAACjB,QAAQ,GAAG,SAAS,EAAEQ,KAAK,KAAK;MAC/C,IAAItB,iBAAiB,EAAE;MACvBL,IAAI,CAAC,OAAO,EAAEmB,QAAQ,CAAC;MACvB,IAAIjB,MAAM,IAAI,CAACC,SAAS,EAAE;QACxBE,iBAAiB,GAAG,IAAI;QACxB3B,eAAe,CAACmB,KAAK,CAACJ,WAAW,EAAE;UACjC4C,IAAI,EAAE,CAAC;YACLV,KAAK;YACLzC,IAAI,EAAEW,KAAK,CAACX,IAAI;YAChBiC;UACF,CAAC,CAAC;UACFmB,IAAI,EAAEA,CAAA,KAAM;YACVjC,iBAAiB,GAAG,KAAK;YACzBe,KAAK,CAACD,QAAQ,CAAC;UACjB,CAAC;UACDoB,QAAQ,EAAEA,CAAA,KAAMlC,iBAAiB,GAAG,KAAK;UACzCmC,KAAK,EAAEA,CAAA,KAAMnC,iBAAiB,GAAG;QACnC,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAMoC,eAAe,GAAItB,QAAQ,IAAMQ,KAAK,IAAK;MAC/C,IAAIxB,SAAS,IAAID,MAAM,EAAE;QACvByB,KAAK,CAAChC,eAAe,CAAC,CAAC;MACzB;MACA,IAAIQ,SAAS,EAAE;QACb;MACF;MACAiC,OAAO,CAACjB,QAAQ,EAAEQ,KAAK,CAAC;IAC1B,CAAC;IACD,MAAMe,iBAAiB,GAAGA,CAACxB,IAAI,EAAEJ,IAAI,KAAK;MACxC,MAAM6B,WAAW,GAAG1C,KAAK,CAACiB,IAAI,CAAC;MAC/B,IAAIyB,WAAW,EAAE;QACf,OAAOtE,YAAY,CAAC,KAAK,EAAE;UACzB,KAAK,EAAEyC,IAAI;UACX,OAAO,EAAE3B,GAAG,CAAC+B,IAAI,CAAC;UAClB,SAAS,EAAEuB,eAAe,CAACvB,IAAI;QACjC,CAAC,EAAE,CAACyB,WAAW,CAAC,CAAC,CAAC,CAAC;MACrB;IACF,CAAC;IACD1D,SAAS,CAAC;MACRgC,IAAI;MACJG;IACF,CAAC,CAAC;IACFtC,YAAY,CAACwB,IAAI,EAAGqB,KAAK,IAAKS,OAAO,CAAC,SAAS,EAAET,KAAK,CAAC,EAAE;MACvDiB,SAAS,EAAE;IACb,CAAC,CAAC;IACF7D,gBAAgB,CAAC,WAAW,EAAE8C,WAAW,EAAE;MACzCgB,MAAM,EAAEvC;IACV,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIwC,EAAE;MACN,MAAMC,YAAY,GAAG;QACnBC,SAAS,EAAE,eAAevC,KAAK,CAACC,MAAM,WAAW;QACjDuC,kBAAkB,EAAExC,KAAK,CAACE,QAAQ,GAAG,IAAI,GAAG;MAC9C,CAAC;MACD,OAAOtC,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEiC,IAAI;QACX,OAAO,EAAEnB,GAAG,CAAC,CAAC;QACd,SAAS,EAAEsD,eAAe,CAAC,MAAM,CAAC;QAClC,qBAAqB,EAAEf,YAAY;QACnC,YAAY,EAAEQ,UAAU;QACxB,eAAe,EAAEA;MACnB,CAAC,EAAE,CAAC7D,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEc,GAAG,CAAC,SAAS,CAAC;QACvB,OAAO,EAAE4D;MACX,CAAC,EAAE,CAACL,iBAAiB,CAAC,MAAM,EAAEnC,OAAO,CAAC,EAAE,CAACuC,EAAE,GAAG7C,KAAK,CAACiD,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,EAAE,CAACK,IAAI,CAAClD,KAAK,CAAC,EAAEyC,iBAAiB,CAAC,OAAO,EAAElC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1I,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEZ,aAAa,IAAIsD,OAAO,EACxB9D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}