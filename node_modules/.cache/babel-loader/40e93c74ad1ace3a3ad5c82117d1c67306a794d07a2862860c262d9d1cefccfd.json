{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { pick, extend, truthProp, makeArrayProp, createNamespace, HAPTICS_FEEDBACK } from \"../utils/index.mjs\";\nimport { popupSharedProps, popupSharedPropKeys } from \"../popup/shared.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nconst isImage = name2 => name2 == null ? void 0 : name2.includes(\"/\");\nconst popupInheritKeys = [...popupSharedPropKeys, \"round\", \"closeOnPopstate\", \"safeAreaInsetBottom\"];\nconst iconMap = {\n  qq: \"qq\",\n  link: \"link-o\",\n  weibo: \"weibo\",\n  qrcode: \"qr\",\n  poster: \"photo-o\",\n  wechat: \"wechat\",\n  \"weapp-qrcode\": \"miniprogram-o\",\n  \"wechat-moments\": \"wechat-moments\"\n};\nconst [name, bem, t] = createNamespace(\"share-sheet\");\nconst shareSheetProps = extend({}, popupSharedProps, {\n  title: String,\n  round: truthProp,\n  options: makeArrayProp(),\n  cancelText: String,\n  description: String,\n  closeOnPopstate: truthProp,\n  safeAreaInsetBottom: truthProp\n});\nvar stdin_default = defineComponent({\n  name,\n  props: shareSheetProps,\n  emits: [\"cancel\", \"select\", \"update:show\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const updateShow = value => emit(\"update:show\", value);\n    const onCancel = () => {\n      updateShow(false);\n      emit(\"cancel\");\n    };\n    const onSelect = (option, index) => emit(\"select\", option, index);\n    const renderHeader = () => {\n      const title = slots.title ? slots.title() : props.title;\n      const description = slots.description ? slots.description() : props.description;\n      if (title || description) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"header\")\n        }, [title && _createVNode(\"h2\", {\n          \"class\": bem(\"title\")\n        }, [title]), description && _createVNode(\"span\", {\n          \"class\": bem(\"description\")\n        }, [description])]);\n      }\n    };\n    const renderIcon = icon => {\n      if (isImage(icon)) {\n        return _createVNode(\"img\", {\n          \"src\": icon,\n          \"class\": bem(\"image-icon\")\n        }, null);\n      }\n      return _createVNode(\"div\", {\n        \"class\": bem(\"icon\", [icon])\n      }, [_createVNode(Icon, {\n        \"name\": iconMap[icon] || icon\n      }, null)]);\n    };\n    const renderOption = (option, index) => {\n      const {\n        name: name2,\n        icon,\n        className,\n        description\n      } = option;\n      return _createVNode(\"div\", {\n        \"role\": \"button\",\n        \"tabindex\": 0,\n        \"class\": [bem(\"option\"), className, HAPTICS_FEEDBACK],\n        \"onClick\": () => onSelect(option, index)\n      }, [renderIcon(icon), name2 && _createVNode(\"span\", {\n        \"class\": bem(\"name\")\n      }, [name2]), description && _createVNode(\"span\", {\n        \"class\": bem(\"option-description\")\n      }, [description])]);\n    };\n    const renderOptions = (options, border) => _createVNode(\"div\", {\n      \"class\": bem(\"options\", {\n        border\n      })\n    }, [options.map(renderOption)]);\n    const renderRows = () => {\n      const {\n        options\n      } = props;\n      if (Array.isArray(options[0])) {\n        return options.map((item, index) => renderOptions(item, index !== 0));\n      }\n      return renderOptions(options);\n    };\n    const renderCancelButton = () => {\n      var _a;\n      const cancelText = (_a = props.cancelText) != null ? _a : t(\"cancel\");\n      if (slots.cancel || cancelText) {\n        return _createVNode(\"button\", {\n          \"type\": \"button\",\n          \"class\": bem(\"cancel\"),\n          \"onClick\": onCancel\n        }, [slots.cancel ? slots.cancel() : cancelText]);\n      }\n    };\n    return () => _createVNode(Popup, _mergeProps({\n      \"class\": bem(),\n      \"position\": \"bottom\",\n      \"onUpdate:show\": updateShow\n    }, pick(props, popupInheritKeys)), {\n      default: () => [renderHeader(), renderRows(), renderCancelButton()]\n    });\n  }\n});\nexport { stdin_default as default, shareSheetProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "mergeProps", "_mergeProps", "pick", "extend", "truthProp", "makeArrayProp", "createNamespace", "HAPTICS_FEEDBACK", "popupSharedProps", "popupSharedPropKeys", "Icon", "Popup", "isImage", "name2", "includes", "popupInheritKeys", "iconMap", "qq", "link", "weibo", "qrcode", "poster", "wechat", "name", "bem", "t", "shareSheetProps", "title", "String", "round", "options", "cancelText", "description", "closeOnPopstate", "safeAreaInsetBottom", "stdin_default", "props", "emits", "setup", "emit", "slots", "updateShow", "value", "onCancel", "onSelect", "option", "index", "renderHeader", "renderIcon", "icon", "renderOption", "className", "onClick", "renderOptions", "border", "map", "renderRows", "Array", "isArray", "item", "renderCancelButton", "_a", "cancel", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/share-sheet/ShareSheet.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { pick, extend, truthProp, makeArrayProp, createNamespace, HAPTICS_FEEDBACK } from \"../utils/index.mjs\";\nimport { popupSharedProps, popupSharedPropKeys } from \"../popup/shared.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Popup } from \"../popup/index.mjs\";\nconst isImage = (name2) => name2 == null ? void 0 : name2.includes(\"/\");\nconst popupInheritKeys = [...popupSharedPropKeys, \"round\", \"closeOnPopstate\", \"safeAreaInsetBottom\"];\nconst iconMap = {\n  qq: \"qq\",\n  link: \"link-o\",\n  weibo: \"weibo\",\n  qrcode: \"qr\",\n  poster: \"photo-o\",\n  wechat: \"wechat\",\n  \"weapp-qrcode\": \"miniprogram-o\",\n  \"wechat-moments\": \"wechat-moments\"\n};\nconst [name, bem, t] = createNamespace(\"share-sheet\");\nconst shareSheetProps = extend({}, popupSharedProps, {\n  title: String,\n  round: truthProp,\n  options: makeArrayProp(),\n  cancelText: String,\n  description: String,\n  closeOnPopstate: truthProp,\n  safeAreaInsetBottom: truthProp\n});\nvar stdin_default = defineComponent({\n  name,\n  props: shareSheetProps,\n  emits: [\"cancel\", \"select\", \"update:show\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const updateShow = (value) => emit(\"update:show\", value);\n    const onCancel = () => {\n      updateShow(false);\n      emit(\"cancel\");\n    };\n    const onSelect = (option, index) => emit(\"select\", option, index);\n    const renderHeader = () => {\n      const title = slots.title ? slots.title() : props.title;\n      const description = slots.description ? slots.description() : props.description;\n      if (title || description) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"header\")\n        }, [title && _createVNode(\"h2\", {\n          \"class\": bem(\"title\")\n        }, [title]), description && _createVNode(\"span\", {\n          \"class\": bem(\"description\")\n        }, [description])]);\n      }\n    };\n    const renderIcon = (icon) => {\n      if (isImage(icon)) {\n        return _createVNode(\"img\", {\n          \"src\": icon,\n          \"class\": bem(\"image-icon\")\n        }, null);\n      }\n      return _createVNode(\"div\", {\n        \"class\": bem(\"icon\", [icon])\n      }, [_createVNode(Icon, {\n        \"name\": iconMap[icon] || icon\n      }, null)]);\n    };\n    const renderOption = (option, index) => {\n      const {\n        name: name2,\n        icon,\n        className,\n        description\n      } = option;\n      return _createVNode(\"div\", {\n        \"role\": \"button\",\n        \"tabindex\": 0,\n        \"class\": [bem(\"option\"), className, HAPTICS_FEEDBACK],\n        \"onClick\": () => onSelect(option, index)\n      }, [renderIcon(icon), name2 && _createVNode(\"span\", {\n        \"class\": bem(\"name\")\n      }, [name2]), description && _createVNode(\"span\", {\n        \"class\": bem(\"option-description\")\n      }, [description])]);\n    };\n    const renderOptions = (options, border) => _createVNode(\"div\", {\n      \"class\": bem(\"options\", {\n        border\n      })\n    }, [options.map(renderOption)]);\n    const renderRows = () => {\n      const {\n        options\n      } = props;\n      if (Array.isArray(options[0])) {\n        return options.map((item, index) => renderOptions(item, index !== 0));\n      }\n      return renderOptions(options);\n    };\n    const renderCancelButton = () => {\n      var _a;\n      const cancelText = (_a = props.cancelText) != null ? _a : t(\"cancel\");\n      if (slots.cancel || cancelText) {\n        return _createVNode(\"button\", {\n          \"type\": \"button\",\n          \"class\": bem(\"cancel\"),\n          \"onClick\": onCancel\n        }, [slots.cancel ? slots.cancel() : cancelText]);\n      }\n    };\n    return () => _createVNode(Popup, _mergeProps({\n      \"class\": bem(),\n      \"position\": \"bottom\",\n      \"onUpdate:show\": updateShow\n    }, pick(props, popupInheritKeys)), {\n      default: () => [renderHeader(), renderRows(), renderCancelButton()]\n    });\n  }\n});\nexport {\n  stdin_default as default,\n  shareSheetProps\n};\n"], "mappings": ";;AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC7F,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC9G,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,qBAAqB;AAC3E,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,MAAMC,OAAO,GAAIC,KAAK,IAAKA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC;AACvE,MAAMC,gBAAgB,GAAG,CAAC,GAAGN,mBAAmB,EAAE,OAAO,EAAE,iBAAiB,EAAE,qBAAqB,CAAC;AACpG,MAAMO,OAAO,GAAG;EACdC,EAAE,EAAE,IAAI;EACRC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE,QAAQ;EAChB,cAAc,EAAE,eAAe;EAC/B,gBAAgB,EAAE;AACpB,CAAC;AACD,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGnB,eAAe,CAAC,aAAa,CAAC;AACrD,MAAMoB,eAAe,GAAGvB,MAAM,CAAC,CAAC,CAAC,EAAEK,gBAAgB,EAAE;EACnDmB,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAEzB,SAAS;EAChB0B,OAAO,EAAEzB,aAAa,CAAC,CAAC;EACxB0B,UAAU,EAAEH,MAAM;EAClBI,WAAW,EAAEJ,MAAM;EACnBK,eAAe,EAAE7B,SAAS;EAC1B8B,mBAAmB,EAAE9B;AACvB,CAAC,CAAC;AACF,IAAI+B,aAAa,GAAGtC,eAAe,CAAC;EAClC0B,IAAI;EACJa,KAAK,EAAEV,eAAe;EACtBW,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;EAC1CC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,UAAU,GAAIC,KAAK,IAAKH,IAAI,CAAC,aAAa,EAAEG,KAAK,CAAC;IACxD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACrBF,UAAU,CAAC,KAAK,CAAC;MACjBF,IAAI,CAAC,QAAQ,CAAC;IAChB,CAAC;IACD,MAAMK,QAAQ,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAKP,IAAI,CAAC,QAAQ,EAAEM,MAAM,EAAEC,KAAK,CAAC;IACjE,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMpB,KAAK,GAAGa,KAAK,CAACb,KAAK,GAAGa,KAAK,CAACb,KAAK,CAAC,CAAC,GAAGS,KAAK,CAACT,KAAK;MACvD,MAAMK,WAAW,GAAGQ,KAAK,CAACR,WAAW,GAAGQ,KAAK,CAACR,WAAW,CAAC,CAAC,GAAGI,KAAK,CAACJ,WAAW;MAC/E,IAAIL,KAAK,IAAIK,WAAW,EAAE;QACxB,OAAOjC,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEyB,GAAG,CAAC,QAAQ;QACvB,CAAC,EAAE,CAACG,KAAK,IAAI5B,YAAY,CAAC,IAAI,EAAE;UAC9B,OAAO,EAAEyB,GAAG,CAAC,OAAO;QACtB,CAAC,EAAE,CAACG,KAAK,CAAC,CAAC,EAAEK,WAAW,IAAIjC,YAAY,CAAC,MAAM,EAAE;UAC/C,OAAO,EAAEyB,GAAG,CAAC,aAAa;QAC5B,CAAC,EAAE,CAACQ,WAAW,CAAC,CAAC,CAAC,CAAC;MACrB;IACF,CAAC;IACD,MAAMgB,UAAU,GAAIC,IAAI,IAAK;MAC3B,IAAIrC,OAAO,CAACqC,IAAI,CAAC,EAAE;QACjB,OAAOlD,YAAY,CAAC,KAAK,EAAE;UACzB,KAAK,EAAEkD,IAAI;UACX,OAAO,EAAEzB,GAAG,CAAC,YAAY;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV;MACA,OAAOzB,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEyB,GAAG,CAAC,MAAM,EAAE,CAACyB,IAAI,CAAC;MAC7B,CAAC,EAAE,CAAClD,YAAY,CAACW,IAAI,EAAE;QACrB,MAAM,EAAEM,OAAO,CAACiC,IAAI,CAAC,IAAIA;MAC3B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,MAAMC,YAAY,GAAGA,CAACL,MAAM,EAAEC,KAAK,KAAK;MACtC,MAAM;QACJvB,IAAI,EAAEV,KAAK;QACXoC,IAAI;QACJE,SAAS;QACTnB;MACF,CAAC,GAAGa,MAAM;MACV,OAAO9C,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE,CAAC;QACb,OAAO,EAAE,CAACyB,GAAG,CAAC,QAAQ,CAAC,EAAE2B,SAAS,EAAE5C,gBAAgB,CAAC;QACrD,SAAS,EAAE6C,CAAA,KAAMR,QAAQ,CAACC,MAAM,EAAEC,KAAK;MACzC,CAAC,EAAE,CAACE,UAAU,CAACC,IAAI,CAAC,EAAEpC,KAAK,IAAId,YAAY,CAAC,MAAM,EAAE;QAClD,OAAO,EAAEyB,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACX,KAAK,CAAC,CAAC,EAAEmB,WAAW,IAAIjC,YAAY,CAAC,MAAM,EAAE;QAC/C,OAAO,EAAEyB,GAAG,CAAC,oBAAoB;MACnC,CAAC,EAAE,CAACQ,WAAW,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IACD,MAAMqB,aAAa,GAAGA,CAACvB,OAAO,EAAEwB,MAAM,KAAKvD,YAAY,CAAC,KAAK,EAAE;MAC7D,OAAO,EAAEyB,GAAG,CAAC,SAAS,EAAE;QACtB8B;MACF,CAAC;IACH,CAAC,EAAE,CAACxB,OAAO,CAACyB,GAAG,CAACL,YAAY,CAAC,CAAC,CAAC;IAC/B,MAAMM,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAM;QACJ1B;MACF,CAAC,GAAGM,KAAK;MACT,IAAIqB,KAAK,CAACC,OAAO,CAAC5B,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,OAAOA,OAAO,CAACyB,GAAG,CAAC,CAACI,IAAI,EAAEb,KAAK,KAAKO,aAAa,CAACM,IAAI,EAAEb,KAAK,KAAK,CAAC,CAAC,CAAC;MACvE;MACA,OAAOO,aAAa,CAACvB,OAAO,CAAC;IAC/B,CAAC;IACD,MAAM8B,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAIC,EAAE;MACN,MAAM9B,UAAU,GAAG,CAAC8B,EAAE,GAAGzB,KAAK,CAACL,UAAU,KAAK,IAAI,GAAG8B,EAAE,GAAGpC,CAAC,CAAC,QAAQ,CAAC;MACrE,IAAIe,KAAK,CAACsB,MAAM,IAAI/B,UAAU,EAAE;QAC9B,OAAOhC,YAAY,CAAC,QAAQ,EAAE;UAC5B,MAAM,EAAE,QAAQ;UAChB,OAAO,EAAEyB,GAAG,CAAC,QAAQ,CAAC;UACtB,SAAS,EAAEmB;QACb,CAAC,EAAE,CAACH,KAAK,CAACsB,MAAM,GAAGtB,KAAK,CAACsB,MAAM,CAAC,CAAC,GAAG/B,UAAU,CAAC,CAAC;MAClD;IACF,CAAC;IACD,OAAO,MAAMhC,YAAY,CAACY,KAAK,EAAEV,WAAW,CAAC;MAC3C,OAAO,EAAEuB,GAAG,CAAC,CAAC;MACd,UAAU,EAAE,QAAQ;MACpB,eAAe,EAAEiB;IACnB,CAAC,EAAEvC,IAAI,CAACkC,KAAK,EAAErB,gBAAgB,CAAC,CAAC,EAAE;MACjCgD,OAAO,EAAEA,CAAA,KAAM,CAAChB,YAAY,CAAC,CAAC,EAAES,UAAU,CAAC,CAAC,EAAEI,kBAAkB,CAAC,CAAC;IACpE,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AACF,SACEzB,aAAa,IAAI4B,OAAO,EACxBrC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}