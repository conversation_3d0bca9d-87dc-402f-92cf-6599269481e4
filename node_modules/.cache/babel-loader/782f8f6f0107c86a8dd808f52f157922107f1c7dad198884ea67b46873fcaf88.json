{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _PickerGroup from \"./PickerGroup.mjs\";\nconst PickerGroup = withInstall(_PickerGroup);\nvar stdin_default = PickerGroup;\nimport { pickerGroupProps } from \"./PickerGroup.mjs\";\nexport { PickerGroup, stdin_default as default, pickerGroupProps };", "map": {"version": 3, "names": ["withInstall", "_PickerGroup", "PickerGroup", "stdin_default", "pickerGroupProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/picker-group/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _PickerGroup from \"./PickerGroup.mjs\";\nconst PickerGroup = withInstall(_PickerGroup);\nvar stdin_default = PickerGroup;\nimport { pickerGroupProps } from \"./PickerGroup.mjs\";\nexport {\n  PickerGroup,\n  stdin_default as default,\n  pickerGroupProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXC,aAAa,IAAIE,OAAO,EACxBD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}