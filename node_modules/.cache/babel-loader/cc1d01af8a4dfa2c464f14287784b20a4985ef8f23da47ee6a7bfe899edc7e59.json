{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton-avatar\");\nconst skeletonAvatarProps = {\n  avatarSize: numericProp,\n  avatarShape: makeStringProp(\"round\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: skeletonAvatarProps,\n  setup(props) {\n    return () => _createVNode(\"div\", {\n      \"class\": bem([props.avatarShape]),\n      \"style\": getSizeStyle(props.avatarSize)\n    }, null);\n  }\n});\nexport { stdin_default as default, skeletonAvatarProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "numericProp", "getSizeStyle", "makeStringProp", "createNamespace", "name", "bem", "skeletonAvatarProps", "avatarSize", "avatar<PERSON><PERSON><PERSON>", "stdin_default", "props", "setup", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/skeleton-avatar/SkeletonAvatar.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton-avatar\");\nconst skeletonAvatarProps = {\n  avatarSize: numericProp,\n  avatarShape: makeStringProp(\"round\")\n};\nvar stdin_default = defineComponent({\n  name,\n  props: skeletonAvatarProps,\n  setup(props) {\n    return () => _createVNode(\"div\", {\n      \"class\": bem([props.avatarShape]),\n      \"style\": getSizeStyle(props.avatarSize)\n    }, null);\n  }\n});\nexport {\n  stdin_default as default,\n  skeletonAvatarProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC/F,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGF,eAAe,CAAC,iBAAiB,CAAC;AACtD,MAAMG,mBAAmB,GAAG;EAC1BC,UAAU,EAAEP,WAAW;EACvBQ,WAAW,EAAEN,cAAc,CAAC,OAAO;AACrC,CAAC;AACD,IAAIO,aAAa,GAAGZ,eAAe,CAAC;EAClCO,IAAI;EACJM,KAAK,EAAEJ,mBAAmB;EAC1BK,KAAKA,CAACD,KAAK,EAAE;IACX,OAAO,MAAMX,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEM,GAAG,CAAC,CAACK,KAAK,CAACF,WAAW,CAAC,CAAC;MACjC,OAAO,EAAEP,YAAY,CAACS,KAAK,CAACH,UAAU;IACxC,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC,CAAC;AACF,SACEE,aAAa,IAAIG,OAAO,EACxBN,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}