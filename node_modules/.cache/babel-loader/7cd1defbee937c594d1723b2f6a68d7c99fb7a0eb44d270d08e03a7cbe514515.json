{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, createVNode as _createVNode, Fragment as _Fragment, mergeProps as _mergeProps } from \"vue\";\nimport { addUnit, truthProp, numericProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport SkeletonTitle from \"../skeleton-title/index.mjs\";\nimport SkeletonAvatar from \"../skeleton-avatar/index.mjs\";\nimport SkeletonParagraph, { DEFAULT_ROW_WIDTH } from \"../skeleton-paragraph/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton\");\nconst DEFAULT_LAST_ROW_WIDTH = \"60%\";\nconst skeletonProps = {\n  row: makeNumericProp(0),\n  round: Boolean,\n  title: Boolean,\n  titleWidth: numericProp,\n  avatar: Boolean,\n  avatarSize: numericProp,\n  avatarShape: makeStringProp(\"round\"),\n  loading: truthProp,\n  animate: truthProp,\n  rowWidth: {\n    type: [Number, String, Array],\n    default: DEFAULT_ROW_WIDTH\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: skeletonProps,\n  setup(props, {\n    slots,\n    attrs\n  }) {\n    const renderAvatar = () => {\n      if (props.avatar) {\n        return _createVNode(SkeletonAvatar, {\n          \"avatarShape\": props.avatarShape,\n          \"avatarSize\": props.avatarSize\n        }, null);\n      }\n    };\n    const renderTitle = () => {\n      if (props.title) {\n        return _createVNode(SkeletonTitle, {\n          \"round\": props.round,\n          \"titleWidth\": props.titleWidth\n        }, null);\n      }\n    };\n    const getRowWidth = index => {\n      const {\n        rowWidth\n      } = props;\n      if (rowWidth === DEFAULT_ROW_WIDTH && index === +props.row - 1) {\n        return DEFAULT_LAST_ROW_WIDTH;\n      }\n      if (Array.isArray(rowWidth)) {\n        return rowWidth[index];\n      }\n      return rowWidth;\n    };\n    const renderRows = () => Array(+props.row).fill(\"\").map((_, i) => _createVNode(SkeletonParagraph, {\n      \"key\": i,\n      \"round\": props.round,\n      \"rowWidth\": addUnit(getRowWidth(i))\n    }, null));\n    const renderContents = () => {\n      if (slots.template) {\n        return slots.template();\n      }\n      return _createVNode(_Fragment, null, [renderAvatar(), _createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [renderTitle(), renderRows()])]);\n    };\n    return () => {\n      var _a;\n      if (!props.loading) {\n        return (_a = slots.default) == null ? void 0 : _a.call(slots);\n      }\n      return _createVNode(\"div\", _mergeProps({\n        \"class\": bem({\n          animate: props.animate,\n          round: props.round\n        })\n      }, attrs), [renderContents()]);\n    };\n  }\n});\nexport { stdin_default as default, skeletonProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "Fragment", "_Fragment", "mergeProps", "_mergeProps", "addUnit", "truthProp", "numericProp", "makeStringProp", "makeNumericProp", "createNamespace", "SkeletonTitle", "SkeletonAvatar", "SkeletonParagraph", "DEFAULT_ROW_WIDTH", "name", "bem", "DEFAULT_LAST_ROW_WIDTH", "skeletonProps", "row", "round", "Boolean", "title", "titleWidth", "avatar", "avatarSize", "avatar<PERSON><PERSON><PERSON>", "loading", "animate", "row<PERSON>id<PERSON>", "type", "Number", "String", "Array", "default", "stdin_default", "inheritAttrs", "props", "setup", "slots", "attrs", "renderAvat<PERSON>", "renderTitle", "getRowWidth", "index", "isArray", "renderRows", "fill", "map", "_", "i", "renderContents", "template", "_a", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/skeleton/Skeleton.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode, Fragment as _Fragment, mergeProps as _mergeProps } from \"vue\";\nimport { addUnit, truthProp, numericProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport SkeletonTitle from \"../skeleton-title/index.mjs\";\nimport SkeletonAvatar from \"../skeleton-avatar/index.mjs\";\nimport SkeletonParagraph, { DEFAULT_ROW_WIDTH } from \"../skeleton-paragraph/index.mjs\";\nconst [name, bem] = createNamespace(\"skeleton\");\nconst DEFAULT_LAST_ROW_WIDTH = \"60%\";\nconst skeletonProps = {\n  row: makeNumericProp(0),\n  round: <PERSON><PERSON><PERSON>,\n  title: <PERSON><PERSON>an,\n  titleWidth: numericProp,\n  avatar: Boolean,\n  avatarSize: numericProp,\n  avatarShape: makeStringProp(\"round\"),\n  loading: truthProp,\n  animate: truthProp,\n  rowWidth: {\n    type: [Number, String, Array],\n    default: DEFAULT_ROW_WIDTH\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: skeletonProps,\n  setup(props, {\n    slots,\n    attrs\n  }) {\n    const renderAvatar = () => {\n      if (props.avatar) {\n        return _createVNode(SkeletonAvatar, {\n          \"avatarShape\": props.avatarShape,\n          \"avatarSize\": props.avatarSize\n        }, null);\n      }\n    };\n    const renderTitle = () => {\n      if (props.title) {\n        return _createVNode(SkeletonTitle, {\n          \"round\": props.round,\n          \"titleWidth\": props.titleWidth\n        }, null);\n      }\n    };\n    const getRowWidth = (index) => {\n      const {\n        rowWidth\n      } = props;\n      if (rowWidth === DEFAULT_ROW_WIDTH && index === +props.row - 1) {\n        return DEFAULT_LAST_ROW_WIDTH;\n      }\n      if (Array.isArray(rowWidth)) {\n        return rowWidth[index];\n      }\n      return rowWidth;\n    };\n    const renderRows = () => Array(+props.row).fill(\"\").map((_, i) => _createVNode(SkeletonParagraph, {\n      \"key\": i,\n      \"round\": props.round,\n      \"rowWidth\": addUnit(getRowWidth(i))\n    }, null));\n    const renderContents = () => {\n      if (slots.template) {\n        return slots.template();\n      }\n      return _createVNode(_Fragment, null, [renderAvatar(), _createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [renderTitle(), renderRows()])]);\n    };\n    return () => {\n      var _a;\n      if (!props.loading) {\n        return (_a = slots.default) == null ? void 0 : _a.call(slots);\n      }\n      return _createVNode(\"div\", _mergeProps({\n        \"class\": bem({\n          animate: props.animate,\n          round: props.round\n        })\n      }, attrs), [renderContents()]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  skeletonProps\n};\n"], "mappings": ";;AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,EAAEC,QAAQ,IAAIC,SAAS,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AACpH,SAASC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACtH,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,iBAAiB,IAAIC,iBAAiB,QAAQ,iCAAiC;AACtF,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGN,eAAe,CAAC,UAAU,CAAC;AAC/C,MAAMO,sBAAsB,GAAG,KAAK;AACpC,MAAMC,aAAa,GAAG;EACpBC,GAAG,EAAEV,eAAe,CAAC,CAAC,CAAC;EACvBW,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAED,OAAO;EACdE,UAAU,EAAEhB,WAAW;EACvBiB,MAAM,EAAEH,OAAO;EACfI,UAAU,EAAElB,WAAW;EACvBmB,WAAW,EAAElB,cAAc,CAAC,OAAO,CAAC;EACpCmB,OAAO,EAAErB,SAAS;EAClBsB,OAAO,EAAEtB,SAAS;EAClBuB,QAAQ,EAAE;IACRC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC;IAC7BC,OAAO,EAAEpB;EACX;AACF,CAAC;AACD,IAAIqB,aAAa,GAAGrC,eAAe,CAAC;EAClCiB,IAAI;EACJqB,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAEnB,aAAa;EACpBoB,KAAKA,CAACD,KAAK,EAAE;IACXE,KAAK;IACLC;EACF,CAAC,EAAE;IACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIJ,KAAK,CAACb,MAAM,EAAE;QAChB,OAAOxB,YAAY,CAACY,cAAc,EAAE;UAClC,aAAa,EAAEyB,KAAK,CAACX,WAAW;UAChC,YAAY,EAAEW,KAAK,CAACZ;QACtB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,MAAMiB,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIL,KAAK,CAACf,KAAK,EAAE;QACf,OAAOtB,YAAY,CAACW,aAAa,EAAE;UACjC,OAAO,EAAE0B,KAAK,CAACjB,KAAK;UACpB,YAAY,EAAEiB,KAAK,CAACd;QACtB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,MAAMoB,WAAW,GAAIC,KAAK,IAAK;MAC7B,MAAM;QACJf;MACF,CAAC,GAAGQ,KAAK;MACT,IAAIR,QAAQ,KAAKf,iBAAiB,IAAI8B,KAAK,KAAK,CAACP,KAAK,CAAClB,GAAG,GAAG,CAAC,EAAE;QAC9D,OAAOF,sBAAsB;MAC/B;MACA,IAAIgB,KAAK,CAACY,OAAO,CAAChB,QAAQ,CAAC,EAAE;QAC3B,OAAOA,QAAQ,CAACe,KAAK,CAAC;MACxB;MACA,OAAOf,QAAQ;IACjB,CAAC;IACD,MAAMiB,UAAU,GAAGA,CAAA,KAAMb,KAAK,CAAC,CAACI,KAAK,CAAClB,GAAG,CAAC,CAAC4B,IAAI,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKlD,YAAY,CAACa,iBAAiB,EAAE;MAChG,KAAK,EAAEqC,CAAC;MACR,OAAO,EAAEb,KAAK,CAACjB,KAAK;MACpB,UAAU,EAAEf,OAAO,CAACsC,WAAW,CAACO,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC,CAAC;IACT,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIZ,KAAK,CAACa,QAAQ,EAAE;QAClB,OAAOb,KAAK,CAACa,QAAQ,CAAC,CAAC;MACzB;MACA,OAAOpD,YAAY,CAACE,SAAS,EAAE,IAAI,EAAE,CAACuC,YAAY,CAAC,CAAC,EAAEzC,YAAY,CAAC,KAAK,EAAE;QACxE,OAAO,EAAEgB,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,CAAC0B,WAAW,CAAC,CAAC,EAAEI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,OAAO,MAAM;MACX,IAAIO,EAAE;MACN,IAAI,CAAChB,KAAK,CAACV,OAAO,EAAE;QAClB,OAAO,CAAC0B,EAAE,GAAGd,KAAK,CAACL,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,EAAE,CAACC,IAAI,CAACf,KAAK,CAAC;MAC/D;MACA,OAAOvC,YAAY,CAAC,KAAK,EAAEI,WAAW,CAAC;QACrC,OAAO,EAAEY,GAAG,CAAC;UACXY,OAAO,EAAES,KAAK,CAACT,OAAO;UACtBR,KAAK,EAAEiB,KAAK,CAACjB;QACf,CAAC;MACH,CAAC,EAAEoB,KAAK,CAAC,EAAE,CAACW,cAAc,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEhB,aAAa,IAAID,OAAO,EACxBhB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}