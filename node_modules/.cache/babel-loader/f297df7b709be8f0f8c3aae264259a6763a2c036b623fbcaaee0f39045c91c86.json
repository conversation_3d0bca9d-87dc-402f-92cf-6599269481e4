{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _BackTop from \"./BackTop.mjs\";\nconst BackTop = withInstall(_BackTop);\nvar stdin_default = BackTop;\nimport { backTopProps } from \"./BackTop.mjs\";\nexport { BackTop, backTopProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_BackTop", "BackTop", "stdin_default", "backTopProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/back-top/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _BackTop from \"./BackTop.mjs\";\nconst BackTop = withInstall(_BackTop);\nvar stdin_default = BackTop;\nimport { backTopProps } from \"./BackTop.mjs\";\nexport {\n  BackTop,\n  backTopProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,MAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SAASE,YAAY,QAAQ,eAAe;AAC5C,SACEF,OAAO,EACPE,YAAY,EACZD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}