{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _CouponList from \"./CouponList.mjs\";\nconst CouponList = withInstall(_CouponList);\nvar stdin_default = CouponList;\nimport { couponListProps } from \"./CouponList.mjs\";\nexport { CouponList, couponListProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_CouponList", "CouponList", "stdin_default", "couponListProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/coupon-list/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _CouponList from \"./CouponList.mjs\";\nconst CouponList = withInstall(_CouponList);\nvar stdin_default = CouponList;\nimport { couponListProps } from \"./CouponList.mjs\";\nexport {\n  CouponList,\n  couponListProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVE,eAAe,EACfD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}