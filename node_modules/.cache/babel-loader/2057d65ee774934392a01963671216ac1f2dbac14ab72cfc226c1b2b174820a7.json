{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, watch, computed, nextTick, Teleport, onMounted, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, isHidden, truthProp, numericProp, getScrollTop, preventDefault, makeNumberProp, createNamespace, getRootScrollTop, setRootScrollTop } from \"../utils/index.mjs\";\nimport { useRect, useChildren, useScrollParent, useEventListener } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nfunction genAlphabet() {\n  const charCodeOfA = \"A\".charCodeAt(0);\n  const indexList = Array(26).fill(\"\").map((_, i) => String.fromCharCode(charCodeOfA + i));\n  return indexList;\n}\nconst [name, bem] = createNamespace(\"index-bar\");\nconst indexBarProps = {\n  sticky: truthProp,\n  zIndex: numericProp,\n  teleport: [String, Object],\n  highlightColor: String,\n  stickyOffsetTop: makeNumberProp(0),\n  indexList: {\n    type: Array,\n    default: genAlphabet\n  }\n};\nconst INDEX_BAR_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: indexBarProps,\n  emits: [\"select\", \"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const sidebar = ref();\n    const activeAnchor = ref(\"\");\n    const touch = useTouch();\n    const scrollParent = useScrollParent(root);\n    const {\n      children,\n      linkChildren\n    } = useChildren(INDEX_BAR_KEY);\n    let selectActiveIndex;\n    linkChildren({\n      props\n    });\n    const sidebarStyle = computed(() => {\n      if (isDef(props.zIndex)) {\n        return {\n          zIndex: +props.zIndex + 1\n        };\n      }\n    });\n    const highlightStyle = computed(() => {\n      if (props.highlightColor) {\n        return {\n          color: props.highlightColor\n        };\n      }\n    });\n    const getActiveAnchor = (scrollTop, rects) => {\n      for (let i = children.length - 1; i >= 0; i--) {\n        const prevHeight = i > 0 ? rects[i - 1].height : 0;\n        const reachTop = props.sticky ? prevHeight + props.stickyOffsetTop : 0;\n        if (scrollTop + reachTop >= rects[i].top) {\n          return i;\n        }\n      }\n      return -1;\n    };\n    const getMatchAnchor = index => children.find(item => String(item.index) === index);\n    const onScroll = () => {\n      if (isHidden(root)) {\n        return;\n      }\n      const {\n        sticky,\n        indexList\n      } = props;\n      const scrollTop = getScrollTop(scrollParent.value);\n      const scrollParentRect = useRect(scrollParent);\n      const rects = children.map(item => item.getRect(scrollParent.value, scrollParentRect));\n      let active = -1;\n      if (selectActiveIndex) {\n        const match = getMatchAnchor(selectActiveIndex);\n        if (match) {\n          const rect = match.getRect(scrollParent.value, scrollParentRect);\n          if (props.sticky && props.stickyOffsetTop) {\n            active = getActiveAnchor(rect.top - props.stickyOffsetTop, rects);\n          } else {\n            active = getActiveAnchor(rect.top, rects);\n          }\n        }\n      } else {\n        active = getActiveAnchor(scrollTop, rects);\n      }\n      activeAnchor.value = indexList[active];\n      if (sticky) {\n        children.forEach((item, index) => {\n          const {\n            state,\n            $el\n          } = item;\n          if (index === active || index === active - 1) {\n            const rect = $el.getBoundingClientRect();\n            state.left = rect.left;\n            state.width = rect.width;\n          } else {\n            state.left = null;\n            state.width = null;\n          }\n          if (index === active) {\n            state.active = true;\n            state.top = Math.max(props.stickyOffsetTop, rects[index].top - scrollTop) + scrollParentRect.top;\n          } else if (index === active - 1 && selectActiveIndex === \"\") {\n            const activeItemTop = rects[active].top - scrollTop;\n            state.active = activeItemTop > 0;\n            state.top = activeItemTop + scrollParentRect.top - rects[index].height;\n          } else {\n            state.active = false;\n          }\n        });\n      }\n      selectActiveIndex = \"\";\n    };\n    const init = () => {\n      nextTick(onScroll);\n    };\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    onMounted(init);\n    watch(() => props.indexList, init);\n    watch(activeAnchor, value => {\n      if (value) {\n        emit(\"change\", value);\n      }\n    });\n    const renderIndexes = () => props.indexList.map(index => {\n      const active = index === activeAnchor.value;\n      return _createVNode(\"span\", {\n        \"class\": bem(\"index\", {\n          active\n        }),\n        \"style\": active ? highlightStyle.value : void 0,\n        \"data-index\": index\n      }, [index]);\n    });\n    const scrollTo = index => {\n      selectActiveIndex = String(index);\n      const match = getMatchAnchor(selectActiveIndex);\n      if (match) {\n        const scrollTop = getScrollTop(scrollParent.value);\n        const scrollParentRect = useRect(scrollParent);\n        const {\n          offsetHeight\n        } = document.documentElement;\n        match.$el.scrollIntoView();\n        if (scrollTop === offsetHeight - scrollParentRect.height) {\n          onScroll();\n          return;\n        }\n        if (props.sticky && props.stickyOffsetTop) {\n          if (getRootScrollTop() === offsetHeight - scrollParentRect.height) {\n            setRootScrollTop(getRootScrollTop());\n          } else {\n            setRootScrollTop(getRootScrollTop() - props.stickyOffsetTop);\n          }\n        }\n        emit(\"select\", match.index);\n      }\n    };\n    const scrollToElement = element => {\n      const {\n        index\n      } = element.dataset;\n      if (index) {\n        scrollTo(index);\n      }\n    };\n    const onClickSidebar = event => {\n      scrollToElement(event.target);\n    };\n    let touchActiveIndex;\n    const onTouchMove = event => {\n      touch.move(event);\n      if (touch.isVertical()) {\n        preventDefault(event);\n        const {\n          clientX,\n          clientY\n        } = event.touches[0];\n        const target = document.elementFromPoint(clientX, clientY);\n        if (target) {\n          const {\n            index\n          } = target.dataset;\n          if (index && touchActiveIndex !== index) {\n            touchActiveIndex = index;\n            scrollToElement(target);\n          }\n        }\n      }\n    };\n    const renderSidebar = () => _createVNode(\"div\", {\n      \"ref\": sidebar,\n      \"class\": bem(\"sidebar\"),\n      \"style\": sidebarStyle.value,\n      \"onClick\": onClickSidebar,\n      \"onTouchstartPassive\": touch.start\n    }, [renderIndexes()]);\n    useExpose({\n      scrollTo\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: sidebar\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [props.teleport ? _createVNode(Teleport, {\n        \"to\": props.teleport\n      }, {\n        default: () => [renderSidebar()]\n      }) : renderSidebar(), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { INDEX_BAR_KEY, stdin_default as default, indexBarProps };", "map": {"version": 3, "names": ["ref", "watch", "computed", "nextTick", "Teleport", "onMounted", "defineComponent", "createVNode", "_createVNode", "isDef", "isHidden", "truthProp", "numericProp", "getScrollTop", "preventDefault", "makeNumberProp", "createNamespace", "getRootScrollTop", "setRootScrollTop", "useRect", "useChildren", "useScrollParent", "useEventListener", "useTouch", "useExpose", "gen<PERSON><PERSON><PERSON><PERSON>", "charCodeOfA", "charCodeAt", "indexList", "Array", "fill", "map", "_", "i", "String", "fromCharCode", "name", "bem", "indexBarProps", "sticky", "zIndex", "teleport", "Object", "highlightColor", "stickyOffsetTop", "type", "default", "INDEX_BAR_KEY", "Symbol", "stdin_default", "props", "emits", "setup", "emit", "slots", "root", "sidebar", "activeAnchor", "touch", "scrollParent", "children", "linkChildren", "selectActiveIndex", "sidebarStyle", "highlightStyle", "color", "getActiveAnchor", "scrollTop", "rects", "length", "prevHeight", "height", "reachTop", "top", "getMatchAnchor", "index", "find", "item", "onScroll", "value", "scrollParentRect", "getRect", "active", "match", "rect", "for<PERSON>ach", "state", "$el", "getBoundingClientRect", "left", "width", "Math", "max", "activeItemTop", "init", "target", "passive", "renderIndexes", "scrollTo", "offsetHeight", "document", "documentElement", "scrollIntoView", "scrollToElement", "element", "dataset", "onClickSidebar", "event", "touchActiveIndex", "onTouchMove", "move", "isVertical", "clientX", "clientY", "touches", "elementFromPoint", "renderSidebar", "start", "_a", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/index-bar/IndexBar.mjs"], "sourcesContent": ["import { ref, watch, computed, nextTick, Teleport, onMounted, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, isHidden, truthProp, numericProp, getScrollTop, preventDefault, makeNumberProp, createNamespace, getRootScrollTop, setRootScrollTop } from \"../utils/index.mjs\";\nimport { useRect, useChildren, useScrollParent, useEventListener } from \"@vant/use\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nfunction genAlphabet() {\n  const charCodeOfA = \"A\".charCodeAt(0);\n  const indexList = Array(26).fill(\"\").map((_, i) => String.fromCharCode(charCodeOfA + i));\n  return indexList;\n}\nconst [name, bem] = createNamespace(\"index-bar\");\nconst indexBarProps = {\n  sticky: truthProp,\n  zIndex: numericProp,\n  teleport: [String, Object],\n  highlightColor: String,\n  stickyOffsetTop: makeNumberProp(0),\n  indexList: {\n    type: Array,\n    default: genAlphabet\n  }\n};\nconst INDEX_BAR_KEY = Symbol(name);\nvar stdin_default = defineComponent({\n  name,\n  props: indexBarProps,\n  emits: [\"select\", \"change\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const root = ref();\n    const sidebar = ref();\n    const activeAnchor = ref(\"\");\n    const touch = useTouch();\n    const scrollParent = useScrollParent(root);\n    const {\n      children,\n      linkChildren\n    } = useChildren(INDEX_BAR_KEY);\n    let selectActiveIndex;\n    linkChildren({\n      props\n    });\n    const sidebarStyle = computed(() => {\n      if (isDef(props.zIndex)) {\n        return {\n          zIndex: +props.zIndex + 1\n        };\n      }\n    });\n    const highlightStyle = computed(() => {\n      if (props.highlightColor) {\n        return {\n          color: props.highlightColor\n        };\n      }\n    });\n    const getActiveAnchor = (scrollTop, rects) => {\n      for (let i = children.length - 1; i >= 0; i--) {\n        const prevHeight = i > 0 ? rects[i - 1].height : 0;\n        const reachTop = props.sticky ? prevHeight + props.stickyOffsetTop : 0;\n        if (scrollTop + reachTop >= rects[i].top) {\n          return i;\n        }\n      }\n      return -1;\n    };\n    const getMatchAnchor = (index) => children.find((item) => String(item.index) === index);\n    const onScroll = () => {\n      if (isHidden(root)) {\n        return;\n      }\n      const {\n        sticky,\n        indexList\n      } = props;\n      const scrollTop = getScrollTop(scrollParent.value);\n      const scrollParentRect = useRect(scrollParent);\n      const rects = children.map((item) => item.getRect(scrollParent.value, scrollParentRect));\n      let active = -1;\n      if (selectActiveIndex) {\n        const match = getMatchAnchor(selectActiveIndex);\n        if (match) {\n          const rect = match.getRect(scrollParent.value, scrollParentRect);\n          if (props.sticky && props.stickyOffsetTop) {\n            active = getActiveAnchor(rect.top - props.stickyOffsetTop, rects);\n          } else {\n            active = getActiveAnchor(rect.top, rects);\n          }\n        }\n      } else {\n        active = getActiveAnchor(scrollTop, rects);\n      }\n      activeAnchor.value = indexList[active];\n      if (sticky) {\n        children.forEach((item, index) => {\n          const {\n            state,\n            $el\n          } = item;\n          if (index === active || index === active - 1) {\n            const rect = $el.getBoundingClientRect();\n            state.left = rect.left;\n            state.width = rect.width;\n          } else {\n            state.left = null;\n            state.width = null;\n          }\n          if (index === active) {\n            state.active = true;\n            state.top = Math.max(props.stickyOffsetTop, rects[index].top - scrollTop) + scrollParentRect.top;\n          } else if (index === active - 1 && selectActiveIndex === \"\") {\n            const activeItemTop = rects[active].top - scrollTop;\n            state.active = activeItemTop > 0;\n            state.top = activeItemTop + scrollParentRect.top - rects[index].height;\n          } else {\n            state.active = false;\n          }\n        });\n      }\n      selectActiveIndex = \"\";\n    };\n    const init = () => {\n      nextTick(onScroll);\n    };\n    useEventListener(\"scroll\", onScroll, {\n      target: scrollParent,\n      passive: true\n    });\n    onMounted(init);\n    watch(() => props.indexList, init);\n    watch(activeAnchor, (value) => {\n      if (value) {\n        emit(\"change\", value);\n      }\n    });\n    const renderIndexes = () => props.indexList.map((index) => {\n      const active = index === activeAnchor.value;\n      return _createVNode(\"span\", {\n        \"class\": bem(\"index\", {\n          active\n        }),\n        \"style\": active ? highlightStyle.value : void 0,\n        \"data-index\": index\n      }, [index]);\n    });\n    const scrollTo = (index) => {\n      selectActiveIndex = String(index);\n      const match = getMatchAnchor(selectActiveIndex);\n      if (match) {\n        const scrollTop = getScrollTop(scrollParent.value);\n        const scrollParentRect = useRect(scrollParent);\n        const {\n          offsetHeight\n        } = document.documentElement;\n        match.$el.scrollIntoView();\n        if (scrollTop === offsetHeight - scrollParentRect.height) {\n          onScroll();\n          return;\n        }\n        if (props.sticky && props.stickyOffsetTop) {\n          if (getRootScrollTop() === offsetHeight - scrollParentRect.height) {\n            setRootScrollTop(getRootScrollTop());\n          } else {\n            setRootScrollTop(getRootScrollTop() - props.stickyOffsetTop);\n          }\n        }\n        emit(\"select\", match.index);\n      }\n    };\n    const scrollToElement = (element) => {\n      const {\n        index\n      } = element.dataset;\n      if (index) {\n        scrollTo(index);\n      }\n    };\n    const onClickSidebar = (event) => {\n      scrollToElement(event.target);\n    };\n    let touchActiveIndex;\n    const onTouchMove = (event) => {\n      touch.move(event);\n      if (touch.isVertical()) {\n        preventDefault(event);\n        const {\n          clientX,\n          clientY\n        } = event.touches[0];\n        const target = document.elementFromPoint(clientX, clientY);\n        if (target) {\n          const {\n            index\n          } = target.dataset;\n          if (index && touchActiveIndex !== index) {\n            touchActiveIndex = index;\n            scrollToElement(target);\n          }\n        }\n      }\n    };\n    const renderSidebar = () => _createVNode(\"div\", {\n      \"ref\": sidebar,\n      \"class\": bem(\"sidebar\"),\n      \"style\": sidebarStyle.value,\n      \"onClick\": onClickSidebar,\n      \"onTouchstartPassive\": touch.start\n    }, [renderIndexes()]);\n    useExpose({\n      scrollTo\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: sidebar\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": bem()\n      }, [props.teleport ? _createVNode(Teleport, {\n        \"to\": props.teleport\n      }, {\n        default: () => [renderSidebar()]\n      }) : renderSidebar(), (_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  INDEX_BAR_KEY,\n  stdin_default as default,\n  indexBarProps\n};\n"], "mappings": ";;;;AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACvH,SAASC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC/K,SAASC,OAAO,EAAEC,WAAW,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,WAAW;AACnF,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,WAAW,GAAG,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;EACrC,MAAMC,SAAS,GAAGC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKC,MAAM,CAACC,YAAY,CAACT,WAAW,GAAGO,CAAC,CAAC,CAAC;EACxF,OAAOL,SAAS;AAClB;AACA,MAAM,CAACQ,IAAI,EAAEC,GAAG,CAAC,GAAGrB,eAAe,CAAC,WAAW,CAAC;AAChD,MAAMsB,aAAa,GAAG;EACpBC,MAAM,EAAE5B,SAAS;EACjB6B,MAAM,EAAE5B,WAAW;EACnB6B,QAAQ,EAAE,CAACP,MAAM,EAAEQ,MAAM,CAAC;EAC1BC,cAAc,EAAET,MAAM;EACtBU,eAAe,EAAE7B,cAAc,CAAC,CAAC,CAAC;EAClCa,SAAS,EAAE;IACTiB,IAAI,EAAEhB,KAAK;IACXiB,OAAO,EAAErB;EACX;AACF,CAAC;AACD,MAAMsB,aAAa,GAAGC,MAAM,CAACZ,IAAI,CAAC;AAClC,IAAIa,aAAa,GAAG3C,eAAe,CAAC;EAClC8B,IAAI;EACJc,KAAK,EAAEZ,aAAa;EACpBa,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC3BC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAGvD,GAAG,CAAC,CAAC;IAClB,MAAMwD,OAAO,GAAGxD,GAAG,CAAC,CAAC;IACrB,MAAMyD,YAAY,GAAGzD,GAAG,CAAC,EAAE,CAAC;IAC5B,MAAM0D,KAAK,GAAGnC,QAAQ,CAAC,CAAC;IACxB,MAAMoC,YAAY,GAAGtC,eAAe,CAACkC,IAAI,CAAC;IAC1C,MAAM;MACJK,QAAQ;MACRC;IACF,CAAC,GAAGzC,WAAW,CAAC2B,aAAa,CAAC;IAC9B,IAAIe,iBAAiB;IACrBD,YAAY,CAAC;MACXX;IACF,CAAC,CAAC;IACF,MAAMa,YAAY,GAAG7D,QAAQ,CAAC,MAAM;MAClC,IAAIO,KAAK,CAACyC,KAAK,CAACV,MAAM,CAAC,EAAE;QACvB,OAAO;UACLA,MAAM,EAAE,CAACU,KAAK,CAACV,MAAM,GAAG;QAC1B,CAAC;MACH;IACF,CAAC,CAAC;IACF,MAAMwB,cAAc,GAAG9D,QAAQ,CAAC,MAAM;MACpC,IAAIgD,KAAK,CAACP,cAAc,EAAE;QACxB,OAAO;UACLsB,KAAK,EAAEf,KAAK,CAACP;QACf,CAAC;MACH;IACF,CAAC,CAAC;IACF,MAAMuB,eAAe,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK;MAC5C,KAAK,IAAInC,CAAC,GAAG2B,QAAQ,CAACS,MAAM,GAAG,CAAC,EAAEpC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC7C,MAAMqC,UAAU,GAAGrC,CAAC,GAAG,CAAC,GAAGmC,KAAK,CAACnC,CAAC,GAAG,CAAC,CAAC,CAACsC,MAAM,GAAG,CAAC;QAClD,MAAMC,QAAQ,GAAGtB,KAAK,CAACX,MAAM,GAAG+B,UAAU,GAAGpB,KAAK,CAACN,eAAe,GAAG,CAAC;QACtE,IAAIuB,SAAS,GAAGK,QAAQ,IAAIJ,KAAK,CAACnC,CAAC,CAAC,CAACwC,GAAG,EAAE;UACxC,OAAOxC,CAAC;QACV;MACF;MACA,OAAO,CAAC,CAAC;IACX,CAAC;IACD,MAAMyC,cAAc,GAAIC,KAAK,IAAKf,QAAQ,CAACgB,IAAI,CAAEC,IAAI,IAAK3C,MAAM,CAAC2C,IAAI,CAACF,KAAK,CAAC,KAAKA,KAAK,CAAC;IACvF,MAAMG,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAIpE,QAAQ,CAAC6C,IAAI,CAAC,EAAE;QAClB;MACF;MACA,MAAM;QACJhB,MAAM;QACNX;MACF,CAAC,GAAGsB,KAAK;MACT,MAAMiB,SAAS,GAAGtD,YAAY,CAAC8C,YAAY,CAACoB,KAAK,CAAC;MAClD,MAAMC,gBAAgB,GAAG7D,OAAO,CAACwC,YAAY,CAAC;MAC9C,MAAMS,KAAK,GAAGR,QAAQ,CAAC7B,GAAG,CAAE8C,IAAI,IAAKA,IAAI,CAACI,OAAO,CAACtB,YAAY,CAACoB,KAAK,EAAEC,gBAAgB,CAAC,CAAC;MACxF,IAAIE,MAAM,GAAG,CAAC,CAAC;MACf,IAAIpB,iBAAiB,EAAE;QACrB,MAAMqB,KAAK,GAAGT,cAAc,CAACZ,iBAAiB,CAAC;QAC/C,IAAIqB,KAAK,EAAE;UACT,MAAMC,IAAI,GAAGD,KAAK,CAACF,OAAO,CAACtB,YAAY,CAACoB,KAAK,EAAEC,gBAAgB,CAAC;UAChE,IAAI9B,KAAK,CAACX,MAAM,IAAIW,KAAK,CAACN,eAAe,EAAE;YACzCsC,MAAM,GAAGhB,eAAe,CAACkB,IAAI,CAACX,GAAG,GAAGvB,KAAK,CAACN,eAAe,EAAEwB,KAAK,CAAC;UACnE,CAAC,MAAM;YACLc,MAAM,GAAGhB,eAAe,CAACkB,IAAI,CAACX,GAAG,EAAEL,KAAK,CAAC;UAC3C;QACF;MACF,CAAC,MAAM;QACLc,MAAM,GAAGhB,eAAe,CAACC,SAAS,EAAEC,KAAK,CAAC;MAC5C;MACAX,YAAY,CAACsB,KAAK,GAAGnD,SAAS,CAACsD,MAAM,CAAC;MACtC,IAAI3C,MAAM,EAAE;QACVqB,QAAQ,CAACyB,OAAO,CAAC,CAACR,IAAI,EAAEF,KAAK,KAAK;UAChC,MAAM;YACJW,KAAK;YACLC;UACF,CAAC,GAAGV,IAAI;UACR,IAAIF,KAAK,KAAKO,MAAM,IAAIP,KAAK,KAAKO,MAAM,GAAG,CAAC,EAAE;YAC5C,MAAME,IAAI,GAAGG,GAAG,CAACC,qBAAqB,CAAC,CAAC;YACxCF,KAAK,CAACG,IAAI,GAAGL,IAAI,CAACK,IAAI;YACtBH,KAAK,CAACI,KAAK,GAAGN,IAAI,CAACM,KAAK;UAC1B,CAAC,MAAM;YACLJ,KAAK,CAACG,IAAI,GAAG,IAAI;YACjBH,KAAK,CAACI,KAAK,GAAG,IAAI;UACpB;UACA,IAAIf,KAAK,KAAKO,MAAM,EAAE;YACpBI,KAAK,CAACJ,MAAM,GAAG,IAAI;YACnBI,KAAK,CAACb,GAAG,GAAGkB,IAAI,CAACC,GAAG,CAAC1C,KAAK,CAACN,eAAe,EAAEwB,KAAK,CAACO,KAAK,CAAC,CAACF,GAAG,GAAGN,SAAS,CAAC,GAAGa,gBAAgB,CAACP,GAAG;UAClG,CAAC,MAAM,IAAIE,KAAK,KAAKO,MAAM,GAAG,CAAC,IAAIpB,iBAAiB,KAAK,EAAE,EAAE;YAC3D,MAAM+B,aAAa,GAAGzB,KAAK,CAACc,MAAM,CAAC,CAACT,GAAG,GAAGN,SAAS;YACnDmB,KAAK,CAACJ,MAAM,GAAGW,aAAa,GAAG,CAAC;YAChCP,KAAK,CAACb,GAAG,GAAGoB,aAAa,GAAGb,gBAAgB,CAACP,GAAG,GAAGL,KAAK,CAACO,KAAK,CAAC,CAACJ,MAAM;UACxE,CAAC,MAAM;YACLe,KAAK,CAACJ,MAAM,GAAG,KAAK;UACtB;QACF,CAAC,CAAC;MACJ;MACApB,iBAAiB,GAAG,EAAE;IACxB,CAAC;IACD,MAAMgC,IAAI,GAAGA,CAAA,KAAM;MACjB3F,QAAQ,CAAC2E,QAAQ,CAAC;IACpB,CAAC;IACDxD,gBAAgB,CAAC,QAAQ,EAAEwD,QAAQ,EAAE;MACnCiB,MAAM,EAAEpC,YAAY;MACpBqC,OAAO,EAAE;IACX,CAAC,CAAC;IACF3F,SAAS,CAACyF,IAAI,CAAC;IACf7F,KAAK,CAAC,MAAMiD,KAAK,CAACtB,SAAS,EAAEkE,IAAI,CAAC;IAClC7F,KAAK,CAACwD,YAAY,EAAGsB,KAAK,IAAK;MAC7B,IAAIA,KAAK,EAAE;QACT1B,IAAI,CAAC,QAAQ,EAAE0B,KAAK,CAAC;MACvB;IACF,CAAC,CAAC;IACF,MAAMkB,aAAa,GAAGA,CAAA,KAAM/C,KAAK,CAACtB,SAAS,CAACG,GAAG,CAAE4C,KAAK,IAAK;MACzD,MAAMO,MAAM,GAAGP,KAAK,KAAKlB,YAAY,CAACsB,KAAK;MAC3C,OAAOvE,YAAY,CAAC,MAAM,EAAE;QAC1B,OAAO,EAAE6B,GAAG,CAAC,OAAO,EAAE;UACpB6C;QACF,CAAC,CAAC;QACF,OAAO,EAAEA,MAAM,GAAGlB,cAAc,CAACe,KAAK,GAAG,KAAK,CAAC;QAC/C,YAAY,EAAEJ;MAChB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;IACb,CAAC,CAAC;IACF,MAAMuB,QAAQ,GAAIvB,KAAK,IAAK;MAC1Bb,iBAAiB,GAAG5B,MAAM,CAACyC,KAAK,CAAC;MACjC,MAAMQ,KAAK,GAAGT,cAAc,CAACZ,iBAAiB,CAAC;MAC/C,IAAIqB,KAAK,EAAE;QACT,MAAMhB,SAAS,GAAGtD,YAAY,CAAC8C,YAAY,CAACoB,KAAK,CAAC;QAClD,MAAMC,gBAAgB,GAAG7D,OAAO,CAACwC,YAAY,CAAC;QAC9C,MAAM;UACJwC;QACF,CAAC,GAAGC,QAAQ,CAACC,eAAe;QAC5BlB,KAAK,CAACI,GAAG,CAACe,cAAc,CAAC,CAAC;QAC1B,IAAInC,SAAS,KAAKgC,YAAY,GAAGnB,gBAAgB,CAACT,MAAM,EAAE;UACxDO,QAAQ,CAAC,CAAC;UACV;QACF;QACA,IAAI5B,KAAK,CAACX,MAAM,IAAIW,KAAK,CAACN,eAAe,EAAE;UACzC,IAAI3B,gBAAgB,CAAC,CAAC,KAAKkF,YAAY,GAAGnB,gBAAgB,CAACT,MAAM,EAAE;YACjErD,gBAAgB,CAACD,gBAAgB,CAAC,CAAC,CAAC;UACtC,CAAC,MAAM;YACLC,gBAAgB,CAACD,gBAAgB,CAAC,CAAC,GAAGiC,KAAK,CAACN,eAAe,CAAC;UAC9D;QACF;QACAS,IAAI,CAAC,QAAQ,EAAE8B,KAAK,CAACR,KAAK,CAAC;MAC7B;IACF,CAAC;IACD,MAAM4B,eAAe,GAAIC,OAAO,IAAK;MACnC,MAAM;QACJ7B;MACF,CAAC,GAAG6B,OAAO,CAACC,OAAO;MACnB,IAAI9B,KAAK,EAAE;QACTuB,QAAQ,CAACvB,KAAK,CAAC;MACjB;IACF,CAAC;IACD,MAAM+B,cAAc,GAAIC,KAAK,IAAK;MAChCJ,eAAe,CAACI,KAAK,CAACZ,MAAM,CAAC;IAC/B,CAAC;IACD,IAAIa,gBAAgB;IACpB,MAAMC,WAAW,GAAIF,KAAK,IAAK;MAC7BjD,KAAK,CAACoD,IAAI,CAACH,KAAK,CAAC;MACjB,IAAIjD,KAAK,CAACqD,UAAU,CAAC,CAAC,EAAE;QACtBjG,cAAc,CAAC6F,KAAK,CAAC;QACrB,MAAM;UACJK,OAAO;UACPC;QACF,CAAC,GAAGN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC;QACpB,MAAMnB,MAAM,GAAGK,QAAQ,CAACe,gBAAgB,CAACH,OAAO,EAAEC,OAAO,CAAC;QAC1D,IAAIlB,MAAM,EAAE;UACV,MAAM;YACJpB;UACF,CAAC,GAAGoB,MAAM,CAACU,OAAO;UAClB,IAAI9B,KAAK,IAAIiC,gBAAgB,KAAKjC,KAAK,EAAE;YACvCiC,gBAAgB,GAAGjC,KAAK;YACxB4B,eAAe,CAACR,MAAM,CAAC;UACzB;QACF;MACF;IACF,CAAC;IACD,MAAMqB,aAAa,GAAGA,CAAA,KAAM5G,YAAY,CAAC,KAAK,EAAE;MAC9C,KAAK,EAAEgD,OAAO;MACd,OAAO,EAAEnB,GAAG,CAAC,SAAS,CAAC;MACvB,OAAO,EAAE0B,YAAY,CAACgB,KAAK;MAC3B,SAAS,EAAE2B,cAAc;MACzB,qBAAqB,EAAEhD,KAAK,CAAC2D;IAC/B,CAAC,EAAE,CAACpB,aAAa,CAAC,CAAC,CAAC,CAAC;IACrBzE,SAAS,CAAC;MACR0E;IACF,CAAC,CAAC;IACF5E,gBAAgB,CAAC,WAAW,EAAEuF,WAAW,EAAE;MACzCd,MAAM,EAAEvC;IACV,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAI8D,EAAE;MACN,OAAO9G,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAE+C,IAAI;QACX,OAAO,EAAElB,GAAG,CAAC;MACf,CAAC,EAAE,CAACa,KAAK,CAACT,QAAQ,GAAGjC,YAAY,CAACJ,QAAQ,EAAE;QAC1C,IAAI,EAAE8C,KAAK,CAACT;MACd,CAAC,EAAE;QACDK,OAAO,EAAEA,CAAA,KAAM,CAACsE,aAAa,CAAC,CAAC;MACjC,CAAC,CAAC,GAAGA,aAAa,CAAC,CAAC,EAAE,CAACE,EAAE,GAAGhE,KAAK,CAACR,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwE,EAAE,CAACC,IAAI,CAACjE,KAAK,CAAC,CAAC,CAAC;IAChF,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEP,aAAa,EACbE,aAAa,IAAIH,OAAO,EACxBR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}