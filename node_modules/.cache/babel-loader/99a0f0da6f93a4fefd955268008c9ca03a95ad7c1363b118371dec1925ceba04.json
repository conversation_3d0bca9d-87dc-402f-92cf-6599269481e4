{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _RollingText from \"./RollingText.mjs\";\nconst RollingText = withInstall(_RollingText);\nvar stdin_default = RollingText;\nimport { rollingTextProps } from \"./RollingText.mjs\";\nexport { RollingText, stdin_default as default, rollingTextProps };", "map": {"version": 3, "names": ["withInstall", "_RollingText", "RollingText", "stdin_default", "rollingTextProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/rolling-text/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _RollingText from \"./RollingText.mjs\";\nconst RollingText = withInstall(_RollingText);\nvar stdin_default = RollingText;\nimport { rollingTextProps } from \"./RollingText.mjs\";\nexport {\n  RollingText,\n  stdin_default as default,\n  rollingTextProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXC,aAAa,IAAIE,OAAO,EACxBD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}