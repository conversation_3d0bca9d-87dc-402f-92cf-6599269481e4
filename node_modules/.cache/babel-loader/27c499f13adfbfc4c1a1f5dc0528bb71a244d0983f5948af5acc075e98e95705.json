{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { toArray, createNamespace, isFunction } from \"../utils/index.mjs\";\nconst [name, bem, t] = createNamespace(\"uploader\");\nfunction readFileContent(file, resultType) {\n  return new Promise(resolve => {\n    if (resultType === \"file\") {\n      resolve();\n      return;\n    }\n    const reader = new FileReader();\n    reader.onload = event => {\n      resolve(event.target.result);\n    };\n    if (resultType === \"dataUrl\") {\n      reader.readAsDataURL(file);\n    } else if (resultType === \"text\") {\n      reader.readAsText(file);\n    }\n  });\n}\nfunction isOversize(items, maxSize) {\n  return toArray(items).some(item => {\n    if (item.file) {\n      if (isFunction(maxSize)) {\n        return maxSize(item.file);\n      }\n      return item.file.size > +maxSize;\n    }\n    return false;\n  });\n}\nfunction filterFiles(items, maxSize) {\n  const valid = [];\n  const invalid = [];\n  items.forEach(item => {\n    if (isOversize(item, maxSize)) {\n      invalid.push(item);\n    } else {\n      valid.push(item);\n    }\n  });\n  return {\n    valid,\n    invalid\n  };\n}\nconst IMAGE_REGEXP = /\\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg|avif)/i;\nconst isImageUrl = url => IMAGE_REGEXP.test(url);\nfunction isImageFile(item) {\n  if (item.isImage) {\n    return true;\n  }\n  if (item.file && item.file.type) {\n    return item.file.type.indexOf(\"image\") === 0;\n  }\n  if (item.url) {\n    return isImageUrl(item.url);\n  }\n  if (typeof item.content === \"string\") {\n    return item.content.indexOf(\"data:image\") === 0;\n  }\n  return false;\n}\nexport { bem, filterFiles, isImageFile, isImageUrl, isOversize, name, readFileContent, t };", "map": {"version": 3, "names": ["toArray", "createNamespace", "isFunction", "name", "bem", "t", "readFileContent", "file", "resultType", "Promise", "resolve", "reader", "FileReader", "onload", "event", "target", "result", "readAsDataURL", "readAsText", "isOversize", "items", "maxSize", "some", "item", "size", "filterFiles", "valid", "invalid", "for<PERSON>ach", "push", "IMAGE_REGEXP", "isImageUrl", "url", "test", "isImageFile", "isImage", "type", "indexOf", "content"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/uploader/utils.mjs"], "sourcesContent": ["import { toArray, createNamespace, isFunction } from \"../utils/index.mjs\";\nconst [name, bem, t] = createNamespace(\"uploader\");\nfunction readFileContent(file, resultType) {\n  return new Promise((resolve) => {\n    if (resultType === \"file\") {\n      resolve();\n      return;\n    }\n    const reader = new FileReader();\n    reader.onload = (event) => {\n      resolve(event.target.result);\n    };\n    if (resultType === \"dataUrl\") {\n      reader.readAsDataURL(file);\n    } else if (resultType === \"text\") {\n      reader.readAsText(file);\n    }\n  });\n}\nfunction isOversize(items, maxSize) {\n  return toArray(items).some((item) => {\n    if (item.file) {\n      if (isFunction(maxSize)) {\n        return maxSize(item.file);\n      }\n      return item.file.size > +maxSize;\n    }\n    return false;\n  });\n}\nfunction filterFiles(items, maxSize) {\n  const valid = [];\n  const invalid = [];\n  items.forEach((item) => {\n    if (isOversize(item, maxSize)) {\n      invalid.push(item);\n    } else {\n      valid.push(item);\n    }\n  });\n  return { valid, invalid };\n}\nconst IMAGE_REGEXP = /\\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg|avif)/i;\nconst isImageUrl = (url) => IMAGE_REGEXP.test(url);\nfunction isImageFile(item) {\n  if (item.isImage) {\n    return true;\n  }\n  if (item.file && item.file.type) {\n    return item.file.type.indexOf(\"image\") === 0;\n  }\n  if (item.url) {\n    return isImageUrl(item.url);\n  }\n  if (typeof item.content === \"string\") {\n    return item.content.indexOf(\"data:image\") === 0;\n  }\n  return false;\n}\nexport {\n  bem,\n  filterFiles,\n  isImageFile,\n  isImageUrl,\n  isOversize,\n  name,\n  readFileContent,\n  t\n};\n"], "mappings": ";;;;AAAA,SAASA,OAAO,EAAEC,eAAe,EAAEC,UAAU,QAAQ,oBAAoB;AACzE,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGJ,eAAe,CAAC,UAAU,CAAC;AAClD,SAASK,eAAeA,CAACC,IAAI,EAAEC,UAAU,EAAE;EACzC,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9B,IAAIF,UAAU,KAAK,MAAM,EAAE;MACzBE,OAAO,CAAC,CAAC;MACT;IACF;IACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;MACzBJ,OAAO,CAACI,KAAK,CAACC,MAAM,CAACC,MAAM,CAAC;IAC9B,CAAC;IACD,IAAIR,UAAU,KAAK,SAAS,EAAE;MAC5BG,MAAM,CAACM,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAIC,UAAU,KAAK,MAAM,EAAE;MAChCG,MAAM,CAACO,UAAU,CAACX,IAAI,CAAC;IACzB;EACF,CAAC,CAAC;AACJ;AACA,SAASY,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAClC,OAAOrB,OAAO,CAACoB,KAAK,CAAC,CAACE,IAAI,CAAEC,IAAI,IAAK;IACnC,IAAIA,IAAI,CAAChB,IAAI,EAAE;MACb,IAAIL,UAAU,CAACmB,OAAO,CAAC,EAAE;QACvB,OAAOA,OAAO,CAACE,IAAI,CAAChB,IAAI,CAAC;MAC3B;MACA,OAAOgB,IAAI,CAAChB,IAAI,CAACiB,IAAI,GAAG,CAACH,OAAO;IAClC;IACA,OAAO,KAAK;EACd,CAAC,CAAC;AACJ;AACA,SAASI,WAAWA,CAACL,KAAK,EAAEC,OAAO,EAAE;EACnC,MAAMK,KAAK,GAAG,EAAE;EAChB,MAAMC,OAAO,GAAG,EAAE;EAClBP,KAAK,CAACQ,OAAO,CAAEL,IAAI,IAAK;IACtB,IAAIJ,UAAU,CAACI,IAAI,EAAEF,OAAO,CAAC,EAAE;MAC7BM,OAAO,CAACE,IAAI,CAACN,IAAI,CAAC;IACpB,CAAC,MAAM;MACLG,KAAK,CAACG,IAAI,CAACN,IAAI,CAAC;IAClB;EACF,CAAC,CAAC;EACF,OAAO;IAAEG,KAAK;IAAEC;EAAQ,CAAC;AAC3B;AACA,MAAMG,YAAY,GAAG,kDAAkD;AACvE,MAAMC,UAAU,GAAIC,GAAG,IAAKF,YAAY,CAACG,IAAI,CAACD,GAAG,CAAC;AAClD,SAASE,WAAWA,CAACX,IAAI,EAAE;EACzB,IAAIA,IAAI,CAACY,OAAO,EAAE;IAChB,OAAO,IAAI;EACb;EACA,IAAIZ,IAAI,CAAChB,IAAI,IAAIgB,IAAI,CAAChB,IAAI,CAAC6B,IAAI,EAAE;IAC/B,OAAOb,IAAI,CAAChB,IAAI,CAAC6B,IAAI,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;EAC9C;EACA,IAAId,IAAI,CAACS,GAAG,EAAE;IACZ,OAAOD,UAAU,CAACR,IAAI,CAACS,GAAG,CAAC;EAC7B;EACA,IAAI,OAAOT,IAAI,CAACe,OAAO,KAAK,QAAQ,EAAE;IACpC,OAAOf,IAAI,CAACe,OAAO,CAACD,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC;EACjD;EACA,OAAO,KAAK;AACd;AACA,SACEjC,GAAG,EACHqB,WAAW,EACXS,WAAW,EACXH,UAAU,EACVZ,UAAU,EACVhB,IAAI,EACJG,eAAe,EACfD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}