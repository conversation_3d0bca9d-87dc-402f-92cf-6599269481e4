{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Uploader from \"./Uploader.mjs\";\nconst Uploader = withInstall(_Uploader);\nvar stdin_default = Uploader;\nimport { uploaderProps } from \"./Uploader.mjs\";\nexport { Uploader, stdin_default as default, uploaderProps };", "map": {"version": 3, "names": ["withInstall", "_Uploader", "Uploader", "stdin_default", "uploaderProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/uploader/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Uploader from \"./Uploader.mjs\";\nconst Uploader = withInstall(_Uploader);\nvar stdin_default = Uploader;\nimport { uploaderProps } from \"./Uploader.mjs\";\nexport {\n  Uploader,\n  stdin_default as default,\n  uploaderProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SAASE,aAAa,QAAQ,gBAAgB;AAC9C,SACEF,QAAQ,EACRC,aAAa,IAAIE,OAAO,EACxBD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}