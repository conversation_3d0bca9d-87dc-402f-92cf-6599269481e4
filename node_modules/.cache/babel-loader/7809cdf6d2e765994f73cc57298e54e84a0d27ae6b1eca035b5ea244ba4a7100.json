{"ast": null, "code": "import { defineComponent, ref, createVNode as _createVNode } from \"vue\";\nimport { truthProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nconst [name, bem] = createNamespace(\"action-bar\");\nconst ACTION_BAR_KEY = Symbol(name);\nconst actionBarProps = {\n  placeholder: Boolean,\n  safeAreaInsetBottom: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: actionBarProps,\n  setup(props, {\n    slots\n  }) {\n    const root = ref();\n    const renderPlaceholder = usePlaceholder(root, bem);\n    const {\n      linkChildren\n    } = useChildren(ACTION_BAR_KEY);\n    linkChildren();\n    const renderActionBar = () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": [bem(), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n    return () => {\n      if (props.placeholder) {\n        return renderPlaceholder(renderActionBar);\n      }\n      return renderActionBar();\n    };\n  }\n});\nexport { ACTION_BAR_KEY, actionBarProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "ref", "createVNode", "_createVNode", "truthProp", "createNamespace", "useChildren", "usePlaceholder", "name", "bem", "ACTION_BAR_KEY", "Symbol", "actionBarProps", "placeholder", "Boolean", "safeAreaInsetBottom", "stdin_default", "props", "setup", "slots", "root", "renderPlaceholder", "linkChildren", "renderActionBar", "_a", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/action-bar/ActionBar.mjs"], "sourcesContent": ["import { defineComponent, ref, createVNode as _createVNode } from \"vue\";\nimport { truthProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { usePlaceholder } from \"../composables/use-placeholder.mjs\";\nconst [name, bem] = createNamespace(\"action-bar\");\nconst ACTION_BAR_KEY = Symbol(name);\nconst actionBarProps = {\n  placeholder: Boolean,\n  safeAreaInsetBottom: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: actionBarProps,\n  setup(props, {\n    slots\n  }) {\n    const root = ref();\n    const renderPlaceholder = usePlaceholder(root, bem);\n    const {\n      linkChildren\n    } = useChildren(ACTION_BAR_KEY);\n    linkChildren();\n    const renderActionBar = () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"ref\": root,\n        \"class\": [bem(), {\n          \"van-safe-area-bottom\": props.safeAreaInsetBottom\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n    return () => {\n      if (props.placeholder) {\n        return renderPlaceholder(renderActionBar);\n      }\n      return renderActionBar();\n    };\n  }\n});\nexport {\n  ACTION_BAR_KEY,\n  actionBarProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,GAAG,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACvE,SAASC,SAAS,EAAEC,eAAe,QAAQ,oBAAoB;AAC/D,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,cAAc,QAAQ,oCAAoC;AACnE,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,YAAY,CAAC;AACjD,MAAMK,cAAc,GAAGC,MAAM,CAACH,IAAI,CAAC;AACnC,MAAMI,cAAc,GAAG;EACrBC,WAAW,EAAEC,OAAO;EACpBC,mBAAmB,EAAEX;AACvB,CAAC;AACD,IAAIY,aAAa,GAAGhB,eAAe,CAAC;EAClCQ,IAAI;EACJS,KAAK,EAAEL,cAAc;EACrBM,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAGnB,GAAG,CAAC,CAAC;IAClB,MAAMoB,iBAAiB,GAAGd,cAAc,CAACa,IAAI,EAAEX,GAAG,CAAC;IACnD,MAAM;MACJa;IACF,CAAC,GAAGhB,WAAW,CAACI,cAAc,CAAC;IAC/BY,YAAY,CAAC,CAAC;IACd,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIC,EAAE;MACN,OAAOrB,YAAY,CAAC,KAAK,EAAE;QACzB,KAAK,EAAEiB,IAAI;QACX,OAAO,EAAE,CAACX,GAAG,CAAC,CAAC,EAAE;UACf,sBAAsB,EAAEQ,KAAK,CAACF;QAChC,CAAC;MACH,CAAC,EAAE,CAAC,CAACS,EAAE,GAAGL,KAAK,CAACM,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACP,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,MAAM;MACX,IAAIF,KAAK,CAACJ,WAAW,EAAE;QACrB,OAAOQ,iBAAiB,CAACE,eAAe,CAAC;MAC3C;MACA,OAAOA,eAAe,CAAC,CAAC;IAC1B,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEb,cAAc,EACdE,cAAc,EACdI,aAAa,IAAIS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}