{"ast": null, "code": "import { raf, cancelRaf } from \"@vant/use\";\nimport { getScrollTop, setScrollTop } from \"../utils/index.mjs\";\nfunction scrollLeftTo(scroller, to, duration) {\n  let rafId;\n  let count = 0;\n  const from = scroller.scrollLeft;\n  const frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  let scrollLeft = from;\n  function cancel() {\n    cancelRaf(rafId);\n  }\n  function animate() {\n    scrollLeft += (to - from) / frames;\n    scroller.scrollLeft = scrollLeft;\n    if (++count < frames) {\n      rafId = raf(animate);\n    }\n  }\n  animate();\n  return cancel;\n}\nfunction scrollTopTo(scroller, to, duration, callback) {\n  let rafId;\n  let current = getScrollTop(scroller);\n  const isDown = current < to;\n  const frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  const step = (to - current) / frames;\n  function cancel() {\n    cancelRaf(rafId);\n  }\n  function animate() {\n    current += step;\n    if (isDown && current > to || !isDown && current < to) {\n      current = to;\n    }\n    setScrollTop(scroller, current);\n    if (isDown && current < to || !isDown && current > to) {\n      rafId = raf(animate);\n    } else if (callback) {\n      rafId = raf(callback);\n    }\n  }\n  animate();\n  return cancel;\n}\nexport { scrollLeftTo, scrollTopTo };", "map": {"version": 3, "names": ["raf", "cancelRaf", "getScrollTop", "setScrollTop", "scrollLeftTo", "scroller", "to", "duration", "rafId", "count", "from", "scrollLeft", "frames", "Math", "round", "cancel", "animate", "scrollTopTo", "callback", "current", "isDown", "step"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tabs/utils.mjs"], "sourcesContent": ["import { raf, cancelRaf } from \"@vant/use\";\nimport { getScrollTop, setScrollTop } from \"../utils/index.mjs\";\nfunction scrollLeftTo(scroller, to, duration) {\n  let rafId;\n  let count = 0;\n  const from = scroller.scrollLeft;\n  const frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  let scrollLeft = from;\n  function cancel() {\n    cancelRaf(rafId);\n  }\n  function animate() {\n    scrollLeft += (to - from) / frames;\n    scroller.scrollLeft = scrollLeft;\n    if (++count < frames) {\n      rafId = raf(animate);\n    }\n  }\n  animate();\n  return cancel;\n}\nfunction scrollTopTo(scroller, to, duration, callback) {\n  let rafId;\n  let current = getScrollTop(scroller);\n  const isDown = current < to;\n  const frames = duration === 0 ? 1 : Math.round(duration * 1e3 / 16);\n  const step = (to - current) / frames;\n  function cancel() {\n    cancelRaf(rafId);\n  }\n  function animate() {\n    current += step;\n    if (isDown && current > to || !isDown && current < to) {\n      current = to;\n    }\n    setScrollTop(scroller, current);\n    if (isDown && current < to || !isDown && current > to) {\n      rafId = raf(animate);\n    } else if (callback) {\n      rafId = raf(callback);\n    }\n  }\n  animate();\n  return cancel;\n}\nexport {\n  scrollLeftTo,\n  scrollTopTo\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,SAAS,QAAQ,WAAW;AAC1C,SAASC,YAAY,EAAEC,YAAY,QAAQ,oBAAoB;AAC/D,SAASC,YAAYA,CAACC,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAE;EAC5C,IAAIC,KAAK;EACT,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMC,IAAI,GAAGL,QAAQ,CAACM,UAAU;EAChC,MAAMC,MAAM,GAAGL,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACP,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC;EACnE,IAAII,UAAU,GAAGD,IAAI;EACrB,SAASK,MAAMA,CAAA,EAAG;IAChBd,SAAS,CAACO,KAAK,CAAC;EAClB;EACA,SAASQ,OAAOA,CAAA,EAAG;IACjBL,UAAU,IAAI,CAACL,EAAE,GAAGI,IAAI,IAAIE,MAAM;IAClCP,QAAQ,CAACM,UAAU,GAAGA,UAAU;IAChC,IAAI,EAAEF,KAAK,GAAGG,MAAM,EAAE;MACpBJ,KAAK,GAAGR,GAAG,CAACgB,OAAO,CAAC;IACtB;EACF;EACAA,OAAO,CAAC,CAAC;EACT,OAAOD,MAAM;AACf;AACA,SAASE,WAAWA,CAACZ,QAAQ,EAAEC,EAAE,EAAEC,QAAQ,EAAEW,QAAQ,EAAE;EACrD,IAAIV,KAAK;EACT,IAAIW,OAAO,GAAGjB,YAAY,CAACG,QAAQ,CAAC;EACpC,MAAMe,MAAM,GAAGD,OAAO,GAAGb,EAAE;EAC3B,MAAMM,MAAM,GAAGL,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACP,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC;EACnE,MAAMc,IAAI,GAAG,CAACf,EAAE,GAAGa,OAAO,IAAIP,MAAM;EACpC,SAASG,MAAMA,CAAA,EAAG;IAChBd,SAAS,CAACO,KAAK,CAAC;EAClB;EACA,SAASQ,OAAOA,CAAA,EAAG;IACjBG,OAAO,IAAIE,IAAI;IACf,IAAID,MAAM,IAAID,OAAO,GAAGb,EAAE,IAAI,CAACc,MAAM,IAAID,OAAO,GAAGb,EAAE,EAAE;MACrDa,OAAO,GAAGb,EAAE;IACd;IACAH,YAAY,CAACE,QAAQ,EAAEc,OAAO,CAAC;IAC/B,IAAIC,MAAM,IAAID,OAAO,GAAGb,EAAE,IAAI,CAACc,MAAM,IAAID,OAAO,GAAGb,EAAE,EAAE;MACrDE,KAAK,GAAGR,GAAG,CAACgB,OAAO,CAAC;IACtB,CAAC,MAAM,IAAIE,QAAQ,EAAE;MACnBV,KAAK,GAAGR,GAAG,CAACkB,QAAQ,CAAC;IACvB;EACF;EACAF,OAAO,CAAC,CAAC;EACT,OAAOD,MAAM;AACf;AACA,SACEX,YAAY,EACZa,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}