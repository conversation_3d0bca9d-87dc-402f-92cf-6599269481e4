{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _FloatingBubble from \"./FloatingBubble.mjs\";\nconst FloatingBubble = withInstall(_FloatingBubble);\nvar stdin_default = FloatingBubble;\nimport { floatingBubbleProps } from \"./FloatingBubble.mjs\";\nexport { FloatingBubble, stdin_default as default, floatingBubbleProps };", "map": {"version": 3, "names": ["withInstall", "_FloatingBubble", "FloatingBubble", "stdin_default", "floatingBubbleProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/floating-bubble/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _FloatingBubble from \"./FloatingBubble.mjs\";\nconst FloatingBubble = withInstall(_FloatingBubble);\nvar stdin_default = FloatingBubble;\nimport { floatingBubbleProps } from \"./FloatingBubble.mjs\";\nexport {\n  FloatingBubble,\n  stdin_default as default,\n  floatingBubbleProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,MAAMC,cAAc,GAAGF,WAAW,CAACC,eAAe,CAAC;AACnD,IAAIE,aAAa,GAAGD,cAAc;AAClC,SAASE,mBAAmB,QAAQ,sBAAsB;AAC1D,SACEF,cAAc,EACdC,aAAa,IAAIE,OAAO,EACxBD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}