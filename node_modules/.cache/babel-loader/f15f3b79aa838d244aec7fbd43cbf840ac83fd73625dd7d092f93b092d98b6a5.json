{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Empty from \"./Empty.mjs\";\nconst Empty = withInstall(_Empty);\nvar stdin_default = Empty;\nimport { emptyProps } from \"./Empty.mjs\";\nexport { Empty, stdin_default as default, emptyProps };", "map": {"version": 3, "names": ["withInstall", "_Empty", "Empty", "stdin_default", "emptyProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/empty/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Empty from \"./Empty.mjs\";\nconst Empty = withInstall(_Empty);\nvar stdin_default = Empty;\nimport { emptyProps } from \"./Empty.mjs\";\nexport {\n  Empty,\n  stdin_default as default,\n  emptyProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SAASE,UAAU,QAAQ,aAAa;AACxC,SACEF,KAAK,EACLC,aAAa,IAAIE,OAAO,EACxBD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}