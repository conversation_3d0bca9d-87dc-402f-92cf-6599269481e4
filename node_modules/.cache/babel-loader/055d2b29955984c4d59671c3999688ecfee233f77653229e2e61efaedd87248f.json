{"ast": null, "code": "import { defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, createNamespace, unknownProp, numericProp } from \"../utils/index.mjs\";\nimport { ACTION_BAR_KEY } from \"../action-bar/ActionBar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"action-bar-icon\");\nconst actionBarIconProps = extend({}, routeProps, {\n  dot: Boolean,\n  text: String,\n  icon: String,\n  color: String,\n  badge: numericProp,\n  iconClass: unknownProp,\n  badgeProps: Object,\n  iconPrefix: String\n});\nvar stdin_default = defineComponent({\n  name,\n  props: actionBarIconProps,\n  setup(props, {\n    slots\n  }) {\n    const route = useRoute();\n    useParent(ACTION_BAR_KEY);\n    const renderIcon = () => {\n      const {\n        dot,\n        badge,\n        icon,\n        color,\n        iconClass,\n        badgeProps,\n        iconPrefix\n      } = props;\n      if (slots.icon) {\n        return _createVNode(Badge, _mergeProps({\n          \"dot\": dot,\n          \"class\": bem(\"icon\"),\n          \"content\": badge\n        }, badgeProps), {\n          default: slots.icon\n        });\n      }\n      return _createVNode(Icon, {\n        \"tag\": \"div\",\n        \"dot\": dot,\n        \"name\": icon,\n        \"badge\": badge,\n        \"color\": color,\n        \"class\": [bem(\"icon\"), iconClass],\n        \"badgeProps\": badgeProps,\n        \"classPrefix\": iconPrefix\n      }, null);\n    };\n    return () => _createVNode(\"div\", {\n      \"role\": \"button\",\n      \"class\": bem(),\n      \"tabindex\": 0,\n      \"onClick\": route\n    }, [renderIcon(), slots.default ? slots.default() : props.text]);\n  }\n});\nexport { actionBarIconProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "extend", "createNamespace", "unknownProp", "numericProp", "ACTION_BAR_KEY", "useParent", "useRoute", "routeProps", "Icon", "Badge", "name", "bem", "actionBarIconProps", "dot", "Boolean", "text", "String", "icon", "color", "badge", "iconClass", "badgeProps", "Object", "iconPrefix", "stdin_default", "props", "setup", "slots", "route", "renderIcon", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/action-bar-icon/ActionBarIcon.mjs"], "sourcesContent": ["import { defineComponent, mergeProps as _mergeProps, createVNode as _createVNode } from \"vue\";\nimport { extend, createNamespace, unknownProp, numericProp } from \"../utils/index.mjs\";\nimport { ACTION_BAR_KEY } from \"../action-bar/ActionBar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"action-bar-icon\");\nconst actionBarIconProps = extend({}, routeProps, {\n  dot: Boolean,\n  text: String,\n  icon: String,\n  color: String,\n  badge: numericProp,\n  iconClass: unknownProp,\n  badgeProps: Object,\n  iconPrefix: String\n});\nvar stdin_default = defineComponent({\n  name,\n  props: actionBarIconProps,\n  setup(props, {\n    slots\n  }) {\n    const route = useRoute();\n    useParent(ACTION_BAR_KEY);\n    const renderIcon = () => {\n      const {\n        dot,\n        badge,\n        icon,\n        color,\n        iconClass,\n        badgeProps,\n        iconPrefix\n      } = props;\n      if (slots.icon) {\n        return _createVNode(Badge, _mergeProps({\n          \"dot\": dot,\n          \"class\": bem(\"icon\"),\n          \"content\": badge\n        }, badgeProps), {\n          default: slots.icon\n        });\n      }\n      return _createVNode(Icon, {\n        \"tag\": \"div\",\n        \"dot\": dot,\n        \"name\": icon,\n        \"badge\": badge,\n        \"color\": color,\n        \"class\": [bem(\"icon\"), iconClass],\n        \"badgeProps\": badgeProps,\n        \"classPrefix\": iconPrefix\n      }, null);\n    };\n    return () => _createVNode(\"div\", {\n      \"role\": \"button\",\n      \"class\": bem(),\n      \"tabindex\": 0,\n      \"onClick\": route\n    }, [renderIcon(), slots.default ? slots.default() : props.text]);\n  }\n});\nexport {\n  actionBarIconProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC7F,SAASC,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAEC,WAAW,QAAQ,oBAAoB;AACtF,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGV,eAAe,CAAC,iBAAiB,CAAC;AACtD,MAAMW,kBAAkB,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAEO,UAAU,EAAE;EAChDM,GAAG,EAAEC,OAAO;EACZC,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAED,MAAM;EACZE,KAAK,EAAEF,MAAM;EACbG,KAAK,EAAEhB,WAAW;EAClBiB,SAAS,EAAElB,WAAW;EACtBmB,UAAU,EAAEC,MAAM;EAClBC,UAAU,EAAEP;AACd,CAAC,CAAC;AACF,IAAIQ,aAAa,GAAG7B,eAAe,CAAC;EAClCe,IAAI;EACJe,KAAK,EAAEb,kBAAkB;EACzBc,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGtB,QAAQ,CAAC,CAAC;IACxBD,SAAS,CAACD,cAAc,CAAC;IACzB,MAAMyB,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAM;QACJhB,GAAG;QACHM,KAAK;QACLF,IAAI;QACJC,KAAK;QACLE,SAAS;QACTC,UAAU;QACVE;MACF,CAAC,GAAGE,KAAK;MACT,IAAIE,KAAK,CAACV,IAAI,EAAE;QACd,OAAOlB,YAAY,CAACU,KAAK,EAAEZ,WAAW,CAAC;UACrC,KAAK,EAAEgB,GAAG;UACV,OAAO,EAAEF,GAAG,CAAC,MAAM,CAAC;UACpB,SAAS,EAAEQ;QACb,CAAC,EAAEE,UAAU,CAAC,EAAE;UACdS,OAAO,EAAEH,KAAK,CAACV;QACjB,CAAC,CAAC;MACJ;MACA,OAAOlB,YAAY,CAACS,IAAI,EAAE;QACxB,KAAK,EAAE,KAAK;QACZ,KAAK,EAAEK,GAAG;QACV,MAAM,EAAEI,IAAI;QACZ,OAAO,EAAEE,KAAK;QACd,OAAO,EAAED,KAAK;QACd,OAAO,EAAE,CAACP,GAAG,CAAC,MAAM,CAAC,EAAES,SAAS,CAAC;QACjC,YAAY,EAAEC,UAAU;QACxB,aAAa,EAAEE;MACjB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,OAAO,MAAMxB,YAAY,CAAC,KAAK,EAAE;MAC/B,MAAM,EAAE,QAAQ;MAChB,OAAO,EAAEY,GAAG,CAAC,CAAC;MACd,UAAU,EAAE,CAAC;MACb,SAAS,EAAEiB;IACb,CAAC,EAAE,CAACC,UAAU,CAAC,CAAC,EAAEF,KAAK,CAACG,OAAO,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGL,KAAK,CAACV,IAAI,CAAC,CAAC;EAClE;AACF,CAAC,CAAC;AACF,SACEH,kBAAkB,EAClBY,aAAa,IAAIM,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}