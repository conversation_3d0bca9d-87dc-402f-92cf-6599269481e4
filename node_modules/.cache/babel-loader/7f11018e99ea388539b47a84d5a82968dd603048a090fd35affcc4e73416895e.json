{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { computed, defineComponent, getCurrentInstance, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { createNamespace, extend, isObject, numericProp } from \"../utils/index.mjs\";\nimport { TABBAR_KEY } from \"../tabbar/Tabbar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { routeProps, useRoute } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"tabbar-item\");\nconst tabbarItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  icon: String,\n  name: numericProp,\n  badge: numericProp,\n  badgeProps: Object,\n  iconPrefix: String\n});\nvar stdin_default = defineComponent({\n  name,\n  props: tabbarItemProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const vm = getCurrentInstance().proxy;\n    const {\n      parent,\n      index\n    } = useParent(TABBAR_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <TabbarItem> must be a child component of <Tabbar>.\");\n      }\n      return;\n    }\n    const active = computed(() => {\n      var _a;\n      const {\n        route: route2,\n        modelValue\n      } = parent.props;\n      if (route2 && \"$route\" in vm) {\n        const {\n          $route\n        } = vm;\n        const {\n          to\n        } = props;\n        const config = isObject(to) ? to : {\n          path: to\n        };\n        return $route.matched.some(val => {\n          const pathMatched = \"path\" in config && config.path === val.path;\n          const nameMatched = \"name\" in config && config.name === val.name;\n          return pathMatched || nameMatched;\n        });\n      }\n      return ((_a = props.name) != null ? _a : index.value) === modelValue;\n    });\n    const onClick = event => {\n      var _a;\n      if (!active.value) {\n        parent.setActive((_a = props.name) != null ? _a : index.value, route);\n      }\n      emit(\"click\", event);\n    };\n    const renderIcon = () => {\n      if (slots.icon) {\n        return slots.icon({\n          active: active.value\n        });\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"name\": props.icon,\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    return () => {\n      var _a;\n      const {\n        dot,\n        badge\n      } = props;\n      const {\n        activeColor,\n        inactiveColor\n      } = parent.props;\n      const color = active.value ? activeColor : inactiveColor;\n      return _createVNode(\"div\", {\n        \"role\": \"tab\",\n        \"class\": bem({\n          active: active.value\n        }),\n        \"style\": {\n          color\n        },\n        \"tabindex\": 0,\n        \"aria-selected\": active.value,\n        \"onClick\": onClick\n      }, [_createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"class\": bem(\"icon\"),\n        \"content\": badge\n      }, props.badgeProps), {\n        default: renderIcon\n      }), _createVNode(\"div\", {\n        \"class\": bem(\"text\")\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots, {\n        active: active.value\n      })])]);\n    };\n  }\n});\nexport { stdin_default as default, tabbarItemProps };", "map": {"version": 3, "names": ["computed", "defineComponent", "getCurrentInstance", "createVNode", "_createVNode", "mergeProps", "_mergeProps", "createNamespace", "extend", "isObject", "numericProp", "TABBAR_KEY", "useParent", "routeProps", "useRoute", "Icon", "Badge", "name", "bem", "tabbarItemProps", "dot", "Boolean", "icon", "String", "badge", "badgeProps", "Object", "iconPrefix", "stdin_default", "props", "emits", "setup", "emit", "slots", "route", "vm", "proxy", "parent", "index", "process", "env", "NODE_ENV", "console", "error", "active", "_a", "route2", "modelValue", "$route", "to", "config", "path", "matched", "some", "val", "pathMatched", "nameMatched", "value", "onClick", "event", "setActive", "renderIcon", "activeColor", "inactiveColor", "color", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tabbar-item/TabbarItem.mjs"], "sourcesContent": ["import { computed, defineComponent, getCurrentInstance, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { createNamespace, extend, isObject, numericProp } from \"../utils/index.mjs\";\nimport { TABBAR_KEY } from \"../tabbar/Tabbar.mjs\";\nimport { useParent } from \"@vant/use\";\nimport { routeProps, useRoute } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"tabbar-item\");\nconst tabbarItemProps = extend({}, routeProps, {\n  dot: Boolean,\n  icon: String,\n  name: numericProp,\n  badge: numericProp,\n  badgeProps: Object,\n  iconPrefix: String\n});\nvar stdin_default = defineComponent({\n  name,\n  props: tabbarItemProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const vm = getCurrentInstance().proxy;\n    const {\n      parent,\n      index\n    } = useParent(TABBAR_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <TabbarItem> must be a child component of <Tabbar>.\");\n      }\n      return;\n    }\n    const active = computed(() => {\n      var _a;\n      const {\n        route: route2,\n        modelValue\n      } = parent.props;\n      if (route2 && \"$route\" in vm) {\n        const {\n          $route\n        } = vm;\n        const {\n          to\n        } = props;\n        const config = isObject(to) ? to : {\n          path: to\n        };\n        return $route.matched.some((val) => {\n          const pathMatched = \"path\" in config && config.path === val.path;\n          const nameMatched = \"name\" in config && config.name === val.name;\n          return pathMatched || nameMatched;\n        });\n      }\n      return ((_a = props.name) != null ? _a : index.value) === modelValue;\n    });\n    const onClick = (event) => {\n      var _a;\n      if (!active.value) {\n        parent.setActive((_a = props.name) != null ? _a : index.value, route);\n      }\n      emit(\"click\", event);\n    };\n    const renderIcon = () => {\n      if (slots.icon) {\n        return slots.icon({\n          active: active.value\n        });\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"name\": props.icon,\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    return () => {\n      var _a;\n      const {\n        dot,\n        badge\n      } = props;\n      const {\n        activeColor,\n        inactiveColor\n      } = parent.props;\n      const color = active.value ? activeColor : inactiveColor;\n      return _createVNode(\"div\", {\n        \"role\": \"tab\",\n        \"class\": bem({\n          active: active.value\n        }),\n        \"style\": {\n          color\n        },\n        \"tabindex\": 0,\n        \"aria-selected\": active.value,\n        \"onClick\": onClick\n      }, [_createVNode(Badge, _mergeProps({\n        \"dot\": dot,\n        \"class\": bem(\"icon\"),\n        \"content\": badge\n      }, props.badgeProps), {\n        default: renderIcon\n      }), _createVNode(\"div\", {\n        \"class\": bem(\"text\")\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots, {\n        active: active.value\n      })])]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  tabbarItemProps\n};\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC3H,SAASC,eAAe,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,oBAAoB;AACnF,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,UAAU,EAAEC,QAAQ,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGX,eAAe,CAAC,aAAa,CAAC;AAClD,MAAMY,eAAe,GAAGX,MAAM,CAAC,CAAC,CAAC,EAAEK,UAAU,EAAE;EAC7CO,GAAG,EAAEC,OAAO;EACZC,IAAI,EAAEC,MAAM;EACZN,IAAI,EAAEP,WAAW;EACjBc,KAAK,EAAEd,WAAW;EAClBe,UAAU,EAAEC,MAAM;EAClBC,UAAU,EAAEJ;AACd,CAAC,CAAC;AACF,IAAIK,aAAa,GAAG3B,eAAe,CAAC;EAClCgB,IAAI;EACJY,KAAK,EAAEV,eAAe;EACtBW,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGpB,QAAQ,CAAC,CAAC;IACxB,MAAMqB,EAAE,GAAGjC,kBAAkB,CAAC,CAAC,CAACkC,KAAK;IACrC,MAAM;MACJC,MAAM;MACNC;IACF,CAAC,GAAG1B,SAAS,CAACD,UAAU,CAAC;IACzB,IAAI,CAAC0B,MAAM,EAAE;MACX,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,4DAA4D,CAAC;MAC7E;MACA;IACF;IACA,MAAMC,MAAM,GAAG5C,QAAQ,CAAC,MAAM;MAC5B,IAAI6C,EAAE;MACN,MAAM;QACJX,KAAK,EAAEY,MAAM;QACbC;MACF,CAAC,GAAGV,MAAM,CAACR,KAAK;MAChB,IAAIiB,MAAM,IAAI,QAAQ,IAAIX,EAAE,EAAE;QAC5B,MAAM;UACJa;QACF,CAAC,GAAGb,EAAE;QACN,MAAM;UACJc;QACF,CAAC,GAAGpB,KAAK;QACT,MAAMqB,MAAM,GAAGzC,QAAQ,CAACwC,EAAE,CAAC,GAAGA,EAAE,GAAG;UACjCE,IAAI,EAAEF;QACR,CAAC;QACD,OAAOD,MAAM,CAACI,OAAO,CAACC,IAAI,CAAEC,GAAG,IAAK;UAClC,MAAMC,WAAW,GAAG,MAAM,IAAIL,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAKG,GAAG,CAACH,IAAI;UAChE,MAAMK,WAAW,GAAG,MAAM,IAAIN,MAAM,IAAIA,MAAM,CAACjC,IAAI,KAAKqC,GAAG,CAACrC,IAAI;UAChE,OAAOsC,WAAW,IAAIC,WAAW;QACnC,CAAC,CAAC;MACJ;MACA,OAAO,CAAC,CAACX,EAAE,GAAGhB,KAAK,CAACZ,IAAI,KAAK,IAAI,GAAG4B,EAAE,GAAGP,KAAK,CAACmB,KAAK,MAAMV,UAAU;IACtE,CAAC,CAAC;IACF,MAAMW,OAAO,GAAIC,KAAK,IAAK;MACzB,IAAId,EAAE;MACN,IAAI,CAACD,MAAM,CAACa,KAAK,EAAE;QACjBpB,MAAM,CAACuB,SAAS,CAAC,CAACf,EAAE,GAAGhB,KAAK,CAACZ,IAAI,KAAK,IAAI,GAAG4B,EAAE,GAAGP,KAAK,CAACmB,KAAK,EAAEvB,KAAK,CAAC;MACvE;MACAF,IAAI,CAAC,OAAO,EAAE2B,KAAK,CAAC;IACtB,CAAC;IACD,MAAME,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI5B,KAAK,CAACX,IAAI,EAAE;QACd,OAAOW,KAAK,CAACX,IAAI,CAAC;UAChBsB,MAAM,EAAEA,MAAM,CAACa;QACjB,CAAC,CAAC;MACJ;MACA,IAAI5B,KAAK,CAACP,IAAI,EAAE;QACd,OAAOlB,YAAY,CAACW,IAAI,EAAE;UACxB,MAAM,EAAEc,KAAK,CAACP,IAAI;UAClB,aAAa,EAAEO,KAAK,CAACF;QACvB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,OAAO,MAAM;MACX,IAAIkB,EAAE;MACN,MAAM;QACJzB,GAAG;QACHI;MACF,CAAC,GAAGK,KAAK;MACT,MAAM;QACJiC,WAAW;QACXC;MACF,CAAC,GAAG1B,MAAM,CAACR,KAAK;MAChB,MAAMmC,KAAK,GAAGpB,MAAM,CAACa,KAAK,GAAGK,WAAW,GAAGC,aAAa;MACxD,OAAO3D,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,KAAK;QACb,OAAO,EAAEc,GAAG,CAAC;UACX0B,MAAM,EAAEA,MAAM,CAACa;QACjB,CAAC,CAAC;QACF,OAAO,EAAE;UACPO;QACF,CAAC;QACD,UAAU,EAAE,CAAC;QACb,eAAe,EAAEpB,MAAM,CAACa,KAAK;QAC7B,SAAS,EAAEC;MACb,CAAC,EAAE,CAACtD,YAAY,CAACY,KAAK,EAAEV,WAAW,CAAC;QAClC,KAAK,EAAEc,GAAG;QACV,OAAO,EAAEF,GAAG,CAAC,MAAM,CAAC;QACpB,SAAS,EAAEM;MACb,CAAC,EAAEK,KAAK,CAACJ,UAAU,CAAC,EAAE;QACpBwC,OAAO,EAAEJ;MACX,CAAC,CAAC,EAAEzD,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAEc,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAAC,CAAC2B,EAAE,GAAGZ,KAAK,CAACgC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGpB,EAAE,CAACqB,IAAI,CAACjC,KAAK,EAAE;QACzDW,MAAM,EAAEA,MAAM,CAACa;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE7B,aAAa,IAAIqC,OAAO,EACxB9C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}