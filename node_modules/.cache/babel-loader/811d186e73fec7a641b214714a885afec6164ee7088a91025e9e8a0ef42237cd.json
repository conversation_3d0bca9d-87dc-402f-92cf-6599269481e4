{"ast": null, "code": "import { padZero } from \"../utils/index.mjs\";\nfunction getDate(timeStamp) {\n  const date = new Date(timeStamp * 1e3);\n  return `${date.getFullYear()}.${padZero(date.getMonth() + 1)}.${padZero(date.getDate())}`;\n}\nconst formatDiscount = discount => (discount / 10).toFixed(discount % 10 === 0 ? 0 : 1);\nconst formatAmount = amount => (amount / 100).toFixed(amount % 100 === 0 ? 0 : amount % 10 === 0 ? 1 : 2);\nexport { formatAmount, formatDiscount, getDate };", "map": {"version": 3, "names": ["padZero", "getDate", "timeStamp", "date", "Date", "getFullYear", "getMonth", "formatDiscount", "discount", "toFixed", "formatAmount", "amount"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/coupon/utils.mjs"], "sourcesContent": ["import { padZero } from \"../utils/index.mjs\";\nfunction getDate(timeStamp) {\n  const date = new Date(timeStamp * 1e3);\n  return `${date.getFullYear()}.${padZero(date.getMonth() + 1)}.${padZero(\n    date.getDate()\n  )}`;\n}\nconst formatDiscount = (discount) => (discount / 10).toFixed(discount % 10 === 0 ? 0 : 1);\nconst formatAmount = (amount) => (amount / 100).toFixed(amount % 100 === 0 ? 0 : amount % 10 === 0 ? 1 : 2);\nexport {\n  formatAmount,\n  formatDiscount,\n  getDate\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,OAAOA,CAACC,SAAS,EAAE;EAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,GAAG,GAAG,CAAC;EACtC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAIL,OAAO,CAACG,IAAI,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIN,OAAO,CACrEG,IAAI,CAACF,OAAO,CAAC,CACf,CAAC,EAAE;AACL;AACA,MAAMM,cAAc,GAAIC,QAAQ,IAAK,CAACA,QAAQ,GAAG,EAAE,EAAEC,OAAO,CAACD,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzF,MAAME,YAAY,GAAIC,MAAM,IAAK,CAACA,MAAM,GAAG,GAAG,EAAEF,OAAO,CAACE,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3G,SACED,YAAY,EACZH,cAAc,EACdN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}