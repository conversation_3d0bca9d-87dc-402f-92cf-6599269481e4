{"ast": null, "code": "import { camelize } from \"./format.mjs\";\nfunction withInstall(options) {\n  options.install = app => {\n    const {\n      name\n    } = options;\n    if (name) {\n      app.component(name, options);\n      app.component(camelize(`-${name}`), options);\n    }\n  };\n  return options;\n}\nexport { withInstall };", "map": {"version": 3, "names": ["camelize", "withInstall", "options", "install", "app", "name", "component"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/utils/with-install.mjs"], "sourcesContent": ["import { camelize } from \"./format.mjs\";\nfunction withInstall(options) {\n  options.install = (app) => {\n    const { name } = options;\n    if (name) {\n      app.component(name, options);\n      app.component(camelize(`-${name}`), options);\n    }\n  };\n  return options;\n}\nexport {\n  withInstall\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC5BA,OAAO,CAACC,OAAO,GAAIC,GAAG,IAAK;IACzB,MAAM;MAAEC;IAAK,CAAC,GAAGH,OAAO;IACxB,IAAIG,IAAI,EAAE;MACRD,GAAG,CAACE,SAAS,CAACD,IAAI,EAAEH,OAAO,CAAC;MAC5BE,GAAG,CAACE,SAAS,CAACN,QAAQ,CAAC,IAAIK,IAAI,EAAE,CAAC,EAAEH,OAAO,CAAC;IAC9C;EACF,CAAC;EACD,OAAOA,OAAO;AAChB;AACA,SACED,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}