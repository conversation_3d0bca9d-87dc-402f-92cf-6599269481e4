{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, numericProp, preventDefault, makeStringProp, createNamespace, BORDER_SURROUND } from \"../utils/index.mjs\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"button\");\nconst buttonProps = extend({}, routeProps, {\n  tag: makeStringProp(\"button\"),\n  text: String,\n  icon: String,\n  type: makeStringProp(\"default\"),\n  size: makeStringProp(\"normal\"),\n  color: String,\n  block: Boolean,\n  plain: Boolean,\n  round: Boolean,\n  square: Boolean,\n  loading: Boolean,\n  hairline: Boolean,\n  disabled: Boolean,\n  iconPrefix: String,\n  nativeType: makeStringProp(\"button\"),\n  loadingSize: numericProp,\n  loadingText: String,\n  loadingType: String,\n  iconPosition: makeStringProp(\"left\")\n});\nvar stdin_default = defineComponent({\n  name,\n  props: buttonProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const renderLoadingIcon = () => {\n      if (slots.loading) {\n        return slots.loading();\n      }\n      return _createVNode(Loading, {\n        \"size\": props.loadingSize,\n        \"type\": props.loadingType,\n        \"class\": bem(\"loading\")\n      }, null);\n    };\n    const renderIcon = () => {\n      if (props.loading) {\n        return renderLoadingIcon();\n      }\n      if (slots.icon) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"icon\")\n        }, [slots.icon()]);\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"name\": props.icon,\n          \"class\": bem(\"icon\"),\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    const renderText = () => {\n      let text;\n      if (props.loading) {\n        text = props.loadingText;\n      } else {\n        text = slots.default ? slots.default() : props.text;\n      }\n      if (text) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\")\n        }, [text]);\n      }\n    };\n    const getStyle = () => {\n      const {\n        color,\n        plain\n      } = props;\n      if (color) {\n        const style = {\n          color: plain ? color : \"white\"\n        };\n        if (!plain) {\n          style.background = color;\n        }\n        if (color.includes(\"gradient\")) {\n          style.border = 0;\n        } else {\n          style.borderColor = color;\n        }\n        return style;\n      }\n    };\n    const onClick = event => {\n      if (props.loading) {\n        preventDefault(event);\n      } else if (!props.disabled) {\n        emit(\"click\", event);\n        route();\n      }\n    };\n    return () => {\n      const {\n        tag,\n        type,\n        size,\n        block,\n        round,\n        plain,\n        square,\n        loading,\n        disabled,\n        hairline,\n        nativeType,\n        iconPosition\n      } = props;\n      const classes = [bem([type, size, {\n        plain,\n        block,\n        round,\n        square,\n        loading,\n        disabled,\n        hairline\n      }]), {\n        [BORDER_SURROUND]: hairline\n      }];\n      return _createVNode(tag, {\n        \"type\": nativeType,\n        \"class\": classes,\n        \"style\": getStyle(),\n        \"disabled\": disabled,\n        \"onClick\": onClick\n      }, {\n        default: () => [_createVNode(\"div\", {\n          \"class\": bem(\"content\")\n        }, [iconPosition === \"left\" && renderIcon(), renderText(), iconPosition === \"right\" && renderIcon()])]\n      });\n    };\n  }\n});\nexport { buttonProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "extend", "numericProp", "preventDefault", "makeStringProp", "createNamespace", "BORDER_SURROUND", "useRoute", "routeProps", "Icon", "Loading", "name", "bem", "buttonProps", "tag", "text", "String", "icon", "type", "size", "color", "block", "Boolean", "plain", "round", "square", "loading", "hairline", "disabled", "iconPrefix", "nativeType", "loadingSize", "loadingText", "loadingType", "iconPosition", "stdin_default", "props", "emits", "setup", "emit", "slots", "route", "renderLoadingIcon", "renderIcon", "renderText", "default", "getStyle", "style", "background", "includes", "border", "borderColor", "onClick", "event", "classes"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/button/Button.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, numericProp, preventDefault, makeStringProp, createNamespace, BORDER_SURROUND } from \"../utils/index.mjs\";\nimport { useRoute, routeProps } from \"../composables/use-route.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"button\");\nconst buttonProps = extend({}, routeProps, {\n  tag: makeStringProp(\"button\"),\n  text: String,\n  icon: String,\n  type: makeStringProp(\"default\"),\n  size: makeStringProp(\"normal\"),\n  color: String,\n  block: Boolean,\n  plain: Boolean,\n  round: Boolean,\n  square: Boolean,\n  loading: Boolean,\n  hairline: Boolean,\n  disabled: Boolean,\n  iconPrefix: String,\n  nativeType: makeStringProp(\"button\"),\n  loadingSize: numericProp,\n  loadingText: String,\n  loadingType: String,\n  iconPosition: makeStringProp(\"left\")\n});\nvar stdin_default = defineComponent({\n  name,\n  props: buttonProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const route = useRoute();\n    const renderLoadingIcon = () => {\n      if (slots.loading) {\n        return slots.loading();\n      }\n      return _createVNode(Loading, {\n        \"size\": props.loadingSize,\n        \"type\": props.loadingType,\n        \"class\": bem(\"loading\")\n      }, null);\n    };\n    const renderIcon = () => {\n      if (props.loading) {\n        return renderLoadingIcon();\n      }\n      if (slots.icon) {\n        return _createVNode(\"div\", {\n          \"class\": bem(\"icon\")\n        }, [slots.icon()]);\n      }\n      if (props.icon) {\n        return _createVNode(Icon, {\n          \"name\": props.icon,\n          \"class\": bem(\"icon\"),\n          \"classPrefix\": props.iconPrefix\n        }, null);\n      }\n    };\n    const renderText = () => {\n      let text;\n      if (props.loading) {\n        text = props.loadingText;\n      } else {\n        text = slots.default ? slots.default() : props.text;\n      }\n      if (text) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\")\n        }, [text]);\n      }\n    };\n    const getStyle = () => {\n      const {\n        color,\n        plain\n      } = props;\n      if (color) {\n        const style = {\n          color: plain ? color : \"white\"\n        };\n        if (!plain) {\n          style.background = color;\n        }\n        if (color.includes(\"gradient\")) {\n          style.border = 0;\n        } else {\n          style.borderColor = color;\n        }\n        return style;\n      }\n    };\n    const onClick = (event) => {\n      if (props.loading) {\n        preventDefault(event);\n      } else if (!props.disabled) {\n        emit(\"click\", event);\n        route();\n      }\n    };\n    return () => {\n      const {\n        tag,\n        type,\n        size,\n        block,\n        round,\n        plain,\n        square,\n        loading,\n        disabled,\n        hairline,\n        nativeType,\n        iconPosition\n      } = props;\n      const classes = [bem([type, size, {\n        plain,\n        block,\n        round,\n        square,\n        loading,\n        disabled,\n        hairline\n      }]), {\n        [BORDER_SURROUND]: hairline\n      }];\n      return _createVNode(tag, {\n        \"type\": nativeType,\n        \"class\": classes,\n        \"style\": getStyle(),\n        \"disabled\": disabled,\n        \"onClick\": onClick\n      }, {\n        default: () => [_createVNode(\"div\", {\n          \"class\": bem(\"content\")\n        }, [iconPosition === \"left\" && renderIcon(), renderText(), iconPosition === \"right\" && renderIcon()])]\n      });\n    };\n  }\n});\nexport {\n  buttonProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,MAAM,EAAEC,WAAW,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAC1H,SAASC,QAAQ,EAAEC,UAAU,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGP,eAAe,CAAC,QAAQ,CAAC;AAC7C,MAAMQ,WAAW,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAEO,UAAU,EAAE;EACzCM,GAAG,EAAEV,cAAc,CAAC,QAAQ,CAAC;EAC7BW,IAAI,EAAEC,MAAM;EACZC,IAAI,EAAED,MAAM;EACZE,IAAI,EAAEd,cAAc,CAAC,SAAS,CAAC;EAC/Be,IAAI,EAAEf,cAAc,CAAC,QAAQ,CAAC;EAC9BgB,KAAK,EAAEJ,MAAM;EACbK,KAAK,EAAEC,OAAO;EACdC,KAAK,EAAED,OAAO;EACdE,KAAK,EAAEF,OAAO;EACdG,MAAM,EAAEH,OAAO;EACfI,OAAO,EAAEJ,OAAO;EAChBK,QAAQ,EAAEL,OAAO;EACjBM,QAAQ,EAAEN,OAAO;EACjBO,UAAU,EAAEb,MAAM;EAClBc,UAAU,EAAE1B,cAAc,CAAC,QAAQ,CAAC;EACpC2B,WAAW,EAAE7B,WAAW;EACxB8B,WAAW,EAAEhB,MAAM;EACnBiB,WAAW,EAAEjB,MAAM;EACnBkB,YAAY,EAAE9B,cAAc,CAAC,MAAM;AACrC,CAAC,CAAC;AACF,IAAI+B,aAAa,GAAGrC,eAAe,CAAC;EAClCa,IAAI;EACJyB,KAAK,EAAEvB,WAAW;EAClBwB,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGlC,QAAQ,CAAC,CAAC;IACxB,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAIF,KAAK,CAACd,OAAO,EAAE;QACjB,OAAOc,KAAK,CAACd,OAAO,CAAC,CAAC;MACxB;MACA,OAAO1B,YAAY,CAACU,OAAO,EAAE;QAC3B,MAAM,EAAE0B,KAAK,CAACL,WAAW;QACzB,MAAM,EAAEK,KAAK,CAACH,WAAW;QACzB,OAAO,EAAErB,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,MAAM+B,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIP,KAAK,CAACV,OAAO,EAAE;QACjB,OAAOgB,iBAAiB,CAAC,CAAC;MAC5B;MACA,IAAIF,KAAK,CAACvB,IAAI,EAAE;QACd,OAAOjB,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEY,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAAC4B,KAAK,CAACvB,IAAI,CAAC,CAAC,CAAC,CAAC;MACpB;MACA,IAAImB,KAAK,CAACnB,IAAI,EAAE;QACd,OAAOjB,YAAY,CAACS,IAAI,EAAE;UACxB,MAAM,EAAE2B,KAAK,CAACnB,IAAI;UAClB,OAAO,EAAEL,GAAG,CAAC,MAAM,CAAC;UACpB,aAAa,EAAEwB,KAAK,CAACP;QACvB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC;IACD,MAAMe,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI7B,IAAI;MACR,IAAIqB,KAAK,CAACV,OAAO,EAAE;QACjBX,IAAI,GAAGqB,KAAK,CAACJ,WAAW;MAC1B,CAAC,MAAM;QACLjB,IAAI,GAAGyB,KAAK,CAACK,OAAO,GAAGL,KAAK,CAACK,OAAO,CAAC,CAAC,GAAGT,KAAK,CAACrB,IAAI;MACrD;MACA,IAAIA,IAAI,EAAE;QACR,OAAOf,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEY,GAAG,CAAC,MAAM;QACrB,CAAC,EAAE,CAACG,IAAI,CAAC,CAAC;MACZ;IACF,CAAC;IACD,MAAM+B,QAAQ,GAAGA,CAAA,KAAM;MACrB,MAAM;QACJ1B,KAAK;QACLG;MACF,CAAC,GAAGa,KAAK;MACT,IAAIhB,KAAK,EAAE;QACT,MAAM2B,KAAK,GAAG;UACZ3B,KAAK,EAAEG,KAAK,GAAGH,KAAK,GAAG;QACzB,CAAC;QACD,IAAI,CAACG,KAAK,EAAE;UACVwB,KAAK,CAACC,UAAU,GAAG5B,KAAK;QAC1B;QACA,IAAIA,KAAK,CAAC6B,QAAQ,CAAC,UAAU,CAAC,EAAE;UAC9BF,KAAK,CAACG,MAAM,GAAG,CAAC;QAClB,CAAC,MAAM;UACLH,KAAK,CAACI,WAAW,GAAG/B,KAAK;QAC3B;QACA,OAAO2B,KAAK;MACd;IACF,CAAC;IACD,MAAMK,OAAO,GAAIC,KAAK,IAAK;MACzB,IAAIjB,KAAK,CAACV,OAAO,EAAE;QACjBvB,cAAc,CAACkD,KAAK,CAAC;MACvB,CAAC,MAAM,IAAI,CAACjB,KAAK,CAACR,QAAQ,EAAE;QAC1BW,IAAI,CAAC,OAAO,EAAEc,KAAK,CAAC;QACpBZ,KAAK,CAAC,CAAC;MACT;IACF,CAAC;IACD,OAAO,MAAM;MACX,MAAM;QACJ3B,GAAG;QACHI,IAAI;QACJC,IAAI;QACJE,KAAK;QACLG,KAAK;QACLD,KAAK;QACLE,MAAM;QACNC,OAAO;QACPE,QAAQ;QACRD,QAAQ;QACRG,UAAU;QACVI;MACF,CAAC,GAAGE,KAAK;MACT,MAAMkB,OAAO,GAAG,CAAC1C,GAAG,CAAC,CAACM,IAAI,EAAEC,IAAI,EAAE;QAChCI,KAAK;QACLF,KAAK;QACLG,KAAK;QACLC,MAAM;QACNC,OAAO;QACPE,QAAQ;QACRD;MACF,CAAC,CAAC,CAAC,EAAE;QACH,CAACrB,eAAe,GAAGqB;MACrB,CAAC,CAAC;MACF,OAAO3B,YAAY,CAACc,GAAG,EAAE;QACvB,MAAM,EAAEgB,UAAU;QAClB,OAAO,EAAEwB,OAAO;QAChB,OAAO,EAAER,QAAQ,CAAC,CAAC;QACnB,UAAU,EAAElB,QAAQ;QACpB,SAAS,EAAEwB;MACb,CAAC,EAAE;QACDP,OAAO,EAAEA,CAAA,KAAM,CAAC7C,YAAY,CAAC,KAAK,EAAE;UAClC,OAAO,EAAEY,GAAG,CAAC,SAAS;QACxB,CAAC,EAAE,CAACsB,YAAY,KAAK,MAAM,IAAIS,UAAU,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,EAAEV,YAAY,KAAK,OAAO,IAAIS,UAAU,CAAC,CAAC,CAAC,CAAC;MACvG,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE9B,WAAW,EACXsB,aAAa,IAAIU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}