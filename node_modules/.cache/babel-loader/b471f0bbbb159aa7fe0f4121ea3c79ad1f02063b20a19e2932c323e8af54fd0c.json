{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed, watchEffect, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { clamp, truthProp, makeStringProp, makeNumberProp, makeNumericProp, createNamespace, BORDER_SURROUND } from \"../utils/index.mjs\";\nconst [name, bem, t] = createNamespace(\"pagination\");\nconst makePage = (number, text, active) => ({\n  number,\n  text,\n  active\n});\nconst paginationProps = {\n  mode: makeStringProp(\"multi\"),\n  prevText: String,\n  nextText: String,\n  pageCount: makeNumericProp(0),\n  modelValue: makeNumberProp(0),\n  totalItems: makeNumericProp(0),\n  showPageSize: makeNumericProp(5),\n  itemsPerPage: makeNumericProp(10),\n  forceEllipses: <PERSON>olean,\n  showPrevButton: truthProp,\n  showNextButton: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: paginationProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const count = computed(() => {\n      const {\n        pageCount,\n        totalItems,\n        itemsPerPage\n      } = props;\n      const count2 = +pageCount || Math.ceil(+totalItems / +itemsPerPage);\n      return Math.max(1, count2);\n    });\n    const pages = computed(() => {\n      const items = [];\n      const pageCount = count.value;\n      const showPageSize = +props.showPageSize;\n      const {\n        modelValue,\n        forceEllipses\n      } = props;\n      let startPage = 1;\n      let endPage = pageCount;\n      const isMaxSized = showPageSize < pageCount;\n      if (isMaxSized) {\n        startPage = Math.max(modelValue - Math.floor(showPageSize / 2), 1);\n        endPage = startPage + showPageSize - 1;\n        if (endPage > pageCount) {\n          endPage = pageCount;\n          startPage = endPage - showPageSize + 1;\n        }\n      }\n      for (let number = startPage; number <= endPage; number++) {\n        const page = makePage(number, number, number === modelValue);\n        items.push(page);\n      }\n      if (isMaxSized && showPageSize > 0 && forceEllipses) {\n        if (startPage > 1) {\n          const prevPages = makePage(startPage - 1, \"...\");\n          items.unshift(prevPages);\n        }\n        if (endPage < pageCount) {\n          const nextPages = makePage(endPage + 1, \"...\");\n          items.push(nextPages);\n        }\n      }\n      return items;\n    });\n    const updateModelValue = (value, emitChange) => {\n      value = clamp(value, 1, count.value);\n      if (props.modelValue !== value) {\n        emit(\"update:modelValue\", value);\n        if (emitChange) {\n          emit(\"change\", value);\n        }\n      }\n    };\n    watchEffect(() => updateModelValue(props.modelValue));\n    const renderDesc = () => _createVNode(\"li\", {\n      \"class\": bem(\"page-desc\")\n    }, [slots.pageDesc ? slots.pageDesc() : `${props.modelValue}/${count.value}`]);\n    const renderPrevButton = () => {\n      const {\n        mode,\n        modelValue,\n        showPrevButton\n      } = props;\n      if (!showPrevButton) {\n        return;\n      }\n      const slot = slots[\"prev-text\"];\n      const disabled = modelValue === 1;\n      return _createVNode(\"li\", {\n        \"class\": [bem(\"item\", {\n          disabled,\n          border: mode === \"simple\",\n          prev: true\n        }), BORDER_SURROUND]\n      }, [_createVNode(\"button\", {\n        \"type\": \"button\",\n        \"disabled\": disabled,\n        \"onClick\": () => updateModelValue(modelValue - 1, true)\n      }, [slot ? slot() : props.prevText || t(\"prev\")])]);\n    };\n    const renderNextButton = () => {\n      const {\n        mode,\n        modelValue,\n        showNextButton\n      } = props;\n      if (!showNextButton) {\n        return;\n      }\n      const slot = slots[\"next-text\"];\n      const disabled = modelValue === count.value;\n      return _createVNode(\"li\", {\n        \"class\": [bem(\"item\", {\n          disabled,\n          border: mode === \"simple\",\n          next: true\n        }), BORDER_SURROUND]\n      }, [_createVNode(\"button\", {\n        \"type\": \"button\",\n        \"disabled\": disabled,\n        \"onClick\": () => updateModelValue(modelValue + 1, true)\n      }, [slot ? slot() : props.nextText || t(\"next\")])]);\n    };\n    const renderPages = () => pages.value.map(page => _createVNode(\"li\", {\n      \"class\": [bem(\"item\", {\n        active: page.active,\n        page: true\n      }), BORDER_SURROUND]\n    }, [_createVNode(\"button\", {\n      \"type\": \"button\",\n      \"aria-current\": page.active || void 0,\n      \"onClick\": () => updateModelValue(page.number, true)\n    }, [slots.page ? slots.page(page) : page.text])]));\n    return () => _createVNode(\"nav\", {\n      \"role\": \"navigation\",\n      \"class\": bem()\n    }, [_createVNode(\"ul\", {\n      \"class\": bem(\"items\")\n    }, [renderPrevButton(), props.mode === \"simple\" ? renderDesc() : renderPages(), renderNextButton()])]);\n  }\n});\nexport { stdin_default as default, paginationProps };", "map": {"version": 3, "names": ["computed", "watchEffect", "defineComponent", "createVNode", "_createVNode", "clamp", "truthProp", "makeStringProp", "makeNumberProp", "makeNumericProp", "createNamespace", "BORDER_SURROUND", "name", "bem", "t", "makePage", "number", "text", "active", "paginationProps", "mode", "prevText", "String", "nextText", "pageCount", "modelValue", "totalItems", "showPageSize", "itemsPerPage", "forceEllipses", "Boolean", "showPrevButton", "showNextButton", "stdin_default", "props", "emits", "setup", "emit", "slots", "count", "count2", "Math", "ceil", "max", "pages", "items", "value", "startPage", "endPage", "isMaxSized", "floor", "page", "push", "prevPages", "unshift", "nextPages", "updateModelValue", "emitChange", "renderDesc", "pageDesc", "renderPrevButton", "slot", "disabled", "border", "prev", "onClick", "renderNextButton", "next", "renderPages", "map", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/pagination/Pagination.mjs"], "sourcesContent": ["import { computed, watchEffect, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { clamp, truthProp, makeStringProp, makeNumberProp, makeNumericProp, createNamespace, BORDER_SURROUND } from \"../utils/index.mjs\";\nconst [name, bem, t] = createNamespace(\"pagination\");\nconst makePage = (number, text, active) => ({\n  number,\n  text,\n  active\n});\nconst paginationProps = {\n  mode: makeStringProp(\"multi\"),\n  prevText: String,\n  nextText: String,\n  pageCount: makeNumericProp(0),\n  modelValue: makeNumberProp(0),\n  totalItems: makeNumericProp(0),\n  showPageSize: makeNumericProp(5),\n  itemsPerPage: makeNumericProp(10),\n  forceEllipses: Boolean,\n  showPrevButton: truthProp,\n  showNextButton: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  props: paginationProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const count = computed(() => {\n      const {\n        pageCount,\n        totalItems,\n        itemsPerPage\n      } = props;\n      const count2 = +pageCount || Math.ceil(+totalItems / +itemsPerPage);\n      return Math.max(1, count2);\n    });\n    const pages = computed(() => {\n      const items = [];\n      const pageCount = count.value;\n      const showPageSize = +props.showPageSize;\n      const {\n        modelValue,\n        forceEllipses\n      } = props;\n      let startPage = 1;\n      let endPage = pageCount;\n      const isMaxSized = showPageSize < pageCount;\n      if (isMaxSized) {\n        startPage = Math.max(modelValue - Math.floor(showPageSize / 2), 1);\n        endPage = startPage + showPageSize - 1;\n        if (endPage > pageCount) {\n          endPage = pageCount;\n          startPage = endPage - showPageSize + 1;\n        }\n      }\n      for (let number = startPage; number <= endPage; number++) {\n        const page = makePage(number, number, number === modelValue);\n        items.push(page);\n      }\n      if (isMaxSized && showPageSize > 0 && forceEllipses) {\n        if (startPage > 1) {\n          const prevPages = makePage(startPage - 1, \"...\");\n          items.unshift(prevPages);\n        }\n        if (endPage < pageCount) {\n          const nextPages = makePage(endPage + 1, \"...\");\n          items.push(nextPages);\n        }\n      }\n      return items;\n    });\n    const updateModelValue = (value, emitChange) => {\n      value = clamp(value, 1, count.value);\n      if (props.modelValue !== value) {\n        emit(\"update:modelValue\", value);\n        if (emitChange) {\n          emit(\"change\", value);\n        }\n      }\n    };\n    watchEffect(() => updateModelValue(props.modelValue));\n    const renderDesc = () => _createVNode(\"li\", {\n      \"class\": bem(\"page-desc\")\n    }, [slots.pageDesc ? slots.pageDesc() : `${props.modelValue}/${count.value}`]);\n    const renderPrevButton = () => {\n      const {\n        mode,\n        modelValue,\n        showPrevButton\n      } = props;\n      if (!showPrevButton) {\n        return;\n      }\n      const slot = slots[\"prev-text\"];\n      const disabled = modelValue === 1;\n      return _createVNode(\"li\", {\n        \"class\": [bem(\"item\", {\n          disabled,\n          border: mode === \"simple\",\n          prev: true\n        }), BORDER_SURROUND]\n      }, [_createVNode(\"button\", {\n        \"type\": \"button\",\n        \"disabled\": disabled,\n        \"onClick\": () => updateModelValue(modelValue - 1, true)\n      }, [slot ? slot() : props.prevText || t(\"prev\")])]);\n    };\n    const renderNextButton = () => {\n      const {\n        mode,\n        modelValue,\n        showNextButton\n      } = props;\n      if (!showNextButton) {\n        return;\n      }\n      const slot = slots[\"next-text\"];\n      const disabled = modelValue === count.value;\n      return _createVNode(\"li\", {\n        \"class\": [bem(\"item\", {\n          disabled,\n          border: mode === \"simple\",\n          next: true\n        }), BORDER_SURROUND]\n      }, [_createVNode(\"button\", {\n        \"type\": \"button\",\n        \"disabled\": disabled,\n        \"onClick\": () => updateModelValue(modelValue + 1, true)\n      }, [slot ? slot() : props.nextText || t(\"next\")])]);\n    };\n    const renderPages = () => pages.value.map((page) => _createVNode(\"li\", {\n      \"class\": [bem(\"item\", {\n        active: page.active,\n        page: true\n      }), BORDER_SURROUND]\n    }, [_createVNode(\"button\", {\n      \"type\": \"button\",\n      \"aria-current\": page.active || void 0,\n      \"onClick\": () => updateModelValue(page.number, true)\n    }, [slots.page ? slots.page(page) : page.text])]));\n    return () => _createVNode(\"nav\", {\n      \"role\": \"navigation\",\n      \"class\": bem()\n    }, [_createVNode(\"ul\", {\n      \"class\": bem(\"items\")\n    }, [renderPrevButton(), props.mode === \"simple\" ? renderDesc() : renderPages(), renderNextButton()])]);\n  }\n});\nexport {\n  stdin_default as default,\n  paginationProps\n};\n"], "mappings": ";;;AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACzF,SAASC,KAAK,EAAEC,SAAS,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACxI,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGJ,eAAe,CAAC,YAAY,CAAC;AACpD,MAAMK,QAAQ,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAEC,MAAM,MAAM;EAC1CF,MAAM;EACNC,IAAI;EACJC;AACF,CAAC,CAAC;AACF,MAAMC,eAAe,GAAG;EACtBC,IAAI,EAAEb,cAAc,CAAC,OAAO,CAAC;EAC7Bc,QAAQ,EAAEC,MAAM;EAChBC,QAAQ,EAAED,MAAM;EAChBE,SAAS,EAAEf,eAAe,CAAC,CAAC,CAAC;EAC7BgB,UAAU,EAAEjB,cAAc,CAAC,CAAC,CAAC;EAC7BkB,UAAU,EAAEjB,eAAe,CAAC,CAAC,CAAC;EAC9BkB,YAAY,EAAElB,eAAe,CAAC,CAAC,CAAC;EAChCmB,YAAY,EAAEnB,eAAe,CAAC,EAAE,CAAC;EACjCoB,aAAa,EAAEC,OAAO;EACtBC,cAAc,EAAEzB,SAAS;EACzB0B,cAAc,EAAE1B;AAClB,CAAC;AACD,IAAI2B,aAAa,GAAG/B,eAAe,CAAC;EAClCU,IAAI;EACJsB,KAAK,EAAEf,eAAe;EACtBgB,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAGvC,QAAQ,CAAC,MAAM;MAC3B,MAAM;QACJwB,SAAS;QACTE,UAAU;QACVE;MACF,CAAC,GAAGM,KAAK;MACT,MAAMM,MAAM,GAAG,CAAChB,SAAS,IAAIiB,IAAI,CAACC,IAAI,CAAC,CAAChB,UAAU,GAAG,CAACE,YAAY,CAAC;MACnE,OAAOa,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,MAAM,CAAC;IAC5B,CAAC,CAAC;IACF,MAAMI,KAAK,GAAG5C,QAAQ,CAAC,MAAM;MAC3B,MAAM6C,KAAK,GAAG,EAAE;MAChB,MAAMrB,SAAS,GAAGe,KAAK,CAACO,KAAK;MAC7B,MAAMnB,YAAY,GAAG,CAACO,KAAK,CAACP,YAAY;MACxC,MAAM;QACJF,UAAU;QACVI;MACF,CAAC,GAAGK,KAAK;MACT,IAAIa,SAAS,GAAG,CAAC;MACjB,IAAIC,OAAO,GAAGxB,SAAS;MACvB,MAAMyB,UAAU,GAAGtB,YAAY,GAAGH,SAAS;MAC3C,IAAIyB,UAAU,EAAE;QACdF,SAAS,GAAGN,IAAI,CAACE,GAAG,CAAClB,UAAU,GAAGgB,IAAI,CAACS,KAAK,CAACvB,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QAClEqB,OAAO,GAAGD,SAAS,GAAGpB,YAAY,GAAG,CAAC;QACtC,IAAIqB,OAAO,GAAGxB,SAAS,EAAE;UACvBwB,OAAO,GAAGxB,SAAS;UACnBuB,SAAS,GAAGC,OAAO,GAAGrB,YAAY,GAAG,CAAC;QACxC;MACF;MACA,KAAK,IAAIX,MAAM,GAAG+B,SAAS,EAAE/B,MAAM,IAAIgC,OAAO,EAAEhC,MAAM,EAAE,EAAE;QACxD,MAAMmC,IAAI,GAAGpC,QAAQ,CAACC,MAAM,EAAEA,MAAM,EAAEA,MAAM,KAAKS,UAAU,CAAC;QAC5DoB,KAAK,CAACO,IAAI,CAACD,IAAI,CAAC;MAClB;MACA,IAAIF,UAAU,IAAItB,YAAY,GAAG,CAAC,IAAIE,aAAa,EAAE;QACnD,IAAIkB,SAAS,GAAG,CAAC,EAAE;UACjB,MAAMM,SAAS,GAAGtC,QAAQ,CAACgC,SAAS,GAAG,CAAC,EAAE,KAAK,CAAC;UAChDF,KAAK,CAACS,OAAO,CAACD,SAAS,CAAC;QAC1B;QACA,IAAIL,OAAO,GAAGxB,SAAS,EAAE;UACvB,MAAM+B,SAAS,GAAGxC,QAAQ,CAACiC,OAAO,GAAG,CAAC,EAAE,KAAK,CAAC;UAC9CH,KAAK,CAACO,IAAI,CAACG,SAAS,CAAC;QACvB;MACF;MACA,OAAOV,KAAK;IACd,CAAC,CAAC;IACF,MAAMW,gBAAgB,GAAGA,CAACV,KAAK,EAAEW,UAAU,KAAK;MAC9CX,KAAK,GAAGzC,KAAK,CAACyC,KAAK,EAAE,CAAC,EAAEP,KAAK,CAACO,KAAK,CAAC;MACpC,IAAIZ,KAAK,CAACT,UAAU,KAAKqB,KAAK,EAAE;QAC9BT,IAAI,CAAC,mBAAmB,EAAES,KAAK,CAAC;QAChC,IAAIW,UAAU,EAAE;UACdpB,IAAI,CAAC,QAAQ,EAAES,KAAK,CAAC;QACvB;MACF;IACF,CAAC;IACD7C,WAAW,CAAC,MAAMuD,gBAAgB,CAACtB,KAAK,CAACT,UAAU,CAAC,CAAC;IACrD,MAAMiC,UAAU,GAAGA,CAAA,KAAMtD,YAAY,CAAC,IAAI,EAAE;MAC1C,OAAO,EAAES,GAAG,CAAC,WAAW;IAC1B,CAAC,EAAE,CAACyB,KAAK,CAACqB,QAAQ,GAAGrB,KAAK,CAACqB,QAAQ,CAAC,CAAC,GAAG,GAAGzB,KAAK,CAACT,UAAU,IAAIc,KAAK,CAACO,KAAK,EAAE,CAAC,CAAC;IAC9E,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,MAAM;QACJxC,IAAI;QACJK,UAAU;QACVM;MACF,CAAC,GAAGG,KAAK;MACT,IAAI,CAACH,cAAc,EAAE;QACnB;MACF;MACA,MAAM8B,IAAI,GAAGvB,KAAK,CAAC,WAAW,CAAC;MAC/B,MAAMwB,QAAQ,GAAGrC,UAAU,KAAK,CAAC;MACjC,OAAOrB,YAAY,CAAC,IAAI,EAAE;QACxB,OAAO,EAAE,CAACS,GAAG,CAAC,MAAM,EAAE;UACpBiD,QAAQ;UACRC,MAAM,EAAE3C,IAAI,KAAK,QAAQ;UACzB4C,IAAI,EAAE;QACR,CAAC,CAAC,EAAErD,eAAe;MACrB,CAAC,EAAE,CAACP,YAAY,CAAC,QAAQ,EAAE;QACzB,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE0D,QAAQ;QACpB,SAAS,EAAEG,CAAA,KAAMT,gBAAgB,CAAC/B,UAAU,GAAG,CAAC,EAAE,IAAI;MACxD,CAAC,EAAE,CAACoC,IAAI,GAAGA,IAAI,CAAC,CAAC,GAAG3B,KAAK,CAACb,QAAQ,IAAIP,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IACD,MAAMoD,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,MAAM;QACJ9C,IAAI;QACJK,UAAU;QACVO;MACF,CAAC,GAAGE,KAAK;MACT,IAAI,CAACF,cAAc,EAAE;QACnB;MACF;MACA,MAAM6B,IAAI,GAAGvB,KAAK,CAAC,WAAW,CAAC;MAC/B,MAAMwB,QAAQ,GAAGrC,UAAU,KAAKc,KAAK,CAACO,KAAK;MAC3C,OAAO1C,YAAY,CAAC,IAAI,EAAE;QACxB,OAAO,EAAE,CAACS,GAAG,CAAC,MAAM,EAAE;UACpBiD,QAAQ;UACRC,MAAM,EAAE3C,IAAI,KAAK,QAAQ;UACzB+C,IAAI,EAAE;QACR,CAAC,CAAC,EAAExD,eAAe;MACrB,CAAC,EAAE,CAACP,YAAY,CAAC,QAAQ,EAAE;QACzB,MAAM,EAAE,QAAQ;QAChB,UAAU,EAAE0D,QAAQ;QACpB,SAAS,EAAEG,CAAA,KAAMT,gBAAgB,CAAC/B,UAAU,GAAG,CAAC,EAAE,IAAI;MACxD,CAAC,EAAE,CAACoC,IAAI,GAAGA,IAAI,CAAC,CAAC,GAAG3B,KAAK,CAACX,QAAQ,IAAIT,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IACD,MAAMsD,WAAW,GAAGA,CAAA,KAAMxB,KAAK,CAACE,KAAK,CAACuB,GAAG,CAAElB,IAAI,IAAK/C,YAAY,CAAC,IAAI,EAAE;MACrE,OAAO,EAAE,CAACS,GAAG,CAAC,MAAM,EAAE;QACpBK,MAAM,EAAEiC,IAAI,CAACjC,MAAM;QACnBiC,IAAI,EAAE;MACR,CAAC,CAAC,EAAExC,eAAe;IACrB,CAAC,EAAE,CAACP,YAAY,CAAC,QAAQ,EAAE;MACzB,MAAM,EAAE,QAAQ;MAChB,cAAc,EAAE+C,IAAI,CAACjC,MAAM,IAAI,KAAK,CAAC;MACrC,SAAS,EAAE+C,CAAA,KAAMT,gBAAgB,CAACL,IAAI,CAACnC,MAAM,EAAE,IAAI;IACrD,CAAC,EAAE,CAACsB,KAAK,CAACa,IAAI,GAAGb,KAAK,CAACa,IAAI,CAACA,IAAI,CAAC,GAAGA,IAAI,CAAClC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,OAAO,MAAMb,YAAY,CAAC,KAAK,EAAE;MAC/B,MAAM,EAAE,YAAY;MACpB,OAAO,EAAES,GAAG,CAAC;IACf,CAAC,EAAE,CAACT,YAAY,CAAC,IAAI,EAAE;MACrB,OAAO,EAAES,GAAG,CAAC,OAAO;IACtB,CAAC,EAAE,CAAC+C,gBAAgB,CAAC,CAAC,EAAE1B,KAAK,CAACd,IAAI,KAAK,QAAQ,GAAGsC,UAAU,CAAC,CAAC,GAAGU,WAAW,CAAC,CAAC,EAAEF,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxG;AACF,CAAC,CAAC;AACF,SACEjC,aAAa,IAAIqC,OAAO,EACxBnD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}