{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _AddressEdit from \"./AddressEdit.mjs\";\nconst AddressEdit = withInstall(_AddressEdit);\nvar stdin_default = AddressEdit;\nimport { addressEditProps } from \"./AddressEdit.mjs\";\nexport { AddressEdit, addressEditProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_AddressEdit", "AddressEdit", "stdin_default", "addressEditProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/address-edit/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _AddressEdit from \"./AddressEdit.mjs\";\nconst AddressEdit = withInstall(_AddressEdit);\nvar stdin_default = AddressEdit;\nimport { addressEditProps } from \"./AddressEdit.mjs\";\nexport {\n  AddressEdit,\n  addressEditProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXE,gBAAgB,EAChBD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}