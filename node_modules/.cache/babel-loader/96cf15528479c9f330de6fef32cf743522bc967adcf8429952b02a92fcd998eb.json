{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Sticky from \"./Sticky.mjs\";\nconst Sticky = withInstall(_Sticky);\nvar stdin_default = Sticky;\nimport { stickyProps } from \"./Sticky.mjs\";\nexport { Sticky, stdin_default as default, stickyProps };", "map": {"version": 3, "names": ["withInstall", "_Sticky", "<PERSON>y", "stdin_default", "stickyProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/sticky/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Sticky from \"./Sticky.mjs\";\nconst Sticky = withInstall(_Sticky);\nvar stdin_default = Sticky;\nimport { stickyProps } from \"./Sticky.mjs\";\nexport {\n  Sticky,\n  stdin_default as default,\n  stickyProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEF,MAAM,EACNC,aAAa,IAAIE,OAAO,EACxBD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}