{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Form from \"./Form.mjs\";\nconst Form = withInstall(_Form);\nvar stdin_default = Form;\nimport { formProps } from \"./Form.mjs\";\nexport { Form, stdin_default as default, formProps };", "map": {"version": 3, "names": ["withInstall", "_Form", "Form", "stdin_default", "formProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/form/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Form from \"./Form.mjs\";\nconst Form = withInstall(_Form);\nvar stdin_default = Form;\nimport { formProps } from \"./Form.mjs\";\nexport {\n  Form,\n  stdin_default as default,\n  formProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SAASE,SAAS,QAAQ,YAAY;AACtC,SACEF,IAAI,EACJC,aAAa,IAAIE,OAAO,EACxBD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}