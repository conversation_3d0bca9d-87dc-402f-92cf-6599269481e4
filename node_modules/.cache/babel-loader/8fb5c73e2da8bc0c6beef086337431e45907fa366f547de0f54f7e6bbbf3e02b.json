{"ast": null, "code": "import { noop, isPromise } from \"./basic.mjs\";\nfunction callInterceptor(interceptor, {\n  args = [],\n  done,\n  canceled,\n  error\n}) {\n  if (interceptor) {\n    const returnVal = interceptor.apply(null, args);\n    if (isPromise(returnVal)) {\n      returnVal.then(value => {\n        if (value) {\n          done();\n        } else if (canceled) {\n          canceled();\n        }\n      }).catch(error || noop);\n    } else if (returnVal) {\n      done();\n    } else if (canceled) {\n      canceled();\n    }\n  } else {\n    done();\n  }\n}\nexport { callInterceptor };", "map": {"version": 3, "names": ["noop", "isPromise", "callInterceptor", "interceptor", "args", "done", "canceled", "error", "returnVal", "apply", "then", "value", "catch"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/utils/interceptor.mjs"], "sourcesContent": ["import { noop, isPromise } from \"./basic.mjs\";\nfunction callInterceptor(interceptor, {\n  args = [],\n  done,\n  canceled,\n  error\n}) {\n  if (interceptor) {\n    const returnVal = interceptor.apply(null, args);\n    if (isPromise(returnVal)) {\n      returnVal.then((value) => {\n        if (value) {\n          done();\n        } else if (canceled) {\n          canceled();\n        }\n      }).catch(error || noop);\n    } else if (returnVal) {\n      done();\n    } else if (canceled) {\n      canceled();\n    }\n  } else {\n    done();\n  }\n}\nexport {\n  callInterceptor\n};\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,SAAS,QAAQ,aAAa;AAC7C,SAASC,eAAeA,CAACC,WAAW,EAAE;EACpCC,IAAI,GAAG,EAAE;EACTC,IAAI;EACJC,QAAQ;EACRC;AACF,CAAC,EAAE;EACD,IAAIJ,WAAW,EAAE;IACf,MAAMK,SAAS,GAAGL,WAAW,CAACM,KAAK,CAAC,IAAI,EAAEL,IAAI,CAAC;IAC/C,IAAIH,SAAS,CAACO,SAAS,CAAC,EAAE;MACxBA,SAAS,CAACE,IAAI,CAAEC,KAAK,IAAK;QACxB,IAAIA,KAAK,EAAE;UACTN,IAAI,CAAC,CAAC;QACR,CAAC,MAAM,IAAIC,QAAQ,EAAE;UACnBA,QAAQ,CAAC,CAAC;QACZ;MACF,CAAC,CAAC,CAACM,KAAK,CAACL,KAAK,IAAIP,IAAI,CAAC;IACzB,CAAC,MAAM,IAAIQ,SAAS,EAAE;MACpBH,IAAI,CAAC,CAAC;IACR,CAAC,MAAM,IAAIC,QAAQ,EAAE;MACnBA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,MAAM;IACLD,IAAI,CAAC,CAAC;EACR;AACF;AACA,SACEH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}