{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Swipe from \"./Swipe.mjs\";\nconst Swipe = withInstall(_Swipe);\nvar stdin_default = Swipe;\nimport { swipeProps } from \"./Swipe.mjs\";\nexport { Swipe, stdin_default as default, swipeProps };", "map": {"version": 3, "names": ["withInstall", "_Swipe", "Swipe", "stdin_default", "swipeProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/swipe/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Swipe from \"./Swipe.mjs\";\nconst Swipe = withInstall(_Swipe);\nvar stdin_default = Swipe;\nimport { swipeProps } from \"./Swipe.mjs\";\nexport {\n  Swipe,\n  stdin_default as default,\n  swipeProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SAASE,UAAU,QAAQ,aAAa;AACxC,SACEF,KAAK,EACLC,aAAa,IAAIE,OAAO,EACxBD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}