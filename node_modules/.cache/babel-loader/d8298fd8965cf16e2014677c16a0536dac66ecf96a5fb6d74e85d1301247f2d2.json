{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Picker from \"./Picker.mjs\";\nconst Picker = withInstall(_Picker);\nvar stdin_default = Picker;\nimport { pickerProps } from \"./Picker.mjs\";\nexport { Picker, stdin_default as default, pickerProps };", "map": {"version": 3, "names": ["withInstall", "_Picker", "Picker", "stdin_default", "pickerProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/picker/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Picker from \"./Picker.mjs\";\nconst Picker = withInstall(_Picker);\nvar stdin_default = Picker;\nimport { pickerProps } from \"./Picker.mjs\";\nexport {\n  Picker,\n  stdin_default as default,\n  pickerProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEF,MAAM,EACNC,aAAa,IAAIE,OAAO,EACxBD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}