{"ast": null, "code": "import { ref, defineComponent, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { pick, extend, truthProp, preventDefault, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { fieldSharedProps } from \"../field/Field.mjs\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Field } from \"../field/index.mjs\";\nconst [name, bem, t] = createNamespace(\"search\");\nconst searchProps = extend({}, fieldSharedProps, {\n  label: String,\n  shape: makeStringProp(\"square\"),\n  leftIcon: makeStringProp(\"search\"),\n  clearable: truthProp,\n  actionText: String,\n  background: String,\n  showAction: <PERSON>olean\n});\nvar stdin_default = defineComponent({\n  name,\n  props: searchProps,\n  emits: [\"blur\", \"focus\", \"clear\", \"search\", \"cancel\", \"clickInput\", \"clickLeftIcon\", \"clickRightIcon\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots,\n    attrs\n  }) {\n    const id = useId();\n    const fieldRef = ref();\n    const onCancel = () => {\n      if (!slots.action) {\n        emit(\"update:modelValue\", \"\");\n        emit(\"cancel\");\n      }\n    };\n    const onKeypress = event => {\n      const ENTER_CODE = 13;\n      if (event.keyCode === ENTER_CODE) {\n        preventDefault(event);\n        emit(\"search\", props.modelValue);\n      }\n    };\n    const getInputId = () => props.id || `${id}-input`;\n    const renderLabel = () => {\n      if (slots.label || props.label) {\n        return _createVNode(\"label\", {\n          \"class\": bem(\"label\"),\n          \"for\": getInputId(),\n          \"data-allow-mismatch\": \"attribute\"\n        }, [slots.label ? slots.label() : props.label]);\n      }\n    };\n    const renderAction = () => {\n      if (props.showAction) {\n        const text = props.actionText || t(\"cancel\");\n        return _createVNode(\"div\", {\n          \"class\": bem(\"action\"),\n          \"role\": \"button\",\n          \"tabindex\": 0,\n          \"onClick\": onCancel\n        }, [slots.action ? slots.action() : text]);\n      }\n    };\n    const blur = () => {\n      var _a;\n      return (_a = fieldRef.value) == null ? void 0 : _a.blur();\n    };\n    const focus = () => {\n      var _a;\n      return (_a = fieldRef.value) == null ? void 0 : _a.focus();\n    };\n    const onBlur = event => emit(\"blur\", event);\n    const onFocus = event => emit(\"focus\", event);\n    const onClear = event => emit(\"clear\", event);\n    const onClickInput = event => emit(\"clickInput\", event);\n    const onClickLeftIcon = event => emit(\"clickLeftIcon\", event);\n    const onClickRightIcon = event => emit(\"clickRightIcon\", event);\n    const fieldPropNames = Object.keys(fieldSharedProps);\n    const renderField = () => {\n      const fieldAttrs = extend({}, attrs, pick(props, fieldPropNames), {\n        id: getInputId()\n      });\n      const onInput = value => emit(\"update:modelValue\", value);\n      return _createVNode(Field, _mergeProps({\n        \"ref\": fieldRef,\n        \"type\": \"search\",\n        \"class\": bem(\"field\", {\n          \"with-message\": fieldAttrs.errorMessage\n        }),\n        \"border\": false,\n        \"onBlur\": onBlur,\n        \"onFocus\": onFocus,\n        \"onClear\": onClear,\n        \"onKeypress\": onKeypress,\n        \"onClickInput\": onClickInput,\n        \"onClickLeftIcon\": onClickLeftIcon,\n        \"onClickRightIcon\": onClickRightIcon,\n        \"onUpdate:modelValue\": onInput\n      }, fieldAttrs), pick(slots, [\"left-icon\", \"right-icon\"]));\n    };\n    useExpose({\n      focus,\n      blur\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          \"show-action\": props.showAction\n        }),\n        \"style\": {\n          background: props.background\n        }\n      }, [(_a = slots.left) == null ? void 0 : _a.call(slots), _createVNode(\"div\", {\n        \"class\": bem(\"content\", props.shape)\n      }, [renderLabel(), renderField()]), renderAction()]);\n    };\n  }\n});\nexport { stdin_default as default, searchProps };", "map": {"version": 3, "names": ["ref", "defineComponent", "createVNode", "_createVNode", "mergeProps", "_mergeProps", "pick", "extend", "truthProp", "preventDefault", "makeStringProp", "createNamespace", "fieldSharedProps", "useId", "useExpose", "Field", "name", "bem", "t", "searchProps", "label", "String", "shape", "leftIcon", "clearable", "actionText", "background", "showAction", "Boolean", "stdin_default", "props", "emits", "setup", "emit", "slots", "attrs", "id", "fieldRef", "onCancel", "action", "onKeypress", "event", "ENTER_CODE", "keyCode", "modelValue", "getInputId", "renderLabel", "renderAction", "text", "blur", "_a", "value", "focus", "onBlur", "onFocus", "onClear", "onClickInput", "onClickLeftIcon", "onClickRightIcon", "fieldPropNames", "Object", "keys", "renderField", "fieldAttrs", "onInput", "errorMessage", "left", "call", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/search/Search.mjs"], "sourcesContent": ["import { ref, defineComponent, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { pick, extend, truthProp, preventDefault, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { fieldSharedProps } from \"../field/Field.mjs\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { Field } from \"../field/index.mjs\";\nconst [name, bem, t] = createNamespace(\"search\");\nconst searchProps = extend({}, fieldSharedProps, {\n  label: String,\n  shape: makeStringProp(\"square\"),\n  leftIcon: makeStringProp(\"search\"),\n  clearable: truthProp,\n  actionText: String,\n  background: String,\n  showAction: Boolean\n});\nvar stdin_default = defineComponent({\n  name,\n  props: searchProps,\n  emits: [\"blur\", \"focus\", \"clear\", \"search\", \"cancel\", \"clickInput\", \"clickLeftIcon\", \"clickRightIcon\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots,\n    attrs\n  }) {\n    const id = useId();\n    const fieldRef = ref();\n    const onCancel = () => {\n      if (!slots.action) {\n        emit(\"update:modelValue\", \"\");\n        emit(\"cancel\");\n      }\n    };\n    const onKeypress = (event) => {\n      const ENTER_CODE = 13;\n      if (event.keyCode === ENTER_CODE) {\n        preventDefault(event);\n        emit(\"search\", props.modelValue);\n      }\n    };\n    const getInputId = () => props.id || `${id}-input`;\n    const renderLabel = () => {\n      if (slots.label || props.label) {\n        return _createVNode(\"label\", {\n          \"class\": bem(\"label\"),\n          \"for\": getInputId(),\n          \"data-allow-mismatch\": \"attribute\"\n        }, [slots.label ? slots.label() : props.label]);\n      }\n    };\n    const renderAction = () => {\n      if (props.showAction) {\n        const text = props.actionText || t(\"cancel\");\n        return _createVNode(\"div\", {\n          \"class\": bem(\"action\"),\n          \"role\": \"button\",\n          \"tabindex\": 0,\n          \"onClick\": onCancel\n        }, [slots.action ? slots.action() : text]);\n      }\n    };\n    const blur = () => {\n      var _a;\n      return (_a = fieldRef.value) == null ? void 0 : _a.blur();\n    };\n    const focus = () => {\n      var _a;\n      return (_a = fieldRef.value) == null ? void 0 : _a.focus();\n    };\n    const onBlur = (event) => emit(\"blur\", event);\n    const onFocus = (event) => emit(\"focus\", event);\n    const onClear = (event) => emit(\"clear\", event);\n    const onClickInput = (event) => emit(\"clickInput\", event);\n    const onClickLeftIcon = (event) => emit(\"clickLeftIcon\", event);\n    const onClickRightIcon = (event) => emit(\"clickRightIcon\", event);\n    const fieldPropNames = Object.keys(fieldSharedProps);\n    const renderField = () => {\n      const fieldAttrs = extend({}, attrs, pick(props, fieldPropNames), {\n        id: getInputId()\n      });\n      const onInput = (value) => emit(\"update:modelValue\", value);\n      return _createVNode(Field, _mergeProps({\n        \"ref\": fieldRef,\n        \"type\": \"search\",\n        \"class\": bem(\"field\", {\n          \"with-message\": fieldAttrs.errorMessage\n        }),\n        \"border\": false,\n        \"onBlur\": onBlur,\n        \"onFocus\": onFocus,\n        \"onClear\": onClear,\n        \"onKeypress\": onKeypress,\n        \"onClickInput\": onClickInput,\n        \"onClickLeftIcon\": onClickLeftIcon,\n        \"onClickRightIcon\": onClickRightIcon,\n        \"onUpdate:modelValue\": onInput\n      }, fieldAttrs), pick(slots, [\"left-icon\", \"right-icon\"]));\n    };\n    useExpose({\n      focus,\n      blur\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          \"show-action\": props.showAction\n        }),\n        \"style\": {\n          background: props.background\n        }\n      }, [(_a = slots.left) == null ? void 0 : _a.call(slots), _createVNode(\"div\", {\n        \"class\": bem(\"content\", props.shape)\n      }, [renderLabel(), renderField()]), renderAction()]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  searchProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAClG,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,cAAc,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC7G,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGP,eAAe,CAAC,QAAQ,CAAC;AAChD,MAAMQ,WAAW,GAAGZ,MAAM,CAAC,CAAC,CAAC,EAAEK,gBAAgB,EAAE;EAC/CQ,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAEZ,cAAc,CAAC,QAAQ,CAAC;EAC/Ba,QAAQ,EAAEb,cAAc,CAAC,QAAQ,CAAC;EAClCc,SAAS,EAAEhB,SAAS;EACpBiB,UAAU,EAAEJ,MAAM;EAClBK,UAAU,EAAEL,MAAM;EAClBM,UAAU,EAAEC;AACd,CAAC,CAAC;AACF,IAAIC,aAAa,GAAG5B,eAAe,CAAC;EAClCe,IAAI;EACJc,KAAK,EAAEX,WAAW;EAClBY,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;EAC3HC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC,KAAK;IACLC;EACF,CAAC,EAAE;IACD,MAAMC,EAAE,GAAGvB,KAAK,CAAC,CAAC;IAClB,MAAMwB,QAAQ,GAAGrC,GAAG,CAAC,CAAC;IACtB,MAAMsC,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI,CAACJ,KAAK,CAACK,MAAM,EAAE;QACjBN,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;QAC7BA,IAAI,CAAC,QAAQ,CAAC;MAChB;IACF,CAAC;IACD,MAAMO,UAAU,GAAIC,KAAK,IAAK;MAC5B,MAAMC,UAAU,GAAG,EAAE;MACrB,IAAID,KAAK,CAACE,OAAO,KAAKD,UAAU,EAAE;QAChCjC,cAAc,CAACgC,KAAK,CAAC;QACrBR,IAAI,CAAC,QAAQ,EAAEH,KAAK,CAACc,UAAU,CAAC;MAClC;IACF,CAAC;IACD,MAAMC,UAAU,GAAGA,CAAA,KAAMf,KAAK,CAACM,EAAE,IAAI,GAAGA,EAAE,QAAQ;IAClD,MAAMU,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIZ,KAAK,CAACd,KAAK,IAAIU,KAAK,CAACV,KAAK,EAAE;QAC9B,OAAOjB,YAAY,CAAC,OAAO,EAAE;UAC3B,OAAO,EAAEc,GAAG,CAAC,OAAO,CAAC;UACrB,KAAK,EAAE4B,UAAU,CAAC,CAAC;UACnB,qBAAqB,EAAE;QACzB,CAAC,EAAE,CAACX,KAAK,CAACd,KAAK,GAAGc,KAAK,CAACd,KAAK,CAAC,CAAC,GAAGU,KAAK,CAACV,KAAK,CAAC,CAAC;MACjD;IACF,CAAC;IACD,MAAM2B,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIjB,KAAK,CAACH,UAAU,EAAE;QACpB,MAAMqB,IAAI,GAAGlB,KAAK,CAACL,UAAU,IAAIP,CAAC,CAAC,QAAQ,CAAC;QAC5C,OAAOf,YAAY,CAAC,KAAK,EAAE;UACzB,OAAO,EAAEc,GAAG,CAAC,QAAQ,CAAC;UACtB,MAAM,EAAE,QAAQ;UAChB,UAAU,EAAE,CAAC;UACb,SAAS,EAAEqB;QACb,CAAC,EAAE,CAACJ,KAAK,CAACK,MAAM,GAAGL,KAAK,CAACK,MAAM,CAAC,CAAC,GAAGS,IAAI,CAAC,CAAC;MAC5C;IACF,CAAC;IACD,MAAMC,IAAI,GAAGA,CAAA,KAAM;MACjB,IAAIC,EAAE;MACN,OAAO,CAACA,EAAE,GAAGb,QAAQ,CAACc,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACD,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAMG,KAAK,GAAGA,CAAA,KAAM;MAClB,IAAIF,EAAE;MACN,OAAO,CAACA,EAAE,GAAGb,QAAQ,CAACc,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,KAAK,CAAC,CAAC;IAC5D,CAAC;IACD,MAAMC,MAAM,GAAIZ,KAAK,IAAKR,IAAI,CAAC,MAAM,EAAEQ,KAAK,CAAC;IAC7C,MAAMa,OAAO,GAAIb,KAAK,IAAKR,IAAI,CAAC,OAAO,EAAEQ,KAAK,CAAC;IAC/C,MAAMc,OAAO,GAAId,KAAK,IAAKR,IAAI,CAAC,OAAO,EAAEQ,KAAK,CAAC;IAC/C,MAAMe,YAAY,GAAIf,KAAK,IAAKR,IAAI,CAAC,YAAY,EAAEQ,KAAK,CAAC;IACzD,MAAMgB,eAAe,GAAIhB,KAAK,IAAKR,IAAI,CAAC,eAAe,EAAEQ,KAAK,CAAC;IAC/D,MAAMiB,gBAAgB,GAAIjB,KAAK,IAAKR,IAAI,CAAC,gBAAgB,EAAEQ,KAAK,CAAC;IACjE,MAAMkB,cAAc,GAAGC,MAAM,CAACC,IAAI,CAACjD,gBAAgB,CAAC;IACpD,MAAMkD,WAAW,GAAGA,CAAA,KAAM;MACxB,MAAMC,UAAU,GAAGxD,MAAM,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE7B,IAAI,CAACwB,KAAK,EAAE6B,cAAc,CAAC,EAAE;QAChEvB,EAAE,EAAES,UAAU,CAAC;MACjB,CAAC,CAAC;MACF,MAAMmB,OAAO,GAAIb,KAAK,IAAKlB,IAAI,CAAC,mBAAmB,EAAEkB,KAAK,CAAC;MAC3D,OAAOhD,YAAY,CAACY,KAAK,EAAEV,WAAW,CAAC;QACrC,KAAK,EAAEgC,QAAQ;QACf,MAAM,EAAE,QAAQ;QAChB,OAAO,EAAEpB,GAAG,CAAC,OAAO,EAAE;UACpB,cAAc,EAAE8C,UAAU,CAACE;QAC7B,CAAC,CAAC;QACF,QAAQ,EAAE,KAAK;QACf,QAAQ,EAAEZ,MAAM;QAChB,SAAS,EAAEC,OAAO;QAClB,SAAS,EAAEC,OAAO;QAClB,YAAY,EAAEf,UAAU;QACxB,cAAc,EAAEgB,YAAY;QAC5B,iBAAiB,EAAEC,eAAe;QAClC,kBAAkB,EAAEC,gBAAgB;QACpC,qBAAqB,EAAEM;MACzB,CAAC,EAAED,UAAU,CAAC,EAAEzD,IAAI,CAAC4B,KAAK,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;IAC3D,CAAC;IACDpB,SAAS,CAAC;MACRsC,KAAK;MACLH;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIC,EAAE;MACN,OAAO/C,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEc,GAAG,CAAC;UACX,aAAa,EAAEa,KAAK,CAACH;QACvB,CAAC,CAAC;QACF,OAAO,EAAE;UACPD,UAAU,EAAEI,KAAK,CAACJ;QACpB;MACF,CAAC,EAAE,CAAC,CAACwB,EAAE,GAAGhB,KAAK,CAACgC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhB,EAAE,CAACiB,IAAI,CAACjC,KAAK,CAAC,EAAE/B,YAAY,CAAC,KAAK,EAAE;QAC3E,OAAO,EAAEc,GAAG,CAAC,SAAS,EAAEa,KAAK,CAACR,KAAK;MACrC,CAAC,EAAE,CAACwB,WAAW,CAAC,CAAC,EAAEgB,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEf,YAAY,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACElB,aAAa,IAAIuC,OAAO,EACxBjD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}