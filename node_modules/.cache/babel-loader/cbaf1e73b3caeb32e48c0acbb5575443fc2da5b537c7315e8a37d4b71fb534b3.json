{"ast": null, "code": "const unknownProp = null;\nconst numericProp = [Number, String];\nconst truthProp = {\n  type: Boolean,\n  default: true\n};\nconst makeRequiredProp = type => ({\n  type,\n  required: true\n});\nconst makeArrayProp = () => ({\n  type: Array,\n  default: () => []\n});\nconst makeNumberProp = defaultVal => ({\n  type: Number,\n  default: defaultVal\n});\nconst makeNumericProp = defaultVal => ({\n  type: numericProp,\n  default: defaultVal\n});\nconst makeStringProp = defaultVal => ({\n  type: String,\n  default: defaultVal\n});\nexport { makeArrayProp, makeNumberProp, makeNumericProp, makeRequiredProp, makeStringProp, numericProp, truthProp, unknownProp };", "map": {"version": 3, "names": ["unknownProp", "numericProp", "Number", "String", "truthProp", "type", "Boolean", "default", "makeRequiredProp", "required", "makeArrayProp", "Array", "makeNumberProp", "defaultVal", "makeNumericProp", "makeStringProp"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/utils/props.mjs"], "sourcesContent": ["const unknownProp = null;\nconst numericProp = [Number, String];\nconst truthProp = {\n  type: Boolean,\n  default: true\n};\nconst makeRequiredProp = (type) => ({\n  type,\n  required: true\n});\nconst makeArrayProp = () => ({\n  type: Array,\n  default: () => []\n});\nconst makeNumberProp = (defaultVal) => ({\n  type: Number,\n  default: defaultVal\n});\nconst makeNumericProp = (defaultVal) => ({\n  type: numericProp,\n  default: defaultVal\n});\nconst makeStringProp = (defaultVal) => ({\n  type: String,\n  default: defaultVal\n});\nexport {\n  makeArrayProp,\n  makeNumberProp,\n  makeNumericProp,\n  makeRequiredProp,\n  makeStringProp,\n  numericProp,\n  truthProp,\n  unknownProp\n};\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG,IAAI;AACxB,MAAMC,WAAW,GAAG,CAACC,MAAM,EAAEC,MAAM,CAAC;AACpC,MAAMC,SAAS,GAAG;EAChBC,IAAI,EAAEC,OAAO;EACbC,OAAO,EAAE;AACX,CAAC;AACD,MAAMC,gBAAgB,GAAIH,IAAI,KAAM;EAClCA,IAAI;EACJI,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,aAAa,GAAGA,CAAA,MAAO;EAC3BL,IAAI,EAAEM,KAAK;EACXJ,OAAO,EAAEA,CAAA,KAAM;AACjB,CAAC,CAAC;AACF,MAAMK,cAAc,GAAIC,UAAU,KAAM;EACtCR,IAAI,EAAEH,MAAM;EACZK,OAAO,EAAEM;AACX,CAAC,CAAC;AACF,MAAMC,eAAe,GAAID,UAAU,KAAM;EACvCR,IAAI,EAAEJ,WAAW;EACjBM,OAAO,EAAEM;AACX,CAAC,CAAC;AACF,MAAME,cAAc,GAAIF,UAAU,KAAM;EACtCR,IAAI,EAAEF,MAAM;EACZI,OAAO,EAAEM;AACX,CAAC,CAAC;AACF,SACEH,aAAa,EACbE,cAAc,EACdE,eAAe,EACfN,gBAAgB,EAChBO,cAAc,EACdd,WAAW,EACXG,SAAS,EACTJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}