{"ast": null, "code": "import { useRect, useWindowSize } from \"@vant/use\";\nimport { unref } from \"vue\";\nimport { isIOS as checkIsIOS } from \"./basic.mjs\";\nfunction getScrollTop(el) {\n  const top = \"scrollTop\" in el ? el.scrollTop : el.pageYOffset;\n  return Math.max(top, 0);\n}\nfunction setScrollTop(el, value) {\n  if (\"scrollTop\" in el) {\n    el.scrollTop = value;\n  } else {\n    el.scrollTo(el.scrollX, value);\n  }\n}\nfunction getRootScrollTop() {\n  return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n}\nfunction setRootScrollTop(value) {\n  setScrollTop(window, value);\n  setScrollTop(document.body, value);\n}\nfunction getElementTop(el, scroller) {\n  if (el === window) {\n    return 0;\n  }\n  const scrollTop = scroller ? getScrollTop(scroller) : getRootScrollTop();\n  return useRect(el).top + scrollTop;\n}\nconst isIOS = checkIsIOS();\nfunction resetScroll() {\n  if (isIOS) {\n    setRootScrollTop(getRootScrollTop());\n  }\n}\nconst stopPropagation = event => event.stopPropagation();\nfunction preventDefault(event, isStopPropagation) {\n  if (typeof event.cancelable !== \"boolean\" || event.cancelable) {\n    event.preventDefault();\n  }\n  if (isStopPropagation) {\n    stopPropagation(event);\n  }\n}\nfunction isHidden(elementRef) {\n  const el = unref(elementRef);\n  if (!el) {\n    return false;\n  }\n  const style = window.getComputedStyle(el);\n  const hidden = style.display === \"none\";\n  const parentHidden = el.offsetParent === null && style.position !== \"fixed\";\n  return hidden || parentHidden;\n}\nconst {\n  width: windowWidth,\n  height: windowHeight\n} = useWindowSize();\nfunction isContainingBlock(el) {\n  const css = window.getComputedStyle(el);\n  return css.transform !== \"none\" || css.perspective !== \"none\" || [\"transform\", \"perspective\", \"filter\"].some(value => (css.willChange || \"\").includes(value));\n}\nfunction getContainingBlock(el) {\n  let node = el.parentElement;\n  while (node) {\n    if (node && node.tagName !== \"HTML\" && node.tagName !== \"BODY\" && isContainingBlock(node)) {\n      return node;\n    }\n    node = node.parentElement;\n  }\n  return null;\n}\nexport { getContainingBlock, getElementTop, getRootScrollTop, getScrollTop, isHidden, preventDefault, resetScroll, setRootScrollTop, setScrollTop, stopPropagation, windowHeight, windowWidth };", "map": {"version": 3, "names": ["useRect", "useWindowSize", "unref", "isIOS", "checkIsIOS", "getScrollTop", "el", "top", "scrollTop", "pageYOffset", "Math", "max", "setScrollTop", "value", "scrollTo", "scrollX", "getRootScrollTop", "window", "document", "documentElement", "body", "setRootScrollTop", "getElementTop", "scroller", "resetScroll", "stopPropagation", "event", "preventDefault", "isStopPropagation", "cancelable", "isHidden", "elementRef", "style", "getComputedStyle", "hidden", "display", "parentHidden", "offsetParent", "position", "width", "windowWidth", "height", "windowHeight", "isContainingBlock", "css", "transform", "perspective", "some", "<PERSON><PERSON><PERSON><PERSON>", "includes", "getContainingBlock", "node", "parentElement", "tagName"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/utils/dom.mjs"], "sourcesContent": ["import { useRect, useWindowSize } from \"@vant/use\";\nimport { unref } from \"vue\";\nimport { isIOS as checkIsIOS } from \"./basic.mjs\";\nfunction getScrollTop(el) {\n  const top = \"scrollTop\" in el ? el.scrollTop : el.pageYOffset;\n  return Math.max(top, 0);\n}\nfunction setScrollTop(el, value) {\n  if (\"scrollTop\" in el) {\n    el.scrollTop = value;\n  } else {\n    el.scrollTo(el.scrollX, value);\n  }\n}\nfunction getRootScrollTop() {\n  return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n}\nfunction setRootScrollTop(value) {\n  setScrollTop(window, value);\n  setScrollTop(document.body, value);\n}\nfunction getElementTop(el, scroller) {\n  if (el === window) {\n    return 0;\n  }\n  const scrollTop = scroller ? getScrollTop(scroller) : getRootScrollTop();\n  return useRect(el).top + scrollTop;\n}\nconst isIOS = checkIsIOS();\nfunction resetScroll() {\n  if (isIOS) {\n    setRootScrollTop(getRootScrollTop());\n  }\n}\nconst stopPropagation = (event) => event.stopPropagation();\nfunction preventDefault(event, isStopPropagation) {\n  if (typeof event.cancelable !== \"boolean\" || event.cancelable) {\n    event.preventDefault();\n  }\n  if (isStopPropagation) {\n    stopPropagation(event);\n  }\n}\nfunction isHidden(elementRef) {\n  const el = unref(elementRef);\n  if (!el) {\n    return false;\n  }\n  const style = window.getComputedStyle(el);\n  const hidden = style.display === \"none\";\n  const parentHidden = el.offsetParent === null && style.position !== \"fixed\";\n  return hidden || parentHidden;\n}\nconst { width: windowWidth, height: windowHeight } = useWindowSize();\nfunction isContainingBlock(el) {\n  const css = window.getComputedStyle(el);\n  return css.transform !== \"none\" || css.perspective !== \"none\" || [\"transform\", \"perspective\", \"filter\"].some(\n    (value) => (css.willChange || \"\").includes(value)\n  );\n}\nfunction getContainingBlock(el) {\n  let node = el.parentElement;\n  while (node) {\n    if (node && node.tagName !== \"HTML\" && node.tagName !== \"BODY\" && isContainingBlock(node)) {\n      return node;\n    }\n    node = node.parentElement;\n  }\n  return null;\n}\nexport {\n  getContainingBlock,\n  getElementTop,\n  getRootScrollTop,\n  getScrollTop,\n  isHidden,\n  preventDefault,\n  resetScroll,\n  setRootScrollTop,\n  setScrollTop,\n  stopPropagation,\n  windowHeight,\n  windowWidth\n};\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,aAAa,QAAQ,WAAW;AAClD,SAASC,KAAK,QAAQ,KAAK;AAC3B,SAASC,KAAK,IAAIC,UAAU,QAAQ,aAAa;AACjD,SAASC,YAAYA,CAACC,EAAE,EAAE;EACxB,MAAMC,GAAG,GAAG,WAAW,IAAID,EAAE,GAAGA,EAAE,CAACE,SAAS,GAAGF,EAAE,CAACG,WAAW;EAC7D,OAAOC,IAAI,CAACC,GAAG,CAACJ,GAAG,EAAE,CAAC,CAAC;AACzB;AACA,SAASK,YAAYA,CAACN,EAAE,EAAEO,KAAK,EAAE;EAC/B,IAAI,WAAW,IAAIP,EAAE,EAAE;IACrBA,EAAE,CAACE,SAAS,GAAGK,KAAK;EACtB,CAAC,MAAM;IACLP,EAAE,CAACQ,QAAQ,CAACR,EAAE,CAACS,OAAO,EAAEF,KAAK,CAAC;EAChC;AACF;AACA,SAASG,gBAAgBA,CAAA,EAAG;EAC1B,OAAOC,MAAM,CAACR,WAAW,IAAIS,QAAQ,CAACC,eAAe,CAACX,SAAS,IAAIU,QAAQ,CAACE,IAAI,CAACZ,SAAS,IAAI,CAAC;AACjG;AACA,SAASa,gBAAgBA,CAACR,KAAK,EAAE;EAC/BD,YAAY,CAACK,MAAM,EAAEJ,KAAK,CAAC;EAC3BD,YAAY,CAACM,QAAQ,CAACE,IAAI,EAAEP,KAAK,CAAC;AACpC;AACA,SAASS,aAAaA,CAAChB,EAAE,EAAEiB,QAAQ,EAAE;EACnC,IAAIjB,EAAE,KAAKW,MAAM,EAAE;IACjB,OAAO,CAAC;EACV;EACA,MAAMT,SAAS,GAAGe,QAAQ,GAAGlB,YAAY,CAACkB,QAAQ,CAAC,GAAGP,gBAAgB,CAAC,CAAC;EACxE,OAAOhB,OAAO,CAACM,EAAE,CAAC,CAACC,GAAG,GAAGC,SAAS;AACpC;AACA,MAAML,KAAK,GAAGC,UAAU,CAAC,CAAC;AAC1B,SAASoB,WAAWA,CAAA,EAAG;EACrB,IAAIrB,KAAK,EAAE;IACTkB,gBAAgB,CAACL,gBAAgB,CAAC,CAAC,CAAC;EACtC;AACF;AACA,MAAMS,eAAe,GAAIC,KAAK,IAAKA,KAAK,CAACD,eAAe,CAAC,CAAC;AAC1D,SAASE,cAAcA,CAACD,KAAK,EAAEE,iBAAiB,EAAE;EAChD,IAAI,OAAOF,KAAK,CAACG,UAAU,KAAK,SAAS,IAAIH,KAAK,CAACG,UAAU,EAAE;IAC7DH,KAAK,CAACC,cAAc,CAAC,CAAC;EACxB;EACA,IAAIC,iBAAiB,EAAE;IACrBH,eAAe,CAACC,KAAK,CAAC;EACxB;AACF;AACA,SAASI,QAAQA,CAACC,UAAU,EAAE;EAC5B,MAAMzB,EAAE,GAAGJ,KAAK,CAAC6B,UAAU,CAAC;EAC5B,IAAI,CAACzB,EAAE,EAAE;IACP,OAAO,KAAK;EACd;EACA,MAAM0B,KAAK,GAAGf,MAAM,CAACgB,gBAAgB,CAAC3B,EAAE,CAAC;EACzC,MAAM4B,MAAM,GAAGF,KAAK,CAACG,OAAO,KAAK,MAAM;EACvC,MAAMC,YAAY,GAAG9B,EAAE,CAAC+B,YAAY,KAAK,IAAI,IAAIL,KAAK,CAACM,QAAQ,KAAK,OAAO;EAC3E,OAAOJ,MAAM,IAAIE,YAAY;AAC/B;AACA,MAAM;EAAEG,KAAK,EAAEC,WAAW;EAAEC,MAAM,EAAEC;AAAa,CAAC,GAAGzC,aAAa,CAAC,CAAC;AACpE,SAAS0C,iBAAiBA,CAACrC,EAAE,EAAE;EAC7B,MAAMsC,GAAG,GAAG3B,MAAM,CAACgB,gBAAgB,CAAC3B,EAAE,CAAC;EACvC,OAAOsC,GAAG,CAACC,SAAS,KAAK,MAAM,IAAID,GAAG,CAACE,WAAW,KAAK,MAAM,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAACC,IAAI,CACzGlC,KAAK,IAAK,CAAC+B,GAAG,CAACI,UAAU,IAAI,EAAE,EAAEC,QAAQ,CAACpC,KAAK,CAClD,CAAC;AACH;AACA,SAASqC,kBAAkBA,CAAC5C,EAAE,EAAE;EAC9B,IAAI6C,IAAI,GAAG7C,EAAE,CAAC8C,aAAa;EAC3B,OAAOD,IAAI,EAAE;IACX,IAAIA,IAAI,IAAIA,IAAI,CAACE,OAAO,KAAK,MAAM,IAAIF,IAAI,CAACE,OAAO,KAAK,MAAM,IAAIV,iBAAiB,CAACQ,IAAI,CAAC,EAAE;MACzF,OAAOA,IAAI;IACb;IACAA,IAAI,GAAGA,IAAI,CAACC,aAAa;EAC3B;EACA,OAAO,IAAI;AACb;AACA,SACEF,kBAAkB,EAClB5B,aAAa,EACbN,gBAAgB,EAChBX,YAAY,EACZyB,QAAQ,EACRH,cAAc,EACdH,WAAW,EACXH,gBAAgB,EAChBT,YAAY,EACZa,eAAe,EACfiB,YAAY,EACZF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}