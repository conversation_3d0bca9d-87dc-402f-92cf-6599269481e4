{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Pagination from \"./Pagination.mjs\";\nconst Pagination = withInstall(_Pagination);\nvar stdin_default = Pagination;\nimport { paginationProps } from \"./Pagination.mjs\";\nexport { Pagination, stdin_default as default, paginationProps };", "map": {"version": 3, "names": ["withInstall", "_Pagination", "Pagination", "stdin_default", "paginationProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/pagination/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Pagination from \"./Pagination.mjs\";\nconst Pagination = withInstall(_Pagination);\nvar stdin_default = Pagination;\nimport { paginationProps } from \"./Pagination.mjs\";\nexport {\n  Pagination,\n  stdin_default as default,\n  paginationProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVC,aAAa,IAAIE,OAAO,EACxBD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}