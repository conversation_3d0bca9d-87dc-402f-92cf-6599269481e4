{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _DatePicker from \"./DatePicker.mjs\";\nconst DatePicker = withInstall(_DatePicker);\nvar stdin_default = DatePicker;\nimport { datePickerProps } from \"./DatePicker.mjs\";\nexport { DatePicker, datePickerProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_DatePicker", "DatePicker", "stdin_default", "datePickerProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/date-picker/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _DatePicker from \"./DatePicker.mjs\";\nconst DatePicker = withInstall(_DatePicker);\nvar stdin_default = DatePicker;\nimport { datePickerProps } from \"./DatePicker.mjs\";\nexport {\n  DatePicker,\n  datePickerProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,MAAMC,UAAU,GAAGF,WAAW,CAACC,WAAW,CAAC;AAC3C,IAAIE,aAAa,GAAGD,UAAU;AAC9B,SAASE,eAAe,QAAQ,kBAAkB;AAClD,SACEF,UAAU,EACVE,eAAe,EACfD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}