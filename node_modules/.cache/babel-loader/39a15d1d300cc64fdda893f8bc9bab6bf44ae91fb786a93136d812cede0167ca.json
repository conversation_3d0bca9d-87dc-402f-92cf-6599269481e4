{"ast": null, "code": "import { defineComponent, mergeProps as _mergeProps, createVNode as _createVNode, Fragment as _Fragment } from \"vue\";\nimport { truthProp, createNamespace, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useScopeId } from \"../composables/use-scope-id.mjs\";\nconst [name, bem] = createNamespace(\"cell-group\");\nconst cellGroupProps = {\n  title: String,\n  inset: Boolean,\n  border: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: cellGroupProps,\n  setup(props, {\n    slots,\n    attrs\n  }) {\n    const renderGroup = () => {\n      var _a;\n      return _createVNode(\"div\", _mergeProps({\n        \"class\": [bem({\n          inset: props.inset\n        }), {\n          [BORDER_TOP_BOTTOM]: props.border && !props.inset\n        }]\n      }, attrs, useScopeId()), [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n    const renderTitle = () => _createVNode(\"div\", {\n      \"class\": bem(\"title\", {\n        inset: props.inset\n      })\n    }, [slots.title ? slots.title() : props.title]);\n    return () => {\n      if (props.title || slots.title) {\n        return _createVNode(_Fragment, null, [renderTitle(), renderGroup()]);\n      }\n      return renderGroup();\n    };\n  }\n});\nexport { cellGroupProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "Fragment", "_Fragment", "truthProp", "createNamespace", "BORDER_TOP_BOTTOM", "useScopeId", "name", "bem", "cellGroupProps", "title", "String", "inset", "Boolean", "border", "stdin_default", "inheritAttrs", "props", "setup", "slots", "attrs", "renderGroup", "_a", "default", "call", "renderTitle"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/cell-group/CellGroup.mjs"], "sourcesContent": ["import { defineComponent, mergeProps as _mergeProps, createVNode as _createVNode, Fragment as _Fragment } from \"vue\";\nimport { truthProp, createNamespace, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useScopeId } from \"../composables/use-scope-id.mjs\";\nconst [name, bem] = createNamespace(\"cell-group\");\nconst cellGroupProps = {\n  title: String,\n  inset: Boolean,\n  border: truthProp\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: cellGroupProps,\n  setup(props, {\n    slots,\n    attrs\n  }) {\n    const renderGroup = () => {\n      var _a;\n      return _createVNode(\"div\", _mergeProps({\n        \"class\": [bem({\n          inset: props.inset\n        }), {\n          [BORDER_TOP_BOTTOM]: props.border && !props.inset\n        }]\n      }, attrs, useScopeId()), [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n    const renderTitle = () => _createVNode(\"div\", {\n      \"class\": bem(\"title\", {\n        inset: props.inset\n      })\n    }, [slots.title ? slots.title() : props.title]);\n    return () => {\n      if (props.title || slots.title) {\n        return _createVNode(_Fragment, null, [renderTitle(), renderGroup()]);\n      }\n      return renderGroup();\n    };\n  }\n});\nexport {\n  cellGroupProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,EAAEC,QAAQ,IAAIC,SAAS,QAAQ,KAAK;AACpH,SAASC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,oBAAoB;AAClF,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGJ,eAAe,CAAC,YAAY,CAAC;AACjD,MAAMK,cAAc,GAAG;EACrBC,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAEC,OAAO;EACdC,MAAM,EAAEX;AACV,CAAC;AACD,IAAIY,aAAa,GAAGnB,eAAe,CAAC;EAClCW,IAAI;EACJS,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAER,cAAc;EACrBS,KAAKA,CAACD,KAAK,EAAE;IACXE,KAAK;IACLC;EACF,CAAC,EAAE;IACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIC,EAAE;MACN,OAAOtB,YAAY,CAAC,KAAK,EAAEF,WAAW,CAAC;QACrC,OAAO,EAAE,CAACU,GAAG,CAAC;UACZI,KAAK,EAAEK,KAAK,CAACL;QACf,CAAC,CAAC,EAAE;UACF,CAACP,iBAAiB,GAAGY,KAAK,CAACH,MAAM,IAAI,CAACG,KAAK,CAACL;QAC9C,CAAC;MACH,CAAC,EAAEQ,KAAK,EAAEd,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAACgB,EAAE,GAAGH,KAAK,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACL,KAAK,CAAC,CAAC,CAAC;IACpF,CAAC;IACD,MAAMM,WAAW,GAAGA,CAAA,KAAMzB,YAAY,CAAC,KAAK,EAAE;MAC5C,OAAO,EAAEQ,GAAG,CAAC,OAAO,EAAE;QACpBI,KAAK,EAAEK,KAAK,CAACL;MACf,CAAC;IACH,CAAC,EAAE,CAACO,KAAK,CAACT,KAAK,GAAGS,KAAK,CAACT,KAAK,CAAC,CAAC,GAAGO,KAAK,CAACP,KAAK,CAAC,CAAC;IAC/C,OAAO,MAAM;MACX,IAAIO,KAAK,CAACP,KAAK,IAAIS,KAAK,CAACT,KAAK,EAAE;QAC9B,OAAOV,YAAY,CAACE,SAAS,EAAE,IAAI,EAAE,CAACuB,WAAW,CAAC,CAAC,EAAEJ,WAAW,CAAC,CAAC,CAAC,CAAC;MACtE;MACA,OAAOA,WAAW,CAAC,CAAC;IACtB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEZ,cAAc,EACdM,aAAa,IAAIQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}