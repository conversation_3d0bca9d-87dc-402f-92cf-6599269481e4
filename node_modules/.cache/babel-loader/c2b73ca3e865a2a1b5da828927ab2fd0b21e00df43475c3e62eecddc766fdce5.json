{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Area from \"./Area.mjs\";\nconst Area = withInstall(_Area);\nvar stdin_default = Area;\nimport { areaProps } from \"./Area.mjs\";\nexport { Area, areaProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Area", "Area", "stdin_default", "areaProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/area/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Area from \"./Area.mjs\";\nconst Area = withInstall(_Area);\nvar stdin_default = Area;\nimport { areaProps } from \"./Area.mjs\";\nexport {\n  Area,\n  areaProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SAASE,SAAS,QAAQ,YAAY;AACtC,SACEF,IAAI,EACJE,SAAS,EACTD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}