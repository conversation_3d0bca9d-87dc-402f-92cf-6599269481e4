{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref } from 'vue';\nimport { useRouter } from 'vue-router';\nexport default {\n  __name: 'Login',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n\n    // 表单数据\n    const loginForm = ref({\n      username: '',\n      password: ''\n    });\n\n    // 状态管理\n    const loading = ref(false);\n    const errorMessage = ref('');\n\n    // 登录处理\n    const handleLogin = async () => {\n      loading.value = true;\n      errorMessage.value = '';\n\n      // 模拟登录延迟\n      setTimeout(() => {\n        const {\n          username,\n          password\n        } = loginForm.value;\n\n        // 验证用户名和密码\n        if (username === 'admin' && password === '123456') {\n          // 登录成功\n          localStorage.setItem('isLoggedIn', 'true');\n          localStorage.setItem('username', username);\n\n          // 跳转到主页\n          router.push('/home');\n        } else {\n          // 登录失败\n          errorMessage.value = '用户名或密码错误，请重新输入';\n\n          // 清空密码\n          loginForm.value.password = '';\n        }\n        loading.value = false;\n      }, 1000);\n    };\n    const __returned__ = {\n      router,\n      loginForm,\n      loading,\n      errorMessage,\n      handleLogin,\n      ref,\n      get useRouter() {\n        return useRouter;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "useRouter", "router", "loginForm", "username", "password", "loading", "errorMessage", "handleLogin", "value", "setTimeout", "localStorage", "setItem", "push"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/src/views/Login.vue"], "sourcesContent": ["<!-- eslint-disable vue/multi-word-component-names -->\n<template>\n  <div class=\"login-container\">\n    <!-- 背景装饰 -->\n    <div class=\"login-background\">\n      <div class=\"bg-shape shape-1\"></div>\n      <div class=\"bg-shape shape-2\"></div>\n      <div class=\"bg-shape shape-3\"></div>\n    </div>\n    \n    <!-- 登录卡片 -->\n    <div class=\"login-card\">\n      <!-- 系统标题 -->\n      <div class=\"login-header\">\n        <div class=\"logo-section\">\n          <span class=\"logo-icon\">🏥</span>\n          <h1 class=\"system-title\">脊柱侧弯筛查系统</h1>\n        </div>\n        <p class=\"system-subtitle\">专业的脊柱健康检测与管理平台</p>\n      </div>\n      \n      <!-- 登录表单 -->\n      <div class=\"login-form\">\n        <h2 class=\"form-title\">系统登录</h2>\n        <p class=\"form-subtitle\">请输入您的账号信息</p>\n        \n        <van-form @submit=\"handleLogin\">\n          <van-field\n            v-model=\"loginForm.username\"\n            name=\"username\"\n            label=\"用户名\"\n            placeholder=\"请输入用户名\"\n            :rules=\"[{ required: true, message: '请输入用户名' }]\"\n            class=\"login-field\"\n          >\n            <template #left-icon>\n              <span class=\"field-icon\">👤</span>\n            </template>\n          </van-field>\n          \n          <van-field\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            name=\"password\"\n            label=\"密码\"\n            placeholder=\"请输入密码\"\n            :rules=\"[{ required: true, message: '请输入密码' }]\"\n            class=\"login-field\"\n          >\n            <template #left-icon>\n              <span class=\"field-icon\">🔒</span>\n            </template>\n          </van-field>\n          \n          <!-- 错误提示 -->\n          <div v-if=\"errorMessage\" class=\"error-message\">\n            <span class=\"error-icon\">⚠️</span>\n            {{ errorMessage }}\n          </div>\n          \n          <!-- 登录按钮 -->\n          <van-button \n            round \n            block \n            type=\"primary\" \n            native-type=\"submit\"\n            class=\"login-button\"\n            :loading=\"loading\"\n            loading-text=\"登录中...\"\n          >\n            <span class=\"btn-text\">立即登录</span>\n          </van-button>\n        </van-form>\n        \n        <!-- 提示信息 -->\n        <div class=\"login-tips\">\n          <div class=\"tip-item\">\n            <span class=\"tip-icon\">💡</span>\n            <span class=\"tip-text\">测试账号：admin</span>\n          </div>\n          <div class=\"tip-item\">\n            <span class=\"tip-icon\">🔑</span>\n            <span class=\"tip-text\">测试密码：123456</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useRouter } from 'vue-router'\n\nconst router = useRouter()\n\n// 表单数据\nconst loginForm = ref({\n  username: '',\n  password: ''\n})\n\n// 状态管理\nconst loading = ref(false)\nconst errorMessage = ref('')\n\n// 登录处理\nconst handleLogin = async () => {\n  loading.value = true\n  errorMessage.value = ''\n  \n  // 模拟登录延迟\n  setTimeout(() => {\n    const { username, password } = loginForm.value\n    \n    // 验证用户名和密码\n    if (username === 'admin' && password === '123456') {\n      // 登录成功\n      localStorage.setItem('isLoggedIn', 'true')\n      localStorage.setItem('username', username)\n      \n      // 跳转到主页\n      router.push('/home')\n    } else {\n      // 登录失败\n      errorMessage.value = '用户名或密码错误，请重新输入'\n      \n      // 清空密码\n      loginForm.value.password = ''\n    }\n    \n    loading.value = false\n  }, 1000)\n}\n</script>\n\n<style scoped>\n/* 登录容器 */\n.login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 背景装饰 */\n.login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n}\n\n.bg-shape {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.1);\n  animation: float 6s ease-in-out infinite;\n}\n\n.shape-1 {\n  width: 200px;\n  height: 200px;\n  top: 10%;\n  left: 10%;\n  animation-delay: 0s;\n}\n\n.shape-2 {\n  width: 150px;\n  height: 150px;\n  top: 60%;\n  right: 10%;\n  animation-delay: 2s;\n}\n\n.shape-3 {\n  width: 100px;\n  height: 100px;\n  bottom: 20%;\n  left: 20%;\n  animation-delay: 4s;\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n/* 登录卡片 */\n.login-card {\n  background: white;\n  border-radius: 20px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 40px;\n  width: 100%;\n  max-width: 450px;\n  position: relative;\n  animation: slideInUp 0.8s ease-out;\n}\n\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 系统标题区域 */\n.login-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.logo-section {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  margin-bottom: 8px;\n}\n\n.logo-icon {\n  font-size: 36px;\n}\n\n.system-title {\n  margin: 0;\n  font-size: 24px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.system-subtitle {\n  margin: 0;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 表单区域 */\n.login-form {\n  width: 100%;\n}\n\n.form-title {\n  margin: 0 0 8px 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #2c3e50;\n  text-align: center;\n}\n\n.form-subtitle {\n  margin: 0 0 30px 0;\n  font-size: 14px;\n  color: #666;\n  text-align: center;\n}\n\n/* 输入框样式 */\n.login-field {\n  margin-bottom: 20px;\n  --van-field-border-color: #e8e8e8;\n  --van-field-focus-border-color: #1890ff;\n}\n\n.field-icon {\n  font-size: 16px;\n  margin-right: 8px;\n}\n\n/* 错误提示 */\n.error-message {\n  background: #fff2f0;\n  border: 1px solid #ffccc7;\n  border-radius: 8px;\n  padding: 12px 16px;\n  margin-bottom: 20px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #ff4d4f;\n  font-size: 14px;\n  animation: shake 0.5s ease-in-out;\n}\n\n@keyframes shake {\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-5px); }\n  75% { transform: translateX(5px); }\n}\n\n.error-icon {\n  font-size: 16px;\n}\n\n/* 登录按钮 */\n.login-button {\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n  border: none;\n  height: 50px;\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 30px;\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n  transition: all 0.3s ease;\n}\n\n.login-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);\n}\n\n.btn-text {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n/* 提示信息 */\n.login-tips {\n  background: #f8f9ff;\n  border-radius: 12px;\n  padding: 20px;\n  border: 1px solid #e8e8ff;\n}\n\n.tip-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n.tip-item:last-child {\n  margin-bottom: 0;\n}\n\n.tip-icon {\n  font-size: 16px;\n}\n\n/* 响应式设计 */\n@media (max-width: 480px) {\n  .login-card {\n    padding: 30px 20px;\n    margin: 10px;\n    border-radius: 16px;\n  }\n  \n  .system-title {\n    font-size: 20px;\n  }\n  \n  .logo-icon {\n    font-size: 28px;\n  }\n  \n  .form-title {\n    font-size: 18px;\n  }\n}\n</style>\n"], "mappings": ";AA2FA,SAASA,GAAG,QAAQ,KAAI;AACxB,SAASC,SAAS,QAAQ,YAAW;;;;;;;IAErC,MAAMC,MAAM,GAAGD,SAAS,CAAC;;IAEzB;IACA,MAAME,SAAS,GAAGH,GAAG,CAAC;MACpBI,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,OAAO,GAAGN,GAAG,CAAC,KAAK;IACzB,MAAMO,YAAY,GAAGP,GAAG,CAAC,EAAE;;IAE3B;IACA,MAAMQ,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9BF,OAAO,CAACG,KAAK,GAAG,IAAG;MACnBF,YAAY,CAACE,KAAK,GAAG,EAAC;;MAEtB;MACAC,UAAU,CAAC,MAAM;QACf,MAAM;UAAEN,QAAQ;UAAEC;QAAS,CAAC,GAAGF,SAAS,CAACM,KAAI;;QAE7C;QACA,IAAIL,QAAQ,KAAK,OAAO,IAAIC,QAAQ,KAAK,QAAQ,EAAE;UACjD;UACAM,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM;UACzCD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAER,QAAQ;;UAEzC;UACAF,MAAM,CAACW,IAAI,CAAC,OAAO;QACrB,CAAC,MAAM;UACL;UACAN,YAAY,CAACE,KAAK,GAAG,gBAAe;;UAEpC;UACAN,SAAS,CAACM,KAAK,CAACJ,QAAQ,GAAG,EAAC;QAC9B;QAEAC,OAAO,CAACG,KAAK,GAAG,KAAI;MACtB,CAAC,EAAE,IAAI;IACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}