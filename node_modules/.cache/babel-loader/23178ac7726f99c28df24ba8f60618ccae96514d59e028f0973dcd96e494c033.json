{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"sidebar\");\nconst SIDEBAR_KEY = Symbol(name);\nconst sidebarProps = {\n  modelValue: makeNumericProp(0)\n};\nvar stdin_default = defineComponent({\n  name,\n  props: sidebarProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      linkChildren\n    } = useChildren(SIDEBAR_KEY);\n    const getActive = () => +props.modelValue;\n    const setActive = value => {\n      if (value !== getActive()) {\n        emit(\"update:modelValue\", value);\n        emit(\"change\", value);\n      }\n    };\n    linkChildren({\n      getActive,\n      setActive\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"role\": \"tablist\",\n        \"class\": bem()\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { SIDEBAR_KEY, stdin_default as default, sidebarProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "makeNumericProp", "createNamespace", "useChildren", "name", "bem", "SIDEBAR_KEY", "Symbol", "sidebarProps", "modelValue", "stdin_default", "props", "emits", "setup", "emit", "slots", "linkChildren", "getActive", "setActive", "value", "_a", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/sidebar/Sidebar.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"sidebar\");\nconst SIDEBAR_KEY = Symbol(name);\nconst sidebarProps = {\n  modelValue: makeNumericProp(0)\n};\nvar stdin_default = defineComponent({\n  name,\n  props: sidebarProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      linkChildren\n    } = useChildren(SIDEBAR_KEY);\n    const getActive = () => +props.modelValue;\n    const setActive = (value) => {\n      if (value !== getActive()) {\n        emit(\"update:modelValue\", value);\n        emit(\"change\", value);\n      }\n    };\n    linkChildren({\n      getActive,\n      setActive\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"role\": \"tablist\",\n        \"class\": bem()\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  SIDEBAR_KEY,\n  stdin_default as default,\n  sidebarProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AACrE,SAASC,WAAW,QAAQ,WAAW;AACvC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGH,eAAe,CAAC,SAAS,CAAC;AAC9C,MAAMI,WAAW,GAAGC,MAAM,CAACH,IAAI,CAAC;AAChC,MAAMI,YAAY,GAAG;EACnBC,UAAU,EAAER,eAAe,CAAC,CAAC;AAC/B,CAAC;AACD,IAAIS,aAAa,GAAGZ,eAAe,CAAC;EAClCM,IAAI;EACJO,KAAK,EAAEH,YAAY;EACnBI,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAM;MACJC;IACF,CAAC,GAAGb,WAAW,CAACG,WAAW,CAAC;IAC5B,MAAMW,SAAS,GAAGA,CAAA,KAAM,CAACN,KAAK,CAACF,UAAU;IACzC,MAAMS,SAAS,GAAIC,KAAK,IAAK;MAC3B,IAAIA,KAAK,KAAKF,SAAS,CAAC,CAAC,EAAE;QACzBH,IAAI,CAAC,mBAAmB,EAAEK,KAAK,CAAC;QAChCL,IAAI,CAAC,QAAQ,EAAEK,KAAK,CAAC;MACvB;IACF,CAAC;IACDH,YAAY,CAAC;MACXC,SAAS;MACTC;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIE,EAAE;MACN,OAAOpB,YAAY,CAAC,KAAK,EAAE;QACzB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAEK,GAAG,CAAC;MACf,CAAC,EAAE,CAAC,CAACe,EAAE,GAAGL,KAAK,CAACM,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,IAAI,CAACP,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACET,WAAW,EACXI,aAAa,IAAIW,OAAO,EACxBb,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}