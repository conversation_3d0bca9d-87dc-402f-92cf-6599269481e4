{"ast": null, "code": "import _SkeletonParagraph from \"./SkeletonParagraph.mjs\";\nimport { withInstall } from \"../utils/index.mjs\";\nconst SkeletonParagraph = withInstall(_SkeletonParagraph);\nvar stdin_default = SkeletonParagraph;\nimport { skeletonParagraphProps, DEFAULT_ROW_WIDTH } from \"./SkeletonParagraph.mjs\";\nexport { DEFAULT_ROW_WIDTH, SkeletonParagraph, stdin_default as default, skeletonParagraphProps };", "map": {"version": 3, "names": ["_SkeletonParagraph", "withInstall", "SkeletonParagraph", "stdin_default", "skeletonParagraphProps", "DEFAULT_ROW_WIDTH", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/skeleton-paragraph/index.mjs"], "sourcesContent": ["import _SkeletonParagraph from \"./SkeletonParagraph.mjs\";\nimport { withInstall } from \"../utils/index.mjs\";\nconst SkeletonParagraph = withInstall(_SkeletonParagraph);\nvar stdin_default = SkeletonParagraph;\nimport { skeletonParagraphProps, DEFAULT_ROW_WIDTH } from \"./SkeletonParagraph.mjs\";\nexport {\n  DEFAULT_ROW_WIDTH,\n  SkeletonParagraph,\n  stdin_default as default,\n  skeletonParagraphProps\n};\n"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,MAAMC,iBAAiB,GAAGD,WAAW,CAACD,kBAAkB,CAAC;AACzD,IAAIG,aAAa,GAAGD,iBAAiB;AACrC,SAASE,sBAAsB,EAAEC,iBAAiB,QAAQ,yBAAyB;AACnF,SACEA,iBAAiB,EACjBH,iBAAiB,EACjBC,aAAa,IAAIG,OAAO,EACxBF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}