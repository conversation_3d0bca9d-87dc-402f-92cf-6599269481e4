{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, createNamespace, makeRequiredProp, makeStringProp } from \"../utils/index.mjs\";\nimport { Tag } from \"../tag/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Radio } from \"../radio/index.mjs\";\nimport { Checkbox } from \"../checkbox/index.mjs\";\nconst [name, bem] = createNamespace(\"address-item\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    address: makeRequiredProp(Object),\n    disabled: Boolean,\n    switchable: Boolean,\n    singleChoice: Boolean,\n    defaultTagText: String,\n    rightIcon: makeStringProp(\"edit\")\n  },\n  emits: [\"edit\", \"click\", \"select\"],\n  setup(props, {\n    slots,\n    emit\n  }) {\n    const onClick = event => {\n      if (props.switchable) {\n        emit(\"select\");\n      }\n      emit(\"click\", event);\n    };\n    const renderRightIcon = () => _createVNode(Icon, {\n      \"name\": props.rightIcon,\n      \"class\": bem(\"edit\"),\n      \"onClick\": event => {\n        event.stopPropagation();\n        emit(\"edit\");\n        emit(\"click\", event);\n      }\n    }, null);\n    const renderTag = () => {\n      if (slots.tag) {\n        return slots.tag(props.address);\n      }\n      if (props.address.isDefault && props.defaultTagText) {\n        return _createVNode(Tag, {\n          \"type\": \"primary\",\n          \"round\": true,\n          \"class\": bem(\"tag\")\n        }, {\n          default: () => [props.defaultTagText]\n        });\n      }\n    };\n    const renderContent = () => {\n      const {\n        address,\n        disabled,\n        switchable,\n        singleChoice\n      } = props;\n      const Info = [_createVNode(\"div\", {\n        \"class\": bem(\"name\")\n      }, [`${address.name} ${address.tel}`, renderTag()]), _createVNode(\"div\", {\n        \"class\": bem(\"address\")\n      }, [address.address])];\n      if (switchable && !disabled) {\n        if (singleChoice) {\n          return _createVNode(Radio, {\n            \"name\": address.id,\n            \"iconSize\": 18\n          }, {\n            default: () => [Info]\n          });\n        } else {\n          return _createVNode(Checkbox, {\n            \"name\": address.id,\n            \"iconSize\": 18\n          }, {\n            default: () => [Info]\n          });\n        }\n      }\n      return Info;\n    };\n    return () => {\n      var _a;\n      const {\n        disabled\n      } = props;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          disabled\n        }),\n        \"onClick\": onClick\n      }, [_createVNode(Cell, {\n        \"border\": false,\n        \"titleClass\": bem(\"title\")\n      }, {\n        title: renderContent,\n        \"right-icon\": renderRightIcon\n      }), (_a = slots.bottom) == null ? void 0 : _a.call(slots, extend({}, props.address, {\n        disabled\n      }))]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "extend", "createNamespace", "makeRequiredProp", "makeStringProp", "Tag", "Icon", "Cell", "Radio", "Checkbox", "name", "bem", "stdin_default", "props", "address", "Object", "disabled", "Boolean", "switchable", "singleChoice", "defaultTagText", "String", "rightIcon", "emits", "setup", "slots", "emit", "onClick", "event", "renderRightIcon", "stopPropagation", "renderTag", "tag", "isDefault", "default", "renderContent", "Info", "tel", "id", "_a", "title", "bottom", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/address-list/AddressListItem.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, createNamespace, makeRequiredProp, makeStringProp } from \"../utils/index.mjs\";\nimport { Tag } from \"../tag/index.mjs\";\nimport { Icon } from \"../icon/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nimport { Radio } from \"../radio/index.mjs\";\nimport { Checkbox } from \"../checkbox/index.mjs\";\nconst [name, bem] = createNamespace(\"address-item\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    address: makeRequiredProp(Object),\n    disabled: Boolean,\n    switchable: Boolean,\n    singleChoice: Boolean,\n    defaultTagText: String,\n    rightIcon: makeStringProp(\"edit\")\n  },\n  emits: [\"edit\", \"click\", \"select\"],\n  setup(props, {\n    slots,\n    emit\n  }) {\n    const onClick = (event) => {\n      if (props.switchable) {\n        emit(\"select\");\n      }\n      emit(\"click\", event);\n    };\n    const renderRightIcon = () => _createVNode(Icon, {\n      \"name\": props.rightIcon,\n      \"class\": bem(\"edit\"),\n      \"onClick\": (event) => {\n        event.stopPropagation();\n        emit(\"edit\");\n        emit(\"click\", event);\n      }\n    }, null);\n    const renderTag = () => {\n      if (slots.tag) {\n        return slots.tag(props.address);\n      }\n      if (props.address.isDefault && props.defaultTagText) {\n        return _createVNode(Tag, {\n          \"type\": \"primary\",\n          \"round\": true,\n          \"class\": bem(\"tag\")\n        }, {\n          default: () => [props.defaultTagText]\n        });\n      }\n    };\n    const renderContent = () => {\n      const {\n        address,\n        disabled,\n        switchable,\n        singleChoice\n      } = props;\n      const Info = [_createVNode(\"div\", {\n        \"class\": bem(\"name\")\n      }, [`${address.name} ${address.tel}`, renderTag()]), _createVNode(\"div\", {\n        \"class\": bem(\"address\")\n      }, [address.address])];\n      if (switchable && !disabled) {\n        if (singleChoice) {\n          return _createVNode(Radio, {\n            \"name\": address.id,\n            \"iconSize\": 18\n          }, {\n            default: () => [Info]\n          });\n        } else {\n          return _createVNode(Checkbox, {\n            \"name\": address.id,\n            \"iconSize\": 18\n          }, {\n            default: () => [Info]\n          });\n        }\n      }\n      return Info;\n    };\n    return () => {\n      var _a;\n      const {\n        disabled\n      } = props;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          disabled\n        }),\n        \"onClick\": onClick\n      }, [_createVNode(Cell, {\n        \"border\": false,\n        \"titleClass\": bem(\"title\")\n      }, {\n        title: renderContent,\n        \"right-icon\": renderRightIcon\n      }), (_a = slots.bottom) == null ? void 0 : _a.call(slots, extend({}, props.address, {\n        disabled\n      }))]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,MAAM,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,oBAAoB;AAC9F,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGT,eAAe,CAAC,cAAc,CAAC;AACnD,IAAIU,aAAa,GAAGd,eAAe,CAAC;EAClCY,IAAI;EACJG,KAAK,EAAE;IACLC,OAAO,EAAEX,gBAAgB,CAACY,MAAM,CAAC;IACjCC,QAAQ,EAAEC,OAAO;IACjBC,UAAU,EAAED,OAAO;IACnBE,YAAY,EAAEF,OAAO;IACrBG,cAAc,EAAEC,MAAM;IACtBC,SAAS,EAAElB,cAAc,CAAC,MAAM;EAClC,CAAC;EACDmB,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;EAClCC,KAAKA,CAACX,KAAK,EAAE;IACXY,KAAK;IACLC;EACF,CAAC,EAAE;IACD,MAAMC,OAAO,GAAIC,KAAK,IAAK;MACzB,IAAIf,KAAK,CAACK,UAAU,EAAE;QACpBQ,IAAI,CAAC,QAAQ,CAAC;MAChB;MACAA,IAAI,CAAC,OAAO,EAAEE,KAAK,CAAC;IACtB,CAAC;IACD,MAAMC,eAAe,GAAGA,CAAA,KAAM7B,YAAY,CAACM,IAAI,EAAE;MAC/C,MAAM,EAAEO,KAAK,CAACS,SAAS;MACvB,OAAO,EAAEX,GAAG,CAAC,MAAM,CAAC;MACpB,SAAS,EAAGiB,KAAK,IAAK;QACpBA,KAAK,CAACE,eAAe,CAAC,CAAC;QACvBJ,IAAI,CAAC,MAAM,CAAC;QACZA,IAAI,CAAC,OAAO,EAAEE,KAAK,CAAC;MACtB;IACF,CAAC,EAAE,IAAI,CAAC;IACR,MAAMG,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAIN,KAAK,CAACO,GAAG,EAAE;QACb,OAAOP,KAAK,CAACO,GAAG,CAACnB,KAAK,CAACC,OAAO,CAAC;MACjC;MACA,IAAID,KAAK,CAACC,OAAO,CAACmB,SAAS,IAAIpB,KAAK,CAACO,cAAc,EAAE;QACnD,OAAOpB,YAAY,CAACK,GAAG,EAAE;UACvB,MAAM,EAAE,SAAS;UACjB,OAAO,EAAE,IAAI;UACb,OAAO,EAAEM,GAAG,CAAC,KAAK;QACpB,CAAC,EAAE;UACDuB,OAAO,EAAEA,CAAA,KAAM,CAACrB,KAAK,CAACO,cAAc;QACtC,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAMe,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAM;QACJrB,OAAO;QACPE,QAAQ;QACRE,UAAU;QACVC;MACF,CAAC,GAAGN,KAAK;MACT,MAAMuB,IAAI,GAAG,CAACpC,YAAY,CAAC,KAAK,EAAE;QAChC,OAAO,EAAEW,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAAC,GAAGG,OAAO,CAACJ,IAAI,IAAII,OAAO,CAACuB,GAAG,EAAE,EAAEN,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE/B,YAAY,CAAC,KAAK,EAAE;QACvE,OAAO,EAAEW,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,CAACG,OAAO,CAACA,OAAO,CAAC,CAAC,CAAC;MACtB,IAAII,UAAU,IAAI,CAACF,QAAQ,EAAE;QAC3B,IAAIG,YAAY,EAAE;UAChB,OAAOnB,YAAY,CAACQ,KAAK,EAAE;YACzB,MAAM,EAAEM,OAAO,CAACwB,EAAE;YAClB,UAAU,EAAE;UACd,CAAC,EAAE;YACDJ,OAAO,EAAEA,CAAA,KAAM,CAACE,IAAI;UACtB,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,OAAOpC,YAAY,CAACS,QAAQ,EAAE;YAC5B,MAAM,EAAEK,OAAO,CAACwB,EAAE;YAClB,UAAU,EAAE;UACd,CAAC,EAAE;YACDJ,OAAO,EAAEA,CAAA,KAAM,CAACE,IAAI;UACtB,CAAC,CAAC;QACJ;MACF;MACA,OAAOA,IAAI;IACb,CAAC;IACD,OAAO,MAAM;MACX,IAAIG,EAAE;MACN,MAAM;QACJvB;MACF,CAAC,GAAGH,KAAK;MACT,OAAOb,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEW,GAAG,CAAC;UACXK;QACF,CAAC,CAAC;QACF,SAAS,EAAEW;MACb,CAAC,EAAE,CAAC3B,YAAY,CAACO,IAAI,EAAE;QACrB,QAAQ,EAAE,KAAK;QACf,YAAY,EAAEI,GAAG,CAAC,OAAO;MAC3B,CAAC,EAAE;QACD6B,KAAK,EAAEL,aAAa;QACpB,YAAY,EAAEN;MAChB,CAAC,CAAC,EAAE,CAACU,EAAE,GAAGd,KAAK,CAACgB,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,IAAI,CAACjB,KAAK,EAAExB,MAAM,CAAC,CAAC,CAAC,EAAEY,KAAK,CAACC,OAAO,EAAE;QAClFE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEJ,aAAa,IAAIsB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}