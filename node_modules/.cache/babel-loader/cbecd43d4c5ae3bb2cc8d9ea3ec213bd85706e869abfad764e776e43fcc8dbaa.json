{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Card from \"./Card.mjs\";\nconst Card = withInstall(_Card);\nvar stdin_default = Card;\nimport { cardProps } from \"./Card.mjs\";\nexport { Card, cardProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Card", "Card", "stdin_default", "cardProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/card/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Card from \"./Card.mjs\";\nconst Card = withInstall(_Card);\nvar stdin_default = Card;\nimport { cardProps } from \"./Card.mjs\";\nexport {\n  Card,\n  cardProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SAASE,SAAS,QAAQ,YAAY;AACtC,SACEF,IAAI,EACJE,SAAS,EACTD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}