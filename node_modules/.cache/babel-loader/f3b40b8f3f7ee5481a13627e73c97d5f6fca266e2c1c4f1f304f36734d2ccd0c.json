{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _FloatingPanel from \"./FloatingPanel.mjs\";\nconst FloatingPanel = withInstall(_FloatingPanel);\nvar stdin_default = FloatingPanel;\nimport { floatingPanelProps } from \"./FloatingPanel.mjs\";\nexport { FloatingPanel, stdin_default as default, floatingPanelProps };", "map": {"version": 3, "names": ["withInstall", "_FloatingPanel", "FloatingPanel", "stdin_default", "floatingPanelProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/floating-panel/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _FloatingPanel from \"./FloatingPanel.mjs\";\nconst FloatingPanel = withInstall(_FloatingPanel);\nvar stdin_default = FloatingPanel;\nimport { floatingPanelProps } from \"./FloatingPanel.mjs\";\nexport {\n  FloatingPanel,\n  stdin_default as default,\n  floatingPanelProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,MAAMC,aAAa,GAAGF,WAAW,CAACC,cAAc,CAAC;AACjD,IAAIE,aAAa,GAAGD,aAAa;AACjC,SAASE,kBAAkB,QAAQ,qBAAqB;AACxD,SACEF,aAAa,EACbC,aAAa,IAAIE,OAAO,EACxBD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}