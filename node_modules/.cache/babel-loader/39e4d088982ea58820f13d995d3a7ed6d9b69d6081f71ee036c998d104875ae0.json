{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"row\");\nconst ROW_KEY = Symbol(name);\nconst rowProps = {\n  tag: makeStringProp(\"div\"),\n  wrap: truthProp,\n  align: String,\n  gutter: {\n    type: [String, Number, Array],\n    default: 0\n  },\n  justify: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: rowProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      children,\n      linkChildren\n    } = useChildren(ROW_KEY);\n    const groups = computed(() => {\n      const groups2 = [[]];\n      let totalSpan = 0;\n      children.forEach((child, index) => {\n        totalSpan += Number(child.span);\n        if (totalSpan > 24) {\n          groups2.push([index]);\n          totalSpan -= 24;\n        } else {\n          groups2[groups2.length - 1].push(index);\n        }\n      });\n      return groups2;\n    });\n    const spaces = computed(() => {\n      let gutter = 0;\n      if (Array.isArray(props.gutter)) {\n        gutter = Number(props.gutter[0]) || 0;\n      } else {\n        gutter = Number(props.gutter);\n      }\n      const spaces2 = [];\n      if (!gutter) {\n        return spaces2;\n      }\n      groups.value.forEach(group => {\n        const averagePadding = gutter * (group.length - 1) / group.length;\n        group.forEach((item, index) => {\n          if (index === 0) {\n            spaces2.push({\n              right: averagePadding\n            });\n          } else {\n            const left = gutter - spaces2[item - 1].right;\n            const right = averagePadding - left;\n            spaces2.push({\n              left,\n              right\n            });\n          }\n        });\n      });\n      return spaces2;\n    });\n    const verticalSpaces = computed(() => {\n      const {\n        gutter\n      } = props;\n      const spaces2 = [];\n      if (Array.isArray(gutter) && gutter.length > 1) {\n        const bottom = Number(gutter[1]) || 0;\n        if (bottom <= 0) {\n          return spaces2;\n        }\n        groups.value.forEach((group, index) => {\n          if (index === groups.value.length - 1) return;\n          group.forEach(() => {\n            spaces2.push({\n              bottom\n            });\n          });\n        });\n      }\n      return spaces2;\n    });\n    linkChildren({\n      spaces,\n      verticalSpaces\n    });\n    return () => {\n      const {\n        tag,\n        wrap,\n        align,\n        justify\n      } = props;\n      return _createVNode(tag, {\n        \"class\": bem({\n          [`align-${align}`]: align,\n          [`justify-${justify}`]: justify,\n          nowrap: !wrap\n        })\n      }, {\n        default: () => {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      });\n    };\n  }\n});\nexport { ROW_KEY, stdin_default as default, rowProps };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "truthProp", "makeStringProp", "createNamespace", "useChildren", "name", "bem", "ROW_KEY", "Symbol", "rowProps", "tag", "wrap", "align", "String", "gutter", "type", "Number", "Array", "default", "justify", "stdin_default", "props", "setup", "slots", "children", "linkChildren", "groups", "groups2", "totalSpan", "for<PERSON>ach", "child", "index", "span", "push", "length", "spaces", "isArray", "spaces2", "value", "group", "averagePadding", "item", "right", "left", "verticalSpaces", "bottom", "nowrap", "_a", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/row/Row.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nconst [name, bem] = createNamespace(\"row\");\nconst ROW_KEY = Symbol(name);\nconst rowProps = {\n  tag: makeStringProp(\"div\"),\n  wrap: truthProp,\n  align: String,\n  gutter: {\n    type: [String, Number, Array],\n    default: 0\n  },\n  justify: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: rowProps,\n  setup(props, {\n    slots\n  }) {\n    const {\n      children,\n      linkChildren\n    } = useChildren(ROW_KEY);\n    const groups = computed(() => {\n      const groups2 = [[]];\n      let totalSpan = 0;\n      children.forEach((child, index) => {\n        totalSpan += Number(child.span);\n        if (totalSpan > 24) {\n          groups2.push([index]);\n          totalSpan -= 24;\n        } else {\n          groups2[groups2.length - 1].push(index);\n        }\n      });\n      return groups2;\n    });\n    const spaces = computed(() => {\n      let gutter = 0;\n      if (Array.isArray(props.gutter)) {\n        gutter = Number(props.gutter[0]) || 0;\n      } else {\n        gutter = Number(props.gutter);\n      }\n      const spaces2 = [];\n      if (!gutter) {\n        return spaces2;\n      }\n      groups.value.forEach((group) => {\n        const averagePadding = gutter * (group.length - 1) / group.length;\n        group.forEach((item, index) => {\n          if (index === 0) {\n            spaces2.push({\n              right: averagePadding\n            });\n          } else {\n            const left = gutter - spaces2[item - 1].right;\n            const right = averagePadding - left;\n            spaces2.push({\n              left,\n              right\n            });\n          }\n        });\n      });\n      return spaces2;\n    });\n    const verticalSpaces = computed(() => {\n      const {\n        gutter\n      } = props;\n      const spaces2 = [];\n      if (Array.isArray(gutter) && gutter.length > 1) {\n        const bottom = Number(gutter[1]) || 0;\n        if (bottom <= 0) {\n          return spaces2;\n        }\n        groups.value.forEach((group, index) => {\n          if (index === groups.value.length - 1) return;\n          group.forEach(() => {\n            spaces2.push({\n              bottom\n            });\n          });\n        });\n      }\n      return spaces2;\n    });\n    linkChildren({\n      spaces,\n      verticalSpaces\n    });\n    return () => {\n      const {\n        tag,\n        wrap,\n        align,\n        justify\n      } = props;\n      return _createVNode(tag, {\n        \"class\": bem({\n          [`align-${align}`]: align,\n          [`justify-${justify}`]: justify,\n          nowrap: !wrap\n        })\n      }, {\n        default: () => {\n          var _a;\n          return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n        }\n      });\n    };\n  }\n});\nexport {\n  ROW_KEY,\n  stdin_default as default,\n  rowProps\n};\n"], "mappings": ";;;AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAC/E,SAASC,WAAW,QAAQ,WAAW;AACvC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGH,eAAe,CAAC,KAAK,CAAC;AAC1C,MAAMI,OAAO,GAAGC,MAAM,CAACH,IAAI,CAAC;AAC5B,MAAMI,QAAQ,GAAG;EACfC,GAAG,EAAER,cAAc,CAAC,KAAK,CAAC;EAC1BS,IAAI,EAAEV,SAAS;EACfW,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAE;IACNC,IAAI,EAAE,CAACF,MAAM,EAAEG,MAAM,EAAEC,KAAK,CAAC;IAC7BC,OAAO,EAAE;EACX,CAAC;EACDC,OAAO,EAAEN;AACX,CAAC;AACD,IAAIO,aAAa,GAAGtB,eAAe,CAAC;EAClCO,IAAI;EACJgB,KAAK,EAAEZ,QAAQ;EACfa,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAM;MACJC,QAAQ;MACRC;IACF,CAAC,GAAGrB,WAAW,CAACG,OAAO,CAAC;IACxB,MAAMmB,MAAM,GAAG7B,QAAQ,CAAC,MAAM;MAC5B,MAAM8B,OAAO,GAAG,CAAC,EAAE,CAAC;MACpB,IAAIC,SAAS,GAAG,CAAC;MACjBJ,QAAQ,CAACK,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QACjCH,SAAS,IAAIZ,MAAM,CAACc,KAAK,CAACE,IAAI,CAAC;QAC/B,IAAIJ,SAAS,GAAG,EAAE,EAAE;UAClBD,OAAO,CAACM,IAAI,CAAC,CAACF,KAAK,CAAC,CAAC;UACrBH,SAAS,IAAI,EAAE;QACjB,CAAC,MAAM;UACLD,OAAO,CAACA,OAAO,CAACO,MAAM,GAAG,CAAC,CAAC,CAACD,IAAI,CAACF,KAAK,CAAC;QACzC;MACF,CAAC,CAAC;MACF,OAAOJ,OAAO;IAChB,CAAC,CAAC;IACF,MAAMQ,MAAM,GAAGtC,QAAQ,CAAC,MAAM;MAC5B,IAAIiB,MAAM,GAAG,CAAC;MACd,IAAIG,KAAK,CAACmB,OAAO,CAACf,KAAK,CAACP,MAAM,CAAC,EAAE;QAC/BA,MAAM,GAAGE,MAAM,CAACK,KAAK,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;MACvC,CAAC,MAAM;QACLA,MAAM,GAAGE,MAAM,CAACK,KAAK,CAACP,MAAM,CAAC;MAC/B;MACA,MAAMuB,OAAO,GAAG,EAAE;MAClB,IAAI,CAACvB,MAAM,EAAE;QACX,OAAOuB,OAAO;MAChB;MACAX,MAAM,CAACY,KAAK,CAACT,OAAO,CAAEU,KAAK,IAAK;QAC9B,MAAMC,cAAc,GAAG1B,MAAM,IAAIyB,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC,GAAGK,KAAK,CAACL,MAAM;QACjEK,KAAK,CAACV,OAAO,CAAC,CAACY,IAAI,EAAEV,KAAK,KAAK;UAC7B,IAAIA,KAAK,KAAK,CAAC,EAAE;YACfM,OAAO,CAACJ,IAAI,CAAC;cACXS,KAAK,EAAEF;YACT,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAMG,IAAI,GAAG7B,MAAM,GAAGuB,OAAO,CAACI,IAAI,GAAG,CAAC,CAAC,CAACC,KAAK;YAC7C,MAAMA,KAAK,GAAGF,cAAc,GAAGG,IAAI;YACnCN,OAAO,CAACJ,IAAI,CAAC;cACXU,IAAI;cACJD;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOL,OAAO;IAChB,CAAC,CAAC;IACF,MAAMO,cAAc,GAAG/C,QAAQ,CAAC,MAAM;MACpC,MAAM;QACJiB;MACF,CAAC,GAAGO,KAAK;MACT,MAAMgB,OAAO,GAAG,EAAE;MAClB,IAAIpB,KAAK,CAACmB,OAAO,CAACtB,MAAM,CAAC,IAAIA,MAAM,CAACoB,MAAM,GAAG,CAAC,EAAE;QAC9C,MAAMW,MAAM,GAAG7B,MAAM,CAACF,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACrC,IAAI+B,MAAM,IAAI,CAAC,EAAE;UACf,OAAOR,OAAO;QAChB;QACAX,MAAM,CAACY,KAAK,CAACT,OAAO,CAAC,CAACU,KAAK,EAAER,KAAK,KAAK;UACrC,IAAIA,KAAK,KAAKL,MAAM,CAACY,KAAK,CAACJ,MAAM,GAAG,CAAC,EAAE;UACvCK,KAAK,CAACV,OAAO,CAAC,MAAM;YAClBQ,OAAO,CAACJ,IAAI,CAAC;cACXY;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACA,OAAOR,OAAO;IAChB,CAAC,CAAC;IACFZ,YAAY,CAAC;MACXU,MAAM;MACNS;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,MAAM;QACJlC,GAAG;QACHC,IAAI;QACJC,KAAK;QACLO;MACF,CAAC,GAAGE,KAAK;MACT,OAAOrB,YAAY,CAACU,GAAG,EAAE;QACvB,OAAO,EAAEJ,GAAG,CAAC;UACX,CAAC,SAASM,KAAK,EAAE,GAAGA,KAAK;UACzB,CAAC,WAAWO,OAAO,EAAE,GAAGA,OAAO;UAC/B2B,MAAM,EAAE,CAACnC;QACX,CAAC;MACH,CAAC,EAAE;QACDO,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI6B,EAAE;UACN,OAAO,CAAC,CAACA,EAAE,GAAGxB,KAAK,CAACL,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6B,EAAE,CAACC,IAAI,CAACzB,KAAK,CAAC,CAAC;QACjE;MACF,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEhB,OAAO,EACPa,aAAa,IAAIF,OAAO,EACxBT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}