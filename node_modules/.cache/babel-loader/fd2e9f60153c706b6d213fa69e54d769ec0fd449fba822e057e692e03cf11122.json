{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, truthProp, makeArrayProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem, t] = createNamespace(\"coupon-cell\");\nconst couponCellProps = {\n  title: String,\n  border: truthProp,\n  editable: truthProp,\n  coupons: makeArrayProp(),\n  currency: makeStringProp(\"\\xA5\"),\n  chosenCoupon: {\n    type: [Number, Array],\n    default: -1\n  }\n};\nconst getValue = coupon => {\n  const {\n    value,\n    denominations\n  } = coupon;\n  if (isDef(value)) {\n    return value;\n  }\n  if (isDef(denominations)) {\n    return denominations;\n  }\n  return 0;\n};\nfunction formatValue({\n  coupons,\n  chosenCoupon,\n  currency\n}) {\n  let value = 0;\n  let isExist = false;\n  (Array.isArray(chosenCoupon) ? chosenCoupon : [chosenCoupon]).forEach(i => {\n    const coupon = coupons[+i];\n    if (coupon) {\n      isExist = true;\n      value += getValue(coupon);\n    }\n  });\n  if (isExist) {\n    return `-${currency} ${(value / 100).toFixed(2)}`;\n  }\n  return coupons.length === 0 ? t(\"noCoupon\") : t(\"count\", coupons.length);\n}\nvar stdin_default = defineComponent({\n  name,\n  props: couponCellProps,\n  setup(props) {\n    return () => {\n      const selected = Array.isArray(props.chosenCoupon) ? props.chosenCoupon.length : props.coupons[+props.chosenCoupon];\n      return _createVNode(Cell, {\n        \"class\": bem(),\n        \"value\": formatValue(props),\n        \"title\": props.title || t(\"title\"),\n        \"border\": props.border,\n        \"isLink\": props.editable,\n        \"valueClass\": bem(\"value\", {\n          selected\n        })\n      }, null);\n    };\n  }\n});\nexport { couponCellProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "isDef", "truthProp", "makeArrayProp", "makeStringProp", "createNamespace", "Cell", "name", "bem", "t", "couponCellProps", "title", "String", "border", "editable", "coupons", "currency", "chosen<PERSON><PERSON><PERSON><PERSON>", "type", "Number", "Array", "default", "getValue", "coupon", "value", "denominations", "formatValue", "isExist", "isArray", "for<PERSON>ach", "i", "toFixed", "length", "stdin_default", "props", "setup", "selected"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/coupon-cell/CouponCell.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, truthProp, makeArrayProp, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { Cell } from \"../cell/index.mjs\";\nconst [name, bem, t] = createNamespace(\"coupon-cell\");\nconst couponCellProps = {\n  title: String,\n  border: truthProp,\n  editable: truthProp,\n  coupons: makeArrayProp(),\n  currency: makeStringProp(\"\\xA5\"),\n  chosenCoupon: {\n    type: [Number, Array],\n    default: -1\n  }\n};\nconst getValue = (coupon) => {\n  const {\n    value,\n    denominations\n  } = coupon;\n  if (isDef(value)) {\n    return value;\n  }\n  if (isDef(denominations)) {\n    return denominations;\n  }\n  return 0;\n};\nfunction formatValue({\n  coupons,\n  chosenCoupon,\n  currency\n}) {\n  let value = 0;\n  let isExist = false;\n  (Array.isArray(chosenCoupon) ? chosenCoupon : [chosenCoupon]).forEach((i) => {\n    const coupon = coupons[+i];\n    if (coupon) {\n      isExist = true;\n      value += getValue(coupon);\n    }\n  });\n  if (isExist) {\n    return `-${currency} ${(value / 100).toFixed(2)}`;\n  }\n  return coupons.length === 0 ? t(\"noCoupon\") : t(\"count\", coupons.length);\n}\nvar stdin_default = defineComponent({\n  name,\n  props: couponCellProps,\n  setup(props) {\n    return () => {\n      const selected = Array.isArray(props.chosenCoupon) ? props.chosenCoupon.length : props.coupons[+props.chosenCoupon];\n      return _createVNode(Cell, {\n        \"class\": bem(),\n        \"value\": formatValue(props),\n        \"title\": props.title || t(\"title\"),\n        \"border\": props.border,\n        \"isLink\": props.editable,\n        \"valueClass\": bem(\"value\", {\n          selected\n        })\n      }, null);\n    };\n  }\n});\nexport {\n  couponCellProps,\n  stdin_default as default\n};\n"], "mappings": ";;AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,KAAK,EAAEC,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AACrG,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGJ,eAAe,CAAC,aAAa,CAAC;AACrD,MAAMK,eAAe,GAAG;EACtBC,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAEX,SAAS;EACjBY,QAAQ,EAAEZ,SAAS;EACnBa,OAAO,EAAEZ,aAAa,CAAC,CAAC;EACxBa,QAAQ,EAAEZ,cAAc,CAAC,MAAM,CAAC;EAChCa,YAAY,EAAE;IACZC,IAAI,EAAE,CAACC,MAAM,EAAEC,KAAK,CAAC;IACrBC,OAAO,EAAE,CAAC;EACZ;AACF,CAAC;AACD,MAAMC,QAAQ,GAAIC,MAAM,IAAK;EAC3B,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGF,MAAM;EACV,IAAItB,KAAK,CAACuB,KAAK,CAAC,EAAE;IAChB,OAAOA,KAAK;EACd;EACA,IAAIvB,KAAK,CAACwB,aAAa,CAAC,EAAE;IACxB,OAAOA,aAAa;EACtB;EACA,OAAO,CAAC;AACV,CAAC;AACD,SAASC,WAAWA,CAAC;EACnBX,OAAO;EACPE,YAAY;EACZD;AACF,CAAC,EAAE;EACD,IAAIQ,KAAK,GAAG,CAAC;EACb,IAAIG,OAAO,GAAG,KAAK;EACnB,CAACP,KAAK,CAACQ,OAAO,CAACX,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC,EAAEY,OAAO,CAAEC,CAAC,IAAK;IAC3E,MAAMP,MAAM,GAAGR,OAAO,CAAC,CAACe,CAAC,CAAC;IAC1B,IAAIP,MAAM,EAAE;MACVI,OAAO,GAAG,IAAI;MACdH,KAAK,IAAIF,QAAQ,CAACC,MAAM,CAAC;IAC3B;EACF,CAAC,CAAC;EACF,IAAII,OAAO,EAAE;IACX,OAAO,IAAIX,QAAQ,IAAI,CAACQ,KAAK,GAAG,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC,EAAE;EACnD;EACA,OAAOhB,OAAO,CAACiB,MAAM,KAAK,CAAC,GAAGvB,CAAC,CAAC,UAAU,CAAC,GAAGA,CAAC,CAAC,OAAO,EAAEM,OAAO,CAACiB,MAAM,CAAC;AAC1E;AACA,IAAIC,aAAa,GAAGnC,eAAe,CAAC;EAClCS,IAAI;EACJ2B,KAAK,EAAExB,eAAe;EACtByB,KAAKA,CAACD,KAAK,EAAE;IACX,OAAO,MAAM;MACX,MAAME,QAAQ,GAAGhB,KAAK,CAACQ,OAAO,CAACM,KAAK,CAACjB,YAAY,CAAC,GAAGiB,KAAK,CAACjB,YAAY,CAACe,MAAM,GAAGE,KAAK,CAACnB,OAAO,CAAC,CAACmB,KAAK,CAACjB,YAAY,CAAC;MACnH,OAAOjB,YAAY,CAACM,IAAI,EAAE;QACxB,OAAO,EAAEE,GAAG,CAAC,CAAC;QACd,OAAO,EAAEkB,WAAW,CAACQ,KAAK,CAAC;QAC3B,OAAO,EAAEA,KAAK,CAACvB,KAAK,IAAIF,CAAC,CAAC,OAAO,CAAC;QAClC,QAAQ,EAAEyB,KAAK,CAACrB,MAAM;QACtB,QAAQ,EAAEqB,KAAK,CAACpB,QAAQ;QACxB,YAAY,EAAEN,GAAG,CAAC,OAAO,EAAE;UACzB4B;QACF,CAAC;MACH,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE1B,eAAe,EACfuB,aAAa,IAAIZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}