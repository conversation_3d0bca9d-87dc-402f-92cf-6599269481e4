{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, createNamespace, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"collapse\");\nconst COLLAPSE_KEY = Symbol(name);\nconst collapseProps = {\n  border: truthProp,\n  accordion: Boolean,\n  modelValue: {\n    type: [String, Number, Array],\n    default: \"\"\n  }\n};\nfunction validateModelValue(modelValue, accordion) {\n  if (accordion && Array.isArray(modelValue)) {\n    console.error('[Vant] Collapse: \"v-model\" should not be Array in accordion mode');\n    return false;\n  }\n  if (!accordion && !Array.isArray(modelValue)) {\n    console.error('[Vant] Collapse: \"v-model\" should be Array in non-accordion mode');\n    return false;\n  }\n  return true;\n}\nvar stdin_default = defineComponent({\n  name,\n  props: collapseProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      linkChildren,\n      children\n    } = useChildren(COLLAPSE_KEY);\n    const updateName = name2 => {\n      emit(\"change\", name2);\n      emit(\"update:modelValue\", name2);\n    };\n    const toggle = (name2, expanded) => {\n      const {\n        accordion,\n        modelValue\n      } = props;\n      if (accordion) {\n        updateName(name2 === modelValue ? \"\" : name2);\n      } else if (expanded) {\n        updateName(modelValue.concat(name2));\n      } else {\n        updateName(modelValue.filter(activeName => activeName !== name2));\n      }\n    };\n    const toggleAll = (options = {}) => {\n      if (props.accordion) {\n        return;\n      }\n      if (typeof options === \"boolean\") {\n        options = {\n          expanded: options\n        };\n      }\n      const {\n        expanded,\n        skipDisabled\n      } = options;\n      const expandedChildren = children.filter(item => {\n        if (item.disabled && skipDisabled) {\n          return item.expanded.value;\n        }\n        return expanded != null ? expanded : !item.expanded.value;\n      });\n      const names = expandedChildren.map(item => item.itemName.value);\n      updateName(names);\n    };\n    const isExpanded = name2 => {\n      const {\n        accordion,\n        modelValue\n      } = props;\n      if (process.env.NODE_ENV !== \"production\" && !validateModelValue(modelValue, accordion)) {\n        return false;\n      }\n      return accordion ? modelValue === name2 : modelValue.includes(name2);\n    };\n    useExpose({\n      toggleAll\n    });\n    linkChildren({\n      toggle,\n      isExpanded\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": [bem(), {\n          [BORDER_TOP_BOTTOM]: props.border\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { COLLAPSE_KEY, collapseProps, stdin_default as default };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "truthProp", "createNamespace", "BORDER_TOP_BOTTOM", "useChildren", "useExpose", "name", "bem", "COLLAPSE_KEY", "Symbol", "collapseProps", "border", "accordion", "Boolean", "modelValue", "type", "String", "Number", "Array", "default", "validateModelValue", "isArray", "console", "error", "stdin_default", "props", "emits", "setup", "emit", "slots", "linkChildren", "children", "updateName", "name2", "toggle", "expanded", "concat", "filter", "activeName", "toggleAll", "options", "skipDisabled", "expandedChildren", "item", "disabled", "value", "names", "map", "itemName", "isExpanded", "process", "env", "NODE_ENV", "includes", "_a", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/collapse/Collapse.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, createNamespace, BORDER_TOP_BOTTOM } from \"../utils/index.mjs\";\nimport { useChildren } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"collapse\");\nconst COLLAPSE_KEY = Symbol(name);\nconst collapseProps = {\n  border: truthProp,\n  accordion: Boolean,\n  modelValue: {\n    type: [String, Number, Array],\n    default: \"\"\n  }\n};\nfunction validateModelValue(modelValue, accordion) {\n  if (accordion && Array.isArray(modelValue)) {\n    console.error('[Vant] Collapse: \"v-model\" should not be Array in accordion mode');\n    return false;\n  }\n  if (!accordion && !Array.isArray(modelValue)) {\n    console.error('[Vant] Collapse: \"v-model\" should be Array in non-accordion mode');\n    return false;\n  }\n  return true;\n}\nvar stdin_default = defineComponent({\n  name,\n  props: collapseProps,\n  emits: [\"change\", \"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      linkChildren,\n      children\n    } = useChildren(COLLAPSE_KEY);\n    const updateName = (name2) => {\n      emit(\"change\", name2);\n      emit(\"update:modelValue\", name2);\n    };\n    const toggle = (name2, expanded) => {\n      const {\n        accordion,\n        modelValue\n      } = props;\n      if (accordion) {\n        updateName(name2 === modelValue ? \"\" : name2);\n      } else if (expanded) {\n        updateName(modelValue.concat(name2));\n      } else {\n        updateName(modelValue.filter((activeName) => activeName !== name2));\n      }\n    };\n    const toggleAll = (options = {}) => {\n      if (props.accordion) {\n        return;\n      }\n      if (typeof options === \"boolean\") {\n        options = {\n          expanded: options\n        };\n      }\n      const {\n        expanded,\n        skipDisabled\n      } = options;\n      const expandedChildren = children.filter((item) => {\n        if (item.disabled && skipDisabled) {\n          return item.expanded.value;\n        }\n        return expanded != null ? expanded : !item.expanded.value;\n      });\n      const names = expandedChildren.map((item) => item.itemName.value);\n      updateName(names);\n    };\n    const isExpanded = (name2) => {\n      const {\n        accordion,\n        modelValue\n      } = props;\n      if (process.env.NODE_ENV !== \"production\" && !validateModelValue(modelValue, accordion)) {\n        return false;\n      }\n      return accordion ? modelValue === name2 : modelValue.includes(name2);\n    };\n    useExpose({\n      toggleAll\n    });\n    linkChildren({\n      toggle,\n      isExpanded\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": [bem(), {\n          [BORDER_TOP_BOTTOM]: props.border\n        }]\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  COLLAPSE_KEY,\n  collapseProps,\n  stdin_default as default\n};\n"], "mappings": ";;;AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,SAAS,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,oBAAoB;AAClF,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,UAAU,CAAC;AAC/C,MAAMM,YAAY,GAAGC,MAAM,CAACH,IAAI,CAAC;AACjC,MAAMI,aAAa,GAAG;EACpBC,MAAM,EAAEV,SAAS;EACjBW,SAAS,EAAEC,OAAO;EAClBC,UAAU,EAAE;IACVC,IAAI,EAAE,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,CAAC;IAC7BC,OAAO,EAAE;EACX;AACF,CAAC;AACD,SAASC,kBAAkBA,CAACN,UAAU,EAAEF,SAAS,EAAE;EACjD,IAAIA,SAAS,IAAIM,KAAK,CAACG,OAAO,CAACP,UAAU,CAAC,EAAE;IAC1CQ,OAAO,CAACC,KAAK,CAAC,kEAAkE,CAAC;IACjF,OAAO,KAAK;EACd;EACA,IAAI,CAACX,SAAS,IAAI,CAACM,KAAK,CAACG,OAAO,CAACP,UAAU,CAAC,EAAE;IAC5CQ,OAAO,CAACC,KAAK,CAAC,kEAAkE,CAAC;IACjF,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACA,IAAIC,aAAa,GAAG1B,eAAe,CAAC;EAClCQ,IAAI;EACJmB,KAAK,EAAEf,aAAa;EACpBgB,KAAK,EAAE,CAAC,QAAQ,EAAE,mBAAmB,CAAC;EACtCC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAM;MACJC,YAAY;MACZC;IACF,CAAC,GAAG3B,WAAW,CAACI,YAAY,CAAC;IAC7B,MAAMwB,UAAU,GAAIC,KAAK,IAAK;MAC5BL,IAAI,CAAC,QAAQ,EAAEK,KAAK,CAAC;MACrBL,IAAI,CAAC,mBAAmB,EAAEK,KAAK,CAAC;IAClC,CAAC;IACD,MAAMC,MAAM,GAAGA,CAACD,KAAK,EAAEE,QAAQ,KAAK;MAClC,MAAM;QACJvB,SAAS;QACTE;MACF,CAAC,GAAGW,KAAK;MACT,IAAIb,SAAS,EAAE;QACboB,UAAU,CAACC,KAAK,KAAKnB,UAAU,GAAG,EAAE,GAAGmB,KAAK,CAAC;MAC/C,CAAC,MAAM,IAAIE,QAAQ,EAAE;QACnBH,UAAU,CAAClB,UAAU,CAACsB,MAAM,CAACH,KAAK,CAAC,CAAC;MACtC,CAAC,MAAM;QACLD,UAAU,CAAClB,UAAU,CAACuB,MAAM,CAAEC,UAAU,IAAKA,UAAU,KAAKL,KAAK,CAAC,CAAC;MACrE;IACF,CAAC;IACD,MAAMM,SAAS,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;MAClC,IAAIf,KAAK,CAACb,SAAS,EAAE;QACnB;MACF;MACA,IAAI,OAAO4B,OAAO,KAAK,SAAS,EAAE;QAChCA,OAAO,GAAG;UACRL,QAAQ,EAAEK;QACZ,CAAC;MACH;MACA,MAAM;QACJL,QAAQ;QACRM;MACF,CAAC,GAAGD,OAAO;MACX,MAAME,gBAAgB,GAAGX,QAAQ,CAACM,MAAM,CAAEM,IAAI,IAAK;QACjD,IAAIA,IAAI,CAACC,QAAQ,IAAIH,YAAY,EAAE;UACjC,OAAOE,IAAI,CAACR,QAAQ,CAACU,KAAK;QAC5B;QACA,OAAOV,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,CAACQ,IAAI,CAACR,QAAQ,CAACU,KAAK;MAC3D,CAAC,CAAC;MACF,MAAMC,KAAK,GAAGJ,gBAAgB,CAACK,GAAG,CAAEJ,IAAI,IAAKA,IAAI,CAACK,QAAQ,CAACH,KAAK,CAAC;MACjEb,UAAU,CAACc,KAAK,CAAC;IACnB,CAAC;IACD,MAAMG,UAAU,GAAIhB,KAAK,IAAK;MAC5B,MAAM;QACJrB,SAAS;QACTE;MACF,CAAC,GAAGW,KAAK;MACT,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAChC,kBAAkB,CAACN,UAAU,EAAEF,SAAS,CAAC,EAAE;QACvF,OAAO,KAAK;MACd;MACA,OAAOA,SAAS,GAAGE,UAAU,KAAKmB,KAAK,GAAGnB,UAAU,CAACuC,QAAQ,CAACpB,KAAK,CAAC;IACtE,CAAC;IACD5B,SAAS,CAAC;MACRkC;IACF,CAAC,CAAC;IACFT,YAAY,CAAC;MACXI,MAAM;MACNe;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIK,EAAE;MACN,OAAOtD,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAE,CAACO,GAAG,CAAC,CAAC,EAAE;UACf,CAACJ,iBAAiB,GAAGsB,KAAK,CAACd;QAC7B,CAAC;MACH,CAAC,EAAE,CAAC,CAAC2C,EAAE,GAAGzB,KAAK,CAACV,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmC,EAAE,CAACC,IAAI,CAAC1B,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACErB,YAAY,EACZE,aAAa,EACbc,aAAa,IAAIL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}