{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _IndexBar from \"./IndexBar.mjs\";\nconst IndexBar = withInstall(_IndexBar);\nvar stdin_default = IndexBar;\nimport { indexBarProps } from \"./IndexBar.mjs\";\nexport { IndexBar, stdin_default as default, indexBarProps };", "map": {"version": 3, "names": ["withInstall", "_IndexBar", "IndexBar", "stdin_default", "indexBarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/index-bar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _IndexBar from \"./IndexBar.mjs\";\nconst IndexBar = withInstall(_IndexBar);\nvar stdin_default = IndexBar;\nimport { indexBarProps } from \"./IndexBar.mjs\";\nexport {\n  IndexBar,\n  stdin_default as default,\n  indexBarProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,SAAS,CAAC;AACvC,IAAIE,aAAa,GAAGD,QAAQ;AAC5B,SAASE,aAAa,QAAQ,gBAAgB;AAC9C,SACEF,QAAQ,EACRC,aAAa,IAAIE,OAAO,EACxBD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}