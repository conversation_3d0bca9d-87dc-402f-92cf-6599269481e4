{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Signature from \"./Signature.mjs\";\nconst Signature = withInstall(_Signature);\nvar stdin_default = Signature;\nexport { Signature, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Signature", "Signature", "stdin_default", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/signature/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Signature from \"./Signature.mjs\";\nconst Signature = withInstall(_Signature);\nvar stdin_default = Signature;\nexport {\n  Signature,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SACEA,SAAS,EACTC,aAAa,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}