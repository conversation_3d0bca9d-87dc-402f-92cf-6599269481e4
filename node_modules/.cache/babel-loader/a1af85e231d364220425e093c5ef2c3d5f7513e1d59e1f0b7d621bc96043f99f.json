{"ast": null, "code": "import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"tab\");\nconst TabTitle = defineComponent({\n  name,\n  props: {\n    id: String,\n    dot: Boolean,\n    type: String,\n    color: String,\n    title: String,\n    badge: numericProp,\n    shrink: Boolean,\n    isActive: <PERSON><PERSON><PERSON>,\n    disabled: <PERSON><PERSON><PERSON>,\n    controls: String,\n    scrollable: <PERSON>olean,\n    activeColor: String,\n    inactiveColor: String,\n    showZeroBadge: truthProp\n  },\n  setup(props, {\n    slots\n  }) {\n    const style = computed(() => {\n      const style2 = {};\n      const {\n        type,\n        color,\n        disabled,\n        isActive,\n        activeColor,\n        inactiveColor\n      } = props;\n      const isCard = type === \"card\";\n      if (color && isCard) {\n        style2.borderColor = color;\n        if (!disabled) {\n          if (isActive) {\n            style2.backgroundColor = color;\n          } else {\n            style2.color = color;\n          }\n        }\n      }\n      const titleColor = isActive ? activeColor : inactiveColor;\n      if (titleColor) {\n        style2.color = titleColor;\n      }\n      return style2;\n    });\n    const renderText = () => {\n      const Text = _createVNode(\"span\", {\n        \"class\": bem(\"text\", {\n          ellipsis: !props.scrollable\n        })\n      }, [slots.title ? slots.title() : props.title]);\n      if (props.dot || isDef(props.badge) && props.badge !== \"\") {\n        return _createVNode(Badge, {\n          \"dot\": props.dot,\n          \"content\": props.badge,\n          \"showZero\": props.showZeroBadge\n        }, {\n          default: () => [Text]\n        });\n      }\n      return Text;\n    };\n    return () => _createVNode(\"div\", {\n      \"id\": props.id,\n      \"role\": \"tab\",\n      \"class\": [bem([props.type, {\n        grow: props.scrollable && !props.shrink,\n        shrink: props.shrink,\n        active: props.isActive,\n        disabled: props.disabled\n      }])],\n      \"style\": style.value,\n      \"tabindex\": props.disabled ? void 0 : props.isActive ? 0 : -1,\n      \"aria-selected\": props.isActive,\n      \"aria-disabled\": props.disabled || void 0,\n      \"aria-controls\": props.controls,\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderText()]);\n  }\n});\nexport { TabTitle };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "isDef", "truthProp", "numericProp", "createNamespace", "Badge", "name", "bem", "TabTitle", "props", "id", "String", "dot", "Boolean", "type", "color", "title", "badge", "shrink", "isActive", "disabled", "controls", "scrollable", "activeColor", "inactiveColor", "showZeroBadge", "setup", "slots", "style", "style2", "isCard", "borderColor", "backgroundColor", "titleColor", "renderText", "Text", "ellipsis", "default", "grow", "active", "value"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tab/TabTitle.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { isDef, truthProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { Badge } from \"../badge/index.mjs\";\nconst [name, bem] = createNamespace(\"tab\");\nconst TabTitle = defineComponent({\n  name,\n  props: {\n    id: String,\n    dot: Boolean,\n    type: String,\n    color: String,\n    title: String,\n    badge: numericProp,\n    shrink: Boolean,\n    isActive: <PERSON><PERSON><PERSON>,\n    disabled: <PERSON><PERSON><PERSON>,\n    controls: String,\n    scrollable: <PERSON>olean,\n    activeColor: String,\n    inactiveColor: String,\n    showZeroBadge: truthProp\n  },\n  setup(props, {\n    slots\n  }) {\n    const style = computed(() => {\n      const style2 = {};\n      const {\n        type,\n        color,\n        disabled,\n        isActive,\n        activeColor,\n        inactiveColor\n      } = props;\n      const isCard = type === \"card\";\n      if (color && isCard) {\n        style2.borderColor = color;\n        if (!disabled) {\n          if (isActive) {\n            style2.backgroundColor = color;\n          } else {\n            style2.color = color;\n          }\n        }\n      }\n      const titleColor = isActive ? activeColor : inactiveColor;\n      if (titleColor) {\n        style2.color = titleColor;\n      }\n      return style2;\n    });\n    const renderText = () => {\n      const Text = _createVNode(\"span\", {\n        \"class\": bem(\"text\", {\n          ellipsis: !props.scrollable\n        })\n      }, [slots.title ? slots.title() : props.title]);\n      if (props.dot || isDef(props.badge) && props.badge !== \"\") {\n        return _createVNode(Badge, {\n          \"dot\": props.dot,\n          \"content\": props.badge,\n          \"showZero\": props.showZeroBadge\n        }, {\n          default: () => [Text]\n        });\n      }\n      return Text;\n    };\n    return () => _createVNode(\"div\", {\n      \"id\": props.id,\n      \"role\": \"tab\",\n      \"class\": [bem([props.type, {\n        grow: props.scrollable && !props.shrink,\n        shrink: props.shrink,\n        active: props.isActive,\n        disabled: props.disabled\n      }])],\n      \"style\": style.value,\n      \"tabindex\": props.disabled ? void 0 : props.isActive ? 0 : -1,\n      \"aria-selected\": props.isActive,\n      \"aria-disabled\": props.disabled || void 0,\n      \"aria-controls\": props.controls,\n      \"data-allow-mismatch\": \"attribute\"\n    }, [renderText()]);\n  }\n});\nexport {\n  TabTitle\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AACnF,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGH,eAAe,CAAC,KAAK,CAAC;AAC1C,MAAMI,QAAQ,GAAGV,eAAe,CAAC;EAC/BQ,IAAI;EACJG,KAAK,EAAE;IACLC,EAAE,EAAEC,MAAM;IACVC,GAAG,EAAEC,OAAO;IACZC,IAAI,EAAEH,MAAM;IACZI,KAAK,EAAEJ,MAAM;IACbK,KAAK,EAAEL,MAAM;IACbM,KAAK,EAAEd,WAAW;IAClBe,MAAM,EAAEL,OAAO;IACfM,QAAQ,EAAEN,OAAO;IACjBO,QAAQ,EAAEP,OAAO;IACjBQ,QAAQ,EAAEV,MAAM;IAChBW,UAAU,EAAET,OAAO;IACnBU,WAAW,EAAEZ,MAAM;IACnBa,aAAa,EAAEb,MAAM;IACrBc,aAAa,EAAEvB;EACjB,CAAC;EACDwB,KAAKA,CAACjB,KAAK,EAAE;IACXkB;EACF,CAAC,EAAE;IACD,MAAMC,KAAK,GAAG/B,QAAQ,CAAC,MAAM;MAC3B,MAAMgC,MAAM,GAAG,CAAC,CAAC;MACjB,MAAM;QACJf,IAAI;QACJC,KAAK;QACLK,QAAQ;QACRD,QAAQ;QACRI,WAAW;QACXC;MACF,CAAC,GAAGf,KAAK;MACT,MAAMqB,MAAM,GAAGhB,IAAI,KAAK,MAAM;MAC9B,IAAIC,KAAK,IAAIe,MAAM,EAAE;QACnBD,MAAM,CAACE,WAAW,GAAGhB,KAAK;QAC1B,IAAI,CAACK,QAAQ,EAAE;UACb,IAAID,QAAQ,EAAE;YACZU,MAAM,CAACG,eAAe,GAAGjB,KAAK;UAChC,CAAC,MAAM;YACLc,MAAM,CAACd,KAAK,GAAGA,KAAK;UACtB;QACF;MACF;MACA,MAAMkB,UAAU,GAAGd,QAAQ,GAAGI,WAAW,GAAGC,aAAa;MACzD,IAAIS,UAAU,EAAE;QACdJ,MAAM,CAACd,KAAK,GAAGkB,UAAU;MAC3B;MACA,OAAOJ,MAAM;IACf,CAAC,CAAC;IACF,MAAMK,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAMC,IAAI,GAAGnC,YAAY,CAAC,MAAM,EAAE;QAChC,OAAO,EAAEO,GAAG,CAAC,MAAM,EAAE;UACnB6B,QAAQ,EAAE,CAAC3B,KAAK,CAACa;QACnB,CAAC;MACH,CAAC,EAAE,CAACK,KAAK,CAACX,KAAK,GAAGW,KAAK,CAACX,KAAK,CAAC,CAAC,GAAGP,KAAK,CAACO,KAAK,CAAC,CAAC;MAC/C,IAAIP,KAAK,CAACG,GAAG,IAAIX,KAAK,CAACQ,KAAK,CAACQ,KAAK,CAAC,IAAIR,KAAK,CAACQ,KAAK,KAAK,EAAE,EAAE;QACzD,OAAOjB,YAAY,CAACK,KAAK,EAAE;UACzB,KAAK,EAAEI,KAAK,CAACG,GAAG;UAChB,SAAS,EAAEH,KAAK,CAACQ,KAAK;UACtB,UAAU,EAAER,KAAK,CAACgB;QACpB,CAAC,EAAE;UACDY,OAAO,EAAEA,CAAA,KAAM,CAACF,IAAI;QACtB,CAAC,CAAC;MACJ;MACA,OAAOA,IAAI;IACb,CAAC;IACD,OAAO,MAAMnC,YAAY,CAAC,KAAK,EAAE;MAC/B,IAAI,EAAES,KAAK,CAACC,EAAE;MACd,MAAM,EAAE,KAAK;MACb,OAAO,EAAE,CAACH,GAAG,CAAC,CAACE,KAAK,CAACK,IAAI,EAAE;QACzBwB,IAAI,EAAE7B,KAAK,CAACa,UAAU,IAAI,CAACb,KAAK,CAACS,MAAM;QACvCA,MAAM,EAAET,KAAK,CAACS,MAAM;QACpBqB,MAAM,EAAE9B,KAAK,CAACU,QAAQ;QACtBC,QAAQ,EAAEX,KAAK,CAACW;MAClB,CAAC,CAAC,CAAC,CAAC;MACJ,OAAO,EAAEQ,KAAK,CAACY,KAAK;MACpB,UAAU,EAAE/B,KAAK,CAACW,QAAQ,GAAG,KAAK,CAAC,GAAGX,KAAK,CAACU,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;MAC7D,eAAe,EAAEV,KAAK,CAACU,QAAQ;MAC/B,eAAe,EAAEV,KAAK,CAACW,QAAQ,IAAI,KAAK,CAAC;MACzC,eAAe,EAAEX,KAAK,CAACY,QAAQ;MAC/B,qBAAqB,EAAE;IACzB,CAAC,EAAE,CAACa,UAAU,CAAC,CAAC,CAAC,CAAC;EACpB;AACF,CAAC,CAAC;AACF,SACE1B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}