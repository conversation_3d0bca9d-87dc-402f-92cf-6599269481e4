{"ast": null, "code": "import { watch, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { parseFormat } from \"./utils.mjs\";\nimport { useCountDown } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"count-down\");\nconst countDownProps = {\n  time: makeNumericProp(0),\n  format: makeStringProp(\"HH:mm:ss\"),\n  autoStart: truthProp,\n  millisecond: Boolean\n};\nvar stdin_default = defineComponent({\n  name,\n  props: countDownProps,\n  emits: [\"change\", \"finish\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      start,\n      pause,\n      reset,\n      current\n    } = useCountDown({\n      time: +props.time,\n      millisecond: props.millisecond,\n      onChange: current2 => emit(\"change\", current2),\n      onFinish: () => emit(\"finish\")\n    });\n    const timeText = computed(() => parseFormat(props.format, current.value));\n    const resetTime = () => {\n      reset(+props.time);\n      if (props.autoStart) {\n        start();\n      }\n    };\n    watch(() => props.time, resetTime, {\n      immediate: true\n    });\n    useExpose({\n      start,\n      pause,\n      reset: resetTime\n    });\n    return () => _createVNode(\"div\", {\n      \"role\": \"timer\",\n      \"class\": bem()\n    }, [slots.default ? slots.default(current.value) : timeText.value]);\n  }\n});\nexport { countDownProps, stdin_default as default };", "map": {"version": 3, "names": ["watch", "computed", "defineComponent", "createVNode", "_createVNode", "truthProp", "makeStringProp", "makeNumericProp", "createNamespace", "parseFormat", "useCountDown", "useExpose", "name", "bem", "countDownProps", "time", "format", "autoStart", "millisecond", "Boolean", "stdin_default", "props", "emits", "setup", "emit", "slots", "start", "pause", "reset", "current", "onChange", "current2", "onFinish", "timeText", "value", "resetTime", "immediate", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/count-down/CountDown.mjs"], "sourcesContent": ["import { watch, computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { truthProp, makeStringProp, makeNumericProp, createNamespace } from \"../utils/index.mjs\";\nimport { parseFormat } from \"./utils.mjs\";\nimport { useCountDown } from \"@vant/use\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nconst [name, bem] = createNamespace(\"count-down\");\nconst countDownProps = {\n  time: makeNumericProp(0),\n  format: makeStringProp(\"HH:mm:ss\"),\n  autoStart: truthProp,\n  millisecond: Boolean\n};\nvar stdin_default = defineComponent({\n  name,\n  props: countDownProps,\n  emits: [\"change\", \"finish\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const {\n      start,\n      pause,\n      reset,\n      current\n    } = useCountDown({\n      time: +props.time,\n      millisecond: props.millisecond,\n      onChange: (current2) => emit(\"change\", current2),\n      onFinish: () => emit(\"finish\")\n    });\n    const timeText = computed(() => parseFormat(props.format, current.value));\n    const resetTime = () => {\n      reset(+props.time);\n      if (props.autoStart) {\n        start();\n      }\n    };\n    watch(() => props.time, resetTime, {\n      immediate: true\n    });\n    useExpose({\n      start,\n      pause,\n      reset: resetTime\n    });\n    return () => _createVNode(\"div\", {\n      \"role\": \"timer\",\n      \"class\": bem()\n    }, [slots.default ? slots.default(current.value) : timeText.value]);\n  }\n});\nexport {\n  countDownProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACnF,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAChG,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,YAAY,CAAC;AACjD,MAAMM,cAAc,GAAG;EACrBC,IAAI,EAAER,eAAe,CAAC,CAAC,CAAC;EACxBS,MAAM,EAAEV,cAAc,CAAC,UAAU,CAAC;EAClCW,SAAS,EAAEZ,SAAS;EACpBa,WAAW,EAAEC;AACf,CAAC;AACD,IAAIC,aAAa,GAAGlB,eAAe,CAAC;EAClCU,IAAI;EACJS,KAAK,EAAEP,cAAc;EACrBQ,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC3BC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAM;MACJC,KAAK;MACLC,KAAK;MACLC,KAAK;MACLC;IACF,CAAC,GAAGnB,YAAY,CAAC;MACfK,IAAI,EAAE,CAACM,KAAK,CAACN,IAAI;MACjBG,WAAW,EAAEG,KAAK,CAACH,WAAW;MAC9BY,QAAQ,EAAGC,QAAQ,IAAKP,IAAI,CAAC,QAAQ,EAAEO,QAAQ,CAAC;MAChDC,QAAQ,EAAEA,CAAA,KAAMR,IAAI,CAAC,QAAQ;IAC/B,CAAC,CAAC;IACF,MAAMS,QAAQ,GAAGhC,QAAQ,CAAC,MAAMQ,WAAW,CAACY,KAAK,CAACL,MAAM,EAAEa,OAAO,CAACK,KAAK,CAAC,CAAC;IACzE,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACtBP,KAAK,CAAC,CAACP,KAAK,CAACN,IAAI,CAAC;MAClB,IAAIM,KAAK,CAACJ,SAAS,EAAE;QACnBS,KAAK,CAAC,CAAC;MACT;IACF,CAAC;IACD1B,KAAK,CAAC,MAAMqB,KAAK,CAACN,IAAI,EAAEoB,SAAS,EAAE;MACjCC,SAAS,EAAE;IACb,CAAC,CAAC;IACFzB,SAAS,CAAC;MACRe,KAAK;MACLC,KAAK;MACLC,KAAK,EAAEO;IACT,CAAC,CAAC;IACF,OAAO,MAAM/B,YAAY,CAAC,KAAK,EAAE;MAC/B,MAAM,EAAE,OAAO;MACf,OAAO,EAAES,GAAG,CAAC;IACf,CAAC,EAAE,CAACY,KAAK,CAACY,OAAO,GAAGZ,KAAK,CAACY,OAAO,CAACR,OAAO,CAACK,KAAK,CAAC,GAAGD,QAAQ,CAACC,KAAK,CAAC,CAAC;EACrE;AACF,CAAC,CAAC;AACF,SACEpB,cAAc,EACdM,aAAa,IAAIiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}