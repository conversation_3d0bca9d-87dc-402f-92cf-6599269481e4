{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _PasswordInput from \"./PasswordInput.mjs\";\nconst PasswordInput = withInstall(_PasswordInput);\nvar stdin_default = PasswordInput;\nimport { passwordInputProps } from \"./PasswordInput.mjs\";\nexport { PasswordInput, stdin_default as default, passwordInputProps };", "map": {"version": 3, "names": ["withInstall", "_PasswordInput", "PasswordInput", "stdin_default", "passwordInputProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/password-input/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _PasswordInput from \"./PasswordInput.mjs\";\nconst PasswordInput = withInstall(_PasswordInput);\nvar stdin_default = PasswordInput;\nimport { passwordInputProps } from \"./PasswordInput.mjs\";\nexport {\n  PasswordInput,\n  stdin_default as default,\n  passwordInputProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,MAAMC,aAAa,GAAGF,WAAW,CAACC,cAAc,CAAC;AACjD,IAAIE,aAAa,GAAGD,aAAa;AACjC,SAASE,kBAAkB,QAAQ,qBAAqB;AACxD,SACEF,aAAa,EACbC,aAAa,IAAIE,OAAO,EACxBD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}