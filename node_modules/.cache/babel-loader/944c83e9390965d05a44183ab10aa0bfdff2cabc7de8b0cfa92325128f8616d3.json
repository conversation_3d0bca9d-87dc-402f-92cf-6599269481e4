{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { watch, provide, computed, watchEffect, onActivated, onDeactivated, onBeforeUnmount, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, inBrowser, kebabCase, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { setGlobalZIndex } from \"../composables/use-global-z-index.mjs\";\nconst [name, bem] = createNamespace(\"config-provider\");\nconst CONFIG_PROVIDER_KEY = Symbol(name);\nconst configProviderProps = {\n  tag: makeStringProp(\"div\"),\n  theme: makeStringProp(\"light\"),\n  zIndex: Number,\n  themeVars: Object,\n  themeVarsDark: Object,\n  themeVarsLight: Object,\n  themeVarsScope: makeStringProp(\"local\"),\n  iconPrefix: String\n};\nfunction insertDash(str) {\n  return str.replace(/([a-zA-Z])(\\d)/g, \"$1-$2\");\n}\nfunction mapThemeVarsToCSSVars(themeVars) {\n  const cssVars = {};\n  Object.keys(themeVars).forEach(key => {\n    const formattedKey = insertDash(kebabCase(key));\n    cssVars[`--van-${formattedKey}`] = themeVars[key];\n  });\n  return cssVars;\n}\nfunction syncThemeVarsOnRoot(newStyle = {}, oldStyle = {}) {\n  Object.keys(newStyle).forEach(key => {\n    if (newStyle[key] !== oldStyle[key]) {\n      document.documentElement.style.setProperty(key, newStyle[key]);\n    }\n  });\n  Object.keys(oldStyle).forEach(key => {\n    if (!newStyle[key]) {\n      document.documentElement.style.removeProperty(key);\n    }\n  });\n}\nvar stdin_default = defineComponent({\n  name,\n  props: configProviderProps,\n  setup(props, {\n    slots\n  }) {\n    const style = computed(() => mapThemeVarsToCSSVars(extend({}, props.themeVars, props.theme === \"dark\" ? props.themeVarsDark : props.themeVarsLight)));\n    if (inBrowser) {\n      const addTheme = () => {\n        document.documentElement.classList.add(`van-theme-${props.theme}`);\n      };\n      const removeTheme = (theme = props.theme) => {\n        document.documentElement.classList.remove(`van-theme-${theme}`);\n      };\n      watch(() => props.theme, (newVal, oldVal) => {\n        if (oldVal) {\n          removeTheme(oldVal);\n        }\n        addTheme();\n      }, {\n        immediate: true\n      });\n      onActivated(addTheme);\n      onDeactivated(removeTheme);\n      onBeforeUnmount(removeTheme);\n      watch(style, (newStyle, oldStyle) => {\n        if (props.themeVarsScope === \"global\") {\n          syncThemeVarsOnRoot(newStyle, oldStyle);\n        }\n      });\n      watch(() => props.themeVarsScope, (newScope, oldScope) => {\n        if (oldScope === \"global\") {\n          syncThemeVarsOnRoot({}, style.value);\n        }\n        if (newScope === \"global\") {\n          syncThemeVarsOnRoot(style.value, {});\n        }\n      });\n      if (props.themeVarsScope === \"global\") {\n        syncThemeVarsOnRoot(style.value, {});\n      }\n    }\n    provide(CONFIG_PROVIDER_KEY, props);\n    watchEffect(() => {\n      if (props.zIndex !== void 0) {\n        setGlobalZIndex(props.zIndex);\n      }\n    });\n    return () => _createVNode(props.tag, {\n      \"class\": bem(),\n      \"style\": props.themeVarsScope === \"local\" ? style.value : void 0\n    }, {\n      default: () => {\n        var _a;\n        return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n      }\n    });\n  }\n});\nexport { CONFIG_PROVIDER_KEY, configProviderProps, stdin_default as default };", "map": {"version": 3, "names": ["watch", "provide", "computed", "watchEffect", "onActivated", "onDeactivated", "onBeforeUnmount", "defineComponent", "createVNode", "_createVNode", "extend", "inBrowser", "kebabCase", "makeStringProp", "createNamespace", "setGlobalZIndex", "name", "bem", "CONFIG_PROVIDER_KEY", "Symbol", "configProviderProps", "tag", "theme", "zIndex", "Number", "themeVars", "Object", "themeVarsDark", "themeVarsLight", "themeVarsScope", "iconPrefix", "String", "insertDash", "str", "replace", "mapThemeVarsToCSSVars", "cssVars", "keys", "for<PERSON>ach", "key", "formattedKey", "syncThemeVarsOnRoot", "newStyle", "oldStyle", "document", "documentElement", "style", "setProperty", "removeProperty", "stdin_default", "props", "setup", "slots", "addTheme", "classList", "add", "removeTheme", "remove", "newVal", "oldVal", "immediate", "newScope", "oldScope", "value", "default", "_a", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/config-provider/ConfigProvider.mjs"], "sourcesContent": ["import { watch, provide, computed, watchEffect, onActivated, onDeactivated, onBeforeUnmount, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, inBrowser, kebabCase, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nimport { setGlobalZIndex } from \"../composables/use-global-z-index.mjs\";\nconst [name, bem] = createNamespace(\"config-provider\");\nconst CONFIG_PROVIDER_KEY = Symbol(name);\nconst configProviderProps = {\n  tag: makeStringProp(\"div\"),\n  theme: makeStringProp(\"light\"),\n  zIndex: Number,\n  themeVars: Object,\n  themeVarsDark: Object,\n  themeVarsLight: Object,\n  themeVarsScope: makeStringProp(\"local\"),\n  iconPrefix: String\n};\nfunction insertDash(str) {\n  return str.replace(/([a-zA-Z])(\\d)/g, \"$1-$2\");\n}\nfunction mapThemeVarsToCSSVars(themeVars) {\n  const cssVars = {};\n  Object.keys(themeVars).forEach((key) => {\n    const formattedKey = insertDash(kebabCase(key));\n    cssVars[`--van-${formattedKey}`] = themeVars[key];\n  });\n  return cssVars;\n}\nfunction syncThemeVarsOnRoot(newStyle = {}, oldStyle = {}) {\n  Object.keys(newStyle).forEach((key) => {\n    if (newStyle[key] !== oldStyle[key]) {\n      document.documentElement.style.setProperty(key, newStyle[key]);\n    }\n  });\n  Object.keys(oldStyle).forEach((key) => {\n    if (!newStyle[key]) {\n      document.documentElement.style.removeProperty(key);\n    }\n  });\n}\nvar stdin_default = defineComponent({\n  name,\n  props: configProviderProps,\n  setup(props, {\n    slots\n  }) {\n    const style = computed(() => mapThemeVarsToCSSVars(extend({}, props.themeVars, props.theme === \"dark\" ? props.themeVarsDark : props.themeVarsLight)));\n    if (inBrowser) {\n      const addTheme = () => {\n        document.documentElement.classList.add(`van-theme-${props.theme}`);\n      };\n      const removeTheme = (theme = props.theme) => {\n        document.documentElement.classList.remove(`van-theme-${theme}`);\n      };\n      watch(() => props.theme, (newVal, oldVal) => {\n        if (oldVal) {\n          removeTheme(oldVal);\n        }\n        addTheme();\n      }, {\n        immediate: true\n      });\n      onActivated(addTheme);\n      onDeactivated(removeTheme);\n      onBeforeUnmount(removeTheme);\n      watch(style, (newStyle, oldStyle) => {\n        if (props.themeVarsScope === \"global\") {\n          syncThemeVarsOnRoot(newStyle, oldStyle);\n        }\n      });\n      watch(() => props.themeVarsScope, (newScope, oldScope) => {\n        if (oldScope === \"global\") {\n          syncThemeVarsOnRoot({}, style.value);\n        }\n        if (newScope === \"global\") {\n          syncThemeVarsOnRoot(style.value, {});\n        }\n      });\n      if (props.themeVarsScope === \"global\") {\n        syncThemeVarsOnRoot(style.value, {});\n      }\n    }\n    provide(CONFIG_PROVIDER_KEY, props);\n    watchEffect(() => {\n      if (props.zIndex !== void 0) {\n        setGlobalZIndex(props.zIndex);\n      }\n    });\n    return () => _createVNode(props.tag, {\n      \"class\": bem(),\n      \"style\": props.themeVarsScope === \"local\" ? style.value : void 0\n    }, {\n      default: () => {\n        var _a;\n        return [(_a = slots.default) == null ? void 0 : _a.call(slots)];\n      }\n    });\n  }\n});\nexport {\n  CONFIG_PROVIDER_KEY,\n  configProviderProps,\n  stdin_default as default\n};\n"], "mappings": ";;AAAA,SAASA,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACtJ,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAClG,SAASC,eAAe,QAAQ,uCAAuC;AACvE,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGH,eAAe,CAAC,iBAAiB,CAAC;AACtD,MAAMI,mBAAmB,GAAGC,MAAM,CAACH,IAAI,CAAC;AACxC,MAAMI,mBAAmB,GAAG;EAC1BC,GAAG,EAAER,cAAc,CAAC,KAAK,CAAC;EAC1BS,KAAK,EAAET,cAAc,CAAC,OAAO,CAAC;EAC9BU,MAAM,EAAEC,MAAM;EACdC,SAAS,EAAEC,MAAM;EACjBC,aAAa,EAAED,MAAM;EACrBE,cAAc,EAAEF,MAAM;EACtBG,cAAc,EAAEhB,cAAc,CAAC,OAAO,CAAC;EACvCiB,UAAU,EAAEC;AACd,CAAC;AACD,SAASC,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC;AAChD;AACA,SAASC,qBAAqBA,CAACV,SAAS,EAAE;EACxC,MAAMW,OAAO,GAAG,CAAC,CAAC;EAClBV,MAAM,CAACW,IAAI,CAACZ,SAAS,CAAC,CAACa,OAAO,CAAEC,GAAG,IAAK;IACtC,MAAMC,YAAY,GAAGR,UAAU,CAACpB,SAAS,CAAC2B,GAAG,CAAC,CAAC;IAC/CH,OAAO,CAAC,SAASI,YAAY,EAAE,CAAC,GAAGf,SAAS,CAACc,GAAG,CAAC;EACnD,CAAC,CAAC;EACF,OAAOH,OAAO;AAChB;AACA,SAASK,mBAAmBA,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE;EACzDjB,MAAM,CAACW,IAAI,CAACK,QAAQ,CAAC,CAACJ,OAAO,CAAEC,GAAG,IAAK;IACrC,IAAIG,QAAQ,CAACH,GAAG,CAAC,KAAKI,QAAQ,CAACJ,GAAG,CAAC,EAAE;MACnCK,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CAACR,GAAG,EAAEG,QAAQ,CAACH,GAAG,CAAC,CAAC;IAChE;EACF,CAAC,CAAC;EACFb,MAAM,CAACW,IAAI,CAACM,QAAQ,CAAC,CAACL,OAAO,CAAEC,GAAG,IAAK;IACrC,IAAI,CAACG,QAAQ,CAACH,GAAG,CAAC,EAAE;MAClBK,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACE,cAAc,CAACT,GAAG,CAAC;IACpD;EACF,CAAC,CAAC;AACJ;AACA,IAAIU,aAAa,GAAG1C,eAAe,CAAC;EAClCS,IAAI;EACJkC,KAAK,EAAE9B,mBAAmB;EAC1B+B,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMN,KAAK,GAAG5C,QAAQ,CAAC,MAAMiC,qBAAqB,CAACzB,MAAM,CAAC,CAAC,CAAC,EAAEwC,KAAK,CAACzB,SAAS,EAAEyB,KAAK,CAAC5B,KAAK,KAAK,MAAM,GAAG4B,KAAK,CAACvB,aAAa,GAAGuB,KAAK,CAACtB,cAAc,CAAC,CAAC,CAAC;IACrJ,IAAIjB,SAAS,EAAE;MACb,MAAM0C,QAAQ,GAAGA,CAAA,KAAM;QACrBT,QAAQ,CAACC,eAAe,CAACS,SAAS,CAACC,GAAG,CAAC,aAAaL,KAAK,CAAC5B,KAAK,EAAE,CAAC;MACpE,CAAC;MACD,MAAMkC,WAAW,GAAGA,CAAClC,KAAK,GAAG4B,KAAK,CAAC5B,KAAK,KAAK;QAC3CsB,QAAQ,CAACC,eAAe,CAACS,SAAS,CAACG,MAAM,CAAC,aAAanC,KAAK,EAAE,CAAC;MACjE,CAAC;MACDtB,KAAK,CAAC,MAAMkD,KAAK,CAAC5B,KAAK,EAAE,CAACoC,MAAM,EAAEC,MAAM,KAAK;QAC3C,IAAIA,MAAM,EAAE;UACVH,WAAW,CAACG,MAAM,CAAC;QACrB;QACAN,QAAQ,CAAC,CAAC;MACZ,CAAC,EAAE;QACDO,SAAS,EAAE;MACb,CAAC,CAAC;MACFxD,WAAW,CAACiD,QAAQ,CAAC;MACrBhD,aAAa,CAACmD,WAAW,CAAC;MAC1BlD,eAAe,CAACkD,WAAW,CAAC;MAC5BxD,KAAK,CAAC8C,KAAK,EAAE,CAACJ,QAAQ,EAAEC,QAAQ,KAAK;QACnC,IAAIO,KAAK,CAACrB,cAAc,KAAK,QAAQ,EAAE;UACrCY,mBAAmB,CAACC,QAAQ,EAAEC,QAAQ,CAAC;QACzC;MACF,CAAC,CAAC;MACF3C,KAAK,CAAC,MAAMkD,KAAK,CAACrB,cAAc,EAAE,CAACgC,QAAQ,EAAEC,QAAQ,KAAK;QACxD,IAAIA,QAAQ,KAAK,QAAQ,EAAE;UACzBrB,mBAAmB,CAAC,CAAC,CAAC,EAAEK,KAAK,CAACiB,KAAK,CAAC;QACtC;QACA,IAAIF,QAAQ,KAAK,QAAQ,EAAE;UACzBpB,mBAAmB,CAACK,KAAK,CAACiB,KAAK,EAAE,CAAC,CAAC,CAAC;QACtC;MACF,CAAC,CAAC;MACF,IAAIb,KAAK,CAACrB,cAAc,KAAK,QAAQ,EAAE;QACrCY,mBAAmB,CAACK,KAAK,CAACiB,KAAK,EAAE,CAAC,CAAC,CAAC;MACtC;IACF;IACA9D,OAAO,CAACiB,mBAAmB,EAAEgC,KAAK,CAAC;IACnC/C,WAAW,CAAC,MAAM;MAChB,IAAI+C,KAAK,CAAC3B,MAAM,KAAK,KAAK,CAAC,EAAE;QAC3BR,eAAe,CAACmC,KAAK,CAAC3B,MAAM,CAAC;MAC/B;IACF,CAAC,CAAC;IACF,OAAO,MAAMd,YAAY,CAACyC,KAAK,CAAC7B,GAAG,EAAE;MACnC,OAAO,EAAEJ,GAAG,CAAC,CAAC;MACd,OAAO,EAAEiC,KAAK,CAACrB,cAAc,KAAK,OAAO,GAAGiB,KAAK,CAACiB,KAAK,GAAG,KAAK;IACjE,CAAC,EAAE;MACDC,OAAO,EAAEA,CAAA,KAAM;QACb,IAAIC,EAAE;QACN,OAAO,CAAC,CAACA,EAAE,GAAGb,KAAK,CAACY,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,EAAE,CAACC,IAAI,CAACd,KAAK,CAAC,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AACF,SACElC,mBAAmB,EACnBE,mBAAmB,EACnB6B,aAAa,IAAIe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}