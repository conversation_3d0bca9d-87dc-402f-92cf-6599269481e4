{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _NoticeBar from \"./NoticeBar.mjs\";\nconst NoticeBar = withInstall(_NoticeBar);\nvar stdin_default = NoticeBar;\nimport { noticeBarProps } from \"./NoticeBar.mjs\";\nexport { NoticeBar, stdin_default as default, noticeBarProps };", "map": {"version": 3, "names": ["withInstall", "_NoticeBar", "NoticeBar", "stdin_default", "noticeBarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/notice-bar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _NoticeBar from \"./NoticeBar.mjs\";\nconst NoticeBar = withInstall(_NoticeBar);\nvar stdin_default = NoticeBar;\nimport { noticeBarProps } from \"./NoticeBar.mjs\";\nexport {\n  NoticeBar,\n  stdin_default as default,\n  noticeBarProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SAASE,cAAc,QAAQ,iBAAiB;AAChD,SACEF,SAAS,EACTC,aAAa,IAAIE,OAAO,EACxBD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}