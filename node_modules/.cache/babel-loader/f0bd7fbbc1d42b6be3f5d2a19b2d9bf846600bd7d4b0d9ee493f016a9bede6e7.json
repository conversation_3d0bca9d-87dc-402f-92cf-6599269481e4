{"ast": null, "code": "import { ref, watch, computed, Teleport, nextTick, onMounted, defineComponent, onDeactivated, onActivated, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { extend, addUnit, inBrowser, numericProp, getScrollTop, getZIndexStyle, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { throttle } from \"../lazyload/vue-lazyload/util.mjs\";\nimport { useEventListener, getScrollParent } from \"@vant/use\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"back-top\");\nconst backTopProps = {\n  right: numericProp,\n  bottom: numericProp,\n  zIndex: numericProp,\n  target: [String, Object],\n  offset: makeNumericProp(200),\n  immediate: Boolean,\n  teleport: {\n    type: [String, Object],\n    default: \"body\"\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: backTopProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots,\n    attrs\n  }) {\n    let shouldReshow = false;\n    const show = ref(false);\n    const root = ref();\n    const scrollParent = ref();\n    const style = computed(() => extend(getZIndexStyle(props.zIndex), {\n      right: addUnit(props.right),\n      bottom: addUnit(props.bottom)\n    }));\n    const onClick = event => {\n      var _a;\n      emit(\"click\", event);\n      (_a = scrollParent.value) == null ? void 0 : _a.scrollTo({\n        top: 0,\n        behavior: props.immediate ? \"auto\" : \"smooth\"\n      });\n    };\n    const scroll = () => {\n      show.value = scrollParent.value ? getScrollTop(scrollParent.value) >= +props.offset : false;\n    };\n    const getTarget = () => {\n      const {\n        target\n      } = props;\n      if (typeof target === \"string\") {\n        const el = document.querySelector(target);\n        if (el) {\n          return el;\n        }\n        if (process.env.NODE_ENV !== \"production\") {\n          console.error(`[Vant] BackTop: target element \"${target}\" was not found, the BackTop component will not be rendered.`);\n        }\n      } else {\n        return target;\n      }\n    };\n    const updateTarget = () => {\n      if (inBrowser) {\n        nextTick(() => {\n          scrollParent.value = props.target ? getTarget() : getScrollParent(root.value);\n          scroll();\n        });\n      }\n    };\n    useEventListener(\"scroll\", throttle(scroll, 100), {\n      target: scrollParent\n    });\n    onMounted(updateTarget);\n    onActivated(() => {\n      if (shouldReshow) {\n        show.value = true;\n        shouldReshow = false;\n      }\n    });\n    onDeactivated(() => {\n      if (show.value && props.teleport) {\n        show.value = false;\n        shouldReshow = true;\n      }\n    });\n    watch(() => props.target, updateTarget);\n    return () => {\n      const Content = _createVNode(\"div\", _mergeProps({\n        \"ref\": !props.teleport ? root : void 0,\n        \"class\": bem({\n          active: show.value\n        }),\n        \"style\": style.value,\n        \"onClick\": onClick\n      }, attrs), [slots.default ? slots.default() : _createVNode(Icon, {\n        \"name\": \"back-top\",\n        \"class\": bem(\"icon\")\n      }, null)]);\n      if (props.teleport) {\n        return [_createVNode(\"div\", {\n          \"ref\": root,\n          \"class\": bem(\"placeholder\")\n        }, null), _createVNode(Teleport, {\n          \"to\": props.teleport\n        }, {\n          default: () => [Content]\n        })];\n      }\n      return Content;\n    };\n  }\n});\nexport { backTopProps, stdin_default as default };", "map": {"version": 3, "names": ["ref", "watch", "computed", "Teleport", "nextTick", "onMounted", "defineComponent", "onDeactivated", "onActivated", "createVNode", "_createVNode", "mergeProps", "_mergeProps", "extend", "addUnit", "inBrowser", "numericProp", "getScrollTop", "getZIndexStyle", "createNamespace", "makeNumericProp", "throttle", "useEventListener", "getScrollParent", "Icon", "name", "bem", "backTopProps", "right", "bottom", "zIndex", "target", "String", "Object", "offset", "immediate", "Boolean", "teleport", "type", "default", "stdin_default", "inheritAttrs", "props", "emits", "setup", "emit", "slots", "attrs", "shouldReshow", "show", "root", "scrollParent", "style", "onClick", "event", "_a", "value", "scrollTo", "top", "behavior", "scroll", "get<PERSON><PERSON><PERSON>", "el", "document", "querySelector", "process", "env", "NODE_ENV", "console", "error", "updateTarget", "Content", "active"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/back-top/BackTop.mjs"], "sourcesContent": ["import { ref, watch, computed, Teleport, nextTick, onMounted, defineComponent, onDeactivated, onActivated, createVNode as _createVNode, mergeProps as _mergeProps } from \"vue\";\nimport { extend, addUnit, inBrowser, numericProp, getScrollTop, getZIndexStyle, createNamespace, makeNumericProp } from \"../utils/index.mjs\";\nimport { throttle } from \"../lazyload/vue-lazyload/util.mjs\";\nimport { useEventListener, getScrollParent } from \"@vant/use\";\nimport { Icon } from \"../icon/index.mjs\";\nconst [name, bem] = createNamespace(\"back-top\");\nconst backTopProps = {\n  right: numericProp,\n  bottom: numericProp,\n  zIndex: numericProp,\n  target: [String, Object],\n  offset: makeNumericProp(200),\n  immediate: Boolean,\n  teleport: {\n    type: [String, Object],\n    default: \"body\"\n  }\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: backTopProps,\n  emits: [\"click\"],\n  setup(props, {\n    emit,\n    slots,\n    attrs\n  }) {\n    let shouldReshow = false;\n    const show = ref(false);\n    const root = ref();\n    const scrollParent = ref();\n    const style = computed(() => extend(getZIndexStyle(props.zIndex), {\n      right: addUnit(props.right),\n      bottom: addUnit(props.bottom)\n    }));\n    const onClick = (event) => {\n      var _a;\n      emit(\"click\", event);\n      (_a = scrollParent.value) == null ? void 0 : _a.scrollTo({\n        top: 0,\n        behavior: props.immediate ? \"auto\" : \"smooth\"\n      });\n    };\n    const scroll = () => {\n      show.value = scrollParent.value ? getScrollTop(scrollParent.value) >= +props.offset : false;\n    };\n    const getTarget = () => {\n      const {\n        target\n      } = props;\n      if (typeof target === \"string\") {\n        const el = document.querySelector(target);\n        if (el) {\n          return el;\n        }\n        if (process.env.NODE_ENV !== \"production\") {\n          console.error(`[Vant] BackTop: target element \"${target}\" was not found, the BackTop component will not be rendered.`);\n        }\n      } else {\n        return target;\n      }\n    };\n    const updateTarget = () => {\n      if (inBrowser) {\n        nextTick(() => {\n          scrollParent.value = props.target ? getTarget() : getScrollParent(root.value);\n          scroll();\n        });\n      }\n    };\n    useEventListener(\"scroll\", throttle(scroll, 100), {\n      target: scrollParent\n    });\n    onMounted(updateTarget);\n    onActivated(() => {\n      if (shouldReshow) {\n        show.value = true;\n        shouldReshow = false;\n      }\n    });\n    onDeactivated(() => {\n      if (show.value && props.teleport) {\n        show.value = false;\n        shouldReshow = true;\n      }\n    });\n    watch(() => props.target, updateTarget);\n    return () => {\n      const Content = _createVNode(\"div\", _mergeProps({\n        \"ref\": !props.teleport ? root : void 0,\n        \"class\": bem({\n          active: show.value\n        }),\n        \"style\": style.value,\n        \"onClick\": onClick\n      }, attrs), [slots.default ? slots.default() : _createVNode(Icon, {\n        \"name\": \"back-top\",\n        \"class\": bem(\"icon\")\n      }, null)]);\n      if (props.teleport) {\n        return [_createVNode(\"div\", {\n          \"ref\": root,\n          \"class\": bem(\"placeholder\")\n        }, null), _createVNode(Teleport, {\n          \"to\": props.teleport\n        }, {\n          default: () => [Content]\n        })];\n      }\n      return Content;\n    };\n  }\n});\nexport {\n  backTopProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,IAAIC,YAAY,EAAEC,UAAU,IAAIC,WAAW,QAAQ,KAAK;AAC9K,SAASC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ,oBAAoB;AAC5I,SAASC,QAAQ,QAAQ,mCAAmC;AAC5D,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,WAAW;AAC7D,SAASC,IAAI,QAAQ,mBAAmB;AACxC,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGP,eAAe,CAAC,UAAU,CAAC;AAC/C,MAAMQ,YAAY,GAAG;EACnBC,KAAK,EAAEZ,WAAW;EAClBa,MAAM,EAAEb,WAAW;EACnBc,MAAM,EAAEd,WAAW;EACnBe,MAAM,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;EACxBC,MAAM,EAAEd,eAAe,CAAC,GAAG,CAAC;EAC5Be,SAAS,EAAEC,OAAO;EAClBC,QAAQ,EAAE;IACRC,IAAI,EAAE,CAACN,MAAM,EAAEC,MAAM,CAAC;IACtBM,OAAO,EAAE;EACX;AACF,CAAC;AACD,IAAIC,aAAa,GAAGlC,eAAe,CAAC;EAClCmB,IAAI;EACJgB,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAEf,YAAY;EACnBgB,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC,KAAK;IACLC;EACF,CAAC,EAAE;IACD,IAAIC,YAAY,GAAG,KAAK;IACxB,MAAMC,IAAI,GAAGjD,GAAG,CAAC,KAAK,CAAC;IACvB,MAAMkD,IAAI,GAAGlD,GAAG,CAAC,CAAC;IAClB,MAAMmD,YAAY,GAAGnD,GAAG,CAAC,CAAC;IAC1B,MAAMoD,KAAK,GAAGlD,QAAQ,CAAC,MAAMW,MAAM,CAACK,cAAc,CAACwB,KAAK,CAACZ,MAAM,CAAC,EAAE;MAChEF,KAAK,EAAEd,OAAO,CAAC4B,KAAK,CAACd,KAAK,CAAC;MAC3BC,MAAM,EAAEf,OAAO,CAAC4B,KAAK,CAACb,MAAM;IAC9B,CAAC,CAAC,CAAC;IACH,MAAMwB,OAAO,GAAIC,KAAK,IAAK;MACzB,IAAIC,EAAE;MACNV,IAAI,CAAC,OAAO,EAAES,KAAK,CAAC;MACpB,CAACC,EAAE,GAAGJ,YAAY,CAACK,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,QAAQ,CAAC;QACvDC,GAAG,EAAE,CAAC;QACNC,QAAQ,EAAEjB,KAAK,CAACP,SAAS,GAAG,MAAM,GAAG;MACvC,CAAC,CAAC;IACJ,CAAC;IACD,MAAMyB,MAAM,GAAGA,CAAA,KAAM;MACnBX,IAAI,CAACO,KAAK,GAAGL,YAAY,CAACK,KAAK,GAAGvC,YAAY,CAACkC,YAAY,CAACK,KAAK,CAAC,IAAI,CAACd,KAAK,CAACR,MAAM,GAAG,KAAK;IAC7F,CAAC;IACD,MAAM2B,SAAS,GAAGA,CAAA,KAAM;MACtB,MAAM;QACJ9B;MACF,CAAC,GAAGW,KAAK;MACT,IAAI,OAAOX,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM+B,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAACjC,MAAM,CAAC;QACzC,IAAI+B,EAAE,EAAE;UACN,OAAOA,EAAE;QACX;QACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCC,OAAO,CAACC,KAAK,CAAC,mCAAmCtC,MAAM,8DAA8D,CAAC;QACxH;MACF,CAAC,MAAM;QACL,OAAOA,MAAM;MACf;IACF,CAAC;IACD,MAAMuC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIvD,SAAS,EAAE;QACbX,QAAQ,CAAC,MAAM;UACb+C,YAAY,CAACK,KAAK,GAAGd,KAAK,CAACX,MAAM,GAAG8B,SAAS,CAAC,CAAC,GAAGtC,eAAe,CAAC2B,IAAI,CAACM,KAAK,CAAC;UAC7EI,MAAM,CAAC,CAAC;QACV,CAAC,CAAC;MACJ;IACF,CAAC;IACDtC,gBAAgB,CAAC,QAAQ,EAAED,QAAQ,CAACuC,MAAM,EAAE,GAAG,CAAC,EAAE;MAChD7B,MAAM,EAAEoB;IACV,CAAC,CAAC;IACF9C,SAAS,CAACiE,YAAY,CAAC;IACvB9D,WAAW,CAAC,MAAM;MAChB,IAAIwC,YAAY,EAAE;QAChBC,IAAI,CAACO,KAAK,GAAG,IAAI;QACjBR,YAAY,GAAG,KAAK;MACtB;IACF,CAAC,CAAC;IACFzC,aAAa,CAAC,MAAM;MAClB,IAAI0C,IAAI,CAACO,KAAK,IAAId,KAAK,CAACL,QAAQ,EAAE;QAChCY,IAAI,CAACO,KAAK,GAAG,KAAK;QAClBR,YAAY,GAAG,IAAI;MACrB;IACF,CAAC,CAAC;IACF/C,KAAK,CAAC,MAAMyC,KAAK,CAACX,MAAM,EAAEuC,YAAY,CAAC;IACvC,OAAO,MAAM;MACX,MAAMC,OAAO,GAAG7D,YAAY,CAAC,KAAK,EAAEE,WAAW,CAAC;QAC9C,KAAK,EAAE,CAAC8B,KAAK,CAACL,QAAQ,GAAGa,IAAI,GAAG,KAAK,CAAC;QACtC,OAAO,EAAExB,GAAG,CAAC;UACX8C,MAAM,EAAEvB,IAAI,CAACO;QACf,CAAC,CAAC;QACF,OAAO,EAAEJ,KAAK,CAACI,KAAK;QACpB,SAAS,EAAEH;MACb,CAAC,EAAEN,KAAK,CAAC,EAAE,CAACD,KAAK,CAACP,OAAO,GAAGO,KAAK,CAACP,OAAO,CAAC,CAAC,GAAG7B,YAAY,CAACc,IAAI,EAAE;QAC/D,MAAM,EAAE,UAAU;QAClB,OAAO,EAAEE,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACV,IAAIgB,KAAK,CAACL,QAAQ,EAAE;QAClB,OAAO,CAAC3B,YAAY,CAAC,KAAK,EAAE;UAC1B,KAAK,EAAEwC,IAAI;UACX,OAAO,EAAExB,GAAG,CAAC,aAAa;QAC5B,CAAC,EAAE,IAAI,CAAC,EAAEhB,YAAY,CAACP,QAAQ,EAAE;UAC/B,IAAI,EAAEuC,KAAK,CAACL;QACd,CAAC,EAAE;UACDE,OAAO,EAAEA,CAAA,KAAM,CAACgC,OAAO;QACzB,CAAC,CAAC,CAAC;MACL;MACA,OAAOA,OAAO;IAChB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE5C,YAAY,EACZa,aAAa,IAAID,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}