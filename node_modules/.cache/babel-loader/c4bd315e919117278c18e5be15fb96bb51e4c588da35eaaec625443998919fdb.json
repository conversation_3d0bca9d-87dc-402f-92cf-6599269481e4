{"ast": null, "code": "import { ref, defineComponent, Teleport, Transition, vShow as _vShow, mergeProps as _mergeProps, createVNode as _createVNode, withDirectives as _withDirectives } from \"vue\";\nimport { isDef, extend, truthProp, numericProp, unknownProp, preventDefault, createNamespace, getZIndexStyle } from \"../utils/index.mjs\";\nimport { useEventListener } from \"@vant/use\";\nimport { useLazyRender } from \"../composables/use-lazy-render.mjs\";\nconst [name, bem] = createNamespace(\"overlay\");\nconst overlayProps = {\n  show: Boolean,\n  zIndex: numericProp,\n  duration: numericProp,\n  className: unknownProp,\n  lockScroll: truthProp,\n  lazyRender: truthProp,\n  customStyle: Object,\n  teleport: [String, Object]\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: overlayProps,\n  setup(props, {\n    attrs,\n    slots\n  }) {\n    const root = ref();\n    const lazyRender = useLazyRender(() => props.show || !props.lazyRender);\n    const onTouchMove = event => {\n      if (props.lockScroll) {\n        preventDefault(event, true);\n      }\n    };\n    const renderOverlay = lazyRender(() => {\n      var _a;\n      const style = extend(getZIndexStyle(props.zIndex), props.customStyle);\n      if (isDef(props.duration)) {\n        style.animationDuration = `${props.duration}s`;\n      }\n      return _withDirectives(_createVNode(\"div\", _mergeProps({\n        \"ref\": root,\n        \"style\": style,\n        \"class\": [bem(), props.className]\n      }, attrs), [(_a = slots.default) == null ? void 0 : _a.call(slots)]), [[_vShow, props.show]]);\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: root\n    });\n    return () => {\n      const Content = _createVNode(Transition, {\n        \"name\": \"van-fade\",\n        \"appear\": true\n      }, {\n        default: renderOverlay\n      });\n      if (props.teleport) {\n        return _createVNode(Teleport, {\n          \"to\": props.teleport\n        }, {\n          default: () => [Content]\n        });\n      }\n      return Content;\n    };\n  }\n});\nexport { stdin_default as default, overlayProps };", "map": {"version": 3, "names": ["ref", "defineComponent", "Teleport", "Transition", "vShow", "_vShow", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "withDirectives", "_withDirectives", "isDef", "extend", "truthProp", "numericProp", "unknownProp", "preventDefault", "createNamespace", "getZIndexStyle", "useEventListener", "useLazyRender", "name", "bem", "overlayProps", "show", "Boolean", "zIndex", "duration", "className", "lockScroll", "lazy<PERSON>ender", "customStyle", "Object", "teleport", "String", "stdin_default", "inheritAttrs", "props", "setup", "attrs", "slots", "root", "onTouchMove", "event", "renderOverlay", "_a", "style", "animationDuration", "default", "call", "target", "Content"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/overlay/Overlay.mjs"], "sourcesContent": ["import { ref, defineComponent, Teleport, Transition, vShow as _vShow, mergeProps as _mergeProps, createVNode as _createVNode, withDirectives as _withDirectives } from \"vue\";\nimport { isDef, extend, truthProp, numericProp, unknownProp, preventDefault, createNamespace, getZIndexStyle } from \"../utils/index.mjs\";\nimport { useEventListener } from \"@vant/use\";\nimport { useLazyRender } from \"../composables/use-lazy-render.mjs\";\nconst [name, bem] = createNamespace(\"overlay\");\nconst overlayProps = {\n  show: Boolean,\n  zIndex: numericProp,\n  duration: numericProp,\n  className: unknownProp,\n  lockScroll: truthProp,\n  lazyRender: truthProp,\n  customStyle: Object,\n  teleport: [String, Object]\n};\nvar stdin_default = defineComponent({\n  name,\n  inheritAttrs: false,\n  props: overlayProps,\n  setup(props, {\n    attrs,\n    slots\n  }) {\n    const root = ref();\n    const lazyRender = useLazyRender(() => props.show || !props.lazyRender);\n    const onTouchMove = (event) => {\n      if (props.lockScroll) {\n        preventDefault(event, true);\n      }\n    };\n    const renderOverlay = lazyRender(() => {\n      var _a;\n      const style = extend(getZIndexStyle(props.zIndex), props.customStyle);\n      if (isDef(props.duration)) {\n        style.animationDuration = `${props.duration}s`;\n      }\n      return _withDirectives(_createVNode(\"div\", _mergeProps({\n        \"ref\": root,\n        \"style\": style,\n        \"class\": [bem(), props.className]\n      }, attrs), [(_a = slots.default) == null ? void 0 : _a.call(slots)]), [[_vShow, props.show]]);\n    });\n    useEventListener(\"touchmove\", onTouchMove, {\n      target: root\n    });\n    return () => {\n      const Content = _createVNode(Transition, {\n        \"name\": \"van-fade\",\n        \"appear\": true\n      }, {\n        default: renderOverlay\n      });\n      if (props.teleport) {\n        return _createVNode(Teleport, {\n          \"to\": props.teleport\n        }, {\n          default: () => [Content]\n        });\n      }\n      return Content;\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  overlayProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,IAAIC,MAAM,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,EAAEC,cAAc,IAAIC,eAAe,QAAQ,KAAK;AAC5K,SAASC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,cAAc,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACxI,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,SAASC,aAAa,QAAQ,oCAAoC;AAClE,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,SAAS,CAAC;AAC9C,MAAMM,YAAY,GAAG;EACnBC,IAAI,EAAEC,OAAO;EACbC,MAAM,EAAEZ,WAAW;EACnBa,QAAQ,EAAEb,WAAW;EACrBc,SAAS,EAAEb,WAAW;EACtBc,UAAU,EAAEhB,SAAS;EACrBiB,UAAU,EAAEjB,SAAS;EACrBkB,WAAW,EAAEC,MAAM;EACnBC,QAAQ,EAAE,CAACC,MAAM,EAAEF,MAAM;AAC3B,CAAC;AACD,IAAIG,aAAa,GAAGnC,eAAe,CAAC;EAClCqB,IAAI;EACJe,YAAY,EAAE,KAAK;EACnBC,KAAK,EAAEd,YAAY;EACnBe,KAAKA,CAACD,KAAK,EAAE;IACXE,KAAK;IACLC;EACF,CAAC,EAAE;IACD,MAAMC,IAAI,GAAG1C,GAAG,CAAC,CAAC;IAClB,MAAM+B,UAAU,GAAGV,aAAa,CAAC,MAAMiB,KAAK,CAACb,IAAI,IAAI,CAACa,KAAK,CAACP,UAAU,CAAC;IACvE,MAAMY,WAAW,GAAIC,KAAK,IAAK;MAC7B,IAAIN,KAAK,CAACR,UAAU,EAAE;QACpBb,cAAc,CAAC2B,KAAK,EAAE,IAAI,CAAC;MAC7B;IACF,CAAC;IACD,MAAMC,aAAa,GAAGd,UAAU,CAAC,MAAM;MACrC,IAAIe,EAAE;MACN,MAAMC,KAAK,GAAGlC,MAAM,CAACM,cAAc,CAACmB,KAAK,CAACX,MAAM,CAAC,EAAEW,KAAK,CAACN,WAAW,CAAC;MACrE,IAAIpB,KAAK,CAAC0B,KAAK,CAACV,QAAQ,CAAC,EAAE;QACzBmB,KAAK,CAACC,iBAAiB,GAAG,GAAGV,KAAK,CAACV,QAAQ,GAAG;MAChD;MACA,OAAOjB,eAAe,CAACF,YAAY,CAAC,KAAK,EAAEF,WAAW,CAAC;QACrD,KAAK,EAAEmC,IAAI;QACX,OAAO,EAAEK,KAAK;QACd,OAAO,EAAE,CAACxB,GAAG,CAAC,CAAC,EAAEe,KAAK,CAACT,SAAS;MAClC,CAAC,EAAEW,KAAK,CAAC,EAAE,CAAC,CAACM,EAAE,GAAGL,KAAK,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACI,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAACpC,MAAM,EAAEiC,KAAK,CAACb,IAAI,CAAC,CAAC,CAAC;IAC/F,CAAC,CAAC;IACFL,gBAAgB,CAAC,WAAW,EAAEuB,WAAW,EAAE;MACzCQ,MAAM,EAAET;IACV,CAAC,CAAC;IACF,OAAO,MAAM;MACX,MAAMU,OAAO,GAAG3C,YAAY,CAACN,UAAU,EAAE;QACvC,MAAM,EAAE,UAAU;QAClB,QAAQ,EAAE;MACZ,CAAC,EAAE;QACD8C,OAAO,EAAEJ;MACX,CAAC,CAAC;MACF,IAAIP,KAAK,CAACJ,QAAQ,EAAE;QAClB,OAAOzB,YAAY,CAACP,QAAQ,EAAE;UAC5B,IAAI,EAAEoC,KAAK,CAACJ;QACd,CAAC,EAAE;UACDe,OAAO,EAAEA,CAAA,KAAM,CAACG,OAAO;QACzB,CAAC,CAAC;MACJ;MACA,OAAOA,OAAO;IAChB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEhB,aAAa,IAAIa,OAAO,EACxBzB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}