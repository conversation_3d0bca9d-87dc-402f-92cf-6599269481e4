{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, addUnit, numericProp, getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"loading\");\nconst SpinIcon = Array(12).fill(null).map((_, index) => _createVNode(\"i\", {\n  \"class\": bem(\"line\", String(index + 1))\n}, null));\nconst CircularIcon = _createVNode(\"svg\", {\n  \"class\": bem(\"circular\"),\n  \"viewBox\": \"25 25 50 50\"\n}, [_createVNode(\"circle\", {\n  \"cx\": \"50\",\n  \"cy\": \"50\",\n  \"r\": \"20\",\n  \"fill\": \"none\"\n}, null)]);\nconst loadingProps = {\n  size: numericProp,\n  type: makeStringProp(\"circular\"),\n  color: String,\n  vertical: Boolean,\n  textSize: numericProp,\n  textColor: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: loadingProps,\n  setup(props, {\n    slots\n  }) {\n    const spinnerStyle = computed(() => extend({\n      color: props.color\n    }, getSizeStyle(props.size)));\n    const renderIcon = () => {\n      const DefaultIcon = props.type === \"spinner\" ? SpinIcon : CircularIcon;\n      return _createVNode(\"span\", {\n        \"class\": bem(\"spinner\", props.type),\n        \"style\": spinnerStyle.value\n      }, [slots.icon ? slots.icon() : DefaultIcon]);\n    };\n    const renderText = () => {\n      var _a;\n      if (slots.default) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\"),\n          \"style\": {\n            fontSize: addUnit(props.textSize),\n            color: (_a = props.textColor) != null ? _a : props.color\n          }\n        }, [slots.default()]);\n      }\n    };\n    return () => {\n      const {\n        type,\n        vertical\n      } = props;\n      return _createVNode(\"div\", {\n        \"class\": bem([type, {\n          vertical\n        }]),\n        \"aria-live\": \"polite\",\n        \"aria-busy\": true\n      }, [renderIcon(), renderText()]);\n    };\n  }\n});\nexport { stdin_default as default, loadingProps };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "extend", "addUnit", "numericProp", "getSizeStyle", "makeStringProp", "createNamespace", "name", "bem", "SpinIcon", "Array", "fill", "map", "_", "index", "String", "CircularIcon", "loadingProps", "size", "type", "color", "vertical", "Boolean", "textSize", "textColor", "stdin_default", "props", "setup", "slots", "spinnerStyle", "renderIcon", "DefaultIcon", "value", "icon", "renderText", "_a", "default", "fontSize"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/loading/Loading.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { extend, addUnit, numericProp, getSizeStyle, makeStringProp, createNamespace } from \"../utils/index.mjs\";\nconst [name, bem] = createNamespace(\"loading\");\nconst SpinIcon = Array(12).fill(null).map((_, index) => _createVNode(\"i\", {\n  \"class\": bem(\"line\", String(index + 1))\n}, null));\nconst CircularIcon = _createVNode(\"svg\", {\n  \"class\": bem(\"circular\"),\n  \"viewBox\": \"25 25 50 50\"\n}, [_createVNode(\"circle\", {\n  \"cx\": \"50\",\n  \"cy\": \"50\",\n  \"r\": \"20\",\n  \"fill\": \"none\"\n}, null)]);\nconst loadingProps = {\n  size: numericProp,\n  type: makeStringProp(\"circular\"),\n  color: String,\n  vertical: Boolean,\n  textSize: numericProp,\n  textColor: String\n};\nvar stdin_default = defineComponent({\n  name,\n  props: loadingProps,\n  setup(props, {\n    slots\n  }) {\n    const spinnerStyle = computed(() => extend({\n      color: props.color\n    }, getSizeStyle(props.size)));\n    const renderIcon = () => {\n      const DefaultIcon = props.type === \"spinner\" ? SpinIcon : CircularIcon;\n      return _createVNode(\"span\", {\n        \"class\": bem(\"spinner\", props.type),\n        \"style\": spinnerStyle.value\n      }, [slots.icon ? slots.icon() : DefaultIcon]);\n    };\n    const renderText = () => {\n      var _a;\n      if (slots.default) {\n        return _createVNode(\"span\", {\n          \"class\": bem(\"text\"),\n          \"style\": {\n            fontSize: addUnit(props.textSize),\n            color: (_a = props.textColor) != null ? _a : props.color\n          }\n        }, [slots.default()]);\n      }\n    };\n    return () => {\n      const {\n        type,\n        vertical\n      } = props;\n      return _createVNode(\"div\", {\n        \"class\": bem([type, {\n          vertical\n        }]),\n        \"aria-live\": \"polite\",\n        \"aria-busy\": true\n      }, [renderIcon(), renderText()]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  loadingProps\n};\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,eAAe,QAAQ,oBAAoB;AAChH,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGF,eAAe,CAAC,SAAS,CAAC;AAC9C,MAAMG,QAAQ,GAAGC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAKd,YAAY,CAAC,GAAG,EAAE;EACxE,OAAO,EAAEQ,GAAG,CAAC,MAAM,EAAEO,MAAM,CAACD,KAAK,GAAG,CAAC,CAAC;AACxC,CAAC,EAAE,IAAI,CAAC,CAAC;AACT,MAAME,YAAY,GAAGhB,YAAY,CAAC,KAAK,EAAE;EACvC,OAAO,EAAEQ,GAAG,CAAC,UAAU,CAAC;EACxB,SAAS,EAAE;AACb,CAAC,EAAE,CAACR,YAAY,CAAC,QAAQ,EAAE;EACzB,IAAI,EAAE,IAAI;EACV,IAAI,EAAE,IAAI;EACV,GAAG,EAAE,IAAI;EACT,MAAM,EAAE;AACV,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACV,MAAMiB,YAAY,GAAG;EACnBC,IAAI,EAAEf,WAAW;EACjBgB,IAAI,EAAEd,cAAc,CAAC,UAAU,CAAC;EAChCe,KAAK,EAAEL,MAAM;EACbM,QAAQ,EAAEC,OAAO;EACjBC,QAAQ,EAAEpB,WAAW;EACrBqB,SAAS,EAAET;AACb,CAAC;AACD,IAAIU,aAAa,GAAG3B,eAAe,CAAC;EAClCS,IAAI;EACJmB,KAAK,EAAET,YAAY;EACnBU,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,YAAY,GAAGhC,QAAQ,CAAC,MAAMI,MAAM,CAAC;MACzCmB,KAAK,EAAEM,KAAK,CAACN;IACf,CAAC,EAAEhB,YAAY,CAACsB,KAAK,CAACR,IAAI,CAAC,CAAC,CAAC;IAC7B,MAAMY,UAAU,GAAGA,CAAA,KAAM;MACvB,MAAMC,WAAW,GAAGL,KAAK,CAACP,IAAI,KAAK,SAAS,GAAGV,QAAQ,GAAGO,YAAY;MACtE,OAAOhB,YAAY,CAAC,MAAM,EAAE;QAC1B,OAAO,EAAEQ,GAAG,CAAC,SAAS,EAAEkB,KAAK,CAACP,IAAI,CAAC;QACnC,OAAO,EAAEU,YAAY,CAACG;MACxB,CAAC,EAAE,CAACJ,KAAK,CAACK,IAAI,GAAGL,KAAK,CAACK,IAAI,CAAC,CAAC,GAAGF,WAAW,CAAC,CAAC;IAC/C,CAAC;IACD,MAAMG,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAIC,EAAE;MACN,IAAIP,KAAK,CAACQ,OAAO,EAAE;QACjB,OAAOpC,YAAY,CAAC,MAAM,EAAE;UAC1B,OAAO,EAAEQ,GAAG,CAAC,MAAM,CAAC;UACpB,OAAO,EAAE;YACP6B,QAAQ,EAAEnC,OAAO,CAACwB,KAAK,CAACH,QAAQ,CAAC;YACjCH,KAAK,EAAE,CAACe,EAAE,GAAGT,KAAK,CAACF,SAAS,KAAK,IAAI,GAAGW,EAAE,GAAGT,KAAK,CAACN;UACrD;QACF,CAAC,EAAE,CAACQ,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IACD,OAAO,MAAM;MACX,MAAM;QACJjB,IAAI;QACJE;MACF,CAAC,GAAGK,KAAK;MACT,OAAO1B,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEQ,GAAG,CAAC,CAACW,IAAI,EAAE;UAClBE;QACF,CAAC,CAAC,CAAC;QACH,WAAW,EAAE,QAAQ;QACrB,WAAW,EAAE;MACf,CAAC,EAAE,CAACS,UAAU,CAAC,CAAC,EAAEI,UAAU,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACET,aAAa,IAAIW,OAAO,EACxBnB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}