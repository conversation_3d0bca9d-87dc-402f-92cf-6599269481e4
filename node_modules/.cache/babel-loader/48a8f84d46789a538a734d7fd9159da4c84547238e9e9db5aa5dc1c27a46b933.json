{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = value => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = value => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = x => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\nimport { defineComponent, onMounted, ref, nextTick, watch, createVNode as _createVNode } from \"vue\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { createNamespace, makeArrayProp, makeNumberProp, makeNumericProp, truthProp } from \"../utils/index.mjs\";\nconst barrageProps = {\n  top: makeNumericProp(10),\n  rows: makeNumericProp(4),\n  duration: makeNumericProp(4e3),\n  autoPlay: truthProp,\n  delay: makeNumberProp(300),\n  modelValue: makeArrayProp()\n};\nconst [name, bem] = createNamespace(\"barrage\");\nvar stdin_default = defineComponent({\n  name,\n  props: barrageProps,\n  emits: [\"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const barrageWrapper = ref();\n    const className = bem(\"item\");\n    const total = ref(0);\n    const barrageItems = [];\n    const createBarrageItem = (text, delay = props.delay) => {\n      const item = document.createElement(\"span\");\n      item.className = className;\n      item.innerText = String(text);\n      item.style.animationDuration = `${props.duration}ms`;\n      item.style.animationDelay = `${delay}ms`;\n      item.style.animationName = \"van-barrage\";\n      item.style.animationTimingFunction = \"linear\";\n      return item;\n    };\n    const isInitBarrage = ref(true);\n    const isPlay = ref(props.autoPlay);\n    const appendBarrageItem = ({\n      id,\n      text\n    }, i) => {\n      var _a;\n      const item = createBarrageItem(text, isInitBarrage.value ? i * props.delay : void 0);\n      if (!props.autoPlay && isPlay.value === false) {\n        item.style.animationPlayState = \"paused\";\n      }\n      (_a = barrageWrapper.value) == null ? void 0 : _a.append(item);\n      total.value++;\n      const top = (total.value - 1) % +props.rows * item.offsetHeight + +props.top;\n      item.style.top = `${top}px`;\n      item.dataset.id = String(id);\n      barrageItems.push(item);\n      item.addEventListener(\"animationend\", () => {\n        emit(\"update:modelValue\", [...props.modelValue].filter(v => String(v.id) !== item.dataset.id));\n      });\n    };\n    const updateBarrages = (newValue, oldValue) => {\n      const map = new Map(oldValue.map(item => [item.id, item]));\n      newValue.forEach((item, i) => {\n        if (map.has(item.id)) {\n          map.delete(item.id);\n        } else {\n          appendBarrageItem(item, i);\n        }\n      });\n      map.forEach(item => {\n        const index = barrageItems.findIndex(span => span.dataset.id === String(item.id));\n        if (index > -1) {\n          barrageItems[index].remove();\n          barrageItems.splice(index, 1);\n        }\n      });\n      isInitBarrage.value = false;\n    };\n    watch(() => props.modelValue.slice(), (newValue, oldValue) => updateBarrages(newValue != null ? newValue : [], oldValue != null ? oldValue : []), {\n      deep: true\n    });\n    const rootStyle = ref({});\n    onMounted(() => __async(null, null, function* () {\n      var _a;\n      rootStyle.value[\"--move-distance\"] = `-${(_a = barrageWrapper.value) == null ? void 0 : _a.offsetWidth}px`;\n      yield nextTick();\n      updateBarrages(props.modelValue, []);\n    }));\n    const play = () => {\n      isPlay.value = true;\n      barrageItems.forEach(item => {\n        item.style.animationPlayState = \"running\";\n      });\n    };\n    const pause = () => {\n      isPlay.value = false;\n      barrageItems.forEach(item => {\n        item.style.animationPlayState = \"paused\";\n      });\n    };\n    useExpose({\n      play,\n      pause\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"ref\": barrageWrapper,\n        \"style\": rootStyle.value\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport { barrageProps, stdin_default as default };", "map": {"version": 3, "names": ["__async", "__this", "__arguments", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "throw", "x", "done", "then", "apply", "defineComponent", "onMounted", "ref", "nextTick", "watch", "createVNode", "_createVNode", "useExpose", "createNamespace", "makeArrayProp", "makeNumberProp", "makeNumericProp", "truthProp", "barrageProps", "top", "rows", "duration", "autoPlay", "delay", "modelValue", "name", "bem", "stdin_default", "props", "emits", "setup", "emit", "slots", "barrageWrapper", "className", "total", "barrageItems", "createBarrageItem", "text", "item", "document", "createElement", "innerText", "String", "style", "animationDuration", "animationDelay", "animationName", "animationTimingFunction", "isInitBarrage", "isPlay", "appendBarrageItem", "id", "i", "_a", "animationPlayState", "append", "offsetHeight", "dataset", "push", "addEventListener", "filter", "v", "updateBarrages", "newValue", "oldValue", "map", "Map", "for<PERSON>ach", "has", "delete", "index", "findIndex", "span", "remove", "splice", "slice", "deep", "rootStyle", "offsetWidth", "play", "pause", "default", "call"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/barrage/Barrage.mjs"], "sourcesContent": ["var __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\nimport { defineComponent, onMounted, ref, nextTick, watch, createVNode as _createVNode } from \"vue\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { createNamespace, makeArrayProp, makeNumberProp, makeNumericProp, truthProp } from \"../utils/index.mjs\";\nconst barrageProps = {\n  top: makeNumericProp(10),\n  rows: makeNumericProp(4),\n  duration: makeNumericProp(4e3),\n  autoPlay: truthProp,\n  delay: makeNumberProp(300),\n  modelValue: makeArrayProp()\n};\nconst [name, bem] = createNamespace(\"barrage\");\nvar stdin_default = defineComponent({\n  name,\n  props: barrageProps,\n  emits: [\"update:modelValue\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const barrageWrapper = ref();\n    const className = bem(\"item\");\n    const total = ref(0);\n    const barrageItems = [];\n    const createBarrageItem = (text, delay = props.delay) => {\n      const item = document.createElement(\"span\");\n      item.className = className;\n      item.innerText = String(text);\n      item.style.animationDuration = `${props.duration}ms`;\n      item.style.animationDelay = `${delay}ms`;\n      item.style.animationName = \"van-barrage\";\n      item.style.animationTimingFunction = \"linear\";\n      return item;\n    };\n    const isInitBarrage = ref(true);\n    const isPlay = ref(props.autoPlay);\n    const appendBarrageItem = ({\n      id,\n      text\n    }, i) => {\n      var _a;\n      const item = createBarrageItem(text, isInitBarrage.value ? i * props.delay : void 0);\n      if (!props.autoPlay && isPlay.value === false) {\n        item.style.animationPlayState = \"paused\";\n      }\n      (_a = barrageWrapper.value) == null ? void 0 : _a.append(item);\n      total.value++;\n      const top = (total.value - 1) % +props.rows * item.offsetHeight + +props.top;\n      item.style.top = `${top}px`;\n      item.dataset.id = String(id);\n      barrageItems.push(item);\n      item.addEventListener(\"animationend\", () => {\n        emit(\"update:modelValue\", [...props.modelValue].filter((v) => String(v.id) !== item.dataset.id));\n      });\n    };\n    const updateBarrages = (newValue, oldValue) => {\n      const map = new Map(oldValue.map((item) => [item.id, item]));\n      newValue.forEach((item, i) => {\n        if (map.has(item.id)) {\n          map.delete(item.id);\n        } else {\n          appendBarrageItem(item, i);\n        }\n      });\n      map.forEach((item) => {\n        const index = barrageItems.findIndex((span) => span.dataset.id === String(item.id));\n        if (index > -1) {\n          barrageItems[index].remove();\n          barrageItems.splice(index, 1);\n        }\n      });\n      isInitBarrage.value = false;\n    };\n    watch(() => props.modelValue.slice(), (newValue, oldValue) => updateBarrages(newValue != null ? newValue : [], oldValue != null ? oldValue : []), {\n      deep: true\n    });\n    const rootStyle = ref({});\n    onMounted(() => __async(null, null, function* () {\n      var _a;\n      rootStyle.value[\"--move-distance\"] = `-${(_a = barrageWrapper.value) == null ? void 0 : _a.offsetWidth}px`;\n      yield nextTick();\n      updateBarrages(props.modelValue, []);\n    }));\n    const play = () => {\n      isPlay.value = true;\n      barrageItems.forEach((item) => {\n        item.style.animationPlayState = \"running\";\n      });\n    };\n    const pause = () => {\n      isPlay.value = false;\n      barrageItems.forEach((item) => {\n        item.style.animationPlayState = \"paused\";\n      });\n    };\n    useExpose({\n      play,\n      pause\n    });\n    return () => {\n      var _a;\n      return _createVNode(\"div\", {\n        \"class\": bem(),\n        \"ref\": barrageWrapper,\n        \"style\": rootStyle.value\n      }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n    };\n  }\n});\nexport {\n  barrageProps,\n  stdin_default as default\n};\n"], "mappings": ";;;;AAAA,IAAIA,OAAO,GAAGA,CAACC,MAAM,EAAEC,WAAW,EAAEC,SAAS,KAAK;EAChD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAIC,SAAS,GAAIC,KAAK,IAAK;MACzB,IAAI;QACFC,IAAI,CAACN,SAAS,CAACO,IAAI,CAACF,KAAK,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVL,MAAM,CAACK,CAAC,CAAC;MACX;IACF,CAAC;IACD,IAAIC,QAAQ,GAAIJ,KAAK,IAAK;MACxB,IAAI;QACFC,IAAI,CAACN,SAAS,CAACU,KAAK,CAACL,KAAK,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVL,MAAM,CAACK,CAAC,CAAC;MACX;IACF,CAAC;IACD,IAAIF,IAAI,GAAIK,CAAC,IAAKA,CAAC,CAACC,IAAI,GAAGV,OAAO,CAACS,CAAC,CAACN,KAAK,CAAC,GAAGJ,OAAO,CAACC,OAAO,CAACS,CAAC,CAACN,KAAK,CAAC,CAACQ,IAAI,CAACT,SAAS,EAAEK,QAAQ,CAAC;IAChGH,IAAI,CAAC,CAACN,SAAS,GAAGA,SAAS,CAACc,KAAK,CAAChB,MAAM,EAAEC,WAAW,CAAC,EAAEQ,IAAI,CAAC,CAAC,CAAC;EACjE,CAAC,CAAC;AACJ,CAAC;AACD,SAASQ,eAAe,EAAEC,SAAS,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACnG,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,eAAe,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,EAAEC,SAAS,QAAQ,oBAAoB;AAC/G,MAAMC,YAAY,GAAG;EACnBC,GAAG,EAAEH,eAAe,CAAC,EAAE,CAAC;EACxBI,IAAI,EAAEJ,eAAe,CAAC,CAAC,CAAC;EACxBK,QAAQ,EAAEL,eAAe,CAAC,GAAG,CAAC;EAC9BM,QAAQ,EAAEL,SAAS;EACnBM,KAAK,EAAER,cAAc,CAAC,GAAG,CAAC;EAC1BS,UAAU,EAAEV,aAAa,CAAC;AAC5B,CAAC;AACD,MAAM,CAACW,IAAI,EAAEC,GAAG,CAAC,GAAGb,eAAe,CAAC,SAAS,CAAC;AAC9C,IAAIc,aAAa,GAAGtB,eAAe,CAAC;EAClCoB,IAAI;EACJG,KAAK,EAAEV,YAAY;EACnBW,KAAK,EAAE,CAAC,mBAAmB,CAAC;EAC5BC,KAAKA,CAACF,KAAK,EAAE;IACXG,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,cAAc,GAAG1B,GAAG,CAAC,CAAC;IAC5B,MAAM2B,SAAS,GAAGR,GAAG,CAAC,MAAM,CAAC;IAC7B,MAAMS,KAAK,GAAG5B,GAAG,CAAC,CAAC,CAAC;IACpB,MAAM6B,YAAY,GAAG,EAAE;IACvB,MAAMC,iBAAiB,GAAGA,CAACC,IAAI,EAAEf,KAAK,GAAGK,KAAK,CAACL,KAAK,KAAK;MACvD,MAAMgB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAC3CF,IAAI,CAACL,SAAS,GAAGA,SAAS;MAC1BK,IAAI,CAACG,SAAS,GAAGC,MAAM,CAACL,IAAI,CAAC;MAC7BC,IAAI,CAACK,KAAK,CAACC,iBAAiB,GAAG,GAAGjB,KAAK,CAACP,QAAQ,IAAI;MACpDkB,IAAI,CAACK,KAAK,CAACE,cAAc,GAAG,GAAGvB,KAAK,IAAI;MACxCgB,IAAI,CAACK,KAAK,CAACG,aAAa,GAAG,aAAa;MACxCR,IAAI,CAACK,KAAK,CAACI,uBAAuB,GAAG,QAAQ;MAC7C,OAAOT,IAAI;IACb,CAAC;IACD,MAAMU,aAAa,GAAG1C,GAAG,CAAC,IAAI,CAAC;IAC/B,MAAM2C,MAAM,GAAG3C,GAAG,CAACqB,KAAK,CAACN,QAAQ,CAAC;IAClC,MAAM6B,iBAAiB,GAAGA,CAAC;MACzBC,EAAE;MACFd;IACF,CAAC,EAAEe,CAAC,KAAK;MACP,IAAIC,EAAE;MACN,MAAMf,IAAI,GAAGF,iBAAiB,CAACC,IAAI,EAAEW,aAAa,CAACtD,KAAK,GAAG0D,CAAC,GAAGzB,KAAK,CAACL,KAAK,GAAG,KAAK,CAAC,CAAC;MACpF,IAAI,CAACK,KAAK,CAACN,QAAQ,IAAI4B,MAAM,CAACvD,KAAK,KAAK,KAAK,EAAE;QAC7C4C,IAAI,CAACK,KAAK,CAACW,kBAAkB,GAAG,QAAQ;MAC1C;MACA,CAACD,EAAE,GAAGrB,cAAc,CAACtC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2D,EAAE,CAACE,MAAM,CAACjB,IAAI,CAAC;MAC9DJ,KAAK,CAACxC,KAAK,EAAE;MACb,MAAMwB,GAAG,GAAG,CAACgB,KAAK,CAACxC,KAAK,GAAG,CAAC,IAAI,CAACiC,KAAK,CAACR,IAAI,GAAGmB,IAAI,CAACkB,YAAY,GAAG,CAAC7B,KAAK,CAACT,GAAG;MAC5EoB,IAAI,CAACK,KAAK,CAACzB,GAAG,GAAG,GAAGA,GAAG,IAAI;MAC3BoB,IAAI,CAACmB,OAAO,CAACN,EAAE,GAAGT,MAAM,CAACS,EAAE,CAAC;MAC5BhB,YAAY,CAACuB,IAAI,CAACpB,IAAI,CAAC;MACvBA,IAAI,CAACqB,gBAAgB,CAAC,cAAc,EAAE,MAAM;QAC1C7B,IAAI,CAAC,mBAAmB,EAAE,CAAC,GAAGH,KAAK,CAACJ,UAAU,CAAC,CAACqC,MAAM,CAAEC,CAAC,IAAKnB,MAAM,CAACmB,CAAC,CAACV,EAAE,CAAC,KAAKb,IAAI,CAACmB,OAAO,CAACN,EAAE,CAAC,CAAC;MAClG,CAAC,CAAC;IACJ,CAAC;IACD,MAAMW,cAAc,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK;MAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACF,QAAQ,CAACC,GAAG,CAAE3B,IAAI,IAAK,CAACA,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC,CAAC,CAAC;MAC5DyB,QAAQ,CAACI,OAAO,CAAC,CAAC7B,IAAI,EAAEc,CAAC,KAAK;QAC5B,IAAIa,GAAG,CAACG,GAAG,CAAC9B,IAAI,CAACa,EAAE,CAAC,EAAE;UACpBc,GAAG,CAACI,MAAM,CAAC/B,IAAI,CAACa,EAAE,CAAC;QACrB,CAAC,MAAM;UACLD,iBAAiB,CAACZ,IAAI,EAAEc,CAAC,CAAC;QAC5B;MACF,CAAC,CAAC;MACFa,GAAG,CAACE,OAAO,CAAE7B,IAAI,IAAK;QACpB,MAAMgC,KAAK,GAAGnC,YAAY,CAACoC,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACf,OAAO,CAACN,EAAE,KAAKT,MAAM,CAACJ,IAAI,CAACa,EAAE,CAAC,CAAC;QACnF,IAAImB,KAAK,GAAG,CAAC,CAAC,EAAE;UACdnC,YAAY,CAACmC,KAAK,CAAC,CAACG,MAAM,CAAC,CAAC;UAC5BtC,YAAY,CAACuC,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC;MACFtB,aAAa,CAACtD,KAAK,GAAG,KAAK;IAC7B,CAAC;IACDc,KAAK,CAAC,MAAMmB,KAAK,CAACJ,UAAU,CAACoD,KAAK,CAAC,CAAC,EAAE,CAACZ,QAAQ,EAAEC,QAAQ,KAAKF,cAAc,CAACC,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,EAAE,EAAEC,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,EAAE,CAAC,EAAE;MAChJY,IAAI,EAAE;IACR,CAAC,CAAC;IACF,MAAMC,SAAS,GAAGvE,GAAG,CAAC,CAAC,CAAC,CAAC;IACzBD,SAAS,CAAC,MAAMnB,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa;MAC/C,IAAImE,EAAE;MACNwB,SAAS,CAACnF,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC2D,EAAE,GAAGrB,cAAc,CAACtC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2D,EAAE,CAACyB,WAAW,IAAI;MAC1G,MAAMvE,QAAQ,CAAC,CAAC;MAChBuD,cAAc,CAACnC,KAAK,CAACJ,UAAU,EAAE,EAAE,CAAC;IACtC,CAAC,CAAC,CAAC;IACH,MAAMwD,IAAI,GAAGA,CAAA,KAAM;MACjB9B,MAAM,CAACvD,KAAK,GAAG,IAAI;MACnByC,YAAY,CAACgC,OAAO,CAAE7B,IAAI,IAAK;QAC7BA,IAAI,CAACK,KAAK,CAACW,kBAAkB,GAAG,SAAS;MAC3C,CAAC,CAAC;IACJ,CAAC;IACD,MAAM0B,KAAK,GAAGA,CAAA,KAAM;MAClB/B,MAAM,CAACvD,KAAK,GAAG,KAAK;MACpByC,YAAY,CAACgC,OAAO,CAAE7B,IAAI,IAAK;QAC7BA,IAAI,CAACK,KAAK,CAACW,kBAAkB,GAAG,QAAQ;MAC1C,CAAC,CAAC;IACJ,CAAC;IACD3C,SAAS,CAAC;MACRoE,IAAI;MACJC;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAI3B,EAAE;MACN,OAAO3C,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAEe,GAAG,CAAC,CAAC;QACd,KAAK,EAAEO,cAAc;QACrB,OAAO,EAAE6C,SAAS,CAACnF;MACrB,CAAC,EAAE,CAAC,CAAC2D,EAAE,GAAGtB,KAAK,CAACkD,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG5B,EAAE,CAAC6B,IAAI,CAACnD,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEd,YAAY,EACZS,aAAa,IAAIuD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}