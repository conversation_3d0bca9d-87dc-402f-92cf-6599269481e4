{"ast": null, "code": "const BORDER = \"van-hairline\";\nconst BORDER_TOP = `${BORDE<PERSON>}--top`;\nconst BORDER_LEFT = `${BORDER}--left`;\nconst BORDER_RIGHT = `${BORDER}--right`;\nconst BORDER_BOTTOM = `${BORDER}--bottom`;\nconst BORDER_SURROUND = `${BORDER}--surround`;\nconst BORDER_TOP_BOTTOM = `${BORDER}--top-bottom`;\nconst BORDER_UNSET_TOP_BOTTOM = `${BORDER}-unset--top-bottom`;\nconst HAPTICS_FEEDBACK = \"van-haptics-feedback\";\nconst FORM_KEY = Symbol(\"van-form\");\nconst LONG_PRESS_START_TIME = 500;\nconst TAP_OFFSET = 5;\nexport { BORDER, BORDER_BOTTOM, BORDER_LEFT, BORDER_RIGHT, BORDER_SURROUND, BOR<PERSON>R_TOP, BORDER_TOP_BOTTOM, BOR<PERSON>R_UNSET_TOP_BOTTOM, FORM_KEY, HAPTICS_FEEDBACK, LONG_PRESS_START_TIME, TAP_OFFSET };", "map": {"version": 3, "names": ["BORDER", "BORDER_TOP", "BORDER_LEFT", "BORDER_RIGHT", "BORDER_BOTTOM", "BORDER_SURROUND", "BORDER_TOP_BOTTOM", "BORDER_UNSET_TOP_BOTTOM", "HAPTICS_FEEDBACK", "FORM_KEY", "Symbol", "LONG_PRESS_START_TIME", "TAP_OFFSET"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/utils/constant.mjs"], "sourcesContent": ["const BORDER = \"van-hairline\";\nconst BORDER_TOP = `${BORDE<PERSON>}--top`;\nconst BORDER_LEFT = `${BORDER}--left`;\nconst BORDER_RIGHT = `${BORDER}--right`;\nconst BORDER_BOTTOM = `${BORDER}--bottom`;\nconst BORDER_SURROUND = `${BORDER}--surround`;\nconst BORDER_TOP_BOTTOM = `${BORDER}--top-bottom`;\nconst BORDER_UNSET_TOP_BOTTOM = `${BORDER}-unset--top-bottom`;\nconst HAPTICS_FEEDBACK = \"van-haptics-feedback\";\nconst FORM_KEY = Symbol(\"van-form\");\nconst LONG_PRESS_START_TIME = 500;\nconst TAP_OFFSET = 5;\nexport {\n  BORDER,\n  BORDER_BOTTOM,\n  BORDER_LEFT,\n  BORDER_RIGHT,\n  BORDER_SURROUND,\n  BOR<PERSON>R_TOP,\n  BORDER_TOP_BOTTOM,\n  BOR<PERSON>R_UNSET_TOP_BOTTOM,\n  FORM_KEY,\n  HAPTICS_FEEDBACK,\n  LONG_PRESS_START_TIME,\n  TAP_OFFSET\n};\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG,cAAc;AAC7B,MAAMC,UAAU,GAAG,GAAGD,MAAM,OAAO;AACnC,MAAME,WAAW,GAAG,GAAGF,MAAM,QAAQ;AACrC,MAAMG,YAAY,GAAG,GAAGH,MAAM,SAAS;AACvC,MAAMI,aAAa,GAAG,GAAGJ,MAAM,UAAU;AACzC,MAAMK,eAAe,GAAG,GAAGL,MAAM,YAAY;AAC7C,MAAMM,iBAAiB,GAAG,GAAGN,MAAM,cAAc;AACjD,MAAMO,uBAAuB,GAAG,GAAGP,MAAM,oBAAoB;AAC7D,MAAMQ,gBAAgB,GAAG,sBAAsB;AAC/C,MAAMC,QAAQ,GAAGC,MAAM,CAAC,UAAU,CAAC;AACnC,MAAMC,qBAAqB,GAAG,GAAG;AACjC,MAAMC,UAAU,GAAG,CAAC;AACpB,SACEZ,MAAM,EACNI,aAAa,EACbF,WAAW,EACXC,YAAY,EACZE,eAAe,EACfJ,UAAU,EACVK,iBAAiB,EACjBC,uBAAuB,EACvBE,QAAQ,EACRD,gBAAgB,EAChBG,qBAAqB,EACrBC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}