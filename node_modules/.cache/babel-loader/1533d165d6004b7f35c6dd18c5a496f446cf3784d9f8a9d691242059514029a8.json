{"ast": null, "code": "import { inBrowser } from \"./basic.mjs\";\nimport { windowWidth, windowHeight } from \"./dom.mjs\";\nimport { isDef, isNumeric } from \"./basic.mjs\";\nfunction addUnit(value) {\n  if (isDef(value)) {\n    return isNumeric(value) ? `${value}px` : String(value);\n  }\n  return void 0;\n}\nfunction getSizeStyle(originSize) {\n  if (isDef(originSize)) {\n    if (Array.isArray(originSize)) {\n      return {\n        width: addUnit(originSize[0]),\n        height: addUnit(originSize[1])\n      };\n    }\n    const size = addUnit(originSize);\n    return {\n      width: size,\n      height: size\n    };\n  }\n}\nfunction getZIndexStyle(zIndex) {\n  const style = {};\n  if (zIndex !== void 0) {\n    style.zIndex = +zIndex;\n  }\n  return style;\n}\nlet rootFontSize;\nfunction getRootFontSize() {\n  if (!rootFontSize) {\n    const doc = document.documentElement;\n    const fontSize = doc.style.fontSize || window.getComputedStyle(doc).fontSize;\n    rootFontSize = parseFloat(fontSize);\n  }\n  return rootFontSize;\n}\nfunction convertRem(value) {\n  value = value.replace(/rem/g, \"\");\n  return +value * getRootFontSize();\n}\nfunction convertVw(value) {\n  value = value.replace(/vw/g, \"\");\n  return +value * windowWidth.value / 100;\n}\nfunction convertVh(value) {\n  value = value.replace(/vh/g, \"\");\n  return +value * windowHeight.value / 100;\n}\nfunction unitToPx(value) {\n  if (typeof value === \"number\") {\n    return value;\n  }\n  if (inBrowser) {\n    if (value.includes(\"rem\")) {\n      return convertRem(value);\n    }\n    if (value.includes(\"vw\")) {\n      return convertVw(value);\n    }\n    if (value.includes(\"vh\")) {\n      return convertVh(value);\n    }\n  }\n  return parseFloat(value);\n}\nconst camelizeRE = /-(\\w)/g;\nconst camelize = str => str.replace(camelizeRE, (_, c) => c.toUpperCase());\nconst kebabCase = str => str.replace(/([A-Z])/g, \"-$1\").toLowerCase().replace(/^-/, \"\");\nfunction padZero(num, targetLength = 2) {\n  let str = num + \"\";\n  while (str.length < targetLength) {\n    str = \"0\" + str;\n  }\n  return str;\n}\nconst clamp = (num, min, max) => Math.min(Math.max(num, min), max);\nfunction trimExtraChar(value, char, regExp) {\n  const index = value.indexOf(char);\n  if (index === -1) {\n    return value;\n  }\n  if (char === \"-\" && index !== 0) {\n    return value.slice(0, index);\n  }\n  return value.slice(0, index + 1) + value.slice(index).replace(regExp, \"\");\n}\nfunction formatNumber(value, allowDot = true, allowMinus = true) {\n  if (allowDot) {\n    value = trimExtraChar(value, \".\", /\\./g);\n  } else {\n    value = value.split(\".\")[0];\n  }\n  if (allowMinus) {\n    value = trimExtraChar(value, \"-\", /-/g);\n  } else {\n    value = value.replace(/-/, \"\");\n  }\n  const regExp = allowDot ? /[^-0-9.]/g : /[^-0-9]/g;\n  return value.replace(regExp, \"\");\n}\nfunction addNumber(num1, num2) {\n  const cardinal = 10 ** 10;\n  return Math.round((num1 + num2) * cardinal) / cardinal;\n}\nexport { addNumber, addUnit, camelize, clamp, formatNumber, getSizeStyle, getZIndexStyle, kebabCase, padZero, unitToPx };", "map": {"version": 3, "names": ["inBrowser", "windowWidth", "windowHeight", "isDef", "isNumeric", "addUnit", "value", "String", "getSizeStyle", "originSize", "Array", "isArray", "width", "height", "size", "getZIndexStyle", "zIndex", "style", "rootFontSize", "getRootFontSize", "doc", "document", "documentElement", "fontSize", "window", "getComputedStyle", "parseFloat", "convertRem", "replace", "convertVw", "convertVh", "unitToPx", "includes", "camelizeRE", "camelize", "str", "_", "c", "toUpperCase", "kebabCase", "toLowerCase", "padZero", "num", "targetLength", "length", "clamp", "min", "max", "Math", "trimExtraChar", "char", "regExp", "index", "indexOf", "slice", "formatNumber", "allowDot", "allowMinus", "split", "addNumber", "num1", "num2", "cardinal", "round"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/utils/format.mjs"], "sourcesContent": ["import { inBrowser } from \"./basic.mjs\";\nimport { windowWidth, windowHeight } from \"./dom.mjs\";\nimport { isDef, isNumeric } from \"./basic.mjs\";\nfunction addUnit(value) {\n  if (isDef(value)) {\n    return isNumeric(value) ? `${value}px` : String(value);\n  }\n  return void 0;\n}\nfunction getSizeStyle(originSize) {\n  if (isDef(originSize)) {\n    if (Array.isArray(originSize)) {\n      return {\n        width: addUnit(originSize[0]),\n        height: addUnit(originSize[1])\n      };\n    }\n    const size = addUnit(originSize);\n    return {\n      width: size,\n      height: size\n    };\n  }\n}\nfunction getZIndexStyle(zIndex) {\n  const style = {};\n  if (zIndex !== void 0) {\n    style.zIndex = +zIndex;\n  }\n  return style;\n}\nlet rootFontSize;\nfunction getRootFontSize() {\n  if (!rootFontSize) {\n    const doc = document.documentElement;\n    const fontSize = doc.style.fontSize || window.getComputedStyle(doc).fontSize;\n    rootFontSize = parseFloat(fontSize);\n  }\n  return rootFontSize;\n}\nfunction convertRem(value) {\n  value = value.replace(/rem/g, \"\");\n  return +value * getRootFontSize();\n}\nfunction convertVw(value) {\n  value = value.replace(/vw/g, \"\");\n  return +value * windowWidth.value / 100;\n}\nfunction convertVh(value) {\n  value = value.replace(/vh/g, \"\");\n  return +value * windowHeight.value / 100;\n}\nfunction unitToPx(value) {\n  if (typeof value === \"number\") {\n    return value;\n  }\n  if (inBrowser) {\n    if (value.includes(\"rem\")) {\n      return convertRem(value);\n    }\n    if (value.includes(\"vw\")) {\n      return convertVw(value);\n    }\n    if (value.includes(\"vh\")) {\n      return convertVh(value);\n    }\n  }\n  return parseFloat(value);\n}\nconst camelizeRE = /-(\\w)/g;\nconst camelize = (str) => str.replace(camelizeRE, (_, c) => c.toUpperCase());\nconst kebabCase = (str) => str.replace(/([A-Z])/g, \"-$1\").toLowerCase().replace(/^-/, \"\");\nfunction padZero(num, targetLength = 2) {\n  let str = num + \"\";\n  while (str.length < targetLength) {\n    str = \"0\" + str;\n  }\n  return str;\n}\nconst clamp = (num, min, max) => Math.min(Math.max(num, min), max);\nfunction trimExtraChar(value, char, regExp) {\n  const index = value.indexOf(char);\n  if (index === -1) {\n    return value;\n  }\n  if (char === \"-\" && index !== 0) {\n    return value.slice(0, index);\n  }\n  return value.slice(0, index + 1) + value.slice(index).replace(regExp, \"\");\n}\nfunction formatNumber(value, allowDot = true, allowMinus = true) {\n  if (allowDot) {\n    value = trimExtraChar(value, \".\", /\\./g);\n  } else {\n    value = value.split(\".\")[0];\n  }\n  if (allowMinus) {\n    value = trimExtraChar(value, \"-\", /-/g);\n  } else {\n    value = value.replace(/-/, \"\");\n  }\n  const regExp = allowDot ? /[^-0-9.]/g : /[^-0-9]/g;\n  return value.replace(regExp, \"\");\n}\nfunction addNumber(num1, num2) {\n  const cardinal = 10 ** 10;\n  return Math.round((num1 + num2) * cardinal) / cardinal;\n}\nexport {\n  addNumber,\n  addUnit,\n  camelize,\n  clamp,\n  formatNumber,\n  getSizeStyle,\n  getZIndexStyle,\n  kebabCase,\n  padZero,\n  unitToPx\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,WAAW,EAAEC,YAAY,QAAQ,WAAW;AACrD,SAASC,KAAK,EAAEC,SAAS,QAAQ,aAAa;AAC9C,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,IAAIH,KAAK,CAACG,KAAK,CAAC,EAAE;IAChB,OAAOF,SAAS,CAACE,KAAK,CAAC,GAAG,GAAGA,KAAK,IAAI,GAAGC,MAAM,CAACD,KAAK,CAAC;EACxD;EACA,OAAO,KAAK,CAAC;AACf;AACA,SAASE,YAAYA,CAACC,UAAU,EAAE;EAChC,IAAIN,KAAK,CAACM,UAAU,CAAC,EAAE;IACrB,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7B,OAAO;QACLG,KAAK,EAAEP,OAAO,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC;QAC7BI,MAAM,EAAER,OAAO,CAACI,UAAU,CAAC,CAAC,CAAC;MAC/B,CAAC;IACH;IACA,MAAMK,IAAI,GAAGT,OAAO,CAACI,UAAU,CAAC;IAChC,OAAO;MACLG,KAAK,EAAEE,IAAI;MACXD,MAAM,EAAEC;IACV,CAAC;EACH;AACF;AACA,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC9B,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,IAAID,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBC,KAAK,CAACD,MAAM,GAAG,CAACA,MAAM;EACxB;EACA,OAAOC,KAAK;AACd;AACA,IAAIC,YAAY;AAChB,SAASC,eAAeA,CAAA,EAAG;EACzB,IAAI,CAACD,YAAY,EAAE;IACjB,MAAME,GAAG,GAAGC,QAAQ,CAACC,eAAe;IACpC,MAAMC,QAAQ,GAAGH,GAAG,CAACH,KAAK,CAACM,QAAQ,IAAIC,MAAM,CAACC,gBAAgB,CAACL,GAAG,CAAC,CAACG,QAAQ;IAC5EL,YAAY,GAAGQ,UAAU,CAACH,QAAQ,CAAC;EACrC;EACA,OAAOL,YAAY;AACrB;AACA,SAASS,UAAUA,CAACrB,KAAK,EAAE;EACzBA,KAAK,GAAGA,KAAK,CAACsB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EACjC,OAAO,CAACtB,KAAK,GAAGa,eAAe,CAAC,CAAC;AACnC;AACA,SAASU,SAASA,CAACvB,KAAK,EAAE;EACxBA,KAAK,GAAGA,KAAK,CAACsB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAChC,OAAO,CAACtB,KAAK,GAAGL,WAAW,CAACK,KAAK,GAAG,GAAG;AACzC;AACA,SAASwB,SAASA,CAACxB,KAAK,EAAE;EACxBA,KAAK,GAAGA,KAAK,CAACsB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAChC,OAAO,CAACtB,KAAK,GAAGJ,YAAY,CAACI,KAAK,GAAG,GAAG;AAC1C;AACA,SAASyB,QAAQA,CAACzB,KAAK,EAAE;EACvB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA,IAAIN,SAAS,EAAE;IACb,IAAIM,KAAK,CAAC0B,QAAQ,CAAC,KAAK,CAAC,EAAE;MACzB,OAAOL,UAAU,CAACrB,KAAK,CAAC;IAC1B;IACA,IAAIA,KAAK,CAAC0B,QAAQ,CAAC,IAAI,CAAC,EAAE;MACxB,OAAOH,SAAS,CAACvB,KAAK,CAAC;IACzB;IACA,IAAIA,KAAK,CAAC0B,QAAQ,CAAC,IAAI,CAAC,EAAE;MACxB,OAAOF,SAAS,CAACxB,KAAK,CAAC;IACzB;EACF;EACA,OAAOoB,UAAU,CAACpB,KAAK,CAAC;AAC1B;AACA,MAAM2B,UAAU,GAAG,QAAQ;AAC3B,MAAMC,QAAQ,GAAIC,GAAG,IAAKA,GAAG,CAACP,OAAO,CAACK,UAAU,EAAE,CAACG,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;AAC5E,MAAMC,SAAS,GAAIJ,GAAG,IAAKA,GAAG,CAACP,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACY,WAAW,CAAC,CAAC,CAACZ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;AACzF,SAASa,OAAOA,CAACC,GAAG,EAAEC,YAAY,GAAG,CAAC,EAAE;EACtC,IAAIR,GAAG,GAAGO,GAAG,GAAG,EAAE;EAClB,OAAOP,GAAG,CAACS,MAAM,GAAGD,YAAY,EAAE;IAChCR,GAAG,GAAG,GAAG,GAAGA,GAAG;EACjB;EACA,OAAOA,GAAG;AACZ;AACA,MAAMU,KAAK,GAAGA,CAACH,GAAG,EAAEI,GAAG,EAAEC,GAAG,KAAKC,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACL,GAAG,EAAEI,GAAG,CAAC,EAAEC,GAAG,CAAC;AAClE,SAASE,aAAaA,CAAC3C,KAAK,EAAE4C,IAAI,EAAEC,MAAM,EAAE;EAC1C,MAAMC,KAAK,GAAG9C,KAAK,CAAC+C,OAAO,CAACH,IAAI,CAAC;EACjC,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,OAAO9C,KAAK;EACd;EACA,IAAI4C,IAAI,KAAK,GAAG,IAAIE,KAAK,KAAK,CAAC,EAAE;IAC/B,OAAO9C,KAAK,CAACgD,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC;EAC9B;EACA,OAAO9C,KAAK,CAACgD,KAAK,CAAC,CAAC,EAAEF,KAAK,GAAG,CAAC,CAAC,GAAG9C,KAAK,CAACgD,KAAK,CAACF,KAAK,CAAC,CAACxB,OAAO,CAACuB,MAAM,EAAE,EAAE,CAAC;AAC3E;AACA,SAASI,YAAYA,CAACjD,KAAK,EAAEkD,QAAQ,GAAG,IAAI,EAAEC,UAAU,GAAG,IAAI,EAAE;EAC/D,IAAID,QAAQ,EAAE;IACZlD,KAAK,GAAG2C,aAAa,CAAC3C,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC;EAC1C,CAAC,MAAM;IACLA,KAAK,GAAGA,KAAK,CAACoD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7B;EACA,IAAID,UAAU,EAAE;IACdnD,KAAK,GAAG2C,aAAa,CAAC3C,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC;EACzC,CAAC,MAAM;IACLA,KAAK,GAAGA,KAAK,CAACsB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAChC;EACA,MAAMuB,MAAM,GAAGK,QAAQ,GAAG,WAAW,GAAG,UAAU;EAClD,OAAOlD,KAAK,CAACsB,OAAO,CAACuB,MAAM,EAAE,EAAE,CAAC;AAClC;AACA,SAASQ,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC7B,MAAMC,QAAQ,GAAG,EAAE,IAAI,EAAE;EACzB,OAAOd,IAAI,CAACe,KAAK,CAAC,CAACH,IAAI,GAAGC,IAAI,IAAIC,QAAQ,CAAC,GAAGA,QAAQ;AACxD;AACA,SACEH,SAAS,EACTtD,OAAO,EACP6B,QAAQ,EACRW,KAAK,EACLU,YAAY,EACZ/C,YAAY,EACZO,cAAc,EACdwB,SAAS,EACTE,OAAO,EACPV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}