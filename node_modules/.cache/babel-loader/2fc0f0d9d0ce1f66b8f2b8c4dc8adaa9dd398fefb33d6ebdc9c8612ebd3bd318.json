{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Tabbar from \"./Tabbar.mjs\";\nconst Tabbar = withInstall(_Tabbar);\nvar stdin_default = Tabbar;\nimport { tabbarProps } from \"./Tabbar.mjs\";\nexport { Tabbar, stdin_default as default, tabbarProps };", "map": {"version": 3, "names": ["withInstall", "_<PERSON><PERSON>r", "Ta<PERSON><PERSON>", "stdin_default", "tabbarProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tabbar/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Tabbar from \"./Tabbar.mjs\";\nconst Tabbar = withInstall(_Tabbar);\nvar stdin_default = Tabbar;\nimport { tabbarProps } from \"./Tabbar.mjs\";\nexport {\n  Tabbar,\n  stdin_default as default,\n  tabbarProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,MAAMC,MAAM,GAAGF,WAAW,CAACC,OAAO,CAAC;AACnC,IAAIE,aAAa,GAAGD,MAAM;AAC1B,SAASE,WAAW,QAAQ,cAAc;AAC1C,SACEF,MAAM,EACNC,aAAa,IAAIE,OAAO,EACxBD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}