{"ast": null, "code": "import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { makeStringProp, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { getDate, formatAmount, formatDiscount } from \"./utils.mjs\";\nimport { Checkbox } from \"../checkbox/index.mjs\";\nconst [name, bem, t] = createNamespace(\"coupon\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    chosen: Boolean,\n    coupon: makeRequiredProp(Object),\n    disabled: Boolean,\n    currency: makeStringProp(\"\\xA5\")\n  },\n  setup(props) {\n    const validPeriod = computed(() => {\n      const {\n        startAt,\n        endAt\n      } = props.coupon;\n      return `${getDate(startAt)} - ${getDate(endAt)}`;\n    });\n    const faceAmount = computed(() => {\n      const {\n        coupon,\n        currency\n      } = props;\n      if (coupon.valueDesc) {\n        return [coupon.valueDesc, _createVNode(\"span\", null, [coupon.unitDesc || \"\"])];\n      }\n      if (coupon.denominations) {\n        const denominations = formatAmount(coupon.denominations);\n        return [_createVNode(\"span\", null, [currency]), ` ${denominations}`];\n      }\n      if (coupon.discount) {\n        return t(\"discount\", formatDiscount(coupon.discount));\n      }\n      return \"\";\n    });\n    const conditionMessage = computed(() => {\n      const condition = formatAmount(props.coupon.originCondition || 0);\n      return condition === \"0\" ? t(\"unlimited\") : t(\"condition\", condition);\n    });\n    return () => {\n      const {\n        chosen,\n        coupon,\n        disabled\n      } = props;\n      const description = disabled && coupon.reason || coupon.description;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          disabled\n        })\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"head\")\n      }, [_createVNode(\"h2\", {\n        \"class\": bem(\"amount\")\n      }, [faceAmount.value]), _createVNode(\"p\", {\n        \"class\": bem(\"condition\")\n      }, [coupon.condition || conditionMessage.value])]), _createVNode(\"div\", {\n        \"class\": bem(\"body\")\n      }, [_createVNode(\"p\", {\n        \"class\": bem(\"name\")\n      }, [coupon.name]), _createVNode(\"p\", {\n        \"class\": bem(\"valid\")\n      }, [validPeriod.value]), !disabled && _createVNode(Checkbox, {\n        \"class\": bem(\"corner\"),\n        \"modelValue\": chosen\n      }, null)])]), description && _createVNode(\"p\", {\n        \"class\": bem(\"description\")\n      }, [description])]);\n    };\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["computed", "defineComponent", "createVNode", "_createVNode", "makeStringProp", "createNamespace", "makeRequiredProp", "getDate", "formatAmount", "formatDiscount", "Checkbox", "name", "bem", "t", "stdin_default", "props", "chosen", "Boolean", "coupon", "Object", "disabled", "currency", "setup", "validPeriod", "startAt", "endAt", "faceAmount", "valueDesc", "unitDesc", "denominations", "discount", "conditionMessage", "condition", "originCondition", "description", "reason", "value", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/coupon/Coupon.mjs"], "sourcesContent": ["import { computed, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { makeStringProp, createNamespace, makeRequiredProp } from \"../utils/index.mjs\";\nimport { getDate, formatAmount, formatDiscount } from \"./utils.mjs\";\nimport { Checkbox } from \"../checkbox/index.mjs\";\nconst [name, bem, t] = createNamespace(\"coupon\");\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    chosen: Boolean,\n    coupon: makeRequiredProp(Object),\n    disabled: Boolean,\n    currency: makeStringProp(\"\\xA5\")\n  },\n  setup(props) {\n    const validPeriod = computed(() => {\n      const {\n        startAt,\n        endAt\n      } = props.coupon;\n      return `${getDate(startAt)} - ${getDate(endAt)}`;\n    });\n    const faceAmount = computed(() => {\n      const {\n        coupon,\n        currency\n      } = props;\n      if (coupon.valueDesc) {\n        return [coupon.valueDesc, _createVNode(\"span\", null, [coupon.unitDesc || \"\"])];\n      }\n      if (coupon.denominations) {\n        const denominations = formatAmount(coupon.denominations);\n        return [_createVNode(\"span\", null, [currency]), ` ${denominations}`];\n      }\n      if (coupon.discount) {\n        return t(\"discount\", formatDiscount(coupon.discount));\n      }\n      return \"\";\n    });\n    const conditionMessage = computed(() => {\n      const condition = formatAmount(props.coupon.originCondition || 0);\n      return condition === \"0\" ? t(\"unlimited\") : t(\"condition\", condition);\n    });\n    return () => {\n      const {\n        chosen,\n        coupon,\n        disabled\n      } = props;\n      const description = disabled && coupon.reason || coupon.description;\n      return _createVNode(\"div\", {\n        \"class\": bem({\n          disabled\n        })\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"content\")\n      }, [_createVNode(\"div\", {\n        \"class\": bem(\"head\")\n      }, [_createVNode(\"h2\", {\n        \"class\": bem(\"amount\")\n      }, [faceAmount.value]), _createVNode(\"p\", {\n        \"class\": bem(\"condition\")\n      }, [coupon.condition || conditionMessage.value])]), _createVNode(\"div\", {\n        \"class\": bem(\"body\")\n      }, [_createVNode(\"p\", {\n        \"class\": bem(\"name\")\n      }, [coupon.name]), _createVNode(\"p\", {\n        \"class\": bem(\"valid\")\n      }, [validPeriod.value]), !disabled && _createVNode(Checkbox, {\n        \"class\": bem(\"corner\"),\n        \"modelValue\": chosen\n      }, null)])]), description && _createVNode(\"p\", {\n        \"class\": bem(\"description\")\n      }, [description])]);\n    };\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAC5E,SAASC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AACtF,SAASC,OAAO,EAAEC,YAAY,EAAEC,cAAc,QAAQ,aAAa;AACnE,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,MAAM,CAACC,IAAI,EAAEC,GAAG,EAAEC,CAAC,CAAC,GAAGR,eAAe,CAAC,QAAQ,CAAC;AAChD,IAAIS,aAAa,GAAGb,eAAe,CAAC;EAClCU,IAAI;EACJI,KAAK,EAAE;IACLC,MAAM,EAAEC,OAAO;IACfC,MAAM,EAAEZ,gBAAgB,CAACa,MAAM,CAAC;IAChCC,QAAQ,EAAEH,OAAO;IACjBI,QAAQ,EAAEjB,cAAc,CAAC,MAAM;EACjC,CAAC;EACDkB,KAAKA,CAACP,KAAK,EAAE;IACX,MAAMQ,WAAW,GAAGvB,QAAQ,CAAC,MAAM;MACjC,MAAM;QACJwB,OAAO;QACPC;MACF,CAAC,GAAGV,KAAK,CAACG,MAAM;MAChB,OAAO,GAAGX,OAAO,CAACiB,OAAO,CAAC,MAAMjB,OAAO,CAACkB,KAAK,CAAC,EAAE;IAClD,CAAC,CAAC;IACF,MAAMC,UAAU,GAAG1B,QAAQ,CAAC,MAAM;MAChC,MAAM;QACJkB,MAAM;QACNG;MACF,CAAC,GAAGN,KAAK;MACT,IAAIG,MAAM,CAACS,SAAS,EAAE;QACpB,OAAO,CAACT,MAAM,CAACS,SAAS,EAAExB,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACe,MAAM,CAACU,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;MAChF;MACA,IAAIV,MAAM,CAACW,aAAa,EAAE;QACxB,MAAMA,aAAa,GAAGrB,YAAY,CAACU,MAAM,CAACW,aAAa,CAAC;QACxD,OAAO,CAAC1B,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAACkB,QAAQ,CAAC,CAAC,EAAE,IAAIQ,aAAa,EAAE,CAAC;MACtE;MACA,IAAIX,MAAM,CAACY,QAAQ,EAAE;QACnB,OAAOjB,CAAC,CAAC,UAAU,EAAEJ,cAAc,CAACS,MAAM,CAACY,QAAQ,CAAC,CAAC;MACvD;MACA,OAAO,EAAE;IACX,CAAC,CAAC;IACF,MAAMC,gBAAgB,GAAG/B,QAAQ,CAAC,MAAM;MACtC,MAAMgC,SAAS,GAAGxB,YAAY,CAACO,KAAK,CAACG,MAAM,CAACe,eAAe,IAAI,CAAC,CAAC;MACjE,OAAOD,SAAS,KAAK,GAAG,GAAGnB,CAAC,CAAC,WAAW,CAAC,GAAGA,CAAC,CAAC,WAAW,EAAEmB,SAAS,CAAC;IACvE,CAAC,CAAC;IACF,OAAO,MAAM;MACX,MAAM;QACJhB,MAAM;QACNE,MAAM;QACNE;MACF,CAAC,GAAGL,KAAK;MACT,MAAMmB,WAAW,GAAGd,QAAQ,IAAIF,MAAM,CAACiB,MAAM,IAAIjB,MAAM,CAACgB,WAAW;MACnE,OAAO/B,YAAY,CAAC,KAAK,EAAE;QACzB,OAAO,EAAES,GAAG,CAAC;UACXQ;QACF,CAAC;MACH,CAAC,EAAE,CAACjB,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAES,GAAG,CAAC,SAAS;MACxB,CAAC,EAAE,CAACT,YAAY,CAAC,KAAK,EAAE;QACtB,OAAO,EAAES,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACT,YAAY,CAAC,IAAI,EAAE;QACrB,OAAO,EAAES,GAAG,CAAC,QAAQ;MACvB,CAAC,EAAE,CAACc,UAAU,CAACU,KAAK,CAAC,CAAC,EAAEjC,YAAY,CAAC,GAAG,EAAE;QACxC,OAAO,EAAES,GAAG,CAAC,WAAW;MAC1B,CAAC,EAAE,CAACM,MAAM,CAACc,SAAS,IAAID,gBAAgB,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEjC,YAAY,CAAC,KAAK,EAAE;QACtE,OAAO,EAAES,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACT,YAAY,CAAC,GAAG,EAAE;QACpB,OAAO,EAAES,GAAG,CAAC,MAAM;MACrB,CAAC,EAAE,CAACM,MAAM,CAACP,IAAI,CAAC,CAAC,EAAER,YAAY,CAAC,GAAG,EAAE;QACnC,OAAO,EAAES,GAAG,CAAC,OAAO;MACtB,CAAC,EAAE,CAACW,WAAW,CAACa,KAAK,CAAC,CAAC,EAAE,CAAChB,QAAQ,IAAIjB,YAAY,CAACO,QAAQ,EAAE;QAC3D,OAAO,EAAEE,GAAG,CAAC,QAAQ,CAAC;QACtB,YAAY,EAAEI;MAChB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEkB,WAAW,IAAI/B,YAAY,CAAC,GAAG,EAAE;QAC7C,OAAO,EAAES,GAAG,CAAC,aAAa;MAC5B,CAAC,EAAE,CAACsB,WAAW,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACEpB,aAAa,IAAIuB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}