{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _SwipeCell from \"./SwipeCell.mjs\";\nconst SwipeCell = withInstall(_SwipeCell);\nvar stdin_default = SwipeCell;\nimport { swipeCellProps } from \"./SwipeCell.mjs\";\nexport { SwipeCell, stdin_default as default, swipeCellProps };", "map": {"version": 3, "names": ["withInstall", "_Swipe<PERSON>ell", "Swi<PERSON><PERSON>ell", "stdin_default", "swipeCellProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/swipe-cell/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _SwipeCell from \"./SwipeCell.mjs\";\nconst SwipeCell = withInstall(_SwipeCell);\nvar stdin_default = SwipeCell;\nimport { swipeCellProps } from \"./SwipeCell.mjs\";\nexport {\n  SwipeCell,\n  stdin_default as default,\n  swipeCellProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,SAAS,GAAGF,WAAW,CAACC,UAAU,CAAC;AACzC,IAAIE,aAAa,GAAGD,SAAS;AAC7B,SAASE,cAAc,QAAQ,iBAAiB;AAChD,SACEF,SAAS,EACTC,aAAa,IAAIE,OAAO,EACxBD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}