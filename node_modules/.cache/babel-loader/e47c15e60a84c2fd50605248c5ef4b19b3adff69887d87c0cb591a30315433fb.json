{"ast": null, "code": "import _Skeleton from \"./Skeleton.mjs\";\nimport { withInstall } from \"../utils/index.mjs\";\nconst Skeleton = withInstall(_Skeleton);\nvar stdin_default = Skeleton;\nimport { skeletonProps } from \"./Skeleton.mjs\";\nexport { Skeleton, stdin_default as default, skeletonProps };", "map": {"version": 3, "names": ["_Skeleton", "withInstall", "Skeleton", "stdin_default", "skeletonProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/skeleton/index.mjs"], "sourcesContent": ["import _Skeleton from \"./Skeleton.mjs\";\nimport { withInstall } from \"../utils/index.mjs\";\nconst Skeleton = withInstall(_Skeleton);\nvar stdin_default = Skeleton;\nimport { skeletonProps } from \"./Skeleton.mjs\";\nexport {\n  Skeleton,\n  stdin_default as default,\n  skeletonProps\n};\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,SAASC,WAAW,QAAQ,oBAAoB;AAChD,MAAMC,QAAQ,GAAGD,WAAW,CAACD,SAAS,CAAC;AACvC,IAAIG,aAAa,GAAGD,QAAQ;AAC5B,SAASE,aAAa,QAAQ,gBAAgB;AAC9C,SACEF,QAAQ,EACRC,aAAa,IAAIE,OAAO,EACxBD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}