{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Icon from \"./Icon.mjs\";\nconst Icon = withInstall(_Icon);\nvar stdin_default = Icon;\nimport { iconProps } from \"./Icon.mjs\";\nexport { Icon, stdin_default as default, iconProps };", "map": {"version": 3, "names": ["withInstall", "_Icon", "Icon", "stdin_default", "iconProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/icon/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Icon from \"./Icon.mjs\";\nconst Icon = withInstall(_Icon);\nvar stdin_default = Icon;\nimport { iconProps } from \"./Icon.mjs\";\nexport {\n  Icon,\n  stdin_default as default,\n  iconProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACC,KAAK,CAAC;AAC/B,IAAIE,aAAa,GAAGD,IAAI;AACxB,SAASE,SAAS,QAAQ,YAAY;AACtC,SACEF,IAAI,EACJC,aAAa,IAAIE,OAAO,EACxBD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}