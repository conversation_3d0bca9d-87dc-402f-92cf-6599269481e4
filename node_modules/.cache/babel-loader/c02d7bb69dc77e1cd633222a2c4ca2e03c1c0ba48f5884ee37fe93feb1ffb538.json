{"ast": null, "code": "import { getCurrentInstance } from \"vue\";\nlet current = 0;\nfunction useId() {\n  const vm = getCurrentInstance();\n  const {\n    name = \"unknown\"\n  } = (vm == null ? void 0 : vm.type) || {};\n  if (process.env.NODE_ENV === \"test\") {\n    return name;\n  }\n  return `${name}-${++current}`;\n}\nexport { useId };", "map": {"version": 3, "names": ["getCurrentInstance", "current", "useId", "vm", "name", "type", "process", "env", "NODE_ENV"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/ui/node_modules/vant/es/composables/use-id.mjs"], "sourcesContent": ["import { getCurrentInstance } from \"vue\";\nlet current = 0;\nfunction useId() {\n  const vm = getCurrentInstance();\n  const { name = \"unknown\" } = (vm == null ? void 0 : vm.type) || {};\n  if (process.env.NODE_ENV === \"test\") {\n    return name;\n  }\n  return `${name}-${++current}`;\n}\nexport {\n  useId\n};\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,KAAK;AACxC,IAAIC,OAAO,GAAG,CAAC;AACf,SAASC,KAAKA,CAAA,EAAG;EACf,MAAMC,EAAE,GAAGH,kBAAkB,CAAC,CAAC;EAC/B,MAAM;IAAEI,IAAI,GAAG;EAAU,CAAC,GAAG,CAACD,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,KAAK,CAAC,CAAC;EAClE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;IACnC,OAAOJ,IAAI;EACb;EACA,OAAO,GAAGA,IAAI,IAAI,EAAEH,OAAO,EAAE;AAC/B;AACA,SACEC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}