{"ast": null, "code": "import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, numericProp } from \"../utils/index.mjs\";\nconst DEFAULT_ROW_WIDTH = \"100%\";\nconst skeletonParagraphProps = {\n  round: <PERSON><PERSON>an,\n  rowWidth: {\n    type: numericProp,\n    default: DEFAULT_ROW_WIDTH\n  }\n};\nconst [name, bem] = createNamespace(\"skeleton-paragraph\");\nvar stdin_default = defineComponent({\n  name,\n  props: skeletonParagraphProps,\n  setup(props) {\n    return () => _createVNode(\"div\", {\n      \"class\": bem([{\n        round: props.round\n      }]),\n      \"style\": {\n        width: props.rowWidth\n      }\n    }, null);\n  }\n});\nexport { DEFAULT_ROW_WIDTH, stdin_default as default, skeletonParagraphProps };", "map": {"version": 3, "names": ["defineComponent", "createVNode", "_createVNode", "createNamespace", "numericProp", "DEFAULT_ROW_WIDTH", "skeletonParagraphProps", "round", "Boolean", "row<PERSON>id<PERSON>", "type", "default", "name", "bem", "stdin_default", "props", "setup", "width"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/skeleton-paragraph/SkeletonParagraph.mjs"], "sourcesContent": ["import { defineComponent, createVNode as _createVNode } from \"vue\";\nimport { createNamespace, numericProp } from \"../utils/index.mjs\";\nconst DEFAULT_ROW_WIDTH = \"100%\";\nconst skeletonParagraphProps = {\n  round: <PERSON><PERSON>an,\n  rowWidth: {\n    type: numericProp,\n    default: DEFAULT_ROW_WIDTH\n  }\n};\nconst [name, bem] = createNamespace(\"skeleton-paragraph\");\nvar stdin_default = defineComponent({\n  name,\n  props: skeletonParagraphProps,\n  setup(props) {\n    return () => _createVNode(\"div\", {\n      \"class\": bem([{\n        round: props.round\n      }]),\n      \"style\": {\n        width: props.rowWidth\n      }\n    }, null);\n  }\n});\nexport {\n  DEFAULT_ROW_WIDTH,\n  stdin_default as default,\n  skeletonParagraphProps\n};\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AAClE,SAASC,eAAe,EAAEC,WAAW,QAAQ,oBAAoB;AACjE,MAAMC,iBAAiB,GAAG,MAAM;AAChC,MAAMC,sBAAsB,GAAG;EAC7BC,KAAK,EAAEC,OAAO;EACdC,QAAQ,EAAE;IACRC,IAAI,EAAEN,WAAW;IACjBO,OAAO,EAAEN;EACX;AACF,CAAC;AACD,MAAM,CAACO,IAAI,EAAEC,GAAG,CAAC,GAAGV,eAAe,CAAC,oBAAoB,CAAC;AACzD,IAAIW,aAAa,GAAGd,eAAe,CAAC;EAClCY,IAAI;EACJG,KAAK,EAAET,sBAAsB;EAC7BU,KAAKA,CAACD,KAAK,EAAE;IACX,OAAO,MAAMb,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEW,GAAG,CAAC,CAAC;QACZN,KAAK,EAAEQ,KAAK,CAACR;MACf,CAAC,CAAC,CAAC;MACH,OAAO,EAAE;QACPU,KAAK,EAAEF,KAAK,CAACN;MACf;IACF,CAAC,EAAE,IAAI,CAAC;EACV;AACF,CAAC,CAAC;AACF,SACEJ,iBAAiB,EACjBS,aAAa,IAAIH,OAAO,EACxBL,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}