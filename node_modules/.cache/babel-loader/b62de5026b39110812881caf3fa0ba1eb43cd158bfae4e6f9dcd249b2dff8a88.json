{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Overlay from \"./Overlay.mjs\";\nconst Overlay = withInstall(_Overlay);\nvar stdin_default = Overlay;\nimport { overlayProps } from \"./Overlay.mjs\";\nexport { Overlay, stdin_default as default, overlayProps };", "map": {"version": 3, "names": ["withInstall", "_Overlay", "Overlay", "stdin_default", "overlayProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/overlay/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Overlay from \"./Overlay.mjs\";\nconst Overlay = withInstall(_Overlay);\nvar stdin_default = Overlay;\nimport { overlayProps } from \"./Overlay.mjs\";\nexport {\n  Overlay,\n  stdin_default as default,\n  overlayProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,MAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SAASE,YAAY,QAAQ,eAAe;AAC5C,SACEF,OAAO,EACPC,aAAa,IAAIE,OAAO,EACxBD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}