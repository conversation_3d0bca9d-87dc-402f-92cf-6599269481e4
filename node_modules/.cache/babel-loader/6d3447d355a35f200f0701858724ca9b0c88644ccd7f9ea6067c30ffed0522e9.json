{"ast": null, "code": "import { ref, watch, computed, nextTick, watchEffect, defineComponent, getCurrentInstance, mergeProps as _mergeProps, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nimport { normalizeClass, normalizeStyle, stringifyStyle } from \"@vue/shared\";\nimport { pick, extend, truthProp, unknownProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { TABS_KEY } from \"../tabs/Tabs.mjs\";\nimport { doubleRaf, useParent } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { routeProps } from \"../composables/use-route.mjs\";\nimport { useProvideTabStatus } from \"../composables/use-tab-status.mjs\";\nimport { TabTitle } from \"./TabTitle.mjs\";\nimport { SwipeItem } from \"../swipe-item/index.mjs\";\nconst [name, bem] = createNamespace(\"tab\");\nconst tabProps = extend({}, routeProps, {\n  dot: Boolean,\n  name: numericProp,\n  badge: numericProp,\n  title: String,\n  disabled: Boolean,\n  titleClass: unknownProp,\n  titleStyle: [String, Object],\n  showZeroBadge: truthProp\n});\nvar stdin_default = defineComponent({\n  name,\n  props: tabProps,\n  setup(props, {\n    slots\n  }) {\n    const id = useId();\n    const inited = ref(false);\n    const instance = getCurrentInstance();\n    const {\n      parent,\n      index\n    } = useParent(TABS_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <Tab> must be a child component of <Tabs>.\");\n      }\n      return;\n    }\n    const getName = () => {\n      var _a;\n      return (_a = props.name) != null ? _a : index.value;\n    };\n    const init = () => {\n      inited.value = true;\n      if (parent.props.lazyRender) {\n        nextTick(() => {\n          parent.onRendered(getName(), props.title);\n        });\n      }\n    };\n    const active = computed(() => {\n      const isActive = getName() === parent.currentName.value;\n      if (isActive && !inited.value) {\n        init();\n      }\n      return isActive;\n    });\n    const parsedClass = ref(\"\");\n    const parsedStyle = ref(\"\");\n    watchEffect(() => {\n      const {\n        titleClass,\n        titleStyle\n      } = props;\n      parsedClass.value = titleClass ? normalizeClass(titleClass) : \"\";\n      parsedStyle.value = titleStyle && typeof titleStyle !== \"string\" ? stringifyStyle(normalizeStyle(titleStyle)) : titleStyle;\n    });\n    const renderTitle = onClickTab => _createVNode(TabTitle, _mergeProps({\n      \"key\": id,\n      \"id\": `${parent.id}-${index.value}`,\n      \"ref\": parent.setTitleRefs(index.value),\n      \"style\": parsedStyle.value,\n      \"class\": parsedClass.value,\n      \"isActive\": active.value,\n      \"controls\": id,\n      \"scrollable\": parent.scrollable.value,\n      \"activeColor\": parent.props.titleActiveColor,\n      \"inactiveColor\": parent.props.titleInactiveColor,\n      \"onClick\": event => onClickTab(instance.proxy, index.value, event)\n    }, pick(parent.props, [\"type\", \"color\", \"shrink\"]), pick(props, [\"dot\", \"badge\", \"title\", \"disabled\", \"showZeroBadge\"])), {\n      title: slots.title\n    });\n    const hasInactiveClass = ref(!active.value);\n    watch(active, val => {\n      if (val) {\n        hasInactiveClass.value = false;\n      } else {\n        doubleRaf(() => {\n          hasInactiveClass.value = true;\n        });\n      }\n    });\n    watch(() => props.title, () => {\n      parent.setLine();\n      parent.scrollIntoView();\n    });\n    useProvideTabStatus(active);\n    useExpose({\n      id,\n      renderTitle\n    });\n    return () => {\n      var _a;\n      const label = `${parent.id}-${index.value}`;\n      const {\n        animated,\n        swipeable,\n        scrollspy,\n        lazyRender\n      } = parent.props;\n      if (!slots.default && !animated) {\n        return;\n      }\n      const show = scrollspy || active.value;\n      if (animated || swipeable) {\n        return _createVNode(SwipeItem, {\n          \"id\": id,\n          \"role\": \"tabpanel\",\n          \"class\": bem(\"panel-wrapper\", {\n            inactive: hasInactiveClass.value\n          }),\n          \"tabindex\": active.value ? 0 : -1,\n          \"aria-hidden\": !active.value,\n          \"aria-labelledby\": label,\n          \"data-allow-mismatch\": \"attribute\"\n        }, {\n          default: () => {\n            var _a2;\n            return [_createVNode(\"div\", {\n              \"class\": bem(\"panel\")\n            }, [(_a2 = slots.default) == null ? void 0 : _a2.call(slots)])];\n          }\n        });\n      }\n      const shouldRender = inited.value || scrollspy || !lazyRender;\n      const Content = shouldRender ? (_a = slots.default) == null ? void 0 : _a.call(slots) : null;\n      return _withDirectives(_createVNode(\"div\", {\n        \"id\": id,\n        \"role\": \"tabpanel\",\n        \"class\": bem(\"panel\"),\n        \"tabindex\": show ? 0 : -1,\n        \"aria-labelledby\": label,\n        \"data-allow-mismatch\": \"attribute\"\n      }, [Content]), [[_vShow, show]]);\n    };\n  }\n});\nexport { stdin_default as default, tabProps };", "map": {"version": 3, "names": ["ref", "watch", "computed", "nextTick", "watchEffect", "defineComponent", "getCurrentInstance", "mergeProps", "_mergeProps", "createVNode", "_createVNode", "vShow", "_vShow", "withDirectives", "_withDirectives", "normalizeClass", "normalizeStyle", "stringifyStyle", "pick", "extend", "truthProp", "unknownProp", "numericProp", "createNamespace", "TABS_KEY", "doubleRaf", "useParent", "useId", "useExpose", "routeProps", "useProvideTabStatus", "TabTitle", "SwipeItem", "name", "bem", "tabProps", "dot", "Boolean", "badge", "title", "String", "disabled", "titleClass", "titleStyle", "Object", "showZeroBadge", "stdin_default", "props", "setup", "slots", "id", "inited", "instance", "parent", "index", "process", "env", "NODE_ENV", "console", "error", "getName", "_a", "value", "init", "lazy<PERSON>ender", "onRendered", "active", "isActive", "currentName", "parsedClass", "parsedStyle", "renderTitle", "onClickTab", "setTitleRefs", "scrollable", "titleActiveColor", "titleInactiveColor", "event", "proxy", "hasInactiveClass", "val", "setLine", "scrollIntoView", "label", "animated", "swipeable", "scrollspy", "default", "show", "inactive", "_a2", "call", "shouldRender", "Content"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/tab/Tab.mjs"], "sourcesContent": ["import { ref, watch, computed, nextTick, watchEffect, defineComponent, getCurrentInstance, mergeProps as _mergeProps, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nimport { normalizeClass, normalizeStyle, stringifyStyle } from \"@vue/shared\";\nimport { pick, extend, truthProp, unknownProp, numericProp, createNamespace } from \"../utils/index.mjs\";\nimport { TABS_KEY } from \"../tabs/Tabs.mjs\";\nimport { doubleRaf, useParent } from \"@vant/use\";\nimport { useId } from \"../composables/use-id.mjs\";\nimport { useExpose } from \"../composables/use-expose.mjs\";\nimport { routeProps } from \"../composables/use-route.mjs\";\nimport { useProvideTabStatus } from \"../composables/use-tab-status.mjs\";\nimport { TabTitle } from \"./TabTitle.mjs\";\nimport { SwipeItem } from \"../swipe-item/index.mjs\";\nconst [name, bem] = createNamespace(\"tab\");\nconst tabProps = extend({}, routeProps, {\n  dot: Boolean,\n  name: numericProp,\n  badge: numericProp,\n  title: String,\n  disabled: Boolean,\n  titleClass: unknownProp,\n  titleStyle: [String, Object],\n  showZeroBadge: truthProp\n});\nvar stdin_default = defineComponent({\n  name,\n  props: tabProps,\n  setup(props, {\n    slots\n  }) {\n    const id = useId();\n    const inited = ref(false);\n    const instance = getCurrentInstance();\n    const {\n      parent,\n      index\n    } = useParent(TABS_KEY);\n    if (!parent) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(\"[Vant] <Tab> must be a child component of <Tabs>.\");\n      }\n      return;\n    }\n    const getName = () => {\n      var _a;\n      return (_a = props.name) != null ? _a : index.value;\n    };\n    const init = () => {\n      inited.value = true;\n      if (parent.props.lazyRender) {\n        nextTick(() => {\n          parent.onRendered(getName(), props.title);\n        });\n      }\n    };\n    const active = computed(() => {\n      const isActive = getName() === parent.currentName.value;\n      if (isActive && !inited.value) {\n        init();\n      }\n      return isActive;\n    });\n    const parsedClass = ref(\"\");\n    const parsedStyle = ref(\"\");\n    watchEffect(() => {\n      const {\n        titleClass,\n        titleStyle\n      } = props;\n      parsedClass.value = titleClass ? normalizeClass(titleClass) : \"\";\n      parsedStyle.value = titleStyle && typeof titleStyle !== \"string\" ? stringifyStyle(normalizeStyle(titleStyle)) : titleStyle;\n    });\n    const renderTitle = (onClickTab) => _createVNode(TabTitle, _mergeProps({\n      \"key\": id,\n      \"id\": `${parent.id}-${index.value}`,\n      \"ref\": parent.setTitleRefs(index.value),\n      \"style\": parsedStyle.value,\n      \"class\": parsedClass.value,\n      \"isActive\": active.value,\n      \"controls\": id,\n      \"scrollable\": parent.scrollable.value,\n      \"activeColor\": parent.props.titleActiveColor,\n      \"inactiveColor\": parent.props.titleInactiveColor,\n      \"onClick\": (event) => onClickTab(instance.proxy, index.value, event)\n    }, pick(parent.props, [\"type\", \"color\", \"shrink\"]), pick(props, [\"dot\", \"badge\", \"title\", \"disabled\", \"showZeroBadge\"])), {\n      title: slots.title\n    });\n    const hasInactiveClass = ref(!active.value);\n    watch(active, (val) => {\n      if (val) {\n        hasInactiveClass.value = false;\n      } else {\n        doubleRaf(() => {\n          hasInactiveClass.value = true;\n        });\n      }\n    });\n    watch(() => props.title, () => {\n      parent.setLine();\n      parent.scrollIntoView();\n    });\n    useProvideTabStatus(active);\n    useExpose({\n      id,\n      renderTitle\n    });\n    return () => {\n      var _a;\n      const label = `${parent.id}-${index.value}`;\n      const {\n        animated,\n        swipeable,\n        scrollspy,\n        lazyRender\n      } = parent.props;\n      if (!slots.default && !animated) {\n        return;\n      }\n      const show = scrollspy || active.value;\n      if (animated || swipeable) {\n        return _createVNode(SwipeItem, {\n          \"id\": id,\n          \"role\": \"tabpanel\",\n          \"class\": bem(\"panel-wrapper\", {\n            inactive: hasInactiveClass.value\n          }),\n          \"tabindex\": active.value ? 0 : -1,\n          \"aria-hidden\": !active.value,\n          \"aria-labelledby\": label,\n          \"data-allow-mismatch\": \"attribute\"\n        }, {\n          default: () => {\n            var _a2;\n            return [_createVNode(\"div\", {\n              \"class\": bem(\"panel\")\n            }, [(_a2 = slots.default) == null ? void 0 : _a2.call(slots)])];\n          }\n        });\n      }\n      const shouldRender = inited.value || scrollspy || !lazyRender;\n      const Content = shouldRender ? (_a = slots.default) == null ? void 0 : _a.call(slots) : null;\n      return _withDirectives(_createVNode(\"div\", {\n        \"id\": id,\n        \"role\": \"tabpanel\",\n        \"class\": bem(\"panel\"),\n        \"tabindex\": show ? 0 : -1,\n        \"aria-labelledby\": label,\n        \"data-allow-mismatch\": \"attribute\"\n      }, [Content]), [[_vShow, show]]);\n    };\n  }\n});\nexport {\n  stdin_default as default,\n  tabProps\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,IAAIC,WAAW,EAAEC,WAAW,IAAIC,YAAY,EAAEC,KAAK,IAAIC,MAAM,EAAEC,cAAc,IAAIC,eAAe,QAAQ,KAAK;AAClN,SAASC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,aAAa;AAC5E,SAASC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,oBAAoB;AACvG,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,SAAS,EAAEC,SAAS,QAAQ,WAAW;AAChD,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGX,eAAe,CAAC,KAAK,CAAC;AAC1C,MAAMY,QAAQ,GAAGhB,MAAM,CAAC,CAAC,CAAC,EAAEU,UAAU,EAAE;EACtCO,GAAG,EAAEC,OAAO;EACZJ,IAAI,EAAEX,WAAW;EACjBgB,KAAK,EAAEhB,WAAW;EAClBiB,KAAK,EAAEC,MAAM;EACbC,QAAQ,EAAEJ,OAAO;EACjBK,UAAU,EAAErB,WAAW;EACvBsB,UAAU,EAAE,CAACH,MAAM,EAAEI,MAAM,CAAC;EAC5BC,aAAa,EAAEzB;AACjB,CAAC,CAAC;AACF,IAAI0B,aAAa,GAAGzC,eAAe,CAAC;EAClC4B,IAAI;EACJc,KAAK,EAAEZ,QAAQ;EACfa,KAAKA,CAACD,KAAK,EAAE;IACXE;EACF,CAAC,EAAE;IACD,MAAMC,EAAE,GAAGvB,KAAK,CAAC,CAAC;IAClB,MAAMwB,MAAM,GAAGnD,GAAG,CAAC,KAAK,CAAC;IACzB,MAAMoD,QAAQ,GAAG9C,kBAAkB,CAAC,CAAC;IACrC,MAAM;MACJ+C,MAAM;MACNC;IACF,CAAC,GAAG5B,SAAS,CAACF,QAAQ,CAAC;IACvB,IAAI,CAAC6B,MAAM,EAAE;MACX,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,mDAAmD,CAAC;MACpE;MACA;IACF;IACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAIC,EAAE;MACN,OAAO,CAACA,EAAE,GAAGd,KAAK,CAACd,IAAI,KAAK,IAAI,GAAG4B,EAAE,GAAGP,KAAK,CAACQ,KAAK;IACrD,CAAC;IACD,MAAMC,IAAI,GAAGA,CAAA,KAAM;MACjBZ,MAAM,CAACW,KAAK,GAAG,IAAI;MACnB,IAAIT,MAAM,CAACN,KAAK,CAACiB,UAAU,EAAE;QAC3B7D,QAAQ,CAAC,MAAM;UACbkD,MAAM,CAACY,UAAU,CAACL,OAAO,CAAC,CAAC,EAAEb,KAAK,CAACR,KAAK,CAAC;QAC3C,CAAC,CAAC;MACJ;IACF,CAAC;IACD,MAAM2B,MAAM,GAAGhE,QAAQ,CAAC,MAAM;MAC5B,MAAMiE,QAAQ,GAAGP,OAAO,CAAC,CAAC,KAAKP,MAAM,CAACe,WAAW,CAACN,KAAK;MACvD,IAAIK,QAAQ,IAAI,CAAChB,MAAM,CAACW,KAAK,EAAE;QAC7BC,IAAI,CAAC,CAAC;MACR;MACA,OAAOI,QAAQ;IACjB,CAAC,CAAC;IACF,MAAME,WAAW,GAAGrE,GAAG,CAAC,EAAE,CAAC;IAC3B,MAAMsE,WAAW,GAAGtE,GAAG,CAAC,EAAE,CAAC;IAC3BI,WAAW,CAAC,MAAM;MAChB,MAAM;QACJsC,UAAU;QACVC;MACF,CAAC,GAAGI,KAAK;MACTsB,WAAW,CAACP,KAAK,GAAGpB,UAAU,GAAG3B,cAAc,CAAC2B,UAAU,CAAC,GAAG,EAAE;MAChE4B,WAAW,CAACR,KAAK,GAAGnB,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,GAAG1B,cAAc,CAACD,cAAc,CAAC2B,UAAU,CAAC,CAAC,GAAGA,UAAU;IAC5H,CAAC,CAAC;IACF,MAAM4B,WAAW,GAAIC,UAAU,IAAK9D,YAAY,CAACqB,QAAQ,EAAEvB,WAAW,CAAC;MACrE,KAAK,EAAE0C,EAAE;MACT,IAAI,EAAE,GAAGG,MAAM,CAACH,EAAE,IAAII,KAAK,CAACQ,KAAK,EAAE;MACnC,KAAK,EAAET,MAAM,CAACoB,YAAY,CAACnB,KAAK,CAACQ,KAAK,CAAC;MACvC,OAAO,EAAEQ,WAAW,CAACR,KAAK;MAC1B,OAAO,EAAEO,WAAW,CAACP,KAAK;MAC1B,UAAU,EAAEI,MAAM,CAACJ,KAAK;MACxB,UAAU,EAAEZ,EAAE;MACd,YAAY,EAAEG,MAAM,CAACqB,UAAU,CAACZ,KAAK;MACrC,aAAa,EAAET,MAAM,CAACN,KAAK,CAAC4B,gBAAgB;MAC5C,eAAe,EAAEtB,MAAM,CAACN,KAAK,CAAC6B,kBAAkB;MAChD,SAAS,EAAGC,KAAK,IAAKL,UAAU,CAACpB,QAAQ,CAAC0B,KAAK,EAAExB,KAAK,CAACQ,KAAK,EAAEe,KAAK;IACrE,CAAC,EAAE3D,IAAI,CAACmC,MAAM,CAACN,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAE7B,IAAI,CAAC6B,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE;MACxHR,KAAK,EAAEU,KAAK,CAACV;IACf,CAAC,CAAC;IACF,MAAMwC,gBAAgB,GAAG/E,GAAG,CAAC,CAACkE,MAAM,CAACJ,KAAK,CAAC;IAC3C7D,KAAK,CAACiE,MAAM,EAAGc,GAAG,IAAK;MACrB,IAAIA,GAAG,EAAE;QACPD,gBAAgB,CAACjB,KAAK,GAAG,KAAK;MAChC,CAAC,MAAM;QACLrC,SAAS,CAAC,MAAM;UACdsD,gBAAgB,CAACjB,KAAK,GAAG,IAAI;QAC/B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF7D,KAAK,CAAC,MAAM8C,KAAK,CAACR,KAAK,EAAE,MAAM;MAC7Bc,MAAM,CAAC4B,OAAO,CAAC,CAAC;MAChB5B,MAAM,CAAC6B,cAAc,CAAC,CAAC;IACzB,CAAC,CAAC;IACFpD,mBAAmB,CAACoC,MAAM,CAAC;IAC3BtC,SAAS,CAAC;MACRsB,EAAE;MACFqB;IACF,CAAC,CAAC;IACF,OAAO,MAAM;MACX,IAAIV,EAAE;MACN,MAAMsB,KAAK,GAAG,GAAG9B,MAAM,CAACH,EAAE,IAAII,KAAK,CAACQ,KAAK,EAAE;MAC3C,MAAM;QACJsB,QAAQ;QACRC,SAAS;QACTC,SAAS;QACTtB;MACF,CAAC,GAAGX,MAAM,CAACN,KAAK;MAChB,IAAI,CAACE,KAAK,CAACsC,OAAO,IAAI,CAACH,QAAQ,EAAE;QAC/B;MACF;MACA,MAAMI,IAAI,GAAGF,SAAS,IAAIpB,MAAM,CAACJ,KAAK;MACtC,IAAIsB,QAAQ,IAAIC,SAAS,EAAE;QACzB,OAAO3E,YAAY,CAACsB,SAAS,EAAE;UAC7B,IAAI,EAAEkB,EAAE;UACR,MAAM,EAAE,UAAU;UAClB,OAAO,EAAEhB,GAAG,CAAC,eAAe,EAAE;YAC5BuD,QAAQ,EAAEV,gBAAgB,CAACjB;UAC7B,CAAC,CAAC;UACF,UAAU,EAAEI,MAAM,CAACJ,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;UACjC,aAAa,EAAE,CAACI,MAAM,CAACJ,KAAK;UAC5B,iBAAiB,EAAEqB,KAAK;UACxB,qBAAqB,EAAE;QACzB,CAAC,EAAE;UACDI,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIG,GAAG;YACP,OAAO,CAAChF,YAAY,CAAC,KAAK,EAAE;cAC1B,OAAO,EAAEwB,GAAG,CAAC,OAAO;YACtB,CAAC,EAAE,CAAC,CAACwD,GAAG,GAAGzC,KAAK,CAACsC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,GAAG,CAACC,IAAI,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC;UACjE;QACF,CAAC,CAAC;MACJ;MACA,MAAM2C,YAAY,GAAGzC,MAAM,CAACW,KAAK,IAAIwB,SAAS,IAAI,CAACtB,UAAU;MAC7D,MAAM6B,OAAO,GAAGD,YAAY,GAAG,CAAC/B,EAAE,GAAGZ,KAAK,CAACsC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG1B,EAAE,CAAC8B,IAAI,CAAC1C,KAAK,CAAC,GAAG,IAAI;MAC5F,OAAOnC,eAAe,CAACJ,YAAY,CAAC,KAAK,EAAE;QACzC,IAAI,EAAEwC,EAAE;QACR,MAAM,EAAE,UAAU;QAClB,OAAO,EAAEhB,GAAG,CAAC,OAAO,CAAC;QACrB,UAAU,EAAEsD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,iBAAiB,EAAEL,KAAK;QACxB,qBAAqB,EAAE;MACzB,CAAC,EAAE,CAACU,OAAO,CAAC,CAAC,EAAE,CAAC,CAACjF,MAAM,EAAE4E,IAAI,CAAC,CAAC,CAAC;IAClC,CAAC;EACH;AACF,CAAC,CAAC;AACF,SACE1C,aAAa,IAAIyC,OAAO,EACxBpD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}