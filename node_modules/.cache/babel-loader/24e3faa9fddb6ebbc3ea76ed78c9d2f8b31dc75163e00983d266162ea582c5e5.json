{"ast": null, "code": "import { inject, watch } from \"vue\";\nconst POPUP_TOGGLE_KEY = Symbol();\nfunction onPopupReopen(callback) {\n  const popupToggleStatus = inject(POPUP_TOGGLE_KEY, null);\n  if (popupToggleStatus) {\n    watch(popupToggleStatus, show => {\n      if (show) {\n        callback();\n      }\n    });\n  }\n}\nexport { POPUP_TOGGLE_KEY, onPopupReopen };", "map": {"version": 3, "names": ["inject", "watch", "POPUP_TOGGLE_KEY", "Symbol", "onPopupReopen", "callback", "popupToggleStatus", "show"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/composables/on-popup-reopen.mjs"], "sourcesContent": ["import { inject, watch } from \"vue\";\nconst POPUP_TOGGLE_KEY = Symbol();\nfunction onPopupReopen(callback) {\n  const popupToggleStatus = inject(POPUP_TOGGLE_KEY, null);\n  if (popupToggleStatus) {\n    watch(popupToggleStatus, (show) => {\n      if (show) {\n        callback();\n      }\n    });\n  }\n}\nexport {\n  POPUP_TOGGLE_KEY,\n  onPopupReopen\n};\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,KAAK,QAAQ,KAAK;AACnC,MAAMC,gBAAgB,GAAGC,MAAM,CAAC,CAAC;AACjC,SAASC,aAAaA,CAACC,QAAQ,EAAE;EAC/B,MAAMC,iBAAiB,GAAGN,MAAM,CAACE,gBAAgB,EAAE,IAAI,CAAC;EACxD,IAAII,iBAAiB,EAAE;IACrBL,KAAK,CAACK,iBAAiB,EAAGC,IAAI,IAAK;MACjC,IAAIA,IAAI,EAAE;QACRF,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,CAAC;EACJ;AACF;AACA,SACEH,gBAAgB,EAChBE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}