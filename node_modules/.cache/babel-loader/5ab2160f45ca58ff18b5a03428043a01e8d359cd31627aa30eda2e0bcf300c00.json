{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Image from \"./Image.mjs\";\nconst Image = withInstall(_Image);\nvar stdin_default = Image;\nimport { imageProps } from \"./Image.mjs\";\nexport { Image, stdin_default as default, imageProps };", "map": {"version": 3, "names": ["withInstall", "_Image", "Image", "stdin_default", "imageProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/image/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Image from \"./Image.mjs\";\nconst Image = withInstall(_Image);\nvar stdin_default = Image;\nimport { imageProps } from \"./Image.mjs\";\nexport {\n  Image,\n  stdin_default as default,\n  imageProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGF,WAAW,CAACC,MAAM,CAAC;AACjC,IAAIE,aAAa,GAAGD,KAAK;AACzB,SAASE,UAAU,QAAQ,aAAa;AACxC,SACEF,KAAK,EACLC,aAAa,IAAIE,OAAO,EACxBD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}