{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _SidebarItem from \"./SidebarItem.mjs\";\nconst SidebarItem = withInstall(_SidebarItem);\nvar stdin_default = SidebarItem;\nimport { sidebarItemProps } from \"./SidebarItem.mjs\";\nexport { SidebarItem, stdin_default as default, sidebarItemProps };", "map": {"version": 3, "names": ["withInstall", "_SidebarItem", "SidebarItem", "stdin_default", "sidebarItemProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/sidebar-item/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _SidebarItem from \"./SidebarItem.mjs\";\nconst SidebarItem = withInstall(_SidebarItem);\nvar stdin_default = SidebarItem;\nimport { sidebarItemProps } from \"./SidebarItem.mjs\";\nexport {\n  SidebarItem,\n  stdin_default as default,\n  sidebarItemProps\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,MAAMC,WAAW,GAAGF,WAAW,CAACC,YAAY,CAAC;AAC7C,IAAIE,aAAa,GAAGD,WAAW;AAC/B,SAASE,gBAAgB,QAAQ,mBAAmB;AACpD,SACEF,WAAW,EACXC,aAAa,IAAIE,OAAO,EACxBD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}