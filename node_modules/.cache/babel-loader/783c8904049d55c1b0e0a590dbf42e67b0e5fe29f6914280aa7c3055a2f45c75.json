{"ast": null, "code": "import { withInstall } from \"../utils/index.mjs\";\nimport _Barrage from \"./Barrage.mjs\";\nconst Barrage = withInstall(_Barrage);\nvar stdin_default = Barrage;\nimport { barrageProps } from \"./Barrage.mjs\";\nexport { Barrage, barrageProps, stdin_default as default };", "map": {"version": 3, "names": ["withInstall", "_Barrage", "Barrage", "stdin_default", "barrageProps", "default"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/barrage/index.mjs"], "sourcesContent": ["import { withInstall } from \"../utils/index.mjs\";\nimport _Barrage from \"./Barrage.mjs\";\nconst Barrage = withInstall(_Barrage);\nvar stdin_default = Barrage;\nimport { barrageProps } from \"./Barrage.mjs\";\nexport {\n  Barrage,\n  barrageProps,\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,eAAe;AACpC,MAAMC,OAAO,GAAGF,WAAW,CAACC,QAAQ,CAAC;AACrC,IAAIE,aAAa,GAAGD,OAAO;AAC3B,SAASE,YAAY,QAAQ,eAAe;AAC5C,SACEF,OAAO,EACPE,YAAY,EACZD,aAAa,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}