{"ast": null, "code": "import { ref, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, createNamespace, preventDefault } from \"../utils/index.mjs\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"key\");\nconst CollapseIcon = _createVNode(\"svg\", {\n  \"class\": bem(\"collapse-icon\"),\n  \"viewBox\": \"0 0 30 24\"\n}, [_createVNode(\"path\", {\n  \"d\": \"M26 13h-2v2h2v-2zm-8-3h2V8h-2v2zm2-4h2V4h-2v2zm2 4h4V4h-2v4h-2v2zm-7 14 3-3h-6l3 3zM6 13H4v2h2v-2zm16 0H8v2h14v-2zm-12-3h2V8h-2v2zM28 0l1 1 1 1v15l-1 2H1l-1-2V2l1-1 1-1zm0 2H2v15h26V2zM6 4v2H4V4zm10 2h2V4h-2v2zM8 9v1H4V8zm8 0v1h-2V8zm-6-5v2H8V4zm4 0v2h-2V4z\",\n  \"fill\": \"currentColor\"\n}, null)]);\nconst DeleteIcon = _createVNode(\"svg\", {\n  \"class\": bem(\"delete-icon\"),\n  \"viewBox\": \"0 0 32 22\"\n}, [_createVNode(\"path\", {\n  \"d\": \"M28 0a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H10.4a2 2 0 0 1-1.4-.6L1 13.1c-.6-.5-.9-1.3-.9-2 0-1 .3-1.7.9-2.2L9 .6a2 2 0 0 1 1.4-.6zm0 2H10.4l-8.2 8.3a1 1 0 0 0-.3.7c0 .3.1.5.3.7l8.2 8.4H28a2 2 0 0 0 2-2V4c0-1.1-.9-2-2-2zm-5 4a1 1 0 0 1 .7.3 1 1 0 0 1 0 1.4L20.4 11l3.3 3.3c.2.2.3.5.3.7 0 .3-.1.5-.3.7a1 1 0 0 1-.7.3 1 1 0 0 1-.7-.3L19 12.4l-3.4 3.3a1 1 0 0 1-.6.3 1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7c0-.2.1-.5.3-.7l3.3-3.3-3.3-3.3A1 1 0 0 1 14 7c0-.3.1-.5.3-.7A1 1 0 0 1 15 6a1 1 0 0 1 .6.3L19 9.6l3.3-3.3A1 1 0 0 1 23 6z\",\n  \"fill\": \"currentColor\"\n}, null)]);\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    type: String,\n    text: numericProp,\n    color: String,\n    wider: Boolean,\n    large: Boolean,\n    loading: Boolean\n  },\n  emits: [\"press\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const active = ref(false);\n    const touch = useTouch();\n    const onTouchStart = event => {\n      touch.start(event);\n      active.value = true;\n    };\n    const onTouchMove = event => {\n      touch.move(event);\n      if (touch.direction.value) {\n        active.value = false;\n      }\n    };\n    const onTouchEnd = event => {\n      if (active.value) {\n        if (!slots.default) {\n          preventDefault(event);\n        }\n        active.value = false;\n        emit(\"press\", props.text, props.type);\n      }\n    };\n    const renderContent = () => {\n      if (props.loading) {\n        return _createVNode(Loading, {\n          \"class\": bem(\"loading-icon\")\n        }, null);\n      }\n      const text = slots.default ? slots.default() : props.text;\n      switch (props.type) {\n        case \"delete\":\n          return text || DeleteIcon;\n        case \"extra\":\n          return text || CollapseIcon;\n        default:\n          return text;\n      }\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(\"wrapper\", {\n        wider: props.wider\n      }),\n      \"onTouchstartPassive\": onTouchStart,\n      \"onTouchmovePassive\": onTouchMove,\n      \"onTouchend\": onTouchEnd,\n      \"onTouchcancel\": onTouchEnd\n    }, [_createVNode(\"div\", {\n      \"role\": \"button\",\n      \"tabindex\": 0,\n      \"class\": bem([props.color, {\n        large: props.large,\n        active: active.value,\n        delete: props.type === \"delete\"\n      }])\n    }, [renderContent()])]);\n  }\n});\nexport { stdin_default as default };", "map": {"version": 3, "names": ["ref", "defineComponent", "createVNode", "_createVNode", "numericProp", "createNamespace", "preventDefault", "useTouch", "Loading", "name", "bem", "CollapseIcon", "DeleteIcon", "stdin_default", "props", "type", "String", "text", "color", "wider", "Boolean", "large", "loading", "emits", "setup", "emit", "slots", "active", "touch", "onTouchStart", "event", "start", "value", "onTouchMove", "move", "direction", "onTouchEnd", "default", "renderContent", "delete"], "sources": ["/Users/<USER>/Desktop/work/code/my/zylp/wr_ui/node_modules/vant/es/number-keyboard/NumberKeyboardKey.mjs"], "sourcesContent": ["import { ref, defineComponent, createVNode as _createVNode } from \"vue\";\nimport { numericProp, createNamespace, preventDefault } from \"../utils/index.mjs\";\nimport { useTouch } from \"../composables/use-touch.mjs\";\nimport { Loading } from \"../loading/index.mjs\";\nconst [name, bem] = createNamespace(\"key\");\nconst CollapseIcon = _createVNode(\"svg\", {\n  \"class\": bem(\"collapse-icon\"),\n  \"viewBox\": \"0 0 30 24\"\n}, [_createVNode(\"path\", {\n  \"d\": \"M26 13h-2v2h2v-2zm-8-3h2V8h-2v2zm2-4h2V4h-2v2zm2 4h4V4h-2v4h-2v2zm-7 14 3-3h-6l3 3zM6 13H4v2h2v-2zm16 0H8v2h14v-2zm-12-3h2V8h-2v2zM28 0l1 1 1 1v15l-1 2H1l-1-2V2l1-1 1-1zm0 2H2v15h26V2zM6 4v2H4V4zm10 2h2V4h-2v2zM8 9v1H4V8zm8 0v1h-2V8zm-6-5v2H8V4zm4 0v2h-2V4z\",\n  \"fill\": \"currentColor\"\n}, null)]);\nconst DeleteIcon = _createVNode(\"svg\", {\n  \"class\": bem(\"delete-icon\"),\n  \"viewBox\": \"0 0 32 22\"\n}, [_createVNode(\"path\", {\n  \"d\": \"M28 0a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H10.4a2 2 0 0 1-1.4-.6L1 13.1c-.6-.5-.9-1.3-.9-2 0-1 .3-1.7.9-2.2L9 .6a2 2 0 0 1 1.4-.6zm0 2H10.4l-8.2 8.3a1 1 0 0 0-.3.7c0 .3.1.5.3.7l8.2 8.4H28a2 2 0 0 0 2-2V4c0-1.1-.9-2-2-2zm-5 4a1 1 0 0 1 .7.3 1 1 0 0 1 0 1.4L20.4 11l3.3 3.3c.2.2.3.5.3.7 0 .3-.1.5-.3.7a1 1 0 0 1-.7.3 1 1 0 0 1-.7-.3L19 12.4l-3.4 3.3a1 1 0 0 1-.6.3 1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7c0-.2.1-.5.3-.7l3.3-3.3-3.3-3.3A1 1 0 0 1 14 7c0-.3.1-.5.3-.7A1 1 0 0 1 15 6a1 1 0 0 1 .6.3L19 9.6l3.3-3.3A1 1 0 0 1 23 6z\",\n  \"fill\": \"currentColor\"\n}, null)]);\nvar stdin_default = defineComponent({\n  name,\n  props: {\n    type: String,\n    text: numericProp,\n    color: String,\n    wider: Boolean,\n    large: Boolean,\n    loading: Boolean\n  },\n  emits: [\"press\"],\n  setup(props, {\n    emit,\n    slots\n  }) {\n    const active = ref(false);\n    const touch = useTouch();\n    const onTouchStart = (event) => {\n      touch.start(event);\n      active.value = true;\n    };\n    const onTouchMove = (event) => {\n      touch.move(event);\n      if (touch.direction.value) {\n        active.value = false;\n      }\n    };\n    const onTouchEnd = (event) => {\n      if (active.value) {\n        if (!slots.default) {\n          preventDefault(event);\n        }\n        active.value = false;\n        emit(\"press\", props.text, props.type);\n      }\n    };\n    const renderContent = () => {\n      if (props.loading) {\n        return _createVNode(Loading, {\n          \"class\": bem(\"loading-icon\")\n        }, null);\n      }\n      const text = slots.default ? slots.default() : props.text;\n      switch (props.type) {\n        case \"delete\":\n          return text || DeleteIcon;\n        case \"extra\":\n          return text || CollapseIcon;\n        default:\n          return text;\n      }\n    };\n    return () => _createVNode(\"div\", {\n      \"class\": bem(\"wrapper\", {\n        wider: props.wider\n      }),\n      \"onTouchstartPassive\": onTouchStart,\n      \"onTouchmovePassive\": onTouchMove,\n      \"onTouchend\": onTouchEnd,\n      \"onTouchcancel\": onTouchEnd\n    }, [_createVNode(\"div\", {\n      \"role\": \"button\",\n      \"tabindex\": 0,\n      \"class\": bem([props.color, {\n        large: props.large,\n        active: active.value,\n        delete: props.type === \"delete\"\n      }])\n    }, [renderContent()])]);\n  }\n});\nexport {\n  stdin_default as default\n};\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,eAAe,EAAEC,WAAW,IAAIC,YAAY,QAAQ,KAAK;AACvE,SAASC,WAAW,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACjF,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,MAAM,CAACC,IAAI,EAAEC,GAAG,CAAC,GAAGL,eAAe,CAAC,KAAK,CAAC;AAC1C,MAAMM,YAAY,GAAGR,YAAY,CAAC,KAAK,EAAE;EACvC,OAAO,EAAEO,GAAG,CAAC,eAAe,CAAC;EAC7B,SAAS,EAAE;AACb,CAAC,EAAE,CAACP,YAAY,CAAC,MAAM,EAAE;EACvB,GAAG,EAAE,mQAAmQ;EACxQ,MAAM,EAAE;AACV,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACV,MAAMS,UAAU,GAAGT,YAAY,CAAC,KAAK,EAAE;EACrC,OAAO,EAAEO,GAAG,CAAC,aAAa,CAAC;EAC3B,SAAS,EAAE;AACb,CAAC,EAAE,CAACP,YAAY,CAAC,MAAM,EAAE;EACvB,GAAG,EAAE,ggBAAggB;EACrgB,MAAM,EAAE;AACV,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACV,IAAIU,aAAa,GAAGZ,eAAe,CAAC;EAClCQ,IAAI;EACJK,KAAK,EAAE;IACLC,IAAI,EAAEC,MAAM;IACZC,IAAI,EAAEb,WAAW;IACjBc,KAAK,EAAEF,MAAM;IACbG,KAAK,EAAEC,OAAO;IACdC,KAAK,EAAED,OAAO;IACdE,OAAO,EAAEF;EACX,CAAC;EACDG,KAAK,EAAE,CAAC,OAAO,CAAC;EAChBC,KAAKA,CAACV,KAAK,EAAE;IACXW,IAAI;IACJC;EACF,CAAC,EAAE;IACD,MAAMC,MAAM,GAAG3B,GAAG,CAAC,KAAK,CAAC;IACzB,MAAM4B,KAAK,GAAGrB,QAAQ,CAAC,CAAC;IACxB,MAAMsB,YAAY,GAAIC,KAAK,IAAK;MAC9BF,KAAK,CAACG,KAAK,CAACD,KAAK,CAAC;MAClBH,MAAM,CAACK,KAAK,GAAG,IAAI;IACrB,CAAC;IACD,MAAMC,WAAW,GAAIH,KAAK,IAAK;MAC7BF,KAAK,CAACM,IAAI,CAACJ,KAAK,CAAC;MACjB,IAAIF,KAAK,CAACO,SAAS,CAACH,KAAK,EAAE;QACzBL,MAAM,CAACK,KAAK,GAAG,KAAK;MACtB;IACF,CAAC;IACD,MAAMI,UAAU,GAAIN,KAAK,IAAK;MAC5B,IAAIH,MAAM,CAACK,KAAK,EAAE;QAChB,IAAI,CAACN,KAAK,CAACW,OAAO,EAAE;UAClB/B,cAAc,CAACwB,KAAK,CAAC;QACvB;QACAH,MAAM,CAACK,KAAK,GAAG,KAAK;QACpBP,IAAI,CAAC,OAAO,EAAEX,KAAK,CAACG,IAAI,EAAEH,KAAK,CAACC,IAAI,CAAC;MACvC;IACF,CAAC;IACD,MAAMuB,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIxB,KAAK,CAACQ,OAAO,EAAE;QACjB,OAAOnB,YAAY,CAACK,OAAO,EAAE;UAC3B,OAAO,EAAEE,GAAG,CAAC,cAAc;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;MACA,MAAMO,IAAI,GAAGS,KAAK,CAACW,OAAO,GAAGX,KAAK,CAACW,OAAO,CAAC,CAAC,GAAGvB,KAAK,CAACG,IAAI;MACzD,QAAQH,KAAK,CAACC,IAAI;QAChB,KAAK,QAAQ;UACX,OAAOE,IAAI,IAAIL,UAAU;QAC3B,KAAK,OAAO;UACV,OAAOK,IAAI,IAAIN,YAAY;QAC7B;UACE,OAAOM,IAAI;MACf;IACF,CAAC;IACD,OAAO,MAAMd,YAAY,CAAC,KAAK,EAAE;MAC/B,OAAO,EAAEO,GAAG,CAAC,SAAS,EAAE;QACtBS,KAAK,EAAEL,KAAK,CAACK;MACf,CAAC,CAAC;MACF,qBAAqB,EAAEU,YAAY;MACnC,oBAAoB,EAAEI,WAAW;MACjC,YAAY,EAAEG,UAAU;MACxB,eAAe,EAAEA;IACnB,CAAC,EAAE,CAACjC,YAAY,CAAC,KAAK,EAAE;MACtB,MAAM,EAAE,QAAQ;MAChB,UAAU,EAAE,CAAC;MACb,OAAO,EAAEO,GAAG,CAAC,CAACI,KAAK,CAACI,KAAK,EAAE;QACzBG,KAAK,EAAEP,KAAK,CAACO,KAAK;QAClBM,MAAM,EAAEA,MAAM,CAACK,KAAK;QACpBO,MAAM,EAAEzB,KAAK,CAACC,IAAI,KAAK;MACzB,CAAC,CAAC;IACJ,CAAC,EAAE,CAACuB,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;AACF,CAAC,CAAC;AACF,SACEzB,aAAa,IAAIwB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}