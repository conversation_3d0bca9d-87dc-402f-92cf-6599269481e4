{"systemParams": "darwin-x64-127", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@babel/core@^7.12.16", "@babel/eslint-parser@^7.12.16", "@vue/cli-plugin-babel@~5.0.0", "@vue/cli-plugin-eslint@~5.0.0", "@vue/cli-service@~5.0.0", "axios@^1.10.0", "core-js@^3.8.3", "eslint-plugin-vue@^8.0.3", "eslint@^7.32.0", "qrcode.vue@^3.6.0", "vant@^4.9.21", "vue-router@^4.5.1", "vue@^3.2.13"], "lockfileEntries": {"@achrinza/node-ipc@^9.2.5": "https://registry.npmjs.org/@achrinza/node-ipc/-/node-ipc-9.2.9.tgz", "@ampproject/remapping@^2.2.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "@babel/code-frame@7.12.11": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.12.11.tgz", "@babel/code-frame@^7.0.0": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@^7.26.2": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@^7.27.1": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/compat-data@^7.27.2": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz", "@babel/compat-data@^7.27.7": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz", "@babel/compat-data@^7.28.0": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz", "@babel/core@^7.12.16": "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz", "@babel/eslint-parser@^7.12.16": "https://registry.npmjs.org/@babel/eslint-parser/-/eslint-parser-7.28.0.tgz", "@babel/generator@^7.28.0": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz", "@babel/helper-annotate-as-pure@^7.27.1": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz", "@babel/helper-annotate-as-pure@^7.27.3": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz", "@babel/helper-compilation-targets@^7.12.16": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-compilation-targets@^7.27.1": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-compilation-targets@^7.27.2": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-create-class-features-plugin@^7.18.6": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz", "@babel/helper-create-class-features-plugin@^7.27.1": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz", "@babel/helper-create-regexp-features-plugin@^7.18.6": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz", "@babel/helper-create-regexp-features-plugin@^7.27.1": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz", "@babel/helper-define-polyfill-provider@^0.6.5": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.5.tgz", "@babel/helper-globals@^7.28.0": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "@babel/helper-member-expression-to-functions@^7.27.1": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz", "@babel/helper-module-imports@^7.0.0": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-imports@^7.12.13": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-imports@^7.25.9": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-imports@^7.27.1": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-transforms@^7.27.1": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "@babel/helper-module-transforms@^7.27.3": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "@babel/helper-optimise-call-expression@^7.27.1": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.18.6": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.26.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.27.1": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-remap-async-to-generator@^7.27.1": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz", "@babel/helper-replace-supers@^7.27.1": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.27.1": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz", "@babel/helper-string-parser@^7.27.1": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "@babel/helper-validator-identifier@^7.25.9": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "@babel/helper-validator-identifier@^7.27.1": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "@babel/helper-validator-option@^7.27.1": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "@babel/helper-wrap-function@^7.27.1": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz", "@babel/helpers@^7.27.6": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "@babel/highlight@^7.10.4": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.25.9.tgz", "@babel/parser@^7.26.9": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "@babel/parser@^7.27.2": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "@babel/parser@^7.27.5": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "@babel/parser@^7.28.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.27.1": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz", "@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.27.1": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.27.1": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.27.1": "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.27.1": "https://registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.27.1.tgz", "@babel/plugin-proposal-class-properties@^7.12.13": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "@babel/plugin-proposal-decorators@^7.12.13": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.28.0.tgz", "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz", "@babel/plugin-syntax-decorators@^7.27.1": "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.27.1.tgz", "@babel/plugin-syntax-dynamic-import@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "@babel/plugin-syntax-import-assertions@^7.27.1": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz", "@babel/plugin-syntax-import-attributes@^7.27.1": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "@babel/plugin-syntax-jsx@^7.12.13": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "@babel/plugin-syntax-jsx@^7.2.0": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "@babel/plugin-syntax-jsx@^7.25.9": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "@babel/plugin-syntax-unicode-sets-regex@^7.18.6": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz", "@babel/plugin-transform-arrow-functions@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz", "@babel/plugin-transform-async-generator-functions@^7.28.0": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.28.0.tgz", "@babel/plugin-transform-async-to-generator@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz", "@babel/plugin-transform-block-scoped-functions@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.27.1.tgz", "@babel/plugin-transform-block-scoping@^7.28.0": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.28.0.tgz", "@babel/plugin-transform-class-properties@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz", "@babel/plugin-transform-class-static-block@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.27.1.tgz", "@babel/plugin-transform-classes@^7.28.0": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.28.0.tgz", "@babel/plugin-transform-computed-properties@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz", "@babel/plugin-transform-destructuring@^7.28.0": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.28.0.tgz", "@babel/plugin-transform-dotall-regex@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.27.1.tgz", "@babel/plugin-transform-duplicate-keys@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.27.1.tgz", "@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz", "@babel/plugin-transform-dynamic-import@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.27.1.tgz", "@babel/plugin-transform-explicit-resource-management@^7.28.0": "https://registry.npmjs.org/@babel/plugin-transform-explicit-resource-management/-/plugin-transform-explicit-resource-management-7.28.0.tgz", "@babel/plugin-transform-exponentiation-operator@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.27.1.tgz", "@babel/plugin-transform-export-namespace-from@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz", "@babel/plugin-transform-for-of@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz", "@babel/plugin-transform-function-name@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz", "@babel/plugin-transform-json-strings@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.27.1.tgz", "@babel/plugin-transform-literals@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz", "@babel/plugin-transform-logical-assignment-operators@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz", "@babel/plugin-transform-member-expression-literals@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.27.1.tgz", "@babel/plugin-transform-modules-amd@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.27.1.tgz", "@babel/plugin-transform-modules-commonjs@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz", "@babel/plugin-transform-modules-systemjs@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.27.1.tgz", "@babel/plugin-transform-modules-umd@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.27.1.tgz", "@babel/plugin-transform-named-capturing-groups-regex@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz", "@babel/plugin-transform-new-target@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.27.1.tgz", "@babel/plugin-transform-nullish-coalescing-operator@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz", "@babel/plugin-transform-numeric-separator@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz", "@babel/plugin-transform-object-rest-spread@^7.28.0": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.28.0.tgz", "@babel/plugin-transform-object-super@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.27.1.tgz", "@babel/plugin-transform-optional-catch-binding@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz", "@babel/plugin-transform-optional-chaining@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz", "@babel/plugin-transform-parameters@^7.27.7": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.7.tgz", "@babel/plugin-transform-private-methods@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.27.1.tgz", "@babel/plugin-transform-private-property-in-object@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz", "@babel/plugin-transform-property-literals@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.27.1.tgz", "@babel/plugin-transform-regenerator@^7.28.0": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.28.1.tgz", "@babel/plugin-transform-regexp-modifiers@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.27.1.tgz", "@babel/plugin-transform-reserved-words@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.27.1.tgz", "@babel/plugin-transform-runtime@^7.12.15": "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.28.0.tgz", "@babel/plugin-transform-shorthand-properties@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz", "@babel/plugin-transform-spread@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz", "@babel/plugin-transform-sticky-regex@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz", "@babel/plugin-transform-template-literals@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz", "@babel/plugin-transform-typeof-symbol@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.27.1.tgz", "@babel/plugin-transform-unicode-escapes@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.27.1.tgz", "@babel/plugin-transform-unicode-property-regex@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.27.1.tgz", "@babel/plugin-transform-unicode-regex@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz", "@babel/plugin-transform-unicode-sets-regex@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.27.1.tgz", "@babel/preset-env@^7.12.16": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.28.0.tgz", "@babel/preset-modules@0.1.6-no-external-plugins": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz", "@babel/runtime@^7.12.13": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/template@^7.26.9": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/template@^7.27.1": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/template@^7.27.2": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/traverse@^7.26.9": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "@babel/traverse@^7.27.1": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "@babel/traverse@^7.27.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "@babel/traverse@^7.28.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "@babel/types@^7.26.9": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.27.1": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.27.3": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.27.6": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.28.0": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.4.4": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@discoveryjs/json-ext@0.5.7": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz", "@eslint/eslintrc@^0.4.3": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.4.3.tgz", "@hapi/hoek@^9.0.0": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.3.0.tgz", "@hapi/hoek@^9.3.0": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.3.0.tgz", "@hapi/topo@^5.1.0": "https://registry.npmjs.org/@hapi/topo/-/topo-5.1.0.tgz", "@humanwhocodes/config-array@^0.5.0": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.5.0.tgz", "@humanwhocodes/object-schema@^1.2.0": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "@jridgewell/gen-mapping@^0.3.12": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/source-map@^0.3.3": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.10.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/sourcemap-codec@^1.5.0": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "@jridgewell/trace-mapping@^0.3.25": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "@jridgewell/trace-mapping@^0.3.28": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "@leichtgewicht/ip-codec@^2.0.1": "https://registry.npmjs.org/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz", "@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1": "https://registry.npmjs.org/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz", "@node-ipc/js-queue@2.0.3": "https://registry.npmjs.org/@node-ipc/js-queue/-/js-queue-2.0.3.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@polka/url@^1.0.0-next.24": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.29.tgz", "@sideway/address@^4.1.5": "https://registry.npmjs.org/@sideway/address/-/address-4.1.5.tgz", "@sideway/formula@^3.0.1": "https://registry.npmjs.org/@sideway/formula/-/formula-3.0.1.tgz", "@sideway/pinpoint@^2.0.0": "https://registry.npmjs.org/@sideway/pinpoint/-/pinpoint-2.0.0.tgz", "@soda/friendly-errors-webpack-plugin@^1.8.0": "https://registry.npmjs.org/@soda/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.8.1.tgz", "@soda/get-current-script@^1.0.2": "https://registry.npmjs.org/@soda/get-current-script/-/get-current-script-1.0.2.tgz", "@trysound/sax@0.2.0": "https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz", "@types/body-parser@*": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.6.tgz", "@types/bonjour@^3.5.9": "https://registry.npmjs.org/@types/bonjour/-/bonjour-3.5.13.tgz", "@types/connect-history-api-fallback@^1.3.5": "https://registry.npmjs.org/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.5.4.tgz", "@types/connect@*": "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz", "@types/eslint-scope@^3.7.7": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz", "@types/eslint@*": "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz", "@types/eslint@^7.29.0 || ^8.4.1": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.12.tgz", "@types/estree@*": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz", "@types/estree@^1.0.8": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz", "@types/express-serve-static-core@*": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.7.tgz", "@types/express-serve-static-core@^4.17.33": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz", "@types/express-serve-static-core@^5.0.0": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.7.tgz", "@types/express@*": "https://registry.npmjs.org/@types/express/-/express-5.0.3.tgz", "@types/express@^4.17.13": "https://registry.npmjs.org/@types/express/-/express-4.17.23.tgz", "@types/html-minifier-terser@^6.0.0": "https://registry.npmjs.org/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz", "@types/http-errors@*": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.5.tgz", "@types/http-proxy@^1.17.8": "https://registry.npmjs.org/@types/http-proxy/-/http-proxy-1.17.16.tgz", "@types/json-schema@*": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.15": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.5": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.8": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.9": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/mime@^1": "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz", "@types/minimist@^1.2.0": "https://registry.npmjs.org/@types/minimist/-/minimist-1.2.5.tgz", "@types/node-forge@^1.3.0": "https://registry.npmjs.org/@types/node-forge/-/node-forge-1.3.13.tgz", "@types/node@*": "https://registry.npmjs.org/@types/node/-/node-24.0.14.tgz", "@types/normalize-package-data@^2.4.0": "https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz", "@types/parse-json@^4.0.0": "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.2.tgz", "@types/qs@*": "https://registry.npmjs.org/@types/qs/-/qs-6.14.0.tgz", "@types/range-parser@*": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz", "@types/retry@0.12.0": "https://registry.npmjs.org/@types/retry/-/retry-0.12.0.tgz", "@types/send@*": "https://registry.npmjs.org/@types/send/-/send-0.17.5.tgz", "@types/serve-index@^1.9.1": "https://registry.npmjs.org/@types/serve-index/-/serve-index-1.9.4.tgz", "@types/serve-static@*": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.8.tgz", "@types/serve-static@^1.13.10": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.8.tgz", "@types/sockjs@^0.3.33": "https://registry.npmjs.org/@types/sockjs/-/sockjs-0.3.36.tgz", "@types/ws@^8.5.5": "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz", "@vant/popperjs@^1.3.0": "https://registry.npmjs.org/@vant/popperjs/-/popperjs-1.3.0.tgz", "@vant/use@^1.6.0": "https://registry.npmjs.org/@vant/use/-/use-1.6.0.tgz", "@vue/babel-helper-vue-jsx-merge-props@^1.4.0": "https://registry.npmjs.org/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz", "@vue/babel-helper-vue-transform-on@1.4.0": "https://registry.npmjs.org/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.4.0.tgz", "@vue/babel-plugin-jsx@^1.0.3": "https://registry.npmjs.org/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.4.0.tgz", "@vue/babel-plugin-resolve-type@1.4.0": "https://registry.npmjs.org/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.4.0.tgz", "@vue/babel-plugin-transform-vue-jsx@^1.4.0": "https://registry.npmjs.org/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz", "@vue/babel-preset-app@^5.0.8": "https://registry.npmjs.org/@vue/babel-preset-app/-/babel-preset-app-5.0.8.tgz", "@vue/babel-preset-jsx@^1.1.2": "https://registry.npmjs.org/@vue/babel-preset-jsx/-/babel-preset-jsx-1.4.0.tgz", "@vue/babel-sugar-composition-api-inject-h@^1.4.0": "https://registry.npmjs.org/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz", "@vue/babel-sugar-composition-api-render-instance@^1.4.0": "https://registry.npmjs.org/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz", "@vue/babel-sugar-functional-vue@^1.4.0": "https://registry.npmjs.org/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz", "@vue/babel-sugar-inject-h@^1.4.0": "https://registry.npmjs.org/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz", "@vue/babel-sugar-v-model@^1.4.0": "https://registry.npmjs.org/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz", "@vue/babel-sugar-v-on@^1.4.0": "https://registry.npmjs.org/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz", "@vue/cli-overlay@^5.0.8": "https://registry.npmjs.org/@vue/cli-overlay/-/cli-overlay-5.0.8.tgz", "@vue/cli-plugin-babel@~5.0.0": "https://registry.npmjs.org/@vue/cli-plugin-babel/-/cli-plugin-babel-5.0.8.tgz", "@vue/cli-plugin-eslint@~5.0.0": "https://registry.npmjs.org/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.8.tgz", "@vue/cli-plugin-router@^5.0.8": "https://registry.npmjs.org/@vue/cli-plugin-router/-/cli-plugin-router-5.0.8.tgz", "@vue/cli-plugin-vuex@^5.0.8": "https://registry.npmjs.org/@vue/cli-plugin-vuex/-/cli-plugin-vuex-5.0.8.tgz", "@vue/cli-service@~5.0.0": "https://registry.npmjs.org/@vue/cli-service/-/cli-service-5.0.8.tgz", "@vue/cli-shared-utils@^5.0.8": "https://registry.npmjs.org/@vue/cli-shared-utils/-/cli-shared-utils-5.0.8.tgz", "@vue/compiler-core@3.5.17": "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.17.tgz", "@vue/compiler-dom@3.5.17": "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.17.tgz", "@vue/compiler-sfc@3.5.17": "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.17.tgz", "@vue/compiler-sfc@^3.5.13": "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.17.tgz", "@vue/compiler-ssr@3.5.17": "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.17.tgz", "@vue/component-compiler-utils@^3.1.0": "https://registry.npmjs.org/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz", "@vue/component-compiler-utils@^3.3.0": "https://registry.npmjs.org/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz", "@vue/devtools-api@^6.6.4": "https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "@vue/reactivity@3.5.17": "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.17.tgz", "@vue/runtime-core@3.5.17": "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.17.tgz", "@vue/runtime-dom@3.5.17": "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.17.tgz", "@vue/server-renderer@3.5.17": "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.17.tgz", "@vue/shared@3.5.17": "https://registry.npmjs.org/@vue/shared/-/shared-3.5.17.tgz", "@vue/shared@^3.5.13": "https://registry.npmjs.org/@vue/shared/-/shared-3.5.17.tgz", "@vue/shared@^3.5.17": "https://registry.npmjs.org/@vue/shared/-/shared-3.5.17.tgz", "@vue/vue-loader-v15@npm:vue-loader@^15.9.7": "https://registry.npmjs.org/vue-loader/-/vue-loader-15.11.1.tgz", "@vue/web-component-wrapper@^1.3.0": "https://registry.npmjs.org/@vue/web-component-wrapper/-/web-component-wrapper-1.3.0.tgz", "@webassemblyjs/ast@1.14.1": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz", "@webassemblyjs/ast@^1.14.1": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz", "@webassemblyjs/floating-point-hex-parser@1.13.2": "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz", "@webassemblyjs/helper-api-error@1.13.2": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz", "@webassemblyjs/helper-buffer@1.14.1": "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz", "@webassemblyjs/helper-numbers@1.13.2": "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz", "@webassemblyjs/helper-wasm-bytecode@1.13.2": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz", "@webassemblyjs/helper-wasm-section@1.14.1": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz", "@webassemblyjs/ieee754@1.13.2": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz", "@webassemblyjs/leb128@1.13.2": "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.13.2.tgz", "@webassemblyjs/utf8@1.13.2": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.2.tgz", "@webassemblyjs/wasm-edit@^1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz", "@webassemblyjs/wasm-gen@1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz", "@webassemblyjs/wasm-opt@1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz", "@webassemblyjs/wasm-parser@1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz", "@webassemblyjs/wasm-parser@^1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz", "@webassemblyjs/wast-printer@1.14.1": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz", "@xtuc/ieee754@^1.2.0": "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "@xtuc/long@4.2.2": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz", "accepts@~1.3.4": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "accepts@~1.3.8": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "acorn-import-phases@^1.0.3": "https://registry.npmjs.org/acorn-import-phases/-/acorn-import-phases-1.0.4.tgz", "acorn-jsx@^5.3.1": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn-jsx@^5.3.2": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn-walk@^8.0.0": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "acorn-walk@^8.0.2": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "acorn@^7.4.0": "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz", "acorn@^8.0.4": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "acorn@^8.0.5": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "acorn@^8.11.0": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "acorn@^8.14.0": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "acorn@^8.15.0": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "acorn@^8.9.0": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "address@^1.1.2": "https://registry.npmjs.org/address/-/address-1.2.2.tgz", "ajv-formats@^2.1.1": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz", "ajv-keywords@^3.5.2": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv-keywords@^5.1.0": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "ajv@^6.10.0": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.4": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.5": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^8.0.0": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "ajv@^8.0.1": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "ajv@^8.9.0": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "ansi-colors@^4.1.1": "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz", "ansi-escapes@^3.0.0": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.2.0.tgz", "ansi-html-community@^0.0.8": "https://registry.npmjs.org/ansi-html-community/-/ansi-html-community-0.0.8.tgz", "ansi-regex@^3.0.0": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.1.tgz", "ansi-regex@^5.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-styles@^3.2.1": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "any-promise@^1.0.0": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz", "anymatch@~3.1.2": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "arch@^2.1.1": "https://registry.npmjs.org/arch/-/arch-2.2.0.tgz", "argparse@^1.0.7": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "array-flatten@1.1.1": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "array-union@^2.1.0": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "astral-regex@^2.0.0": "https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz", "async@^3.2.6": "https://registry.npmjs.org/async/-/async-3.2.6.tgz", "asynckit@^0.4.0": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "at-least-node@^1.0.0": "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz", "autoprefixer@^10.2.4": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz", "axios@^1.10.0": "https://registry.npmjs.org/axios/-/axios-1.10.0.tgz", "babel-loader@^8.2.2": "https://registry.npmjs.org/babel-loader/-/babel-loader-8.4.1.tgz", "babel-plugin-dynamic-import-node@^2.3.3": "https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz", "babel-plugin-polyfill-corejs2@^0.4.14": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.14.tgz", "babel-plugin-polyfill-corejs3@^0.13.0": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.13.0.tgz", "babel-plugin-polyfill-regenerator@^0.6.5": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.5.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "base64-js@^1.3.1": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "batch@0.6.1": "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz", "big.js@^5.2.2": "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz", "binary-extensions@^2.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "bl@^4.1.0": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "bluebird@^3.1.1": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "body-parser@1.20.3": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz", "bonjour-service@^1.0.11": "https://registry.npmjs.org/bonjour-service/-/bonjour-service-1.3.0.tgz", "boolbase@^1.0.0": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "braces@^3.0.3": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "braces@~3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "browserslist@^4.0.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "browserslist@^4.16.3": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "browserslist@^4.21.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "browserslist@^4.24.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "browserslist@^4.24.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "browserslist@^4.25.1": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "buffer-from@^1.0.0": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "buffer@^5.5.0": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "bytes@3.1.2": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "call-bind-apply-helpers@^1.0.0": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.1": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.2": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind@^1.0.8": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz", "call-bound@^1.0.2": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "call-bound@^1.0.3": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "callsites@^3.0.0": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "camel-case@^4.1.2": "https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz", "camelcase@^5.0.0": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "caniuse-api@^3.0.0": "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz", "caniuse-lite@^1.0.0": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "caniuse-lite@^1.0.30001702": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "caniuse-lite@^1.0.30001726": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "case-sensitive-paths-webpack-plugin@^2.3.0": "https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz", "chalk@^2.1.0": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^2.4.2": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^3.0.0": "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz", "chalk@^4.0.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.2": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chokidar@^3.5.3": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "chrome-trace-event@^1.0.2": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz", "ci-info@^1.5.0": "https://registry.npmjs.org/ci-info/-/ci-info-1.6.0.tgz", "clean-css@^5.2.2": "https://registry.npmjs.org/clean-css/-/clean-css-5.3.3.tgz", "cli-cursor@^2.0.0": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz", "cli-cursor@^3.1.0": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz", "cli-highlight@^2.1.10": "https://registry.npmjs.org/cli-highlight/-/cli-highlight-2.1.11.tgz", "cli-spinners@^2.5.0": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz", "clipboardy@^2.3.0": "https://registry.npmjs.org/clipboardy/-/clipboardy-2.3.0.tgz", "cliui@^7.0.2": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "cliui@^7.0.4": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "clone-deep@^4.0.1": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz", "clone@^1.0.2": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "color-convert@^1.9.0": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "color-name@~1.1.4": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "colord@^2.9.1": "https://registry.npmjs.org/colord/-/colord-2.9.3.tgz", "colorette@^2.0.10": "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz", "combined-stream@^1.0.8": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "commander@^2.20.0": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "commander@^7.2.0": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "commander@^8.3.0": "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz", "commondir@^1.0.1": "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz", "compressible@~2.0.18": "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz", "compression@^1.7.4": "https://registry.npmjs.org/compression/-/compression-1.8.1.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "connect-history-api-fallback@^2.0.0": "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz", "consolidate@^0.15.1": "https://registry.npmjs.org/consolidate/-/consolidate-0.15.1.tgz", "content-disposition@0.5.4": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "content-type@~1.0.4": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "content-type@~1.0.5": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "convert-source-map@^2.0.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "cookie-signature@1.0.6": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "cookie@0.7.1": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz", "copy-webpack-plugin@^9.0.1": "https://registry.npmjs.org/copy-webpack-plugin/-/copy-webpack-plugin-9.1.0.tgz", "core-js-compat@^3.43.0": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.44.0.tgz", "core-js-compat@^3.8.3": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.44.0.tgz", "core-js@^3.8.3": "https://registry.npmjs.org/core-js/-/core-js-3.44.0.tgz", "core-util-is@~1.0.0": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "cosmiconfig@^7.0.0": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "cross-spawn@^5.0.1": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz", "cross-spawn@^6.0.0": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.6.tgz", "cross-spawn@^7.0.2": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.3": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "css-declaration-sorter@^6.3.1": "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-6.4.1.tgz", "css-loader@^6.5.0": "https://registry.npmjs.org/css-loader/-/css-loader-6.11.0.tgz", "css-minimizer-webpack-plugin@^3.0.2": "https://registry.npmjs.org/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-3.4.1.tgz", "css-select@^4.1.3": "https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz", "css-tree@^1.1.2": "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz", "css-tree@^1.1.3": "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz", "css-what@^6.0.1": "https://registry.npmjs.org/css-what/-/css-what-6.2.2.tgz", "cssesc@^3.0.0": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz", "cssnano-preset-default@^5.2.14": "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-5.2.14.tgz", "cssnano-utils@^3.1.0": "https://registry.npmjs.org/cssnano-utils/-/cssnano-utils-3.1.0.tgz", "cssnano@^5.0.0": "https://registry.npmjs.org/cssnano/-/cssnano-5.1.15.tgz", "cssnano@^5.0.6": "https://registry.npmjs.org/cssnano/-/cssnano-5.1.15.tgz", "csso@^4.2.0": "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz", "csstype@^3.1.3": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "debounce@^1.2.1": "https://registry.npmjs.org/debounce/-/debounce-1.2.1.tgz", "debug@2.6.9": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@^4.0.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.1.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.2": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.6": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.4.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "deep-is@^0.1.3": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz", "deepmerge@^1.5.2": "https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.2.tgz", "default-gateway@^6.0.3": "https://registry.npmjs.org/default-gateway/-/default-gateway-6.0.3.tgz", "defaults@^1.0.3": "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz", "define-data-property@^1.0.1": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "define-data-property@^1.1.4": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "define-lazy-prop@^2.0.0": "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "define-properties@^1.2.1": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "delayed-stream@~1.0.0": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "depd@2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "depd@~1.1.2": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "destroy@1.2.0": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "detect-node@^2.0.4": "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz", "dir-glob@^3.0.1": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "dns-packet@^5.2.2": "https://registry.npmjs.org/dns-packet/-/dns-packet-5.6.1.tgz", "doctrine@^3.0.0": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz", "dom-converter@^0.2.0": "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz", "dom-serializer@^1.0.1": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz", "domelementtype@^2.0.1": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "domelementtype@^2.2.0": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "domhandler@^4.0.0": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.2.0": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.3.1": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domutils@^2.5.2": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "domutils@^2.8.0": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "dot-case@^3.0.4": "https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz", "dotenv-expand@^5.1.0": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz", "dotenv@^10.0.0": "https://registry.npmjs.org/dotenv/-/dotenv-10.0.0.tgz", "dunder-proto@^1.0.1": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "duplexer@^0.1.2": "https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz", "easy-stack@1.0.1": "https://registry.npmjs.org/easy-stack/-/easy-stack-1.0.1.tgz", "ee-first@1.1.1": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "electron-to-chromium@^1.5.173": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.187.tgz", "emoji-regex@^8.0.0": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "emojis-list@^3.0.0": "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz", "encodeurl@~1.0.2": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "encodeurl@~2.0.0": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "end-of-stream@^1.1.0": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz", "enhanced-resolve@^5.17.2": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz", "enquirer@^2.3.5": "https://registry.npmjs.org/enquirer/-/enquirer-2.4.1.tgz", "entities@^2.0.0": "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz", "entities@^4.5.0": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz", "error-ex@^1.3.1": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "error-stack-parser@^2.0.6": "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz", "es-define-property@^1.0.0": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "es-define-property@^1.0.1": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "es-errors@^1.3.0": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "es-module-lexer@^1.2.1": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "es-object-atoms@^1.0.0": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-object-atoms@^1.1.1": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-set-tostringtag@^2.1.0": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escalade@^3.2.0": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escape-html@~1.0.3": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "eslint-plugin-vue@^8.0.3": "https://registry.npmjs.org/eslint-plugin-vue/-/eslint-plugin-vue-8.7.1.tgz", "eslint-scope@5.1.1": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^5.1.1": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^7.0.0": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz", "eslint-utils@^2.1.0": "https://registry.npmjs.org/eslint-utils/-/eslint-utils-2.1.0.tgz", "eslint-utils@^3.0.0": "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz", "eslint-visitor-keys@^1.1.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "eslint-visitor-keys@^1.3.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "eslint-visitor-keys@^2.0.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "eslint-visitor-keys@^2.1.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "eslint-visitor-keys@^3.1.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-visitor-keys@^3.4.1": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-webpack-plugin@^3.1.0": "https://registry.npmjs.org/eslint-webpack-plugin/-/eslint-webpack-plugin-3.2.0.tgz", "eslint@^7.32.0": "https://registry.npmjs.org/eslint/-/eslint-7.32.0.tgz", "espree@^7.3.0": "https://registry.npmjs.org/espree/-/espree-7.3.1.tgz", "espree@^7.3.1": "https://registry.npmjs.org/espree/-/espree-7.3.1.tgz", "espree@^9.0.0": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz", "esprima@^4.0.0": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "esquery@^1.4.0": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz", "esrecurse@^4.3.0": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.2.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estree-walker@^2.0.2": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz", "esutils@^2.0.2": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "etag@~1.8.1": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "event-pubsub@4.3.0": "https://registry.npmjs.org/event-pubsub/-/event-pubsub-4.3.0.tgz", "eventemitter3@^4.0.0": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "events@^3.2.0": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "execa@^0.8.0": "https://registry.npmjs.org/execa/-/execa-0.8.0.tgz", "execa@^1.0.0": "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz", "execa@^5.0.0": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "express@^4.17.3": "https://registry.npmjs.org/express/-/express-4.21.2.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-glob@^3.2.7": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fast-glob@^3.2.9": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fast-uri@^3.0.1": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz", "fastq@^1.6.0": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz", "faye-websocket@^0.11.3": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz", "figures@^2.0.0": "https://registry.npmjs.org/figures/-/figures-2.0.0.tgz", "file-entry-cache@^6.0.1": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "fill-range@^7.1.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "finalhandler@1.3.1": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz", "find-cache-dir@^3.3.1": "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-3.3.2.tgz", "find-up@^4.0.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@^4.1.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "flat-cache@^3.0.4": "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz", "flat@^5.0.2": "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz", "flatted@^3.2.9": "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz", "follow-redirects@^1.0.0": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "follow-redirects@^1.15.6": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "form-data@^4.0.0": "https://registry.npmjs.org/form-data/-/form-data-4.0.4.tgz", "forwarded@0.2.0": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "fraction.js@^4.3.7": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz", "fresh@0.5.2": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "fs-extra@^9.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs-monkey@^1.0.4": "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.6.tgz", "fs.realpath@^1.0.0": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@~2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.2": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "functional-red-black-tree@^1.0.1": "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.5": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.2.4": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.2.5": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.2.6": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.3.0": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-proto@^1.0.1": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "get-stream@^3.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz", "get-stream@^4.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz", "get-stream@^6.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "glob-parent@^5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^6.0.1": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz", "glob-parent@~5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-to-regexp@^0.4.1": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "glob@^7.1.3": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "globals@^13.6.0": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz", "globals@^13.9.0": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz", "globby@^11.0.2": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "globby@^11.0.3": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "gopd@^1.0.1": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "gopd@^1.2.0": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "graceful-fs@^4.1.2": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.1.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.0": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.11": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.4": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "gzip-size@^6.0.0": "https://registry.npmjs.org/gzip-size/-/gzip-size-6.0.0.tgz", "handle-thing@^2.0.0": "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz", "has-flag@^3.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "has-property-descriptors@^1.0.0": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "has-property-descriptors@^1.0.2": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "has-symbols@^1.0.3": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "has-symbols@^1.1.0": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "has-tostringtag@^1.0.2": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "hash-sum@^1.0.2": "https://registry.npmjs.org/hash-sum/-/hash-sum-1.0.2.tgz", "hash-sum@^2.0.0": "https://registry.npmjs.org/hash-sum/-/hash-sum-2.0.0.tgz", "hasown@^2.0.2": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "he@^1.2.0": "https://registry.npmjs.org/he/-/he-1.2.0.tgz", "highlight.js@^10.7.1": "https://registry.npmjs.org/highlight.js/-/highlight.js-10.7.3.tgz", "hosted-git-info@^2.1.4": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz", "hpack.js@^2.1.6": "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz", "html-entities@^2.3.2": "https://registry.npmjs.org/html-entities/-/html-entities-2.6.0.tgz", "html-escaper@^2.0.2": "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz", "html-minifier-terser@^6.0.2": "https://registry.npmjs.org/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz", "html-tags@^2.0.0": "https://registry.npmjs.org/html-tags/-/html-tags-2.0.0.tgz", "html-webpack-plugin@^5.1.0": "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-5.6.3.tgz", "htmlparser2@^6.1.0": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-6.1.0.tgz", "http-deceiver@^1.2.7": "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz", "http-errors@2.0.0": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "http-errors@~1.6.2": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "http-parser-js@>=0.5.1": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.10.tgz", "http-proxy-middleware@^2.0.3": "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-2.0.9.tgz", "http-proxy@^1.18.1": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz", "human-signals@^2.1.0": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz", "iconv-lite@0.4.24": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "icss-utils@^5.0.0": "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz", "icss-utils@^5.1.0": "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz", "ieee754@^1.1.13": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "ignore@^4.0.6": "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz", "ignore@^5.2.0": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "import-fresh@^3.0.0": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz", "import-fresh@^3.2.1": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz", "imurmurhash@^0.1.4": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "inflight@^1.0.4": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "inherits@2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.1": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "ipaddr.js@1.9.1": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "ipaddr.js@^2.0.1": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.2.0.tgz", "is-arrayish@^0.2.1": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "is-binary-path@~2.1.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-ci@^1.0.10": "https://registry.npmjs.org/is-ci/-/is-ci-1.2.1.tgz", "is-core-module@^2.16.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "is-docker@^2.0.0": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "is-docker@^2.1.1": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "is-extglob@^2.1.1": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-file-esm@^1.0.0": "https://registry.npmjs.org/is-file-esm/-/is-file-esm-1.0.0.tgz", "is-fullwidth-code-point@^2.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-glob@^4.0.0": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.3": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-interactive@^1.0.0": "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-plain-obj@^3.0.0": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-3.0.0.tgz", "is-plain-object@^2.0.4": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "is-stream@^1.1.0": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "is-stream@^2.0.0": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "is-unicode-supported@^0.1.0": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "is-wsl@^2.1.1": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "is-wsl@^2.2.0": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "isarray@~1.0.0": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "isexe@^2.0.0": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "isobject@^3.0.1": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "javascript-stringify@^2.0.1": "https://registry.npmjs.org/javascript-stringify/-/javascript-stringify-2.1.0.tgz", "jest-worker@^27.0.2": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "jest-worker@^27.4.5": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "jest-worker@^28.0.2": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.1.3.tgz", "joi@^17.4.0": "https://registry.npmjs.org/joi/-/joi-17.13.3.tgz", "js-message@1.0.7": "https://registry.npmjs.org/js-message/-/js-message-1.0.7.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@^3.13.1": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "jsesc@^3.0.2": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "jsesc@~3.0.2": "https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz", "json-buffer@3.0.1": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "json-parse-better-errors@^1.0.2": "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "json-parse-even-better-errors@^2.3.0": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-parse-even-better-errors@^2.3.1": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-schema-traverse@^1.0.0": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "json5@^1.0.1": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "json5@^2.1.2": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "json5@^2.2.3": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "jsonfile@^6.0.1": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "keyv@^4.5.3": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz", "kind-of@^6.0.2": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "klona@^2.0.5": "https://registry.npmjs.org/klona/-/klona-2.0.6.tgz", "launch-editor-middleware@^2.2.1": "https://registry.npmjs.org/launch-editor-middleware/-/launch-editor-middleware-2.10.0.tgz", "launch-editor@^2.10.0": "https://registry.npmjs.org/launch-editor/-/launch-editor-2.10.0.tgz", "launch-editor@^2.2.1": "https://registry.npmjs.org/launch-editor/-/launch-editor-2.10.0.tgz", "launch-editor@^2.6.0": "https://registry.npmjs.org/launch-editor/-/launch-editor-2.10.0.tgz", "levn@^0.4.1": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "lilconfig@^2.0.3": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz", "lines-and-columns@^1.1.6": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "loader-runner@^4.1.0": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz", "loader-runner@^4.2.0": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz", "loader-utils@^1.0.2": "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz", "loader-utils@^1.1.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz", "loader-utils@^2.0.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz", "loader-utils@^2.0.4": "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz", "locate-path@^5.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "lodash.debounce@^4.0.8": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "lodash.defaultsdeep@^4.6.1": "https://registry.npmjs.org/lodash.defaultsdeep/-/lodash.defaultsdeep-4.6.1.tgz", "lodash.kebabcase@^4.1.1": "https://registry.npmjs.org/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz", "lodash.mapvalues@^4.6.0": "https://registry.npmjs.org/lodash.mapvalues/-/lodash.mapvalues-4.6.0.tgz", "lodash.memoize@^4.1.2": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "lodash.merge@^4.6.2": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "lodash.truncate@^4.4.2": "https://registry.npmjs.org/lodash.truncate/-/lodash.truncate-4.4.2.tgz", "lodash.uniq@^4.5.0": "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz", "lodash@^4.17.20": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.21": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "log-symbols@^4.1.0": "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz", "log-update@^2.3.0": "https://registry.npmjs.org/log-update/-/log-update-2.3.0.tgz", "lower-case@^2.0.2": "https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz", "lru-cache@^4.0.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz", "lru-cache@^4.1.2": "https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz", "lru-cache@^5.1.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "lru-cache@^6.0.0": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "magic-string@^0.30.17": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "make-dir@^3.0.2": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "make-dir@^3.1.0": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "math-intrinsics@^1.1.0": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "mdn-data@2.0.14": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz", "media-typer@0.3.0": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "memfs@^3.4.3": "https://registry.npmjs.org/memfs/-/memfs-3.6.0.tgz", "merge-descriptors@1.0.3": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "merge-source-map@^1.1.0": "https://registry.npmjs.org/merge-source-map/-/merge-source-map-1.1.0.tgz", "merge-stream@^2.0.0": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "merge2@^1.4.1": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "methods@~1.1.2": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "micromatch@^4.0.2": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "micromatch@^4.0.5": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "micromatch@^4.0.8": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-db@>= 1.43.0 < 2": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "mime-types@^2.1.12": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.27": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.31": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.17": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.24": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.34": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime@1.6.0": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "mimic-fn@^1.0.0": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz", "mimic-fn@^2.1.0": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "mini-css-extract-plugin@^2.5.3": "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-2.9.2.tgz", "minimalistic-assert@^1.0.0": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "minimatch@^3.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.1": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimist@^1.2.0": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minimist@^1.2.5": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minipass@^3.1.1": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "module-alias@^2.2.2": "https://registry.npmjs.org/module-alias/-/module-alias-2.2.3.tgz", "mrmime@^2.0.0": "https://registry.npmjs.org/mrmime/-/mrmime-2.0.1.tgz", "ms@2.0.0": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "ms@2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "multicast-dns@^7.2.5": "https://registry.npmjs.org/multicast-dns/-/multicast-dns-7.2.5.tgz", "mz@^2.4.0": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz", "nanoid@^3.3.11": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "natural-compare@^1.4.0": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "negotiator@0.6.3": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "negotiator@~0.6.4": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz", "neo-async@^2.6.2": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "nice-try@^1.0.4": "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz", "no-case@^3.0.4": "https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz", "node-fetch@^2.6.7": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz", "node-forge@^1": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "node-releases@^2.0.19": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "normalize-package-data@^2.5.0": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "normalize-path@^1.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-1.0.0.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-range@^0.1.2": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "normalize-url@^6.0.1": "https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz", "npm-run-path@^2.0.0": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz", "npm-run-path@^4.0.1": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "nth-check@^2.0.1": "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz", "object-assign@^4.0.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-inspect@^1.13.3": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "object-keys@^1.1.1": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "object.assign@^4.1.0": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz", "obuf@^1.0.0": "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz", "obuf@^1.1.2": "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz", "on-finished@2.4.1": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "on-headers@~1.1.0": "https://registry.npmjs.org/on-headers/-/on-headers-1.1.0.tgz", "once@^1.3.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "once@^1.3.1": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "once@^1.4.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "onetime@^2.0.0": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "onetime@^5.1.0": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "onetime@^5.1.2": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "open@^8.0.2": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "open@^8.0.9": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "opener@^1.5.2": "https://registry.npmjs.org/opener/-/opener-1.5.2.tgz", "optionator@^0.9.1": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz", "ora@^5.3.0": "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz", "p-finally@^1.0.0": "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz", "p-limit@^2.2.0": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "p-locate@^4.1.0": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "p-retry@^4.5.0": "https://registry.npmjs.org/p-retry/-/p-retry-4.6.2.tgz", "p-try@^2.0.0": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "param-case@^3.0.4": "https://registry.npmjs.org/param-case/-/param-case-3.0.4.tgz", "parent-module@^1.0.0": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "parse-json@^5.0.0": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "parse5-htmlparser2-tree-adapter@^6.0.0": "https://registry.npmjs.org/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-6.0.1.tgz", "parse5@^5.1.1": "https://registry.npmjs.org/parse5/-/parse5-5.1.1.tgz", "parse5@^6.0.1": "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz", "parseurl@~1.3.2": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "parseurl@~1.3.3": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "pascal-case@^3.1.2": "https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.2.tgz", "path-exists@^4.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-key@^2.0.0": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "path-key@^2.0.1": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "path-key@^3.0.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-key@^3.1.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-to-regexp@0.1.12": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "path-type@^4.0.0": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "picocolors@^0.2.1": "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz", "picocolors@^1.0.0": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picocolors@^1.1.1": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "pkg-dir@^4.1.0": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "portfinder@^1.0.26": "https://registry.npmjs.org/portfinder/-/portfinder-1.0.37.tgz", "postcss-calc@^8.2.3": "https://registry.npmjs.org/postcss-calc/-/postcss-calc-8.2.4.tgz", "postcss-colormin@^5.3.1": "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-5.3.1.tgz", "postcss-convert-values@^5.1.3": "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-5.1.3.tgz", "postcss-discard-comments@^5.1.2": "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-5.1.2.tgz", "postcss-discard-duplicates@^5.1.0": "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-5.1.0.tgz", "postcss-discard-empty@^5.1.1": "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-5.1.1.tgz", "postcss-discard-overridden@^5.1.0": "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-5.1.0.tgz", "postcss-loader@^6.1.1": "https://registry.npmjs.org/postcss-loader/-/postcss-loader-6.2.1.tgz", "postcss-merge-longhand@^5.1.7": "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-5.1.7.tgz", "postcss-merge-rules@^5.1.4": "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-5.1.4.tgz", "postcss-minify-font-values@^5.1.0": "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-5.1.0.tgz", "postcss-minify-gradients@^5.1.1": "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-5.1.1.tgz", "postcss-minify-params@^5.1.4": "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-5.1.4.tgz", "postcss-minify-selectors@^5.2.1": "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-5.2.1.tgz", "postcss-modules-extract-imports@^3.1.0": "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz", "postcss-modules-local-by-default@^4.0.5": "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.2.0.tgz", "postcss-modules-scope@^3.2.0": "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.2.1.tgz", "postcss-modules-values@^4.0.0": "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz", "postcss-normalize-charset@^5.1.0": "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-5.1.0.tgz", "postcss-normalize-display-values@^5.1.0": "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-5.1.0.tgz", "postcss-normalize-positions@^5.1.1": "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-5.1.1.tgz", "postcss-normalize-repeat-style@^5.1.1": "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.1.1.tgz", "postcss-normalize-string@^5.1.0": "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-5.1.0.tgz", "postcss-normalize-timing-functions@^5.1.0": "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.1.0.tgz", "postcss-normalize-unicode@^5.1.1": "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-5.1.1.tgz", "postcss-normalize-url@^5.1.0": "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-5.1.0.tgz", "postcss-normalize-whitespace@^5.1.1": "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.1.1.tgz", "postcss-ordered-values@^5.1.3": "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-5.1.3.tgz", "postcss-reduce-initial@^5.1.2": "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-5.1.2.tgz", "postcss-reduce-transforms@^5.1.0": "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-5.1.0.tgz", "postcss-selector-parser@^6.0.2": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.0.4": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.0.5": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.0.9": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^7.0.0": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-7.1.0.tgz", "postcss-svgo@^5.1.0": "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-5.1.0.tgz", "postcss-unique-selectors@^5.1.1": "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-5.1.1.tgz", "postcss-value-parser@^4.1.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.2.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss@^7.0.36": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^8.2.6": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "postcss@^8.3.5": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "postcss@^8.4.33": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "postcss@^8.5.6": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "prelude-ls@^1.2.1": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz", "prettier@^1.18.2 || ^2.0.0": "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz", "pretty-error@^4.0.0": "https://registry.npmjs.org/pretty-error/-/pretty-error-4.0.0.tgz", "process-nextick-args@~2.0.0": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "progress-webpack-plugin@^1.0.12": "https://registry.npmjs.org/progress-webpack-plugin/-/progress-webpack-plugin-1.0.16.tgz", "progress@^2.0.0": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "proxy-addr@~2.0.7": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "proxy-from-env@^1.1.0": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "pseudomap@^1.0.2": "https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz", "pump@^3.0.0": "https://registry.npmjs.org/pump/-/pump-3.0.3.tgz", "punycode@^2.1.0": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "qrcode.vue@^3.6.0": "https://registry.npmjs.org/qrcode.vue/-/qrcode.vue-3.6.0.tgz", "qs@6.13.0": "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz", "queue-microtask@^1.2.2": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "randombytes@^2.1.0": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "range-parser@^1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "range-parser@~1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "raw-body@2.5.2": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "read-pkg-up@^7.0.1": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-7.0.1.tgz", "read-pkg@^5.1.1": "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz", "read-pkg@^5.2.0": "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz", "readable-stream@^2.0.1": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^3.0.6": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readable-stream@^3.4.0": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readdirp@~3.6.0": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "regenerate-unicode-properties@^10.2.0": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz", "regenerate@^1.4.2": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz", "regexpp@^3.1.0": "https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz", "regexpu-core@^6.2.0": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz", "regjsgen@^0.8.0": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz", "regjsparser@^0.12.0": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz", "relateurl@^0.2.7": "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz", "renderkid@^3.0.0": "https://registry.npmjs.org/renderkid/-/renderkid-3.0.0.tgz", "require-directory@^2.1.1": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "require-from-string@^2.0.2": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "requires-port@^1.0.0": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "resolve-from@^4.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "resolve@^1.10.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.22.10": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "restore-cursor@^2.0.0": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "restore-cursor@^3.1.0": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz", "retry@^0.13.1": "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz", "reusify@^1.0.4": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz", "rimraf@^3.0.2": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "run-parallel@^1.1.9": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "safe-buffer@5.2.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@>=5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@~5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.2.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "schema-utils@^2.6.5": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz", "schema-utils@^3.0.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz", "schema-utils@^3.1.1": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz", "schema-utils@^4.0.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz", "schema-utils@^4.3.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz", "schema-utils@^4.3.2": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz", "select-hose@^2.0.0": "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz", "selfsigned@^2.1.1": "https://registry.npmjs.org/selfsigned/-/selfsigned-2.4.1.tgz", "semver@2 || 3 || 4 || 5": "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz", "semver@^5.5.0": "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz", "semver@^6.0.0": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^6.3.1": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^7.2.1": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.3.4": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.3.5": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.5.4": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "send@0.19.0": "https://registry.npmjs.org/send/-/send-0.19.0.tgz", "serialize-javascript@^6.0.0": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz", "serialize-javascript@^6.0.2": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz", "serve-index@^1.9.1": "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz", "serve-static@1.16.2": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "set-function-length@^1.2.2": "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz", "setprototypeof@1.1.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz", "setprototypeof@1.2.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "shallow-clone@^3.0.0": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz", "shebang-command@^1.2.0": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "shebang-command@^2.0.0": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^1.0.0": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "shell-quote@^1.8.1": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz", "side-channel-list@^1.0.0": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "side-channel-map@^1.0.1": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "side-channel-weakmap@^1.0.2": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "side-channel@^1.0.6": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "signal-exit@^3.0.0": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.2": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.3": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "sirv@^2.0.3": "https://registry.npmjs.org/sirv/-/sirv-2.0.4.tgz", "slash@^3.0.0": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "slice-ansi@^4.0.0": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz", "sockjs@^0.3.24": "https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz", "source-map-js@^1.2.1": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "source-map-support@~0.5.20": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map@^0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@~0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@~0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "spdx-correct@^3.0.0": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.2.0.tgz", "spdx-exceptions@^2.1.0": "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz", "spdx-expression-parse@^3.0.0": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "spdx-license-ids@^3.0.0": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.21.tgz", "spdy-transport@^3.0.0": "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz", "spdy@^4.0.2": "https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz", "sprintf-js@~1.0.2": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "ssri@^8.0.1": "https://registry.npmjs.org/ssri/-/ssri-8.0.1.tgz", "stable@^0.1.8": "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz", "stackframe@^1.3.4": "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz", "statuses@2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "statuses@>= 1.4.0 < 2": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "string-width@^2.1.1": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "string-width@^4.1.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string_decoder@^1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "string_decoder@~1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "strip-ansi@^4.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "strip-ansi@^6.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-eof@^1.0.0": "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz", "strip-final-newline@^2.0.0": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "strip-indent@^2.0.0": "https://registry.npmjs.org/strip-indent/-/strip-indent-2.0.0.tgz", "strip-json-comments@^3.1.0": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-json-comments@^3.1.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "stylehacks@^5.1.1": "https://registry.npmjs.org/stylehacks/-/stylehacks-5.1.1.tgz", "supports-color@^5.3.0": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "supports-color@^7.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^8.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "svg-tags@^1.0.0": "https://registry.npmjs.org/svg-tags/-/svg-tags-1.0.0.tgz", "svgo@^2.7.0": "https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz", "table@^6.0.9": "https://registry.npmjs.org/table/-/table-6.9.0.tgz", "tapable@^2.0.0": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "tapable@^2.1.1": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "tapable@^2.2.0": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "tapable@^2.2.1": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "terser-webpack-plugin@^5.1.1": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz", "terser-webpack-plugin@^5.3.11": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz", "terser@^5.10.0": "https://registry.npmjs.org/terser/-/terser-5.43.1.tgz", "terser@^5.31.1": "https://registry.npmjs.org/terser/-/terser-5.43.1.tgz", "text-table@^0.2.0": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "thenify-all@^1.0.0": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz", "thenify@>= 3.1.0 < 4": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz", "thread-loader@^3.0.0": "https://registry.npmjs.org/thread-loader/-/thread-loader-3.0.4.tgz", "thunky@^1.0.2": "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "toidentifier@1.0.1": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "totalist@^3.0.0": "https://registry.npmjs.org/totalist/-/totalist-3.0.1.tgz", "tr46@~0.0.3": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "tslib@^2.0.3": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "type-check@^0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-check@~0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-fest@^0.20.2": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "type-fest@^0.6.0": "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz", "type-fest@^0.8.1": "https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz", "type-is@~1.6.18": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "undici-types@~7.8.0": "https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz", "unicode-canonical-property-names-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz", "unicode-match-property-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "unicode-match-property-value-ecmascript@^2.1.0": "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz", "unicode-property-aliases-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "universalify@^2.0.0": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "unpipe@1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "update-browserslist-db@^1.1.3": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "uri-js@^4.2.2": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "util-deprecate@^1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@^1.0.2": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@~1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "utila@~0.4": "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz", "utils-merge@1.0.1": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "uuid@^8.3.2": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "v8-compile-cache@^2.0.3": "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.4.0.tgz", "validate-npm-package-license@^3.0.1": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "vant@^4.9.21": "https://registry.npmjs.org/vant/-/vant-4.9.21.tgz", "vary@~1.1.2": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "vue-eslint-parser@^8.0.1": "https://registry.npmjs.org/vue-eslint-parser/-/vue-eslint-parser-8.3.0.tgz", "vue-hot-reload-api@^2.3.0": "https://registry.npmjs.org/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz", "vue-loader@^17.0.0": "https://registry.npmjs.org/vue-loader/-/vue-loader-17.4.2.tgz", "vue-router@^4.5.1": "https://registry.npmjs.org/vue-router/-/vue-router-4.5.1.tgz", "vue-style-loader@^4.1.0": "https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-4.1.3.tgz", "vue-style-loader@^4.1.3": "https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-4.1.3.tgz", "vue-template-es2015-compiler@^1.9.0": "https://registry.npmjs.org/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz", "vue@^3.2.13": "https://registry.npmjs.org/vue/-/vue-3.5.17.tgz", "watchpack@^2.4.0": "https://registry.npmjs.org/watchpack/-/watchpack-2.4.4.tgz", "watchpack@^2.4.1": "https://registry.npmjs.org/watchpack/-/watchpack-2.4.4.tgz", "wbuf@^1.1.0": "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz", "wbuf@^1.7.3": "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz", "wcwidth@^1.0.1": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "webidl-conversions@^3.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "webpack-bundle-analyzer@^4.4.0": "https://registry.npmjs.org/webpack-bundle-analyzer/-/webpack-bundle-analyzer-4.10.2.tgz", "webpack-chain@^6.5.1": "https://registry.npmjs.org/webpack-chain/-/webpack-chain-6.5.1.tgz", "webpack-dev-middleware@^5.3.4": "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-5.3.4.tgz", "webpack-dev-server@^4.7.3": "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-4.15.2.tgz", "webpack-merge@^5.7.3": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.10.0.tgz", "webpack-sources@^3.3.3": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.3.tgz", "webpack-virtual-modules@^0.4.2": "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.4.6.tgz", "webpack@^5.54.0": "https://registry.npmjs.org/webpack/-/webpack-5.100.2.tgz", "websocket-driver@>=0.5.1": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz", "websocket-driver@^0.7.4": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz", "websocket-extensions@>=0.1.1": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "whatwg-fetch@^3.6.2": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz", "whatwg-url@^5.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "which@^1.2.9": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "which@^2.0.1": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "wildcard@^2.0.0": "https://registry.npmjs.org/wildcard/-/wildcard-2.0.1.tgz", "word-wrap@^1.2.5": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "wrap-ansi@^3.0.1": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-3.0.1.tgz", "wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "ws@^7.3.1": "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz", "ws@^8.13.0": "https://registry.npmjs.org/ws/-/ws-8.18.3.tgz", "y18n@^5.0.5": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "yallist@^2.1.2": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "yallist@^3.0.2": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "yallist@^4.0.0": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "yaml@^1.10.0": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "yaml@^1.10.2": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "yargs-parser@^20.2.2": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "yargs@^16.0.0": "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz", "yorkie@^2.0.0": "https://registry.npmjs.org/yorkie/-/yorkie-2.0.0.tgz"}, "files": [], "artifacts": {}}